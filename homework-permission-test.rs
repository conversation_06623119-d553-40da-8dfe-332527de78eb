use std::sync::Arc;
use uuid::Uuid;
use sqlx::PgPool;
use anyhow::Result;

use crate::service::permission::data_filter::{
    DataFilterManager, FilterContext, HomeworkDataFilter
};
use crate::service::permission::casbin_service::MultiTenantCasbinService;
use crate::service::permission::hierarchy_resolver::PermissionHierarchyResolver;
use crate::service::permission::data_filter::casbin_query::CasbinQueryHelper;
use crate::middleware::auth_middleware::AuthContext;
use crate::service::homework::homework_service::HomeworkService;

/// 作业权限过滤测试
/// 验证不同角色用户的作业访问权限
pub struct HomeworkPermissionTest {
    pool: PgPool,
    homework_service: HomeworkService,
    casbin_service: Arc<MultiTenantCasbinService>,
    data_filter_manager: Arc<DataFilterManager>,
}

impl HomeworkPermissionTest {
    pub async fn new(pool: PgPool) -> Result<Self> {
        // 初始化 Casbin 服务
        let casbin_service = Arc::new(MultiTenantCasbinService::new(pool.clone()).await?);
        
        // 创建增强的数据过滤器管理器
        let data_filter_manager = Arc::new(DataFilterManager::create_enhanced(
            pool.clone(),
            casbin_service.clone(),
        ));

        // 创建带权限的作业服务
        let homework_service = HomeworkService::new_with_permissions(
            pool.clone(),
            data_filter_manager.clone(),
            casbin_service.clone(),
        );

        Ok(Self {
            pool,
            homework_service,
            casbin_service,
            data_filter_manager,
        })
    }

    /// 测试任课老师的作业访问权限
    pub async fn test_teacher_homework_access(&self) -> Result<()> {
        println!("🧪 测试任课老师的作业访问权限");

        // 创建测试用户（任课老师）
        let teacher_user_id = Uuid::new_v4();
        let tenant_id = "tenant_001";
        let schema_name = "tenant_001";

        // 模拟教师身份上下文
        let auth_context = AuthContext {
            user_id: teacher_user_id,
            user_identity: Some("teacher".to_string()),
            tenant_id: Some(tenant_id.to_string()),
            roles: vec!["teacher".to_string()],
            permissions: vec![],
        };

        // 测试作业列表查询
        let page_params = crate::model::homework::homework::PageHomeworkParams {
            page_params: crate::model::PageParams::new(1, 10),
            name: None,
            status: None,
            subject_group_id: None,
        };

        match self.homework_service.page_homework(&auth_context, &schema_name.to_string(), &page_params).await {
            Ok((homework_list, total)) => {
                println!("✅ 任课老师可以访问 {} 个作业（总计 {}）", homework_list.len(), total);
                
                // 验证返回的作业是否都是该老师有权限访问的
                for homework in &homework_list {
                    println!("   - 作业: {} (ID: {})", homework.homework_name, homework.id);
                }
            },
            Err(e) => {
                println!("❌ 任课老师作业访问失败: {}", e);
                return Err(anyhow::anyhow!("Teacher homework access test failed: {}", e));
            }
        }

        Ok(())
    }

    /// 测试学科组长的作业访问权限
    pub async fn test_subject_leader_homework_access(&self) -> Result<()> {
        println!("🧪 测试学科组长的作业访问权限");

        let subject_leader_user_id = Uuid::new_v4();
        let tenant_id = "tenant_001";
        let schema_name = "tenant_001";

        let auth_context = AuthContext {
            user_id: subject_leader_user_id,
            user_identity: Some("subject_leader".to_string()),
            tenant_id: Some(tenant_id.to_string()),
            roles: vec!["subject_leader".to_string()],
            permissions: vec![],
        };

        let page_params = crate::model::homework::homework::PageHomeworkParams {
            page_params: crate::model::PageParams::new(1, 10),
            name: None,
            status: None,
            subject_group_id: None,
        };

        match self.homework_service.page_homework(&auth_context, &schema_name.to_string(), &page_params).await {
            Ok((homework_list, total)) => {
                println!("✅ 学科组长可以访问 {} 个作业（总计 {}）", homework_list.len(), total);
                
                for homework in &homework_list {
                    println!("   - 作业: {} (学科组: {:?})", homework.homework_name, homework.subject_group_id);
                }
            },
            Err(e) => {
                println!("❌ 学科组长作业访问失败: {}", e);
                return Err(anyhow::anyhow!("Subject leader homework access test failed: {}", e));
            }
        }

        Ok(())
    }

    /// 测试学生的作业访问权限
    pub async fn test_student_homework_access(&self) -> Result<()> {
        println!("🧪 测试学生的作业访问权限");

        let student_user_id = Uuid::new_v4();
        let tenant_id = "tenant_001";
        let schema_name = "tenant_001";

        let auth_context = AuthContext {
            user_id: student_user_id,
            user_identity: Some("student".to_string()),
            tenant_id: Some(tenant_id.to_string()),
            roles: vec!["student".to_string()],
            permissions: vec![],
        };

        let page_params = crate::model::homework::homework::PageHomeworkParams {
            page_params: crate::model::PageParams::new(1, 10),
            name: None,
            status: None,
            subject_group_id: None,
        };

        match self.homework_service.page_homework(&auth_context, &schema_name.to_string(), &page_params).await {
            Ok((homework_list, total)) => {
                println!("✅ 学生可以访问 {} 个作业（总计 {}）", homework_list.len(), total);
                
                for homework in &homework_list {
                    println!("   - 作业: {} (状态: {})", homework.homework_name, homework.homework_status);
                }
            },
            Err(e) => {
                println!("❌ 学生作业访问失败: {}", e);
                return Err(anyhow::anyhow!("Student homework access test failed: {}", e));
            }
        }

        Ok(())
    }

    /// 测试无权限用户的作业访问
    pub async fn test_unauthorized_homework_access(&self) -> Result<()> {
        println!("🧪 测试无权限用户的作业访问");

        let unauthorized_user_id = Uuid::new_v4();
        let tenant_id = "tenant_001";
        let schema_name = "tenant_001";

        let auth_context = AuthContext {
            user_id: unauthorized_user_id,
            user_identity: Some("guest".to_string()),
            tenant_id: Some(tenant_id.to_string()),
            roles: vec!["guest".to_string()],
            permissions: vec![],
        };

        let page_params = crate::model::homework::homework::PageHomeworkParams {
            page_params: crate::model::PageParams::new(1, 10),
            name: None,
            status: None,
            subject_group_id: None,
        };

        match self.homework_service.page_homework(&auth_context, &schema_name.to_string(), &page_params).await {
            Ok((homework_list, total)) => {
                if homework_list.is_empty() {
                    println!("✅ 无权限用户正确返回空作业列表");
                } else {
                    println!("❌ 无权限用户不应该能访问任何作业，但返回了 {} 个作业", homework_list.len());
                    return Err(anyhow::anyhow!("Unauthorized user should not access any homework"));
                }
            },
            Err(e) => {
                println!("✅ 无权限用户作业访问被正确拒绝: {}", e);
            }
        }

        Ok(())
    }

    /// 测试数据过滤器的直接功能
    pub async fn test_homework_filter_directly(&self) -> Result<()> {
        println!("🧪 测试作业数据过滤器的直接功能");

        let query_helper = CasbinQueryHelper::new(self.pool.clone());
        let hierarchy_resolver = Arc::new(PermissionHierarchyResolver::new(
            self.pool.clone(),
            self.casbin_service.clone(),
        ));
        
        let homework_filter = HomeworkDataFilter::new(query_helper, hierarchy_resolver);

        // 创建过滤上下文
        let filter_context = FilterContext {
            user_id: Uuid::new_v4(),
            user_identity: "teacher".to_string(),
            tenant_id: "tenant_001".to_string(),
            schema_name: "tenant_001".to_string(),
            resource: "homework".to_string(),
        };

        // 创建查询构建器
        let mut query_builder = sqlx::QueryBuilder::new("SELECT h.* FROM tenant_001.homework h WHERE 1=1");
        let mut count_builder = sqlx::QueryBuilder::new("SELECT COUNT(*) FROM tenant_001.homework h WHERE 1=1");

        // 应用过滤器
        let filter_applied = homework_filter.apply_filter_to_builders(
            &filter_context,
            &mut query_builder,
            &mut count_builder,
            self.casbin_service.as_ref(),
        ).await?;

        if filter_applied {
            println!("✅ 作业数据过滤器成功应用权限过滤");
            println!("   查询SQL: {}", query_builder.sql());
        } else {
            println!("ℹ️ 用户具有全部权限，无需应用过滤器");
        }

        Ok(())
    }

    /// 运行所有测试
    pub async fn run_all_tests(&self) -> Result<()> {
        println!("🚀 开始作业权限过滤测试");
        println!("=" .repeat(50));

        // 测试各种角色的权限
        self.test_teacher_homework_access().await?;
        println!();

        self.test_subject_leader_homework_access().await?;
        println!();

        self.test_student_homework_access().await?;
        println!();

        self.test_unauthorized_homework_access().await?;
        println!();

        self.test_homework_filter_directly().await?;
        println!();

        println!("✅ 所有作业权限过滤测试完成");
        println!("=" .repeat(50));

        Ok(())
    }

    /// 验证权限过滤的性能
    pub async fn benchmark_permission_filtering(&self) -> Result<()> {
        println!("⏱️ 作业权限过滤性能测试");

        let start_time = std::time::Instant::now();
        let iterations = 100;

        for i in 0..iterations {
            let teacher_user_id = Uuid::new_v4();
            let auth_context = AuthContext {
                user_id: teacher_user_id,
                user_identity: Some("teacher".to_string()),
                tenant_id: Some("tenant_001".to_string()),
                roles: vec!["teacher".to_string()],
                permissions: vec![],
            };

            let page_params = crate::model::homework::homework::PageHomeworkParams {
                page_params: crate::model::PageParams::new(1, 10),
                name: None,
                status: None,
                subject_group_id: None,
            };

            let _ = self.homework_service.page_homework(&auth_context, &"tenant_001".to_string(), &page_params).await;

            if i % 10 == 0 {
                print!(".");
            }
        }

        let duration = start_time.elapsed();
        let avg_time_ms = duration.as_millis() as f64 / iterations as f64;

        println!();
        println!("📊 性能测试结果:");
        println!("   总迭代次数: {}", iterations);
        println!("   总耗时: {:?}", duration);
        println!("   平均每次耗时: {:.2}ms", avg_time_ms);

        if avg_time_ms < 10.0 {
            println!("✅ 权限过滤性能符合要求（< 10ms）");
        } else {
            println!("⚠️ 权限过滤性能需要优化（> 10ms）");
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_homework_permission_filtering() {
        // 这里应该设置测试数据库
        let pool = todo!("Setup test database");
        
        let test_suite = HomeworkPermissionTest::new(pool).await.unwrap();
        test_suite.run_all_tests().await.unwrap();
    }

    #[tokio::test]
    async fn test_homework_permission_performance() {
        let pool = todo!("Setup test database");
        
        let test_suite = HomeworkPermissionTest::new(pool).await.unwrap();
        test_suite.benchmark_permission_filtering().await.unwrap();
    }
}
