# 命名规范
1. 以下是实体与命名的对应关系(初稿)，后续评审及持续完善；
2. 大家可以添加已经命好的类及变量，涉及到跨模块的名称必须填写，不确定的部分需要讨论清楚；
3. 细粒度的名字定好后，组合名称可以不用讨论，如：answer_sheet_leaf，在存在与其他leaf共同使用的场景下自行组合；

## 考试及题卡部分

|  实体   |        名称         |  实体   |       名称       |
|:-----:|:-----------------:|:-----:|:--------------:|
|  考试   |       exam        |  作业   |    homework    |
|  答题卡  |   answer_sheet    |  纸张   |      leaf      |
| 答题卡页  | answer_sheet_page |  页码   |      page      |
|  分数   |       score       |  评分   |    scoring     |
|  年级   |       grade       |  答案   |     answer     |
|  扫描   |       scan        | 评分标准  |   criterion    |
| 答题卡单元 |    unit     | 答题卡块 |  block   |
| 扫描切块  |    scan_block     |  学号   | student_number |
|  试题   |     question      |  考号   |  exam_number   |

## 租户权限部分

|      实体       |   名称   | 实体 |     名称      |
|:-------------:|:------:|:--:|:-----------:|
| 租户(不要再schema) | tenant | 身份 |  identity   |

# 代码规范

1. 提交前，确认可以运行，前后端消除简单打开就能看到的报错（特别是刚刚调试的模块）
2. 非紧急测试/上线等时候，要求消除编译警告
3. 要删除的代码直接删除，避免转注释放在那里，必要情况在旁边TODO说明原因，可保留代码块注释
4. 尽量增加复用（包括代码块级别、函数级别、struct级别等），减少重复逻辑的书写与定义
5. 建议单个文件限制在500行内，超过的梳理逻辑逻辑，拆分文件

# 代码结构规范