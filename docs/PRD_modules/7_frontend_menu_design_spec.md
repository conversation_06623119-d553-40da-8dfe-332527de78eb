# 7. 前端菜单设计规范

## 7.1 概述

本文档定义了 Deep-Mate 教育平台前端菜单系统的设计规范，包括菜单类型化设计、交互行为、视觉规范和技术实现标准。通过统一的设计语言和实现标准，确保用户界面的一致性和易用性。

## 7.2 菜单类型体系

### 7.2.1 类型定义与业务映射

#### 核心类型枚举
```typescript
export enum MenuType {
  FUNCTIONAL = 'functional',    // 功能菜单 - 核心业务功能
  MANAGEMENT = 'management',    // 管理菜单 - 系统管理和配置
  ANALYTICS = 'analytics',      // 分析菜单 - 数据分析和报表
  PERSONAL = 'personal',        // 个人中心 - 个人信息和设置
  EXTERNAL = 'external',        // 外部链接 - 外部系统集成
  NAVIGATION = 'navigation'     // 导航菜单 - 纯导航，无具体功能
}
```

#### 业务场景映射表

| 菜单类型 | 业务场景 | 用户群体 | 典型功能模块 | 权限要求 |
|---------|---------|---------|-------------|---------|
| **Functional** | 核心业务操作 | 所有业务用户 | 学生管理、考试管理、阅卷中心、题库管理 | 基础业务权限 |
| **Management** | 系统管理配置 | 管理员角色 | 用户管理、权限配置、租户管理、系统设置 | 高级管理权限 |
| **Analytics** | 数据分析统计 | 管理层、教师 | 学情分析、考试统计、教学报表、成绩分析 | 数据查看权限 |
| **Personal** | 个人信息管理 | 所有用户 | 个人资料、身份切换、账户设置、消息中心 | 个人权限 |
| **External** | 外部资源链接 | 相关授权用户 | 第三方工具、官方资源、帮助文档、API文档 | 外部访问权限 |
| **Navigation** | 导航结构组织 | 系统级别 | 主导航、分组标题、快捷入口、菜单分割 | 无特殊权限 |

### 7.2.2 角色权限矩阵

| 用户角色 | Functional | Management | Analytics | Personal | External | Navigation |
|---------|------------|------------|-----------|----------|----------|------------|
| **超级管理员** | ✓ 全部 | ✓ 全部 | ✓ 全部 | ✓ | ✓ 全部 | ✓ |
| **租户管理员** | ✓ 租户内 | ✓ 租户管理 | ✓ 租户数据 | ✓ | ✓ 部分 | ✓ |
| **校长** | ✓ 学校业务 | ✓ 学校管理 | ✓ 学校分析 | ✓ | ✓ 教育资源 | ✓ |
| **教导主任** | ✓ 教学管理 | ✓ 教学配置 | ✓ 教学分析 | ✓ | ✓ 教学资源 | ✓ |
| **学科组长** | ✓ 学科功能 | ✗ | ✓ 学科分析 | ✓ | ✓ 学科资源 | ✓ |
| **任课教师** | ✓ 基础功能 | ✗ | ✓ 基础分析 | ✓ | ✓ 教学工具 | ✓ |
| **学生** | ✓ 学习功能 | ✗ | ✓ 个人分析 | ✓ | ✓ 学习资源 | ✓ |

## 7.3 视觉设计规范

### 7.3.1 色彩体系

#### 主色彩定义
```typescript
export const MenuTypeColors = {
  functional: {
    primary: '#1890ff',      // 蓝色 - 可信赖的功能色
    light: '#e6f7ff',
    border: '#91d5ff',
    hover: '#40a9ff',
    active: '#096dd9'
  },
  management: {
    primary: '#722ed1',       // 紫色 - 权威的管理色
    light: '#f9f0ff',
    border: '#d3adf7',
    hover: '#9254de',
    active: '#531dab'
  },
  analytics: {
    primary: '#13c2c2',       // 青色 - 清晰的数据色
    light: '#e6fffb',
    border: '#87e8de',
    hover: '#36cfc9',
    active: '#08979c'
  },
  personal: {
    primary: '#52c41a',       // 绿色 - 友好的个人色
    light: '#f6ffed',
    border: '#b7eb8f',
    hover: '#73d13d',
    active: '#389e0d'
  },
  external: {
    primary: '#fa8c16',       // 橙色 - 醒目的外链色
    light: '#fff7e6',
    border: '#ffd591',
    hover: '#ffa940',
    active: '#d46b08'
  },
  navigation: {
    primary: '#8c8c8c',       // 灰色 - 中性的导航色
    light: '#fafafa',
    border: '#d9d9d9',
    hover: '#bfbfbf',
    active: '#595959'
  }
}
```

#### 色彩使用原则
- **主色彩**：用于图标、标题、按钮等主要元素
- **浅色背景**：用于卡片背景、悬浮状态
- **边框色**：用于分割线、边框装饰
- **交互色**：用于悬停、激活状态的反馈

### 7.3.2 图标体系

#### 默认图标映射
```typescript
export const MenuTypeIcons = {
  functional: 'FunctionOutlined',     // 功能图标
  management: 'SettingOutlined',      // 设置图标
  analytics: 'BarChartOutlined',      // 图表图标
  personal: 'UserOutlined',           // 用户图标
  external: 'LinkOutlined',           // 链接图标
  navigation: 'MenuOutlined'          // 菜单图标
}
```

#### 图标使用规范
- **尺寸**：主导航 16px，二级导航 14px，移动端 18px
- **颜色**：继承菜单类型主色彩
- **状态**：支持默认、悬停、激活、禁用四种状态
- **自定义**：允许为特定菜单项设置自定义图标

### 7.3.3 字体排版

#### 字体规范
```css
.menu-typography {
  /* 主导航标题 */
  --menu-title-font: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --menu-title-size: 14px;
  --menu-title-weight: 500;
  --menu-title-line-height: 1.5;
  
  /* 分组标题 */
  --section-title-size: 12px;
  --section-title-weight: 600;
  --section-title-color: rgba(0,0,0,0.65);
  
  /* 面包屑 */
  --breadcrumb-font-size: 14px;
  --breadcrumb-font-weight: 400;
}
```

## 7.4 交互行为设计

### 7.4.1 点击行为规范

```typescript
interface MenuClickBehavior {
  type: MenuType;
  action: 'navigate' | 'modal' | 'drawer' | 'external' | 'expand';
  confirmation?: boolean;
  loading?: boolean;
  analytics?: boolean;
}

export const MenuClickBehaviors: Record<MenuType, MenuClickBehavior> = {
  functional: {
    type: 'functional',
    action: 'navigate',
    confirmation: false,
    loading: false,
    analytics: true
  },
  management: {
    type: 'management', 
    action: 'navigate',
    confirmation: true,      // 需要权限确认
    loading: false,
    analytics: true
  },
  analytics: {
    type: 'analytics',
    action: 'navigate',
    confirmation: false,
    loading: true,           // 显示数据加载
    analytics: true
  },
  personal: {
    type: 'personal',
    action: 'modal',         // 可能打开模态框
    confirmation: false,
    loading: false,
    analytics: false
  },
  external: {
    type: 'external',
    action: 'external',      // 新窗口打开
    confirmation: false,
    loading: false,
    analytics: true
  },
  navigation: {
    type: 'navigation',
    action: 'expand',        // 展开/收起
    confirmation: false,
    loading: false,
    analytics: false
  }
}
```

### 7.4.2 悬停效果

```css
.menu-item {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  
  &:hover {
    background: var(--menu-hover-bg);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  &.functional:hover { --menu-hover-bg: rgba(24, 144, 255, 0.06); }
  &.management:hover { --menu-hover-bg: rgba(114, 46, 209, 0.06); }
  &.analytics:hover { --menu-hover-bg: rgba(19, 194, 194, 0.06); }
  &.personal:hover { --menu-hover-bg: rgba(82, 196, 26, 0.06); }
  &.external:hover { --menu-hover-bg: rgba(250, 140, 22, 0.06); }
  &.navigation:hover { --menu-hover-bg: rgba(140, 140, 140, 0.06); }
}
```

### 7.4.3 状态管理

```typescript
interface MenuItemState {
  active: boolean;        // 当前激活状态
  disabled: boolean;      // 禁用状态
  loading: boolean;       // 加载状态
  badge?: number;         // 徽章数字
  permission: 'allowed' | 'restricted' | 'forbidden';
}

export const getMenuItemClassName = (type: MenuType, state: MenuItemState): string => {
  const baseClass = `menu-item menu-item-${type}`;
  const stateClasses = [
    state.active && 'active',
    state.disabled && 'disabled', 
    state.loading && 'loading',
    `permission-${state.permission}`
  ].filter(Boolean).join(' ');
  
  return `${baseClass} ${stateClasses}`;
}
```

## 7.5 布局设计规范

### 7.5.1 桌面端布局

#### 侧边栏菜单布局
```tsx
const DesktopSideMenu = () => {
  return (
    <aside className="desktop-side-menu">
      {/* 品牌区域 */}
      <div className="brand-section">
        <Logo />
        <div className="brand-title">Deep-Mate</div>
      </div>
      
      {/* 主要菜单区域 */}
      <div className="menu-sections">
        <MenuSection type="functional" title="核心功能" />
        <MenuSection type="analytics" title="数据分析" />
        <MenuSection type="management" title="系统管理" permission="admin" />
      </div>
      
      {/* 底部区域 */}
      <div className="menu-footer">
        <MenuSection type="personal" title="个人中心" compact />
        <MenuSection type="external" title="帮助资源" compact />
      </div>
    </aside>
  )
}
```

#### 布局尺寸规范
```css
.desktop-side-menu {
  width: 240px;                    /* 展开宽度 */
  min-width: 240px;
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 64px;                   /* 收起宽度 */
  }
  
  .menu-item {
    height: 40px;                  /* 菜单项高度 */
    padding: 0 16px;
    margin: 2px 8px;
    border-radius: 6px;
  }
  
  .section-header {
    height: 32px;
    padding: 8px 16px;
    margin: 16px 8px 8px;
  }
}
```

### 7.5.2 移动端布局

#### 底部标签栏
```tsx
const MobileTabBar = () => {
  const primaryTabs = [
    { type: 'functional', key: 'home', title: '首页', icon: <HomeOutlined /> },
    { type: 'analytics', key: 'stats', title: '分析', icon: <BarChartOutlined /> },
    { type: 'personal', key: 'profile', title: '我的', icon: <UserOutlined /> }
  ];
  
  return (
    <div className="mobile-tab-bar">
      {primaryTabs.map(tab => (
        <div key={tab.key} className={`tab-item tab-${tab.type}`}>
          {tab.icon}
          <span className="tab-title">{tab.title}</span>
        </div>
      ))}
      <div className="tab-item tab-more">
        <MenuOutlined />
        <span className="tab-title">更多</span>
      </div>
    </div>
  )
}
```

#### 抽屉式菜单
```tsx
const MobileDrawerMenu = () => {
  return (
    <Drawer
      placement="left"
      className="mobile-menu-drawer"
      width="280px"
    >
      <div className="drawer-header">
        <Avatar src={userAvatar} />
        <div className="user-info">
          <div className="user-name">{userName}</div>
          <div className="user-role">{userRole}</div>
        </div>
      </div>
      
      <div className="drawer-menu-sections">
        <MenuSection type="functional" />
        <MenuSection type="management" />
        <MenuSection type="analytics" />
        <MenuSection type="external" />
      </div>
    </Drawer>
  )
}
```

## 7.6 无障碍设计

### 7.6.1 键盘操作支持

```typescript
const MenuKeyboardNavigation = {
  // 方向键导航
  ArrowDown: 'focusNext',
  ArrowUp: 'focusPrevious', 
  ArrowRight: 'expandSubmenu',
  ArrowLeft: 'collapseSubmenu',
  
  // 功能键
  Enter: 'activateMenuItem',
  Space: 'activateMenuItem',
  Escape: 'closeSubmenu',
  Home: 'focusFirst',
  End: 'focusLast',
  
  // 快捷键
  'Alt + M': 'toggleMainMenu',
  'Ctrl + /', 'showMenuSearch'
}
```

### 7.6.2 ARIA 属性规范

```tsx
const AccessibleMenuItem = ({ item, expanded, selected }) => {
  return (
    <div
      role="menuitem"
      aria-label={item.name}
      aria-expanded={item.children ? expanded : undefined}
      aria-selected={selected}
      aria-disabled={!item.enabled}
      tabIndex={selected ? 0 : -1}
      className={getMenuItemClassName(item.type, item.state)}
    >
      <MenuIcon type={item.type} aria-hidden="true" />
      <span className="menu-title">{item.name}</span>
      {item.badge && (
        <Badge 
          count={item.badge} 
          aria-label={`${item.badge} 条未读消息`}
        />
      )}
    </div>
  )
}
```

## 7.7 性能优化

### 7.7.1 懒加载策略

```typescript
// 菜单数据懒加载
const useMenuData = () => {
  const [menuSections, setMenuSections] = useState<MenuSection[]>([]);
  
  useEffect(() => {
    // 优先加载功能菜单和个人菜单
    loadMenuSections(['functional', 'personal']).then(setMenuSections);
    
    // 延迟加载其他菜单类型
    setTimeout(() => {
      loadMenuSections(['analytics', 'management', 'external'])
        .then(additionalSections => {
          setMenuSections(prev => [...prev, ...additionalSections]);
        });
    }, 100);
  }, []);
  
  return menuSections;
}
```

### 7.7.2 缓存策略

```typescript
// 菜单权限缓存
const MenuPermissionCache = {
  cache: new Map<string, boolean>(),
  TTL: 5 * 60 * 1000, // 5分钟
  
  checkPermission(menuId: string, permissions: string[]): boolean {
    const cacheKey = `${menuId}_${permissions.join(',')}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    const hasPermission = this.calculatePermission(menuId, permissions);
    this.cache.set(cacheKey, hasPermission);
    
    // 设置过期清理
    setTimeout(() => this.cache.delete(cacheKey), this.TTL);
    
    return hasPermission;
  }
}
```

## 7.8 测试规范

### 7.8.1 单元测试

```typescript
describe('MenuType Component', () => {
  test.each([
    ['functional', 'FunctionOutlined', '#1890ff'],
    ['management', 'SettingOutlined', '#722ed1'],
    ['analytics', 'BarChartOutlined', '#13c2c2'],
    ['personal', 'UserOutlined', '#52c41a'],
    ['external', 'LinkOutlined', '#fa8c16'],
    ['navigation', 'MenuOutlined', '#8c8c8c']
  ])('should render %s menu type correctly', (type, expectedIcon, expectedColor) => {
    const { getByRole } = render(
      <MenuItem type={type as MenuType} name="Test Menu" />
    );
    
    const menuItem = getByRole('menuitem');
    expect(menuItem).toHaveClass(`menu-item-${type}`);
    expect(menuItem.querySelector('.menu-icon')).toHaveStyle(`color: ${expectedColor}`);
  });
});
```

### 7.8.2 可访问性测试

```typescript
describe('Menu Accessibility', () => {
  test('should support keyboard navigation', async () => {
    const { container } = render(<MenuComponent />);
    const firstMenuItem = container.querySelector('[role="menuitem"]');
    
    firstMenuItem?.focus();
    expect(document.activeElement).toBe(firstMenuItem);
    
    fireEvent.keyDown(firstMenuItem!, { key: 'ArrowDown' });
    const secondMenuItem = container.querySelectorAll('[role="menuitem"]')[1];
    expect(document.activeElement).toBe(secondMenuItem);
  });
  
  test('should have proper ARIA attributes', () => {
    const { getByRole } = render(<MenuItem type="functional" name="测试菜单" />);
    const menuItem = getByRole('menuitem');
    
    expect(menuItem).toHaveAttribute('aria-label', '测试菜单');
    expect(menuItem).toHaveAttribute('tabIndex');
  });
});
```

## 7.9 实施指南

### 7.9.1 开发清单

#### 基础组件开发
- [ ] `MenuTypeIcon` 组件实现
- [ ] `MenuTypeColors` 主题系统
- [ ] `MenuItem` 基础组件
- [ ] `MenuSection` 分组组件

#### 布局组件开发
- [ ] `DesktopSideMenu` 桌面端侧边栏
- [ ] `MobileTabBar` 移动端标签栏
- [ ] `MobileDrawerMenu` 移动端抽屉菜单
- [ ] `BreadcrumbNavigation` 面包屑导航

#### 高级功能开发
- [ ] 权限验证集成
- [ ] 菜单数据懒加载
- [ ] 键盘导航支持
- [ ] 无障碍功能支持

### 7.9.2 集成步骤

1. **主题配置**：在项目中集成菜单类型色彩主题
2. **组件注册**：注册所有菜单相关组件
3. **权限集成**：与后端权限系统集成
4. **路由配置**：配置菜单与路由的映射关系
5. **测试验证**：完成单元测试和集成测试

### 7.9.3 维护规范

- **定期审查**：每季度审查菜单结构和用户反馈
- **性能监控**：监控菜单加载性能和用户交互数据
- **无障碍审计**：定期进行无障碍功能审计
- **用户体验优化**：基于用户行为数据持续优化

---

本设计规范为 Deep-Mate 教育平台前端菜单系统提供了完整的设计指导，确保在多种设备和使用场景下都能提供一致、高效、易用的用户体验。