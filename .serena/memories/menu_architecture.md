# 菜单系统架构

## 核心文件
1. `frontend/src/components/navigation/DynamicNavigation.tsx` - 主要导航组件
2. `frontend/src/components/ui/sidebar.tsx` - shadcn/ui侧边栏组件
3. `frontend/src/contexts/MenuContext.tsx` - 菜单权限上下文
4. `frontend/src/contexts/PermissionContext.tsx` - 权限管理上下文

## 菜单组件结构
- `DynamicNavigation` - 主导航组件，按功能分组菜单
- `DynamicMenuGroup` - 菜单分组组件  
- `DynamicMenuItem` - 单个菜单项组件，支持子菜单折叠
- 使用 `NavLink` 处理路由，`SidebarMenuButton` 渲染UI

## 菜单分类
- `functional` - 主要功能
- `admin` - 管理功能  
- `system` - 系统管理
- `personal` - 个人中心

## 图标系统
使用 lucide-react 图标库，通过 `iconMap` 映射图标名称到组件。

## 权限控制
通过 `usePermissions` hook 获取可访问菜单，支持基于角色的访问控制。

## 选中状态问题
当前菜单缺少激活状态显示：
- `SidebarMenuButton` 支持 `isActive` 属性和 `data-[active=true]` 样式
- `NavLink` 自动提供激活状态，但未传递给 `SidebarMenuButton`
- 需要利用 `NavLink` 的 render prop 或 `useLocation` 来设置激活状态