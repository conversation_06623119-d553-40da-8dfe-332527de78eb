# 菜单选中样式实现

## 实现内容
为菜单系统添加了选中状态样式，当用户导航到某个菜单项时，该菜单项会高亮显示。

## 修改的文件
- `frontend/src/components/navigation/DynamicNavigation.tsx`

## 具体修改
1. **导入 useLocation hook**：
   - 从 `react-router-dom` 导入 `useLocation`
   - 用于获取当前路由位置

2. **添加激活状态检测**：
   - 在 `DynamicMenuItem` 组件中使用 `useLocation()` 获取当前路径
   - 通过比较 `location.pathname === menu.path` 判断是否激活
   - 为 `SidebarMenuButton` 组件传递 `isActive` 属性

3. **样式应用机制**：
   - shadcn/ui 的 `SidebarMenuButton` 会根据 `isActive` 属性应用 `data-[active=true]` 样式
   - 激活状态下会使用 `bg-sidebar-accent` 背景色和 `font-medium` 加粗字体

## 技术细节
- 使用 React Router 的 `useLocation` hook 获取当前路由
- 利用 shadcn/ui 组件库内置的激活状态样式系统
- 仅对叶子节点菜单项（没有子菜单的项目）应用激活状态
- 父级菜单项（有子菜单的）不应用激活状态，保持折叠/展开功能

## 最终效果
- 当用户点击并导航到某个菜单项时，该菜单项会显示为选中状态
- 选中状态包括：背景高亮、字体加粗等视觉反馈
- 提升了用户体验，让用户清楚知道当前在哪个页面