# 深度学伴 (Deep-Mate) 项目概览

## 项目简介
深度学伴是面向学校提供高效作业考试的解决方案，特色功能包括：
- 教辅练习、真题模拟组卷、单词听写
- 古诗词、词汇练习、学校周测、多校联考
- AI评分/AI辅助评分
- 写阅后扫录入成绩
- 学生学习记录与错题管理

## 技术栈
### 前端 (frontend/)
- React + TypeScript + Vite
- shadcn/ui 组件库
- TailwindCSS
- React Router (NavLink)

### 后端 (backend/)
- Rust + Axum web framework
- PostgreSQL 数据库
- JWT 认证

## 项目结构
```
/deep-mate/
├── frontend/           # React前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   │   ├── ui/           # shadcn/ui组件
│   │   │   └── navigation/   # 导航组件
│   │   ├── pages/            # 页面组件
│   │   ├── contexts/         # React Context
│   │   ├── services/         # API服务
│   │   └── router/           # 路由配置
├── backend/            # Rust后端服务
└── docs/               # 项目文档
```

## 运行命令
### 前端开发
```bash
cd frontend
npm run dev          # 启动开发服务器 (http://localhost:5173)
npm run build        # 构建生产版本
npm run lint         # 代码检查
npm run typecheck    # TypeScript类型检查
```

### 后端开发  
```bash
cd backend
cargo run            # 启动开发服务器 (http://localhost:8080)
cargo build          # 构建项目
cargo test           # 运行测试
```