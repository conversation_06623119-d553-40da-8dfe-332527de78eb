# IdentityService 数据库操作迁移到 Repository 模式

## 概述

本次迁移将 `IdentityService` 中的直接数据库操作重构为使用 Repository 模式，提高了代码的可维护性、可测试性和架构一致性。

## 迁移内容

### 1. 扩展 UserIdentityRepository

在 `backend/src/repository/user/identities/user_identity_repository.rs` 中添加了以下方法来支持 `IdentityService` 的需求：

#### 简单 CRUD 操作方法：
- `get_user_identities_in_schema_simple()` - 获取用户在指定schema中的所有身份
- `create_user_identity_simple()` - 创建单个用户身份
- `get_user_identity_in_schema_simple()` - 获取特定用户身份
- `update_user_identity_simple()` - 更新用户身份（支持动态字段）
- `delete_user_identity_simple()` - 删除用户身份

#### 跨租户和复杂业务逻辑方法：
- `get_all_user_identities_across_tenants()` - 获取用户在所有租户中的身份
- `get_roles_map()` - 获取角色映射
- `get_schema_name_by_tenant_id()` - 根据租户ID获取schema名称

#### 身份绑定相关方法：
- `get_student_id_by_number()` - 根据学号查询学生ID
- `get_student_id_by_name()` - 根据姓名查询学生ID
- `update_student_user_id()` - 更新学生的用户ID
- `get_teacher_by_user_id()` - 查询教师信息
- `get_administrative_class_by_teacher_id()` - 查询行政班信息
- `get_teaching_class_by_teacher_id()` - 查询教学班信息
- `update_teacher_user_id()` - 更新教师的用户ID
- `create_user_tenant_link()` - 创建用户租户链接
- `batch_insert_user_identities()` - 批量插入用户身份

### 2. 重构 IdentityService

在 `backend/src/service/user/identity_service.rs` 中进行了以下修改：

#### 构造函数更新：
- 添加了 `UserIdentityRepository` 依赖注入
- 更新构造函数签名：`new(db: PgPool, repository: Arc<UserIdentityRepository>)`

#### 方法重构：
- `get_user_identities_in_schema()` - 使用 repository 的简单方法
- `create_user_identity()` - 使用 repository 创建身份
- `get_user_identity_in_schema()` - 使用 repository 查询身份
- `update_user_identity()` - 使用 repository 更新身份
- `delete_user_identity()` - 使用 repository 删除身份
- `get_identities()` - 使用 repository 跨租户查询
- `bind_identity()` - 重构复杂的身份绑定逻辑，使用多个 repository 方法

### 3. 更新依赖注入

在 `backend/src/service/user/auth_integration.rs` 中：
- 更新了 `IdentityService` 的初始化代码
- 添加了 `UserIdentityRepository` 的创建和注入

## 迁移优势

### 1. 架构一致性
- 与项目中其他服务保持一致的 Repository 模式
- 清晰的分层架构：Controller -> Service -> Repository -> Database

### 2. 可维护性
- 数据库操作集中在 Repository 层
- 业务逻辑与数据访问逻辑分离
- 更容易进行代码重构和优化

### 3. 可测试性
- Repository 层可以独立测试
- Service 层可以通过 Mock Repository 进行单元测试
- 更好的测试覆盖率

### 4. 代码复用
- Repository 方法可以被多个 Service 复用
- 减少重复的数据库操作代码

### 5. 错误处理
- 统一的错误处理机制
- 更好的错误传播和转换

## 向后兼容性

- 保持了 `IdentityService` 的公共接口不变
- 现有的 Controller 和其他调用方无需修改
- 功能行为保持一致

## 测试

创建了测试文件 `backend/tests/identity_service_migration_test.rs` 来验证迁移的正确性：
- 集成测试验证 Service 和 Repository 的协作
- CRUD 操作测试
- 复杂业务逻辑测试
- 跨租户功能测试

## 后续工作

1. **完善测试覆盖**：添加更多的单元测试和集成测试
2. **性能优化**：分析和优化数据库查询性能
3. **文档更新**：更新相关的 API 文档和开发文档
4. **监控和日志**：添加适当的监控和日志记录

## 注意事项

1. 确保数据库连接池配置适当，以支持增加的数据库操作
2. 监控性能影响，特别是复杂的跨租户查询
3. 在生产环境部署前进行充分的测试

## 结论

本次迁移成功地将 `IdentityService` 的数据库操作重构为 Repository 模式，提高了代码质量和架构一致性，为后续的功能开发和维护奠定了良好的基础。
