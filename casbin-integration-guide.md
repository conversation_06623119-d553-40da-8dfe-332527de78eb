# Casbin 数据权限控制系统集成指南

## 概述

本指南说明如何将新实现的 Casbin 数据权限控制功能集成到现有的 Deep-Mate 系统中。

## 集成步骤

### 第一步：数据库迁移

1. **运行新的迁移文件**
```bash
cd backend
sqlx migrate run
```

2. **验证迁移结果**
```sql
-- 检查新表是否创建成功
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'sensitive_data_access_log',
    'permission_failure_log', 
    'permission_usage_stats',
    'anomaly_access_detection',
    'permission_change_history',
    'audit_retention_policy'
);
```

### 第二步：更新服务配置

1. **修改 `backend/src/service/permission/mod.rs`**
```rust
pub mod hierarchy_resolver;
pub mod cache;
pub mod data_filter;

pub use hierarchy_resolver::PermissionHierarchyResolver;
pub use cache::{PermissionCache, PermissionCacheConfig};
```

2. **更新数据过滤器模块 `backend/src/service/permission/data_filter/mod.rs`**
```rust
pub mod base;
pub mod casbin_query;
pub mod casbin_based;
pub mod manager;
pub mod exam_filter;
pub mod grade_filter;
pub mod student_filter;

pub use base::*;
pub use manager::*;
pub use exam_filter::ExamDataFilter;
pub use grade_filter::GradeDataFilter;
pub use student_filter::StudentDataFilter;
```

### 第三步：注册新的数据过滤器

1. **修改 `backend/src/service/permission/data_filter/manager.rs`**
```rust
impl DataFilterManager {
    pub fn new(pool: PgPool, casbin_service: Arc<MultiTenantCasbinService>) -> Self {
        let mut manager = Self {
            filters: HashMap::new(),
            config: DataFilterConfig::default(),
        };

        // 注册现有过滤器
        let query_helper = CasbinQueryHelper::new(pool.clone());
        let hierarchy_resolver = Arc::new(PermissionHierarchyResolver::new(
            pool.clone(), 
            casbin_service.clone()
        ));

        // 注册新的业务过滤器
        manager.register_filter(
            "exam",
            Arc::new(ExamDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );
        
        manager.register_filter(
            "grade", 
            Arc::new(GradeDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );
        
        manager.register_filter(
            "student",
            Arc::new(StudentDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );

        manager
    }
}
```

### 第四步：集成权限缓存

1. **修改应用状态 `backend/src/main.rs`**
```rust
use crate::service::permission::{PermissionCache, PermissionCacheConfig};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // ... 现有初始化代码 ...

    // 初始化权限缓存
    let cache_config = PermissionCacheConfig {
        ttl_seconds: 300,  // 5分钟
        max_entries: 10000,
        cleanup_interval_seconds: 60,
        enable_metrics: true,
    };
    let permission_cache = Arc::new(PermissionCache::new(cache_config));

    // 更新 Casbin 服务以使用缓存
    let casbin_service = Arc::new(
        MultiTenantCasbinService::new_with_cache(pool.clone(), permission_cache.clone()).await?
    );

    // ... 其余应用设置 ...
}
```

2. **修改 Casbin 服务以支持缓存**
```rust
// 在 backend/src/service/permission/casbin_service.rs 中添加
impl MultiTenantCasbinService {
    pub async fn new_with_cache(
        pool: PgPool, 
        cache: Arc<PermissionCache>
    ) -> Result<Self> {
        let mut service = Self::new(pool).await?;
        service.permission_cache = Some(cache);
        Ok(service)
    }

    pub async fn get_user_data_scopes_cached(
        &self,
        user_identity: &str,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Vec<DataScope>> {
        // 尝试从缓存获取
        if let Some(cache) = &self.permission_cache {
            let user_id = self.extract_user_id_from_identity(user_identity)?;
            
            if let Some(cached_scopes) = cache.get_cached_permissions(&user_id, tenant_id, resource).await? {
                return Ok(cached_scopes);
            }
        }

        // 缓存未命中，从数据库获取
        let scopes = self.get_user_data_scopes(user_identity, tenant_id, resource).await?;

        // 缓存结果
        if let Some(cache) = &self.permission_cache {
            let user_id = self.extract_user_id_from_identity(user_identity)?;
            cache.cache_user_permissions(&user_id, tenant_id, resource, scopes.clone()).await?;
        }

        Ok(scopes)
    }
}
```

### 第五步：更新权限中间件

1. **增强权限中间件性能监控**
```rust
// 修改 backend/src/middleware/permission_middleware.rs
pub async fn permission_middleware(
    State(casbin_service): State<Arc<MultiTenantCasbinService>>,
    State(audit_service): State<Arc<PermissionAuditService>>,
    Extension(config): Extension<PermissionConfig>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = std::time::Instant::now();
    
    // 获取认证上下文
    let auth_context = request.extensions().get::<AuthContext>()
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // 从请求中提取资源和动作
    let (resource, action) = extract_resource_and_action(&request)?;
    let tenant_id = extract_tenant_id(&request)?;

    // 构建用户上下文
    let user_context = UserContext::from_auth_context(auth_context, &tenant_id)
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // 权限检查
    let permission_result = casbin_service.enforce(&PermissionRequest {
        subject: user_context.identity_string(),
        domain: tenant_id.clone(),
        object: resource.clone(),
        action: action.clone(),
    }).await;

    let duration = start_time.elapsed();
    let processing_time_ms = duration.as_millis() as i32;

    match permission_result {
        Ok(true) => {
            // 权限检查成功，记录统计
            audit_service.update_permission_stats(
                &tenant_id,
                &auth_context.user_id,
                &resource,
                &action,
                true,
                processing_time_ms,
            ).await.ok(); // 忽略审计错误

            // 性能监控
            if processing_time_ms > 10 {
                warn!(
                    "Permission check took {}ms for user {} (exceeds 10ms threshold)",
                    processing_time_ms,
                    auth_context.user_id
                );
            }

            Ok(next.run(request).await)
        },
        Ok(false) => {
            // 权限检查失败，记录失败日志
            audit_service.log_permission_failure(
                &auth_context.user_id,
                &tenant_id,
                &resource,
                &action,
                "Insufficient permissions",
                extract_client_ip(&request),
            ).await.ok();

            audit_service.update_permission_stats(
                &tenant_id,
                &auth_context.user_id,
                &resource,
                &action,
                false,
                processing_time_ms,
            ).await.ok();

            Err(StatusCode::FORBIDDEN)
        },
        Err(e) => {
            // 权限检查错误
            error!("Permission check error: {}", e);
            
            audit_service.log_permission_failure(
                &auth_context.user_id,
                &tenant_id,
                &resource,
                &action,
                &format!("Permission check error: {}", e),
                extract_client_ip(&request),
            ).await.ok();

            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}
```

### 第六步：创建权限审计服务

1. **创建 `backend/src/service/permission/audit.rs`**
```rust
use sqlx::PgPool;
use uuid::Uuid;
use anyhow::Result;
use tracing::{info, error};

pub struct PermissionAuditService {
    pool: PgPool,
}

impl PermissionAuditService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn log_sensitive_data_access(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        action: &str,
        target_ids: &[Uuid],
        request_ip: Option<&str>,
    ) -> Result<()> {
        let query = r#"
            INSERT INTO public.sensitive_data_access_log 
            (user_id, tenant_id, resource, action, target_ids, target_count, request_ip)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
        "#;
        
        sqlx::query(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(resource)
            .bind(action)
            .bind(target_ids)
            .bind(target_ids.len() as i32)
            .bind(request_ip)
            .execute(&self.pool)
            .await?;
            
        info!("Logged sensitive data access for user {} in tenant {}", user_id, tenant_id);
        Ok(())
    }

    pub async fn update_permission_stats(
        &self,
        tenant_id: &str,
        user_id: &Uuid,
        resource: &str,
        action: &str,
        success: bool,
        processing_time_ms: i32,
    ) -> Result<()> {
        let query = "SELECT update_permission_usage_stats($1, $2, $3, $4, $5, $6)";
        
        sqlx::query(query)
            .bind(tenant_id)
            .bind(user_id)
            .bind(resource)
            .bind(action)
            .bind(success)
            .bind(processing_time_ms)
            .execute(&self.pool)
            .await?;
            
        Ok(())
    }
}
```

### 第七步：配置环境变量

1. **更新 `backend/.env`**
```bash
# 权限缓存配置
PERMISSION_CACHE_TTL_SECONDS=300
PERMISSION_CACHE_MAX_ENTRIES=10000
PERMISSION_CACHE_CLEANUP_INTERVAL=60

# 审计配置
ENABLE_PERMISSION_AUDIT=true
AUDIT_SENSITIVE_RESOURCES=student,grade,exam
AUDIT_LOG_LEVEL=info

# 性能监控
PERMISSION_CHECK_TIMEOUT_MS=10
ENABLE_PERFORMANCE_METRICS=true
```

### 第八步：测试集成

1. **创建集成测试**
```rust
// tests/permission_integration_test.rs
#[tokio::test]
async fn test_permission_hierarchy_resolution() {
    // 测试权限层级解析
}

#[tokio::test]
async fn test_exam_data_filtering() {
    // 测试考试数据过滤
}

#[tokio::test]
async fn test_permission_caching() {
    // 测试权限缓存
}

#[tokio::test]
async fn test_audit_logging() {
    // 测试审计日志
}
```

2. **运行测试**
```bash
cd backend
cargo test permission_integration_test
```

## 验证清单

- [ ] 数据库迁移成功执行
- [ ] 新的数据过滤器正确注册
- [ ] 权限缓存正常工作
- [ ] 审计日志正确记录
- [ ] 性能监控指标正常
- [ ] 权限检查时间在10ms内
- [ ] 所有集成测试通过

## 监控和维护

### 性能监控
```sql
-- 查看权限检查性能统计
SELECT 
    resource,
    action,
    AVG(avg_processing_time_ms) as avg_time,
    SUM(success_count) as total_success,
    SUM(failure_count) as total_failure
FROM permission_usage_stats 
WHERE stats_date >= CURRENT_DATE - 7
GROUP BY resource, action
ORDER BY avg_time DESC;
```

### 审计报告
```sql
-- 查看异常访问检测
SELECT 
    anomaly_type,
    severity,
    COUNT(*) as count,
    COUNT(*) FILTER (WHERE NOT is_resolved) as unresolved
FROM anomaly_access_detection 
WHERE detected_at >= CURRENT_DATE - 7
GROUP BY anomaly_type, severity
ORDER BY count DESC;
```

### 缓存监控
```rust
// 在应用中添加缓存监控端点
async fn cache_metrics(
    State(permission_cache): State<Arc<PermissionCache>>,
) -> Json<CacheMetrics> {
    Json(permission_cache.get_metrics().await)
}
```

## 故障排除

### 常见问题

1. **权限检查超时**
   - 检查数据库连接池配置
   - 验证索引是否正确创建
   - 检查缓存是否正常工作

2. **缓存未命中率高**
   - 调整缓存TTL配置
   - 检查缓存清理策略
   - 验证缓存键生成逻辑

3. **审计日志过多**
   - 调整敏感资源配置
   - 配置日志级别
   - 设置合适的保留策略

## 后续优化

1. **性能优化**
   - 实现分布式缓存（Redis）
   - 优化数据库查询
   - 添加查询结果缓存

2. **功能增强**
   - 实现权限预览功能
   - 添加批量权限操作
   - 实现权限变更审批流程

3. **监控改进**
   - 集成 Prometheus 指标
   - 添加告警规则
   - 实现实时监控面板
