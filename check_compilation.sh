#!/bin/bash

# 检查编译脚本
echo "=== 检查 IdentityService 迁移编译状态 ==="

# 检查当前目录
echo "当前目录: $(pwd)"
echo "目录内容:"
ls -la

# 检查 backend 目录
if [ -d "backend" ]; then
    echo "✅ backend 目录存在"
    cd backend
    
    # 检查 Cargo.toml
    if [ -f "Cargo.toml" ]; then
        echo "✅ Cargo.toml 存在"
        
        # 设置 SQLX 离线模式并检查编译
        echo "🔍 开始编译检查..."
        export SQLX_OFFLINE=true
        
        # 运行 cargo check
        if cargo check; then
            echo "✅ 编译检查通过！"
            echo "🎉 IdentityService 迁移到 Repository 模式成功完成！"
        else
            echo "❌ 编译检查失败"
            echo "需要修复编译错误"
        fi
    else
        echo "❌ Cargo.toml 不存在"
    fi
else
    echo "❌ backend 目录不存在"
fi
