-- Fix casbin_policies column sizes to support longer values
-- Migration: 20250901_fix_casbin_policies_column_sizes.sql
-- Description: Increase column sizes in casbin_policies table to support longer policy values

-- ================================================================
-- ALTER CASBIN_POLICIES TABLE COLUMN SIZES
-- ================================================================

-- Increase the size of v0-v5 columns to match the original migration
ALTER TABLE public.casbin_policies 
    ALTER COLUMN v0 TYPE VARCHAR(256),
    ALTER COLUMN v1 TYPE VARCHAR(256),
    ALTER COLUMN v2 TYPE VARCHAR(256),
    ALTER COLUMN v3 TYPE VARCHAR(256),
    ALTER COLUMN v4 TYPE VARCHAR(256),
    ALTER COLUMN v5 TYPE VARCHAR(256);

-- ================================================================
-- COMMENTS
-- ================================================================

COMMENT ON TABLE public.casbin_policies IS 'Casbin RBAC 权限策略存储表 - 已修复列大小';
COMMENT ON COLUMN public.casbin_policies.v0 IS '主体 (subject) - 支持最多256字符';
COMMENT ON COLUMN public.casbin_policies.v1 IS '域 (domain/tenant) - 支持最多256字符';
COMMENT ON COLUMN public.casbin_policies.v2 IS '对象 (object) - 支持最多256字符';
COMMENT ON COLUMN public.casbin_policies.v3 IS '动作 (action) - 支持最多256字符';
COMMENT ON COLUMN public.casbin_policies.v4 IS '效果 (effect: allow/deny) - 支持最多256字符';
COMMENT ON COLUMN public.casbin_policies.v5 IS '扩展字段 - 支持最多256字符';