-- 增强权限审计系统
-- Migration: 20250906_enhanced_permission_audit.sql
-- Description: Add comprehensive permission audit tables and functions

-- ================================================================
-- 敏感数据访问日志表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.sensitive_data_access_log (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id),
    tenant_id VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,           -- 资源类型: student, exam, grade等
    action VARCHAR(50) NOT NULL,              -- 操作类型: read, write, delete等
    target_ids UUID[],                        -- 访问的具体资源ID列表
    target_count INTEGER DEFAULT 0,          -- 访问的资源数量
    request_ip INET,                          -- 请求IP地址
    user_agent TEXT,                          -- 用户代理
    request_path VARCHAR(500),                -- 请求路径
    response_status INTEGER,                  -- 响应状态码
    processing_time_ms INTEGER,               -- 处理时间（毫秒）
    accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 索引优化
    CONSTRAINT chk_target_count CHECK (target_count >= 0)
);

-- 创建索引
CREATE INDEX idx_sensitive_access_user_time ON public.sensitive_data_access_log(user_id, accessed_at DESC);
CREATE INDEX idx_sensitive_access_tenant_time ON public.sensitive_data_access_log(tenant_id, accessed_at DESC);
CREATE INDEX idx_sensitive_access_resource ON public.sensitive_data_access_log(resource, accessed_at DESC);
CREATE INDEX idx_sensitive_access_ip ON public.sensitive_data_access_log(request_ip, accessed_at DESC);

-- ================================================================
-- 权限检查失败日志表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.permission_failure_log (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    tenant_id VARCHAR(100),
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    failure_reason VARCHAR(200) NOT NULL,     -- 失败原因
    failure_type VARCHAR(50) NOT NULL,        -- 失败类型: NO_PERMISSION, INVALID_SCOPE, EXPIRED_TOKEN等
    request_context JSONB,                    -- 请求上下文信息
    user_roles TEXT[],                        -- 用户当时的角色列表
    attempted_scope VARCHAR(200),             -- 尝试访问的范围
    request_ip INET,
    user_agent TEXT,
    request_path VARCHAR(500),
    failed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_permission_failure_user_time ON public.permission_failure_log(user_id, failed_at DESC);
CREATE INDEX idx_permission_failure_tenant_time ON public.permission_failure_log(tenant_id, failed_at DESC);
CREATE INDEX idx_permission_failure_type ON public.permission_failure_log(failure_type, failed_at DESC);
CREATE INDEX idx_permission_failure_ip ON public.permission_failure_log(request_ip, failed_at DESC);

-- ================================================================
-- 权限使用统计表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.permission_usage_stats (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES public.users(id),
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    total_processing_time_ms BIGINT DEFAULT 0,
    avg_processing_time_ms DECIMAL(10,2) DEFAULT 0,
    last_access_at TIMESTAMP WITH TIME ZONE,
    stats_date DATE NOT NULL,                 -- 统计日期
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 唯一约束
    UNIQUE(tenant_id, user_id, resource, action, stats_date)
);

-- 创建索引
CREATE INDEX idx_permission_stats_tenant_date ON public.permission_usage_stats(tenant_id, stats_date DESC);
CREATE INDEX idx_permission_stats_user_date ON public.permission_usage_stats(user_id, stats_date DESC);
CREATE INDEX idx_permission_stats_resource ON public.permission_usage_stats(resource, stats_date DESC);

-- ================================================================
-- 异常访问检测表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.anomaly_access_detection (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id),
    tenant_id VARCHAR(100) NOT NULL,
    anomaly_type VARCHAR(50) NOT NULL,        -- 异常类型: UNUSUAL_TIME, HIGH_FREQUENCY, SUSPICIOUS_IP等
    severity VARCHAR(20) NOT NULL,            -- 严重程度: LOW, MEDIUM, HIGH, CRITICAL
    description TEXT NOT NULL,                -- 异常描述
    evidence JSONB,                           -- 证据数据
    risk_score INTEGER DEFAULT 0,            -- 风险评分 (0-100)
    is_resolved BOOLEAN DEFAULT FALSE,        -- 是否已处理
    resolved_by UUID REFERENCES public.users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_risk_score CHECK (risk_score >= 0 AND risk_score <= 100),
    CONSTRAINT chk_severity CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'))
);

-- 创建索引
CREATE INDEX idx_anomaly_detection_user ON public.anomaly_access_detection(user_id, detected_at DESC);
CREATE INDEX idx_anomaly_detection_tenant ON public.anomaly_access_detection(tenant_id, detected_at DESC);
CREATE INDEX idx_anomaly_detection_severity ON public.anomaly_access_detection(severity, detected_at DESC);
CREATE INDEX idx_anomaly_detection_unresolved ON public.anomaly_access_detection(is_resolved, detected_at DESC) WHERE NOT is_resolved;

-- ================================================================
-- 权限变更历史表（增强版）
-- ================================================================

CREATE TABLE IF NOT EXISTS public.permission_change_history (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(100) NOT NULL,
    change_type VARCHAR(50) NOT NULL,         -- 变更类型: ROLE_ASSIGN, ROLE_REVOKE, POLICY_ADD, POLICY_REMOVE等
    target_type VARCHAR(50) NOT NULL,         -- 目标类型: USER, ROLE, POLICY
    target_id VARCHAR(200) NOT NULL,          -- 目标ID
    old_value JSONB,                          -- 变更前的值
    new_value JSONB,                          -- 变更后的值
    change_reason TEXT,                       -- 变更原因
    operator_id UUID REFERENCES public.users(id),
    operator_identity VARCHAR(256),           -- 操作者身份标识
    approval_required BOOLEAN DEFAULT FALSE,  -- 是否需要审批
    approved_by UUID REFERENCES public.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    effective_at TIMESTAMP WITH TIME ZONE,    -- 生效时间
    expires_at TIMESTAMP WITH TIME ZONE,      -- 过期时间（临时权限）
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_permission_change_tenant ON public.permission_change_history(tenant_id, created_at DESC);
CREATE INDEX idx_permission_change_operator ON public.permission_change_history(operator_id, created_at DESC);
CREATE INDEX idx_permission_change_target ON public.permission_change_history(target_type, target_id, created_at DESC);
CREATE INDEX idx_permission_change_pending ON public.permission_change_history(approval_required, approved_at) WHERE approval_required AND approved_at IS NULL;

-- ================================================================
-- 审计数据清理配置表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.audit_retention_policy (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL UNIQUE,
    retention_days INTEGER NOT NULL,          -- 保留天数
    archive_before_delete BOOLEAN DEFAULT TRUE, -- 删除前是否归档
    archive_table_name VARCHAR(100),          -- 归档表名
    last_cleanup_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_retention_days CHECK (retention_days > 0)
);

-- 插入默认保留策略
INSERT INTO public.audit_retention_policy (table_name, retention_days, archive_before_delete, archive_table_name) VALUES
('sensitive_data_access_log', 365, TRUE, 'sensitive_data_access_log_archive'),
('permission_failure_log', 180, TRUE, 'permission_failure_log_archive'),
('permission_usage_stats', 730, TRUE, 'permission_usage_stats_archive'),
('anomaly_access_detection', 365, TRUE, 'anomaly_access_detection_archive'),
('permission_change_history', 1095, TRUE, 'permission_change_history_archive'), -- 3年
('casbin_policy_audit', 730, TRUE, 'casbin_policy_audit_archive');

-- ================================================================
-- 审计数据统计视图
-- ================================================================

-- 权限使用统计视图
CREATE OR REPLACE VIEW public.v_permission_usage_summary AS
SELECT 
    tenant_id,
    resource,
    action,
    SUM(success_count) as total_success,
    SUM(failure_count) as total_failure,
    AVG(avg_processing_time_ms) as avg_processing_time,
    COUNT(DISTINCT user_id) as unique_users,
    MAX(last_access_at) as last_access_at
FROM public.permission_usage_stats
WHERE stats_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY tenant_id, resource, action;

-- 异常访问统计视图
CREATE OR REPLACE VIEW public.v_anomaly_summary AS
SELECT 
    tenant_id,
    anomaly_type,
    severity,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE NOT is_resolved) as unresolved_count,
    AVG(risk_score) as avg_risk_score,
    MAX(detected_at) as latest_detection
FROM public.anomaly_access_detection
WHERE detected_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY tenant_id, anomaly_type, severity;

-- ================================================================
-- 审计数据清理函数
-- ================================================================

CREATE OR REPLACE FUNCTION cleanup_audit_data()
RETURNS TABLE(table_name TEXT, deleted_count BIGINT, archived_count BIGINT) AS $$
DECLARE
    policy_record RECORD;
    delete_count BIGINT;
    archive_count BIGINT;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    FOR policy_record IN 
        SELECT * FROM public.audit_retention_policy WHERE is_active = TRUE
    LOOP
        cutoff_date := NOW() - (policy_record.retention_days || ' days')::INTERVAL;
        delete_count := 0;
        archive_count := 0;
        
        -- 根据表名执行清理
        CASE policy_record.table_name
            WHEN 'sensitive_data_access_log' THEN
                IF policy_record.archive_before_delete THEN
                    -- 归档数据（这里简化处理，实际应该创建归档表）
                    GET DIAGNOSTICS archive_count = ROW_COUNT;
                END IF;
                
                DELETE FROM public.sensitive_data_access_log 
                WHERE accessed_at < cutoff_date;
                GET DIAGNOSTICS delete_count = ROW_COUNT;
                
            WHEN 'permission_failure_log' THEN
                DELETE FROM public.permission_failure_log 
                WHERE failed_at < cutoff_date;
                GET DIAGNOSTICS delete_count = ROW_COUNT;
                
            WHEN 'permission_usage_stats' THEN
                DELETE FROM public.permission_usage_stats 
                WHERE stats_date < cutoff_date::DATE;
                GET DIAGNOSTICS delete_count = ROW_COUNT;
                
            WHEN 'anomaly_access_detection' THEN
                DELETE FROM public.anomaly_access_detection 
                WHERE detected_at < cutoff_date AND is_resolved = TRUE;
                GET DIAGNOSTICS delete_count = ROW_COUNT;
                
            WHEN 'permission_change_history' THEN
                DELETE FROM public.permission_change_history 
                WHERE created_at < cutoff_date;
                GET DIAGNOSTICS delete_count = ROW_COUNT;
                
            WHEN 'casbin_policy_audit' THEN
                DELETE FROM public.casbin_policy_audit 
                WHERE created_at < cutoff_date;
                GET DIAGNOSTICS delete_count = ROW_COUNT;
        END CASE;
        
        -- 更新最后清理时间
        UPDATE public.audit_retention_policy 
        SET last_cleanup_at = NOW() 
        WHERE id = policy_record.id;
        
        -- 返回清理结果
        table_name := policy_record.table_name;
        deleted_count := delete_count;
        archived_count := archive_count;
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- 权限使用统计更新函数
-- ================================================================

CREATE OR REPLACE FUNCTION update_permission_usage_stats(
    p_tenant_id VARCHAR(100),
    p_user_id UUID,
    p_resource VARCHAR(100),
    p_action VARCHAR(50),
    p_success BOOLEAN,
    p_processing_time_ms INTEGER
) RETURNS VOID AS $$
BEGIN
    INSERT INTO public.permission_usage_stats (
        tenant_id, user_id, resource, action, 
        success_count, failure_count, 
        total_processing_time_ms, avg_processing_time_ms,
        last_access_at, stats_date
    ) VALUES (
        p_tenant_id, p_user_id, p_resource, p_action,
        CASE WHEN p_success THEN 1 ELSE 0 END,
        CASE WHEN p_success THEN 0 ELSE 1 END,
        p_processing_time_ms,
        p_processing_time_ms,
        NOW(),
        CURRENT_DATE
    )
    ON CONFLICT (tenant_id, user_id, resource, action, stats_date) 
    DO UPDATE SET
        success_count = permission_usage_stats.success_count + CASE WHEN p_success THEN 1 ELSE 0 END,
        failure_count = permission_usage_stats.failure_count + CASE WHEN p_success THEN 0 ELSE 1 END,
        total_processing_time_ms = permission_usage_stats.total_processing_time_ms + p_processing_time_ms,
        avg_processing_time_ms = (permission_usage_stats.total_processing_time_ms + p_processing_time_ms) / 
                                (permission_usage_stats.success_count + permission_usage_stats.failure_count + 1),
        last_access_at = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- 创建定时清理任务（需要pg_cron扩展）
-- ================================================================

-- 注释：如果安装了pg_cron扩展，可以启用以下定时任务
-- SELECT cron.schedule('audit-cleanup', '0 2 * * *', 'SELECT cleanup_audit_data();');

-- ================================================================
-- 添加注释
-- ================================================================

COMMENT ON TABLE public.sensitive_data_access_log IS '敏感数据访问日志表';
COMMENT ON TABLE public.permission_failure_log IS '权限检查失败日志表';
COMMENT ON TABLE public.permission_usage_stats IS '权限使用统计表';
COMMENT ON TABLE public.anomaly_access_detection IS '异常访问检测表';
COMMENT ON TABLE public.permission_change_history IS '权限变更历史表';
COMMENT ON TABLE public.audit_retention_policy IS '审计数据保留策略表';

COMMENT ON FUNCTION cleanup_audit_data() IS '清理过期审计数据的函数';
COMMENT ON FUNCTION update_permission_usage_stats(VARCHAR, UUID, VARCHAR, VARCHAR, BOOLEAN, INTEGER) IS '更新权限使用统计的函数';
