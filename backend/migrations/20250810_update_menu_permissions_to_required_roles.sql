-- 更新菜单权限表结构，将 required_permissions, data_scopes, permission_mode 改为 required_roles
-- 执行时间: 2025-08-08

-- ================================================================
-- 1. 添加新的 required_roles 字段
-- ================================================================

ALTER TABLE public.menu_permissions 
ADD COLUMN IF NOT EXISTS required_roles TEXT[];

-- ================================================================
-- 2. 迁移现有数据：将 required_permissions 转换为 required_roles
-- ================================================================

-- 将现有的 required_permissions 数据迁移到 required_roles
-- 假设 required_permissions 中存储的是角色名称（如果不是，需要根据实际情况调整）
UPDATE public.menu_permissions 
SET required_roles = required_permissions 
WHERE required_permissions IS NOT NULL AND array_length(required_permissions, 1) > 0;

-- ================================================================
-- 3. 删除旧字段（谨慎操作，建议先备份）
-- ================================================================

-- 注释掉删除操作，以防数据丢失，可以在确认迁移成功后手动执行
-- ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS required_permissions;
-- ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS data_scopes;
-- ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS permission_mode;

-- ================================================================
-- 4. 添加字段注释
-- ================================================================

COMMENT ON COLUMN public.menu_permissions.required_roles IS '访问该菜单所需的角色列表';

-- ================================================================
-- 5. 更新现有菜单数据示例（根据实际业务需求调整）
-- ================================================================

-- 示例：将一些常见的权限转换为角色
UPDATE public.menu_permissions 
SET required_roles = ARRAY['class_teacher'] 
WHERE menu_id = 'teaching_aids_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['principal', 'academic_director'] 
WHERE menu_id = 'exam_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['class_teacher', 'subject_teacher'] 
WHERE menu_id = 'grade_score_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['principal', 'academic_director', 'grade_leader'] 
WHERE menu_id = 'student_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['super_admin'] 
WHERE menu_id = 'system_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['super_admin'] 
WHERE menu_id = 'tenant_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['super_admin', 'principal'] 
WHERE menu_id = 'role_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['super_admin', 'principal'] 
WHERE menu_id = 'user_management';

-- ================================================================
-- 6. 验证迁移结果
-- ================================================================

-- 检查迁移结果
SELECT 
    menu_id, 
    name, 
    required_permissions as old_permissions,
    required_roles as new_roles
FROM public.menu_permissions 
WHERE required_roles IS NOT NULL
ORDER BY menu_id;

-- 统计信息
SELECT 
    COUNT(*) as total_menus,
    COUNT(required_roles) as menus_with_roles,
    COUNT(required_permissions) as menus_with_old_permissions
FROM public.menu_permissions;
