-- 管理员菜单权限管理系统数据库增强
-- Migration: 20250803_super_admin_menu_management.sql
-- Description: Enhance database schema for super admin menu permission management system

-- ================================================================
-- 1. 扩展现有菜单权限表
-- ================================================================

-- 扩展 menu_permissions 表以支持高级菜单权限管理
ALTER TABLE public.menu_permissions 
ADD COLUMN IF NOT EXISTS menu_type VARCHAR(50) DEFAULT 'functional',
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS component_path VARCHAR(500),
ADD COLUMN IF NOT EXISTS external_link VARCHAR(500),
ADD COLUMN IF NOT EXISTS permission_mode VARCHAR(20) DEFAULT 'any',
ADD COLUMN IF NOT EXISTS cache_enabled BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS access_level INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS last_modified_by UUID,
ADD COLUMN IF NOT EXISTS last_modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 添加约束和检查（如果不存在）
DO $$
BEGIN
    -- 添加 chk_menu_type 约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_menu_type'
        AND table_name = 'menu_permissions'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.menu_permissions
        ADD CONSTRAINT chk_menu_type CHECK (menu_type IN ('functional', 'admin', 'personal', 'system'));
    END IF;

    -- 添加 chk_permission_mode 约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_permission_mode'
        AND table_name = 'menu_permissions'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.menu_permissions
        ADD CONSTRAINT chk_permission_mode CHECK (permission_mode IN ('any', 'all', 'custom'));
    END IF;

    -- 添加 chk_access_level 约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_access_level'
        AND table_name = 'menu_permissions'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.menu_permissions
        ADD CONSTRAINT chk_access_level CHECK (access_level >= 0 AND access_level <= 100);
    END IF;
END $$;

-- 为新字段创建索引
CREATE INDEX IF NOT EXISTS idx_menu_permissions_type ON public.menu_permissions(menu_type);
CREATE INDEX IF NOT EXISTS idx_menu_permissions_access_level ON public.menu_permissions(access_level);
CREATE INDEX IF NOT EXISTS idx_menu_permissions_cache ON public.menu_permissions(cache_enabled);
CREATE INDEX IF NOT EXISTS idx_menu_permissions_modified ON public.menu_permissions(last_modified_at);
CREATE INDEX IF NOT EXISTS idx_menu_permissions_modifier ON public.menu_permissions(last_modified_by);

-- ================================================================
-- 2. 创建菜单权限模板表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.menu_permission_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name VARCHAR(100) NOT NULL UNIQUE,
    template_type VARCHAR(50) NOT NULL DEFAULT 'role_based',
    template_category VARCHAR(50) DEFAULT 'general',
    permissions TEXT[] NOT NULL DEFAULT '{}',
    data_scopes TEXT[] DEFAULT '{}',
    permission_mode VARCHAR(20) DEFAULT 'any',
    description TEXT,
    usage_count INTEGER DEFAULT 0,
    is_system_template BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_template_type CHECK (template_type IN ('role_based', 'resource_based', 'custom', 'preset')),
    CONSTRAINT chk_template_permission_mode CHECK (permission_mode IN ('any', 'all', 'custom')),
    CONSTRAINT chk_template_usage_count CHECK (usage_count >= 0)
);

-- 模板表索引
CREATE INDEX IF NOT EXISTS idx_menu_permission_templates_type ON public.menu_permission_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_menu_permission_templates_category ON public.menu_permission_templates(template_category);
CREATE INDEX IF NOT EXISTS idx_menu_permission_templates_active ON public.menu_permission_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_menu_permission_templates_system ON public.menu_permission_templates(is_system_template);
CREATE INDEX IF NOT EXISTS idx_menu_permission_templates_usage ON public.menu_permission_templates(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_menu_permission_templates_created ON public.menu_permission_templates(created_at DESC);

-- ================================================================
-- 3. 创建菜单权限审计表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.menu_permission_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_id VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL,
    old_config JSONB,
    new_config JSONB,
    changes_summary JSONB DEFAULT '{}',
    affected_users_count INTEGER DEFAULT 0,
    operator_id UUID NOT NULL,
    operator_identity VARCHAR(256) NOT NULL,
    tenant_id VARCHAR(100),
    reason TEXT,
    impact_analysis JSONB DEFAULT '{}',
    rollback_data JSONB,
    is_rolled_back BOOLEAN DEFAULT FALSE,
    rollback_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_audit_operation CHECK (operation IN ('CREATE', 'UPDATE', 'DELETE', 'BATCH_UPDATE', 'BATCH_DELETE', 'REORDER', 'STATUS_CHANGE', 'TEMPLATE_APPLY', 'PERMISSION_ASSIGN', 'PERMISSION_REVOKE')),
    CONSTRAINT chk_affected_users_count CHECK (affected_users_count >= 0)
);

-- 审计表索引
CREATE INDEX IF NOT EXISTS idx_menu_permission_audit_menu ON public.menu_permission_audit(menu_id);
CREATE INDEX IF NOT EXISTS idx_menu_permission_audit_operation ON public.menu_permission_audit(operation);
CREATE INDEX IF NOT EXISTS idx_menu_permission_audit_operator ON public.menu_permission_audit(operator_id);
CREATE INDEX IF NOT EXISTS idx_menu_permission_audit_tenant ON public.menu_permission_audit(tenant_id);
CREATE INDEX IF NOT EXISTS idx_menu_permission_audit_created ON public.menu_permission_audit(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_menu_permission_audit_rollback ON public.menu_permission_audit(is_rolled_back, rollback_at);

-- ================================================================
-- 4. 创建菜单权限测试历史表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.menu_permission_test_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_name VARCHAR(200),
    test_type VARCHAR(50) NOT NULL DEFAULT 'single_user',
    test_parameters JSONB NOT NULL DEFAULT '{}',
    test_results JSONB NOT NULL DEFAULT '{}',
    total_tests INTEGER DEFAULT 0,
    passed_tests INTEGER DEFAULT 0,
    failed_tests INTEGER DEFAULT 0,
    execution_time_ms INTEGER DEFAULT 0,
    tester_id UUID NOT NULL,
    tester_identity VARCHAR(256) NOT NULL,
    tenant_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_test_type CHECK (test_type IN ('single_user', 'batch_users', 'role_simulation', 'permission_matrix', 'conflict_detection')),
    CONSTRAINT chk_test_counts CHECK (passed_tests >= 0 AND failed_tests >= 0 AND total_tests = passed_tests + failed_tests)
);

-- 测试历史表索引
CREATE INDEX IF NOT EXISTS idx_menu_permission_test_type ON public.menu_permission_test_history(test_type);
CREATE INDEX IF NOT EXISTS idx_menu_permission_test_tester ON public.menu_permission_test_history(tester_id);
CREATE INDEX IF NOT EXISTS idx_menu_permission_test_tenant ON public.menu_permission_test_history(tenant_id);
CREATE INDEX IF NOT EXISTS idx_menu_permission_test_created ON public.menu_permission_test_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_menu_permission_test_results ON public.menu_permission_test_history(passed_tests, failed_tests);

-- ================================================================
-- 5. 创建菜单使用统计表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.menu_usage_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_id VARCHAR(100) NOT NULL,
    tenant_id VARCHAR(100),
    access_count INTEGER DEFAULT 0,
    unique_user_count INTEGER DEFAULT 0,
    denied_access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    statistics_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(menu_id, tenant_id, statistics_date),
    CONSTRAINT chk_usage_counts CHECK (access_count >= 0 AND unique_user_count >= 0 AND denied_access_count >= 0)
);

-- 使用统计表索引
CREATE INDEX IF NOT EXISTS idx_menu_usage_stats_menu ON public.menu_usage_statistics(menu_id);
CREATE INDEX IF NOT EXISTS idx_menu_usage_stats_tenant ON public.menu_usage_statistics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_menu_usage_stats_date ON public.menu_usage_statistics(statistics_date DESC);
CREATE INDEX IF NOT EXISTS idx_menu_usage_stats_access ON public.menu_usage_statistics(access_count DESC);
CREATE INDEX IF NOT EXISTS idx_menu_usage_stats_denied ON public.menu_usage_statistics(denied_access_count DESC);

-- ================================================================
-- 6. 插入系统预定义权限模板
-- ================================================================

-- 系统管理员权限模板
INSERT INTO public.menu_permission_templates (
    template_name, 
    template_type, 
    template_category,
    permissions, 
    data_scopes, 
    permission_mode,
    description,
    is_system_template,
    created_by
) VALUES
(
    '系统管理员权限模板',
    'role_based',
    'system_admin',
    ARRAY['system:*', 'tenant:*', 'menu:*', 'role:*', 'permission:*'],
    ARRAY['*:*'],
    'all',
    '系统管理员的完整权限模板，拥有所有功能的完全访问权限',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '租户管理员权限模板',
    'role_based',
    'tenant_admin',
    ARRAY['tenant:read', 'tenant:write', 'user:read', 'user:write', 'role:read'],
    ARRAY['tenant:${user.tenant}', 'school:${user.school}'],
    'any',
    '租户管理员权限模板，可管理本租户内的用户和基础设置',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '学校管理员权限模板',
    'role_based',
    'school_admin',
    ARRAY['student:read', 'student:write', 'teacher:read', 'teacher:write', 'class:read', 'class:write', 'exam:read', 'exam:write'],
    ARRAY['school:${user.school}', 'grade:*', 'class:*'],
    'any',
    '学校管理员（校长/教导主任）权限模板',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '教师权限模板',
    'role_based',
    'teacher',
    ARRAY['student:read', 'grade:read', 'grade:write', 'exam:read'],
    ARRAY['class:${user.classes}', 'subject:${user.subject}'],
    'any',
    '任课教师权限模板，可查看学生信息和录入成绩',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '学生权限模板',
    'role_based',
    'student',
    ARRAY['grade:self', 'profile:self', 'schedule:self'],
    ARRAY['student:self'],
    'any',
    '学生权限模板，仅可查看个人相关信息',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '家长权限模板',
    'role_based',
    'parent',
    ARRAY['grade:child', 'profile:child', 'schedule:child'],
    ARRAY['student:${user.children}'],
    'any',
    '家长权限模板，可查看子女相关信息',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
);

-- 功能模块权限模板
INSERT INTO public.menu_permission_templates (
    template_name, 
    template_type, 
    template_category,
    permissions, 
    data_scopes, 
    permission_mode,
    description,
    is_system_template,
    created_by
) VALUES
(
    '学生管理模块权限',
    'resource_based',
    'functional',
    ARRAY['student:read', 'student:create', 'student:update', 'student:delete'],
    ARRAY['class:*'],
    'any',
    '学生管理功能模块的权限模板',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '考试管理模块权限',
    'resource_based',
    'functional',
    ARRAY['exam:read', 'exam:create', 'exam:update', 'exam:delete', 'exam:manage'],
    ARRAY['school:*', 'grade:*'],
    'any',
    '考试管理功能模块的权限模板',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '成绩管理模块权限',
    'resource_based',
    'functional',
    ARRAY['grade:read', 'grade:write', 'grade:analyze'],
    NULL,
    'any',
    '成绩管理功能模块的权限模板',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
),
(
    '只读访问权限',
    'custom',
    'general',
    ARRAY['*:read'],
    NULL,
    'all',
    '通用只读访问权限模板，适用于查看权限',
    TRUE,
    '00000000-0000-0000-0000-000000000000'
);

-- ================================================================
-- 7. 创建触发器和函数
-- ================================================================

-- 更新菜单权限修改时间的触发器函数
CREATE OR REPLACE FUNCTION update_menu_permission_modified_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.last_modified_at = NOW();
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS menu_permissions_update_trigger ON public.menu_permissions;
CREATE TRIGGER menu_permissions_update_trigger
    BEFORE UPDATE ON public.menu_permissions
    FOR EACH ROW EXECUTE FUNCTION update_menu_permission_modified_time();

-- 菜单权限审计触发器函数
CREATE OR REPLACE FUNCTION audit_menu_permission_changes()
RETURNS TRIGGER AS $$
DECLARE
    affected_count INTEGER := 0;
    changes_summary JSONB := '{}';
BEGIN
    -- 估算受影响的用户数量（简化版本）
    IF TG_OP = 'UPDATE' THEN
        -- 比较新旧配置，计算变更摘要
        changes_summary = jsonb_build_object(
            'permissions_changed', (OLD.required_permissions IS DISTINCT FROM NEW.required_permissions),
            'data_scopes_changed', (OLD.data_scopes IS DISTINCT FROM NEW.data_scopes),
            'status_changed', (OLD.is_active IS DISTINCT FROM NEW.is_active),
            'basic_info_changed', (OLD.name IS DISTINCT FROM NEW.name OR OLD.path IS DISTINCT FROM NEW.path)
        );
        
        INSERT INTO public.menu_permission_audit (
            menu_id, 
            operation, 
            old_config, 
            new_config,
            changes_summary,
            affected_users_count,
            operator_id,
            operator_identity,
            rollback_data
        ) VALUES (
            NEW.menu_id,
            'UPDATE',
            row_to_json(OLD),
            row_to_json(NEW),
            changes_summary,
            affected_count,
            COALESCE(NEW.last_modified_by, '00000000-0000-0000-0000-000000000000'),
            'system',
            row_to_json(OLD)
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.menu_permission_audit (
            menu_id, 
            operation, 
            new_config,
            changes_summary,
            operator_id,
            operator_identity
        ) VALUES (
            NEW.menu_id,
            'CREATE',
            row_to_json(NEW),
            jsonb_build_object('action', 'menu_created'),
            COALESCE(NEW.last_modified_by, '00000000-0000-0000-0000-000000000000'),
            'system'
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO public.menu_permission_audit (
            menu_id, 
            operation, 
            old_config,
            changes_summary,
            operator_id,
            operator_identity,
            rollback_data
        ) VALUES (
            OLD.menu_id,
            'DELETE',
            row_to_json(OLD),
            jsonb_build_object('action', 'menu_deleted'),
            '00000000-0000-0000-0000-000000000000',
            'system',
            row_to_json(OLD)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建审计触发器
DROP TRIGGER IF EXISTS menu_permissions_audit_trigger ON public.menu_permissions;
CREATE TRIGGER menu_permissions_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.menu_permissions
    FOR EACH ROW EXECUTE FUNCTION audit_menu_permission_changes();

-- 更新权限模板修改时间的触发器函数
CREATE OR REPLACE FUNCTION update_menu_template_modified_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建模板更新触发器
DROP TRIGGER IF EXISTS menu_permission_templates_update_trigger ON public.menu_permission_templates;
CREATE TRIGGER menu_permission_templates_update_trigger
    BEFORE UPDATE ON public.menu_permission_templates
    FOR EACH ROW EXECUTE FUNCTION update_menu_template_modified_time();

-- 更新模板使用次数的函数
CREATE OR REPLACE FUNCTION increment_template_usage(template_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.menu_permission_templates
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE id = template_id;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- 8. 创建视图
-- ================================================================

-- 菜单权限概览视图
CREATE OR REPLACE VIEW menu_permission_overview AS
SELECT 
    mp.menu_id,
    mp.name,
    mp.path,
    mp.menu_type,
    mp.is_active,
    mp.required_permissions,
    mp.data_scopes,
    mp.permission_mode,
    mp.access_level,
    mp.created_at,
    mp.updated_at,
    mp.version,
    COUNT(DISTINCT children.menu_id) as children_count,
    CASE 
        WHEN mp.parent_id IS NULL THEN 0
        ELSE (
            SELECT COUNT(*) 
            FROM public.menu_permissions ancestors 
            WHERE mp.path LIKE ancestors.path || '%' 
            AND ancestors.menu_id != mp.menu_id
        )
    END as depth_level
FROM public.menu_permissions mp
LEFT JOIN public.menu_permissions children ON children.parent_id = mp.menu_id
GROUP BY mp.menu_id, mp.name, mp.path, mp.menu_type, mp.is_active, 
         mp.required_permissions, mp.data_scopes, mp.permission_mode, 
         mp.access_level, mp.created_at, mp.updated_at, mp.version, mp.parent_id;

-- 权限模板使用统计视图
CREATE OR REPLACE VIEW menu_template_usage_stats AS
SELECT 
    mpt.id,
    mpt.template_name,
    mpt.template_type,
    mpt.template_category,
    mpt.usage_count,
    mpt.is_system_template,
    mpt.is_active,
    mpt.created_at,
    COUNT(DISTINCT mpa.id) as audit_records,
    MAX(mpa.created_at) as last_used_at
FROM public.menu_permission_templates mpt
LEFT JOIN public.menu_permission_audit mpa ON mpa.new_config::text LIKE '%' || mpt.id::text || '%'
GROUP BY mpt.id, mpt.template_name, mpt.template_type, mpt.template_category, 
         mpt.usage_count, mpt.is_system_template, mpt.is_active, mpt.created_at;

-- 菜单权限审计摘要视图
CREATE OR REPLACE VIEW menu_permission_audit_summary AS
SELECT 
    DATE(mpa.created_at) as audit_date,
    mpa.operation,
    COUNT(*) as operation_count,
    COUNT(DISTINCT mpa.menu_id) as affected_menus,
    COUNT(DISTINCT mpa.operator_id) as unique_operators,
    SUM(mpa.affected_users_count) as total_affected_users
FROM public.menu_permission_audit mpa
GROUP BY DATE(mpa.created_at), mpa.operation
ORDER BY audit_date DESC, operation_count DESC;

-- ================================================================
-- 9. 设置权限和安全
-- ================================================================

-- 为管理员角色授予必要权限（假设存在该角色）
-- GRANT ALL PRIVILEGES ON public.menu_permission_templates TO super_admin;
-- GRANT ALL PRIVILEGES ON public.menu_permission_audit TO super_admin;
-- GRANT ALL PRIVILEGES ON public.menu_permission_test_history TO super_admin;
-- GRANT ALL PRIVILEGES ON public.menu_usage_statistics TO super_admin;

-- 为普通用户设置只读权限（根据实际需求调整）
-- GRANT SELECT ON menu_permission_overview TO authenticated_users;

-- ================================================================
-- 10. 添加表注释
-- ================================================================

COMMENT ON TABLE public.menu_permission_templates IS '菜单权限模板表，存储可重用的权限配置模板';
COMMENT ON TABLE public.menu_permission_audit IS '菜单权限审计表，记录所有权限配置变更历史';
COMMENT ON TABLE public.menu_permission_test_history IS '菜单权限测试历史表，记录权限测试执行记录';
COMMENT ON TABLE public.menu_usage_statistics IS '菜单使用统计表，记录菜单访问和使用情况';

-- 字段注释
COMMENT ON COLUMN public.menu_permissions.menu_type IS '菜单类型：functional(功能), admin(管理), personal(个人), system(系统)';
COMMENT ON COLUMN public.menu_permissions.permission_mode IS '权限模式：any(任一), all(全部), custom(自定义)';
COMMENT ON COLUMN public.menu_permissions.cache_enabled IS '是否启用权限检查结果缓存';
COMMENT ON COLUMN public.menu_permissions.access_level IS '访问级别，用于排序和过滤(0-100)';
COMMENT ON COLUMN public.menu_permissions.metadata IS '扩展元数据，存储额外的配置信息';
COMMENT ON COLUMN public.menu_permissions.version IS '配置版本号，每次修改递增';

COMMENT ON COLUMN public.menu_permission_templates.template_type IS '模板类型：role_based(基于角色), resource_based(基于资源), custom(自定义)';
COMMENT ON COLUMN public.menu_permission_templates.permission_mode IS '权限模式：any(任一满足), all(全部满足), custom(自定义逻辑)';
COMMENT ON COLUMN public.menu_permission_templates.is_system_template IS '是否为系统预定义模板';
COMMENT ON COLUMN public.menu_permission_templates.usage_count IS '模板使用次数统计';

COMMENT ON COLUMN public.menu_permission_audit.operation IS '操作类型：CREATE, UPDATE, DELETE, BATCH_UPDATE等';
COMMENT ON COLUMN public.menu_permission_audit.changes_summary IS '变更摘要，JSON格式存储主要变更点';
COMMENT ON COLUMN public.menu_permission_audit.impact_analysis IS '影响分析，评估变更对系统的影响';
COMMENT ON COLUMN public.menu_permission_audit.rollback_data IS '回滚数据，用于快速恢复配置';

-- ================================================================
-- 初始化完成标记
-- ================================================================

-- 插入初始化完成标记
INSERT INTO public.menu_permission_audit (
    menu_id,
    operation,
    new_config,
    changes_summary,
    operator_id,
    operator_identity,
    reason
) VALUES (
    'SYSTEM_INIT',
    'CREATE',
    jsonb_build_object(
        'migration', '20250804_super_admin_menu_management',
        'version', '1.0.0',
        'timestamp', NOW()
    ),
    jsonb_build_object(
        'action', 'database_schema_initialization',
        'tables_created', ARRAY['menu_permission_templates', 'menu_permission_audit', 'menu_permission_test_history', 'menu_usage_statistics'],
        'views_created', ARRAY['menu_permission_overview', 'menu_template_usage_stats', 'menu_permission_audit_summary'],
        'triggers_created', ARRAY['menu_permissions_update_trigger', 'menu_permissions_audit_trigger', 'menu_permission_templates_update_trigger']
    ),
    '00000000-0000-0000-0000-000000000000',
    'system_migration',
    '管理员菜单权限管理系统数据库架构初始化完成'
);

-- ================================================================
-- 迁移完成
-- ================================================================