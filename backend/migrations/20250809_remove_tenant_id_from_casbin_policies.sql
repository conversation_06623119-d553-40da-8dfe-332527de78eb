-- 从 casbin_policies 表中移除 tenant_id 字段
-- Migration: 20250809_remove_tenant_id_from_casbin_policies.sql
-- Description: Remove tenant_id field from casbin_policies to simplify permission system

-- ================================================================
-- 1. 删除依赖 tenant_id 的触发器和函数
-- ================================================================

-- 删除现有触发器
DROP TRIGGER IF EXISTS casbin_policies_stats_trigger ON public.casbin_policies;
DROP TRIGGER IF EXISTS casbin_policies_audit_trigger ON public.casbin_policies;

-- ================================================================
-- 2. 删除与 tenant_id 相关的索引
-- ================================================================

-- 删除包含 tenant_id 的索引
DROP INDEX IF EXISTS idx_casbin_policies_tenant;
DROP INDEX IF EXISTS idx_casbin_policies_tenant_lookup;

-- ================================================================
-- 3. 删除 tenant_id 字段
-- ================================================================

-- 从 casbin_policies 表中删除 tenant_id 列
ALTER TABLE public.casbin_policies DROP COLUMN IF EXISTS tenant_id;

-- ================================================================
-- 4. 更新 casbin_policy_audit 表结构
-- ================================================================

-- 从审计表中也删除 tenant_id 字段（可选，根据需求）
-- 如果审计表仍需要跟踪租户信息，可以保留
-- ALTER TABLE public.casbin_policy_audit DROP COLUMN IF EXISTS tenant_id;

-- ================================================================
-- 5. 重新创建简化的触发器函数（不使用 tenant_id）
-- ================================================================

-- 创建简化的审计触发器函数
CREATE OR REPLACE FUNCTION audit_casbin_policy_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO public.casbin_policy_audit (
            tenant_id, operation, policy_type, new_policy, 
            operator_id, operator_identity
        )
        VALUES (
            'system', 'ADD', NEW.ptype, row_to_json(NEW),
            NULL, NULL
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.casbin_policy_audit (
            tenant_id, operation, policy_type, old_policy, new_policy,
            operator_id, operator_identity
        )
        VALUES (
            'system', 'UPDATE', NEW.ptype, row_to_json(OLD), row_to_json(NEW),
            NULL, NULL
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO public.casbin_policy_audit (
            tenant_id, operation, policy_type, old_policy,
            operator_id, operator_identity
        )
        VALUES (
            'system', 'REMOVE', OLD.ptype, row_to_json(OLD),
            NULL, NULL
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 重新创建审计触发器
CREATE TRIGGER casbin_policies_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.casbin_policies
    FOR EACH ROW EXECUTE FUNCTION audit_casbin_policy_changes();

-- ================================================================
-- 6. 清理或更新统计表
-- ================================================================

-- 选择1: 删除租户统计表（如果不再需要）
DROP TABLE IF EXISTS public.tenant_policy_stats;

-- 选择2: 或者创建全局统计表（如果需要保留统计功能）
CREATE TABLE IF NOT EXISTS public.global_policy_stats (
    id SERIAL PRIMARY KEY,
    total_policies INTEGER DEFAULT 0,
    user_role_mappings INTEGER DEFAULT 0,
    permission_policies INTEGER DEFAULT 0,
    role_inheritance_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建全局统计更新函数
CREATE OR REPLACE FUNCTION update_global_policy_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新全局统计
    INSERT INTO public.global_policy_stats (
        total_policies, 
        user_role_mappings, 
        permission_policies, 
        role_inheritance_count, 
        last_updated
    )
    VALUES (
        (SELECT COUNT(*) FROM public.casbin_policies),
        (SELECT COUNT(*) FROM public.casbin_policies WHERE ptype = 'g'),
        (SELECT COUNT(*) FROM public.casbin_policies WHERE ptype = 'p'),
        (SELECT COUNT(*) FROM public.casbin_policies WHERE ptype = 'g2'),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        total_policies = EXCLUDED.total_policies,
        user_role_mappings = EXCLUDED.user_role_mappings,
        permission_policies = EXCLUDED.permission_policies,
        role_inheritance_count = EXCLUDED.role_inheritance_count,
        last_updated = EXCLUDED.last_updated;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建全局统计触发器
CREATE TRIGGER global_policy_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.casbin_policies
    FOR EACH STATEMENT EXECUTE FUNCTION update_global_policy_stats();

-- ================================================================
-- 7. 初始化全局统计
-- ================================================================

-- 插入初始统计记录
INSERT INTO public.global_policy_stats (
    total_policies,
    user_role_mappings,
    permission_policies,
    role_inheritance_count,
    last_updated
)
VALUES (
    (SELECT COUNT(*) FROM public.casbin_policies),
    (SELECT COUNT(*) FROM public.casbin_policies WHERE ptype = 'g'),
    (SELECT COUNT(*) FROM public.casbin_policies WHERE ptype = 'p'),
    (SELECT COUNT(*) FROM public.casbin_policies WHERE ptype = 'g2'),
    NOW()
);

-- ================================================================
-- 8. 更新注释
-- ================================================================

COMMENT ON TABLE public.casbin_policies IS 'Casbin RBAC 权限策略存储表 - 已移除多租户支持';
COMMENT ON COLUMN public.casbin_policies.ptype IS '策略类型: p(权限策略), g(用户-角色), g2(角色继承)';
COMMENT ON TABLE public.global_policy_stats IS '全局权限策略统计表';

-- ================================================================
-- 9. 清理数据（可选）
-- ================================================================

-- 如果表中有重复的策略（之前按tenant_id区分），现在可能需要去重
-- 这里提供一个去重查询，但实际执行需要谨慎
/*
WITH duplicate_policies AS (
    SELECT id, 
           ROW_NUMBER() OVER (PARTITION BY ptype, v0, v1, v2, v3, v4, v5 ORDER BY id) as rn
    FROM public.casbin_policies
)
DELETE FROM public.casbin_policies 
WHERE id IN (
    SELECT id FROM duplicate_policies WHERE rn > 1
);
*/

COMMENT ON COLUMN public.casbin_policies.id IS '策略记录唯一标识';