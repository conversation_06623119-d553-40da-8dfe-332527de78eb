-- Role Change Audit Table Migration
-- Migration: 20250905_role_change_audit_table.sql
-- Description: Create role change audit table for tracking role assignment changes

-- ================================================================
-- CREATE ROLE CHANGE AUDIT TABLE FUNCTION
-- ================================================================

-- Function to create role_change_audit table in tenant schemas
CREATE OR REPLACE FUNCTION create_role_change_audit_table(schema_name TEXT)
RETURNS VOID AS $$
BEGIN
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS "%I".role_change_audit (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL REFERENCES public.users(id),
            operation VARCHAR(20) NOT NULL CHECK (operation IN (''assign'', ''remove'', ''update'')),
            role_code VARCHAR(50) NOT NULL,
            target_type VARCHAR(30) NOT NULL,
            target_id UUID,
            old_values JSONB,
            new_values JSONB,
            performed_by UUID NOT NULL REFERENCES public.users(id),
            reason TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )', schema_name);

    -- Create indexes
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_role_change_audit_user_id 
        ON "%I".role_change_audit (user_id)', schema_name, schema_name);
    
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_role_change_audit_operation 
        ON "%I".role_change_audit (operation)', schema_name, schema_name);
    
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_role_change_audit_role_code 
        ON "%I".role_change_audit (role_code)', schema_name, schema_name);
    
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_role_change_audit_performed_by 
        ON "%I".role_change_audit (performed_by)', schema_name, schema_name);
    
    EXECUTE format('
        CREATE INDEX IF NOT EXISTS idx_%I_role_change_audit_created_at 
        ON "%I".role_change_audit (created_at)', schema_name, schema_name);

    -- Add comments
    EXECUTE format('
        COMMENT ON TABLE "%I".role_change_audit IS ''角色变更审计表 - 记录用户角色分配的变更历史'';
        COMMENT ON COLUMN "%I".role_change_audit.operation IS ''操作类型: assign-分配, remove-移除, update-更新'';
        COMMENT ON COLUMN "%I".role_change_audit.role_code IS ''角色代码'';
        COMMENT ON COLUMN "%I".role_change_audit.target_type IS ''目标类型: school, subject_group, grade, admin_class, teaching_class'';
        COMMENT ON COLUMN "%I".role_change_audit.target_id IS ''目标ID'';
        COMMENT ON COLUMN "%I".role_change_audit.old_values IS ''变更前的值'';
        COMMENT ON COLUMN "%I".role_change_audit.new_values IS ''变更后的值'';
        COMMENT ON COLUMN "%I".role_change_audit.performed_by IS ''执行变更的用户ID'';
        COMMENT ON COLUMN "%I".role_change_audit.reason IS ''变更原因'';
    ', schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name);
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- CREATE AUDIT TABLES FOR EXISTING TENANT SCHEMAS
-- ================================================================

-- Create audit tables for all existing active tenant schemas
DO $$
DECLARE
    tenant_record RECORD;
BEGIN
    FOR tenant_record IN 
        SELECT schema_name 
        FROM public.tenants 
        WHERE status = 'active'
    LOOP
        PERFORM create_role_change_audit_table(tenant_record.schema_name);
        RAISE NOTICE 'Created role_change_audit table for schema: %', tenant_record.schema_name;
    END LOOP;
END $$;

-- ================================================================
-- UPDATE TENANT TEMPLATE
-- ================================================================

-- Add role_change_audit table creation to tenant template
-- This will be included when new tenant schemas are created

COMMENT ON FUNCTION create_role_change_audit_table(TEXT) IS '为租户模式创建角色变更审计表的函数';