-- DROP TABLE IF EXISTS public.exam_paper_questions;
-- DROP TABLE IF EXISTS public.question_bank;
-- DROP TABLE IF EXISTS public.textbook_exercises;
-- DROP TABLE IF EXISTS public.textbook_chapters;

CREATE TABLE IF NOT EXISTS "public"."questions" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "question_type_code" varchar(20) NOT NULL,
    "items" jsonb NOT NULL,
    "subject_code" varchar(20) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON COLUMN "public"."questions"."question_type_code" IS '题型编码';
COMMENT ON COLUMN "public"."questions"."items" IS '内容段';
COMMENT ON COLUMN "public"."questions"."subject_code" IS '学科编码';
COMMENT ON COLUMN "public"."questions"."updated_at" IS '更新时间';

CREATE TABLE IF NOT EXISTS "public"."question_answers" (
     "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
     "question_id" uuid NOT NULL,
     "answer_area_id" int2 NOT NULL,
     "content" text NOT NULL,
     "explanation" text,
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON COLUMN "public"."question_answers"."question_id" IS '题目id';
COMMENT ON COLUMN "public"."question_answers"."answer_area_id" IS '作答区编号';
COMMENT ON COLUMN "public"."question_answers"."content" IS '答案内容';
COMMENT ON COLUMN "public"."question_answers"."explanation" IS '解析';
COMMENT ON COLUMN "public"."question_answers"."updated_at" IS '更新时间';

alter TABLE  "public"."papers"
    add column title text,
    add column answer_card_id uuid,
    add column "subject_code" varchar(20),
    add column grade_level_code varchar(20),
    add column section_id uuid;

COMMENT ON COLUMN "public"."papers"."title" IS '试卷名';
COMMENT ON COLUMN "public"."papers"."answer_card_id" IS '答题卡id';
COMMENT ON COLUMN "public"."papers"."subject_code" IS '学科编码';
COMMENT ON COLUMN "public"."papers"."grade_level_code" IS '年级编码';
COMMENT ON COLUMN "public"."papers"."section_id" IS '内容小节ID';
COMMENT ON COLUMN "public"."papers"."updated_at" IS '更新时间';

CREATE TABLE IF NOT EXISTS "public"."sections" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    answer_card_id uuid,
    "items" jsonb NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON COLUMN "public"."sections"."items" IS '内容块';
COMMENT ON COLUMN "public"."sections"."answer_card_id" IS '答题卡id';
COMMENT ON COLUMN "public"."sections"."updated_at" IS '更新时间';

CREATE TABLE IF NOT EXISTS "public"."catalogs" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    book_id uuid NOT NULL,
    parent_id uuid,
    section_id uuid,
    "order" int2 NOT NULL,
    level int2 NOT NULL,
    title text NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON COLUMN "public"."catalogs"."book_id" IS '书本id';
COMMENT ON COLUMN "public"."catalogs"."section_id" IS '小节内容id';
COMMENT ON COLUMN "public"."catalogs"."parent_id" IS '父id';
COMMENT ON COLUMN "public"."catalogs"."order" IS '序号';
COMMENT ON COLUMN "public"."catalogs"."level" IS '层级';
COMMENT ON COLUMN "public"."catalogs"."title" IS '目录名';
COMMENT ON COLUMN "public"."catalogs"."updated_at" IS '更新时间';


CREATE TABLE IF NOT EXISTS "public"."books" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text NOT NULL,
    "subject_code" varchar(20),
    grade_level_code varchar(20),
    publisher varchar(40),
    distributor varchar(40),
    year int2 NOT NULL,
    cover_path text,
    isbn varchar(20),
    edition varchar(20),
    printing_version varchar(20),
    authors text,
    summary text,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON COLUMN "public"."books"."id" IS '书本id';
COMMENT ON COLUMN "public"."books"."title" IS '书名';
COMMENT ON COLUMN "public"."books"."subject_code" IS '学科编码';
COMMENT ON COLUMN "public"."books"."grade_level_code" IS '年级编码';
COMMENT ON COLUMN "public"."books"."publisher" IS '出版社';
COMMENT ON COLUMN "public"."books"."distributor" IS '发行机构';
COMMENT ON COLUMN "public"."books"."year" IS '年份';
COMMENT ON COLUMN "public"."books"."cover_path" IS '封面地址';
COMMENT ON COLUMN "public"."books"."isbn" IS 'isbn';
COMMENT ON COLUMN "public"."books"."edition" IS '版本';
COMMENT ON COLUMN "public"."books"."printing_version" IS '印次';
COMMENT ON COLUMN "public"."books"."authors" IS '作者';
COMMENT ON COLUMN "public"."books"."summary" IS '简介或摘要';
COMMENT ON COLUMN "public"."books"."updated_at" IS '更新时间';