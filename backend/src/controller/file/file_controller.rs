use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::{
    body::Body,
    extract::{Path, Query, State},
    http::{header, HeaderMap, StatusCode},
    response::{Json, Response},
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};

/// 文件URL生成请求参数
#[derive(Debug, Deserialize)]
pub struct FileUrlQuery {
    /// 文件类型，用于确定URL生成策略
    #[serde(default)]
    pub file_type: Option<String>,
}

/// 文件URL响应
#[derive(Debug, Serialize)]
pub struct FileUrlResponse {
    /// 文件键
    pub key: String,
    /// 生成的可访问URL
    pub url: String,
    /// URL过期时间（秒）
    pub expires_in: u64,
    /// 文件类型
    pub file_type: Option<String>,
}

/// 批量文件URL请求
#[derive(Debug, Deserialize)]
pub struct BatchFileUrlRequest {
    /// 文件键列表
    pub keys: Vec<String>,
    /// 文件类型
    #[serde(default)]
    pub file_type: Option<String>,
}

/// 批量文件URL响应
#[derive(Debug, Serialize)]
pub struct BatchFileUrlResponse {
    /// 成功生成URL的文件列表
    pub files: Vec<FileUrlResponse>,
    /// 失败的文件键列表
    pub failed_keys: Vec<String>,
}

/// 根据文件键生成可访问的URL
///
/// # 参数
/// - `key`: 文件在MinIO中的键
/// - `query`: 查询参数，包含文件类型和预览模式设置
///
/// # 返回
/// 包含可访问URL的响应
pub async fn generate_file_url(State(state): State<AppState>, Path(key): Path<String>, Query(query): Query<FileUrlQuery>) -> Result<Json<ApiResponse<FileUrlResponse>>, StatusCode> {
    // 检查文件是否存在
    let file_exists = match state.storage_service.exists(&key).await {
        Ok(exists) => exists,
        Err(e) => {
            tracing::error!("Failed to check file existence for key {}: {}", key, e);
            return Ok(Json(ApiResponse::error(format!("Failed to check file existence: {}", e), Some("FILE_CHECK_ERROR".to_string()))));
        }
    };

    if !file_exists {
        return Ok(Json(ApiResponse::error(format!("File with key '{}' not found", key), Some("FILE_NOT_FOUND".to_string()))));
    }

    // 直接返回文件键，让前端构建URL
    let url = key.clone();

    let response = FileUrlResponse {
        key: key.clone(),
        url,
        expires_in: 3600, // 1小时过期
        file_type: query.file_type,
    };

    Ok(Json(ApiResponse::success(response, None)))
}

/// 批量生成文件URL
///
/// # 参数
/// - `request`: 包含文件键列表和配置的请求体
///
/// # 返回
/// 包含成功和失败文件列表的响应
pub async fn generate_batch_file_urls(State(state): State<AppState>, Json(request): Json<BatchFileUrlRequest>) -> Result<Json<ApiResponse<BatchFileUrlResponse>>, StatusCode> {
    let mut files = Vec::new();
    let mut failed_keys = Vec::new();

    for key in request.keys {
        // 检查文件是否存在
        let file_exists = match state.storage_service.exists(&key).await {
            Ok(exists) => exists,
            Err(e) => {
                tracing::warn!("Failed to check existence for key {}: {}", key, e);
                failed_keys.push(key);
                continue;
            }
        };

        if !file_exists {
            tracing::warn!("File not found: {}", key);
            failed_keys.push(key);
            continue;
        }

        // 直接使用文件键作为URL
        files.push(FileUrlResponse {
            key: key.clone(),
            url: key.clone(),
            expires_in: 3600,
            file_type: request.file_type.clone(),
        });
    }

    let response = BatchFileUrlResponse { files, failed_keys };

    Ok(Json(ApiResponse::success(response, None)))
}

/// 获取文件信息
///
/// # 参数
/// - `key`: 文件键
///
/// # 返回
/// 文件的详细信息，包括大小、类型和URL
pub async fn get_file_info(State(state): State<AppState>, Path(key): Path<String>) -> Result<Json<ApiResponse<crate::service::storage::storage_service::FileInfo>>, StatusCode> {
    match state.storage_service.get_info(&key).await {
        Ok(file_info) => Ok(Json(ApiResponse::success(file_info, None))),
        Err(e) => {
            tracing::error!("Failed to get file info for key {}: {}", key, e);
            Ok(Json(ApiResponse::error(format!("Failed to get file information: {}", e), Some("FILE_INFO_ERROR".to_string()))))
        }
    }
}

/// 直接提供文件服务
///
/// # 参数
/// - `key`: 文件键（支持路径参数）
///
/// # 返回
/// 文件的二进制内容，带有适当的Content-Type头
pub async fn serve_file(State(state): State<AppState>, Path(key): Path<String>) -> Result<Response<Body>, StatusCode> {
    dbg!(&key);
    // 检查文件是否存在
    let file_exists = match state.storage_service.exists(&key).await {
        Ok(exists) => exists,
        Err(e) => {
            tracing::error!("Failed to check file existence for key {}: {}", key, e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    if !file_exists {
        return Err(StatusCode::NOT_FOUND);
    }

    // 下载文件内容
    let file_content = match state.storage_service.download(&key).await {
        Ok(content) => content,
        Err(e) => {
            tracing::error!("Failed to download file for key {}: {}", key, e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 推断Content-Type
    let content_type = mime_guess::from_path(&key).first_or_octet_stream().to_string();

    // 构建响应头
    let mut headers = HeaderMap::new();
    headers.insert(header::CONTENT_TYPE, content_type.parse().unwrap_or_else(|_| "application/octet-stream".parse().unwrap()));
    headers.insert(header::CACHE_CONTROL, "public, max-age=3600".parse().unwrap());

    // 返回文件内容
    Ok(Response::builder().status(StatusCode::OK).body(Body::from(file_content)).unwrap())
}

/// 创建文件相关路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/{key}/url", get(generate_file_url))
        .route("/{key}/info", get(get_file_info))
        .route("/batch/urls", post(generate_batch_file_urls))
}

/// 创建公共文件服务路由
pub fn create_public_router() -> Router<AppState> {
    Router::new().route("/{*key}", get(serve_file))
}
