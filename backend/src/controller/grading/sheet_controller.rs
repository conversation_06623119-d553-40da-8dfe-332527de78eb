use crate::service::grading::sheet_service;
use crate::service::grading::vo::sheet_vo::SheetsVo;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::extract::{Path, State};
use axum::routing::post;
use axum::{Json, Router};
use serde::Deserialize;
use uuid::Uuid;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/detail", post(sheet_detail))
}
#[derive(Deserialize)]
struct SheetDetailRequest {
    sheet_id: Option<Uuid>,
    student_id: Option<Uuid>,
}
async fn sheet_detail(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(param): Json<SheetDetailRequest>) -> Result<ApiResponse<SheetsVo>, ApiResponse<String>> {
    let sv = sheet_service::sheet_detail(state.db, tenant_name.as_str(), param.sheet_id, param.student_id, state.storage_service).await
        .map_err(|e| ApiResponse::error(format!("题卡扫描页获取失败: {}", e),None))?;
    Ok(ApiResponse::success(sv, None))
}