use crate::model::homework::homework::Homework;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::extract::{Path, Query, State};
use axum::routing::get;
use axum::Router;
use serde::Deserialize;
use tracing::info;
use uuid::Uuid;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;

/// 用于接收扫描仪请求的开放接口
pub fn create_router() -> Router<AppState> {
    Router::new().route("/qrcode/queryExams", get(query_qrcode_paper_list))
}

/// 扫描查询参数
#[derive(Debug, Deserialize)]
pub struct ScannerQueryParams {
    exam_type: String,
    qrcode: String,
}

/// 查询某场作业/试卷上次批次号列表
pub async fn query_qrcode_paper_list(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Query(params): Query<ScannerQueryParams>,
) -> anyhow::Result<ApiResponse<Vec<Homework>>, ApiResponse<String>> {
    // 提取查询参数中的内容
    let ScannerQueryParams { exam_type, qrcode } = params;
    if exam_type.is_empty() {
        return Err(ApiResponse::error("考试模式类型参数不能为空".to_string(), None));
    }

    if qrcode.is_empty() {
        return Err(ApiResponse::error("二维码参数不能为空".to_string(), None));
    }
    // 提取二维码中的内容
    let mut values: Vec<String> = Vec::new();

    // 校验 二维码中的分割方式
    if qrcode.contains(';') && !qrcode.contains("___") {
        values = qrcode.split(';').map(|s| s.to_string()).collect();
    } else if qrcode.contains("___") && !qrcode.contains(';') {
        values = qrcode.split("___").map(|s| s.to_string()).collect();
    } else {
        // 标准的 Uuid 长度为 36
        // 标准格式 550e8400-e29b-41d4-a716-************ , 长度 36
        // 紧凑格式 550e8400e29b41d4a716************  ,长度 32
        // 带花括号 {550e8400-e29b-41d4-a716-************}  ,长度 38
        // URN 格式 urn:uuid:550e8400-e29b-41d4-a716-************ ,长度 45
        let paper_id = format!("{}", qrcode.trim().chars().take(36).collect::<String>());
        values.push(paper_id);
    }

    // 提取试卷ID
    let mut paper_id = "".to_string();
    if values.len() >= 1 {
        paper_id = values.first().map(|s| s.to_string()).unwrap_or_default();
        if paper_id.is_empty() {
            return Err(ApiResponse::error("试卷 ID 不能为空".to_string(), None));
        }
        paper_id = paper_id.trim().to_string();
    }

    info!("{:?}", paper_id);

    let paper_id = Uuid::parse_str(paper_id.as_str()).map_err(|_e| ApiResponse::error("试卷 UUID 格式不合法".to_string(), None))?;

    let homework_paper_service = HomeworkPapersService::new(state.db, tenant_name);
    // 根据试卷 ID 查询考试/作业信息
    let result: Vec<Homework> = homework_paper_service
        .find_homework_list_by_paper_id(paper_id)
        .await.map_err(|e| ApiResponse::error(e.to_string(), None))?;

    Ok(ApiResponse::success(result, None))
}
