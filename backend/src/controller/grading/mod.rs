use axum::Router;
use crate::web_server::AppState;

mod grading_controller;
mod grading_question_controller;
mod grading_paper_scan_controller;
mod sheet_controller;
mod scanner_controller;

pub fn create_router(app_state: AppState) -> Router {
    Router::new()
        .nest("/sheets", sheet_controller::create_router())
        .nest("/questions", grading_question_controller::create_router())
        .nest("/scans",grading_paper_scan_controller::create_router())
        .nest ("/scanner",scanner_controller::create_router())
        .merge(grading_controller::create_router())
        .with_state(app_state)
}