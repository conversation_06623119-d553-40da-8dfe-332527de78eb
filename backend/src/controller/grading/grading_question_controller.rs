use crate::service::grading::grading_question_service::{GradingQuestionService};
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::extract::{Path, State};
use axum::routing::post;
use axum::{Json, Router};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::service::grading::vo::criteria_vo::CriteriaVo;
use crate::service::grading::vo::score_vo::ScoreVo;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/criteria/{homework_id}", post(get_questions_with_homework_id))
        .route("/scores", post(get_scores))
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct GetQuestionsRequest {
    pub tenant_name:String,
    pub homework_id: Uuid
}

// 返回各个小题的汇总情况
async fn get_questions_with_homework_id(State(state): State<AppState>, Path(params): Path<GetQuestionsRequest>) -> Result<ApiResponse<Vec<CriteriaVo>>, ApiResponse<String>> {
    let service = GradingQuestionService::new(state.db.clone(), params.tenant_name.clone(), state.storage_service);
    let homework_paper_service = HomeworkPapersService::new(state.db, params.tenant_name);
    let criteria_list = homework_paper_service.get_homework_criteria_list(params.homework_id).await.map_err(|e| ApiResponse::error(format!("读取试卷评分标准失败: {}", e), None))?;
    if criteria_list.is_empty() {
        Ok(ApiResponse::success(Vec::new(), None))
    } else {
        let criteria_list = service.get_criteria_list(criteria_list).await.map_err(|e| ApiResponse::error(format!("读取试卷失败: {}", e), None))?;
        Ok(ApiResponse::success(criteria_list, None))
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct GetScoresRequest {
    homework_id: Uuid,
    criteria_id: Uuid,
    page: i32,
    page_size: i32,
}

async fn get_scores(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<GetScoresRequest>) -> Result<PaginatedApiResponse<ScoreVo>, PaginatedApiResponse<()>> {
    let service = GradingQuestionService::new(state.db, tenant_name, state.storage_service);
    let (scores, total) = service.get_scores(params.homework_id, params.criteria_id, params.page_size, params.page).await
        .map_err(|e| PaginatedApiResponse::error(format!("获取试题失败: {}", e),None))?;
    Ok(PaginatedApiResponse::success(scores, params.page, params.page_size, total, None))
}