use crate::model::grading::grading::PaperScanPathRequest;
use crate::model::grading::paper_scans::PaperScanStatus;
use crate::model::paper::paper::Paper;
use crate::model::score::ScoreStatus;
use crate::service::grading::grading_service::GradingService;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::extract::Multipart;
use axum::{
    extract::{Path, State},
    response::Json,
    routing::post,
    Router,
};
use bigdecimal::BigDecimal;
use serde::Deserialize;
use std::collections::{HashMap, HashSet};
use tracing::log::error;
use tracing::{debug, info};
use uuid::Uuid;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/uploadPaperScansHandler/{exam_id}/{batch_no}", post(upload_paper_scans_handler))
        .route("/batchRecognizeScannedPagesHandler", post(batch_recognize_scanned_pages_handler))
        .route("/checkById", post(check_by_id))
        .route("/checkCorrectWithBatchIds", post(check_correct_with_ids))
        .route("/redo_scoring", post(redo_scoring))
}
#[derive(Deserialize, Debug)]
pub struct BatchRecognizeLeafRequest {
    pub exam_id: Uuid,                // 考试或作者ID
    pub batch_number: Option<String>, // 批次号
    pub pages_ids: Option<Vec<Uuid>>, // 纸张页面ID
}

/// 重新识别纸张内容 (会触发从新识别服务调用)
/// 说明: 该操作会联动 (paper_scans、paper_scan_pages、score_blocks、score_details、scores、paper_scan_block) 这几个表中的数据
pub async fn batch_recognize_scanned_pages_handler(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<BatchRecognizeLeafRequest>,
) -> anyhow::Result<ApiResponse<()>, ApiResponse<String>> {
    let BatchRecognizeLeafRequest { exam_id, batch_number, pages_ids, .. } = params;

    if pages_ids.is_none() {
        return Err(ApiResponse::error("纸张页面ID 不能为空".to_string(), None));
    }

    // 启动异步任务-进行识别操作
    tokio::spawn(async move {
        let bucket = state.config.minio_config().default_bucket.clone();
        let config = state.config.grading_config();
        let _ = match state
            .paper_scans_service
            .batch_recognize_scanned_pages(state.clone(), bucket.clone(), config.clone(), tenant_name.as_str(), exam_id, batch_number, pages_ids)
            .await
        {
            Ok(_) => {
                info!("重新识别纸张内容成功!")
            }
            Err(e) => {
                error!("重新识别纸张内容异常:{}", e)
            }
        };
    });

    Ok(ApiResponse::success((), None))
}

///处理扫描仪图片文件上传
pub async fn upload_paper_scans_handler(State(state): State<AppState>, Path(params): Path<PaperScanPathRequest>, payload: Multipart) -> Result<Json<ApiResponse<String>>, ApiResponse<String>> {
    let service = GradingService::new(state.db.clone());

    // 1. 图片上传到 minio 服务端
    let leafs = service.process_upload_payload(state.clone(), &params, payload).await.map_err(|e| {
        error!("图片上传异常: {}", e);
        ApiResponse::error(format!("图片上传异常: {}", e), None)
    })?;

    // 2.将当前纸张记录保存到数据库中 TODO:目前暂时没有考虑终端对同一张图片多次重传问题，导致数据会存在多份情况后面会优化
    let mut leaf_ids: Vec<_> = vec![];
    let mut is_single_sided_multi_page = false;
    for leaf in leafs.iter() {
        leaf_ids.push(leaf.id);
        is_single_sided_multi_page = leaf.is_single_sided_multi_page;
        service.save_upload_paper_scans(params.tenant_name.clone(), &leaf).await.map_err(|e| {
            error!("保存扫描纸张信息异常: {}", e);
            ApiResponse::error(format!("保存扫描纸张信息异常: {}", e), None)
        })?;
    }
    // 3.启动异步任务-调用答题卡识别服务（不阻塞当前请求）
    tokio::spawn(async move {
        let bucket = state.config.minio_config().default_bucket.clone();
        let config = state.config.grading_config();
        let homework_paper_service = HomeworkPapersService::new(state.db.clone(), params.tenant_name.clone());
        // 3.1 根据试卷/作业ID获取
        debug!("exam_id:{}", params.exam_id.to_string());
        let mut papers = match homework_paper_service.get_homework_papers_by_homework_id(params.exam_id).await {
            Ok(papers) => papers,
            Err(e) => {
                let error_message = format!("获取试卷信息异常: {}", e);
                error!("{}", e);
                // 同时修改纸张与页面的ID
                let _ = state
                    .paper_scans_service
                    .update_paper_scans_and_page_status(params.tenant_name.clone().as_str(), leaf_ids, PaperScanStatus::Error, Some(error_message.clone()))
                    .await;
                return;
            }
        };

        // 判断试卷合法性
        if papers.len() == 0 || papers.len() > 1 {
            let error_message = if papers.len() == 0 {
                "当前考试ID,查询不到对应绑定的试卷信息".to_string()
            } else {
                "同一个考试或作业存在多份试卷".to_string()
            };
            error!("{}", error_message);
            // 同时修改纸张与页面的ID
            let _ = state
                .paper_scans_service
                .update_paper_scans_and_page_status(params.tenant_name.clone().as_str(), leaf_ids, PaperScanStatus::Error, Some(error_message))
                .await;

            return;
        }
        let paper: Paper = papers.remove(0);
        // 获取试卷答题卡信息
        let paper_content = paper.paper_content.0.clone();
        let leaf_bases: Vec<_> = leafs.iter().map(|leaf| {
            (leaf.id, leaf.scans.clone().into_iter().map(|s| (s.id, s.file_url.clone())).collect::<Vec<_>>())
        }).collect();

        // 3.2 调用试卷识别服务
        let mut sheet_response_map: HashMap<_, _> = match state
            .paper_scans_service
            .recognize_paper_scans(bucket.clone(), config.clone(), leaf_bases, is_single_sided_multi_page, &paper_content)
            .await
        {
            Ok(s) => s,
            Err(e) => {
                let error_message = format!("调用阅卷识别服务异常: {}", e);
                error!("{}", error_message);
                // 如果出现异常、更新当前纸张状态信息
                let _ = state
                    .paper_scans_service
                    .update_paper_scans_and_page_status(params.tenant_name.clone().as_str(), leaf_ids, PaperScanStatus::Error, Some(error_message.clone()))
                    .await;
                return; // 直接返回，不继续执行
            }
        }.into_iter().map(|v| (v.uuid, v)).collect();

        // 3.3. 分割题块
        for leaf in leafs {
            if let Some(sheet_response) = sheet_response_map.remove(&leaf.id) {
                let paper_scans_split_sheets_answer_block_response = match state
                    .paper_scans_service
                    .split_sheets_answer_block(
                        params.tenant_name.as_str(),
                        &paper_content,
                        sheet_response,
                        leaf.id,
                        leaf.exam_id,
                        leaf.exam_type,
                    ).await
                {
                    Ok(response) => response,
                    Err(error) => {
                        let error_message = format!("调用题块分割服务异常: {}", error);
                        error!("{}", error_message.clone());
                        // 如果出现异常、更新当前纸张状态信息
                        // 如果出现异常、更新当前纸张状态信息
                        let _ = state
                            .paper_scans_service
                            .update_paper_scans_and_page_status(params.tenant_name.clone().as_str(), vec![leaf.id], PaperScanStatus::Error, Some(error_message.clone()))
                            .await;
                        return;
                    }
                };

                // 3.4 根据阅卷评分标准调整相关分数
                if let Err(e) = service
                    .smart_dispatch_grading_score(params.tenant_name.as_str(), state.task_queue.clone(), paper_scans_split_sheets_answer_block_response, paper_content.clone())
                    .await
                {
                    error!("smart_dispatch_grading_score: {}", e);
                }
            }
        }
    });

    Ok(Json(ApiResponse::success("上传成功".to_string(), None)))
}

#[derive(Deserialize, Debug)]
struct ScoringCheckByIdParams {
    id: Uuid,
    score: BigDecimal,
    reason: Option<String>,
}
async fn check_by_id(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<ScoringCheckByIdParams>) -> Result<ApiResponse<u64>, ApiResponse<()>> {
    let service = GradingService::new(state.db);
    let rows_affected = service
        .check_by_id(tenant_name.as_str(), params.id, params.score, params.reason)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    Ok(ApiResponse::success(rows_affected, None))
}
#[derive(Deserialize, Debug)]
struct ScoringCheckCorrectParams {
    score_ids: Vec<Uuid>,
}
async fn check_correct_with_ids(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<ScoringCheckCorrectParams>) -> Result<ApiResponse<u64>, ApiResponse<()>> {
    let service = GradingService::new(state.db);
    let rows_affected = service
        .check_correct_with_ids(tenant_name.as_str(), params.score_ids)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    Ok(ApiResponse::success(rows_affected, None))
}
#[derive(Deserialize, Debug)]
struct ReScoringParams {
    criteria_ids: HashSet<Uuid>,
    student_ids: Vec<Uuid>,
    homework_id: Uuid,
    redo_ocr: bool,
    include_done: bool,
    include_checked: bool,
}
/// 重新批阅，基于输入题号，人员，状态
async fn redo_scoring(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<ReScoringParams>) -> Result<ApiResponse<u64>, ApiResponse<()>> {
    let service = GradingService::new(state.db.clone());
    let criteria_list = HomeworkPapersService::new(state.db.clone(), tenant_name.clone())
        .get_homework_criteria_list(params.homework_id)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    let criteria_list = if params.criteria_ids.is_empty() {
        criteria_list
    } else {
        criteria_list.into_iter().filter(|criteria| params.criteria_ids.contains(&criteria.id)).collect()
    };
    if criteria_list.is_empty() {
        Err(ApiResponse::error("未选择正确的试题".to_string(), None))
    } else {
        let status_list = if params.include_checked {
            Vec::new()
        } else {
            let mut status_list = Vec::new();
            if params.include_done {
                status_list.push(ScoreStatus::Done)
            }
            status_list.push(ScoreStatus::Undistributed);
            status_list.push(ScoreStatus::Distributed);
            status_list.push(ScoreStatus::Excepted);
            status_list
        };
        let ret = service
            .redo_scoring(tenant_name.as_str(), state.task_queue, criteria_list, params.student_ids, status_list, params.redo_ocr)
            .await
            .map_err(|e| ApiResponse::error(e.to_string(), None))?;
        Ok(ApiResponse::success(ret, None))
    }
}
