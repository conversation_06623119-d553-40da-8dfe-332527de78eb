use crate::model::grading::grading::ScanQueryParams;
use crate::service::paper_scan_page::paper_scan_page_service::PaperScansResult;
use crate::service::paper_scans::paper_scans_service::{PaperScansBatchInfoVo, PaperScansStatisticsResponse};
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::extract::{Path, Query, State};
use axum::routing::{get, post};
use axum::{Json, Router};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 阅卷中心-试卷扫描相关
pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getBatchNumberList", get(get_paper_scan_batch_number))
        .route("/getPaperScanFiles", post(get_paper_scan_files_handler))
        .route("/getStatistics", get(get_statistics))
        .route("/batchDeletePaperScans", post(batch_delete_paper_scans))
        .route("/deletePaperScanPage", post(delete_paper_scan_page))
        .route("/updatePageNumber", post(update_paper_scan_page_number))
        .route("/rebindStudentToLeaf", post(rebind_student_to_leaf))
}

/// 查询某场作业/试卷上次批次号列表
pub async fn get_paper_scan_batch_number(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Query(params): Query<ScanQueryParams>,
) -> anyhow::Result<ApiResponse<Vec<PaperScansBatchInfoVo>>, ApiResponse<String>> {
    let result = state
        .paper_scans_service
        .get_paper_scan_batch_number_list(&tenant_name, &params)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    Ok(ApiResponse::success(result, None))
}

/// 获取根据批次号查询试卷扫描文件列表
pub async fn get_paper_scan_files_handler(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<ScanQueryParams>,
) -> Result<PaginatedApiResponse<PaperScansResult>, ApiResponse<String>> {
    // 查询分试卷上传文件列表
    let (mut list, total) = state
        .paper_scan_page_service
        .find_paper_scan_page_list(&tenant_name, &params)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;

    // 将纸张中的源路径转换成网络地址
    for item in list.iter_mut() {
        for page in item.pages.iter_mut() {
            if !page.url.is_empty() {
                match state.storage_service.generate_preview_images_url(page.url.as_str()).await {
                    Ok(url) => page.url = url,
                    Err(_) => {}
                }
            }

            if let Some(rectify_url) = &page.rectify_url {
                match state.storage_service.generate_preview_images_url(rectify_url.as_str()).await {
                    Ok(url) => page.rectify_url = Some(url),
                    Err(_) => {}
                }
            }
        }

        item.pages.sort_by(|a, b| a.page_num.cmp(&b.page_num));
    }
    // 重排序
    list.sort_by(|a, b| b.created_at.cmp(&a.created_at));

    Ok(responses::paginated_success(list, params.page.unwrap_or(1), params.page_size.unwrap_or(10), total, None))
}

/// 扫描信息总览概述
pub async fn get_statistics(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Query(params): Query<ScanQueryParams>,
) -> anyhow::Result<ApiResponse<PaperScansStatisticsResponse>, ApiResponse<String>> {
    if let Some(exam_id) = params.exam_id {
        let result = state
            .paper_scans_service
            .get_statistics(&tenant_name, exam_id)
            .await
            .map_err(|e| ApiResponse::error(e.to_string(), None))?;
        Ok(ApiResponse::success(result, None))
    } else {
        Err(ApiResponse::error("考试ID不能为空".to_string(), None))
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PaperScansRequest {
    pub exam_id: Uuid,
    pub student_id: Option<Uuid>,
    pub target_student_id: Option<Uuid>,
    pub batch_number: Option<String>,
    pub page_ids: Option<Vec<Uuid>>,
    pub page_number: Option<i32>,
}

/// 批量清除扫描批次纸张信息
pub async fn batch_delete_paper_scans(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(payload): Json<PaperScansRequest>) -> Result<ApiResponse<()>, ApiResponse<String>> {
    state
        .paper_scans_service
        .purge_batch_scans(tenant_name.as_str(), payload.exam_id, payload.batch_number)
        .await
        .map_err(|e| ApiResponse::error(e.to_string(), None))?;

    Ok(ApiResponse::success((), None))
}

/// 根据页id,删除指定页面信息
pub async fn delete_paper_scan_page(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(payload): Json<PaperScansRequest>) -> Result<ApiResponse<()>, ApiResponse<String>> {
    let PaperScansRequest { exam_id, page_ids, .. } = payload;

    if page_ids.is_none() || page_ids.clone().unwrap().len() == 0 {
        return Err(ApiResponse::error("page_ids 数组不能为空".to_string(), None));
    }

    if let Some(page_ids) = page_ids {
        state
            .paper_scans_service
            .delete_paper_scan_page(tenant_name.as_str(), exam_id, page_ids)
            .await
            .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    }

    Ok(ApiResponse::success((), None))
}

/// 更新纸张中的页码信息
pub async fn update_paper_scan_page_number(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(payload): Json<PaperScansRequest>) -> Result<ApiResponse<()>, ApiResponse<String>> {
    let PaperScansRequest { exam_id, page_ids, page_number, .. } = payload;

    if page_ids.is_none() || page_ids.clone().unwrap().len() == 0 {
        return Err(ApiResponse::error("page_ids 数组不能为空".to_string(), None));
    }

    if page_number.is_none() || page_number.unwrap() <= 0 {
        return Err(ApiResponse::error("请输入一个合法的页码".to_string(), None));
    }

    if let Some(page_ids) = page_ids {
        state
            .paper_scan_page_service
            .update_paper_scan_page_number(tenant_name.as_str(), exam_id, page_ids, page_number.unwrap())
            .await
            .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    }
    Ok(ApiResponse::success((), None))
}

///重绑学生与纸张对应关系
pub async fn rebind_student_to_leaf(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(payload): Json<PaperScansRequest>) -> Result<ApiResponse<()>, ApiResponse<String>> {
    let PaperScansRequest {
        exam_id,
        page_ids,
        student_id,
        target_student_id,
        ..
    } = payload;

    if page_ids.is_none() || page_ids.clone().unwrap().len() == 0 {
        return Err(ApiResponse::error("page_ids 数组不能为空".to_string(), None));
    }


    if target_student_id.is_none() {
        return Err(ApiResponse::error("target_student_id 不能为空".to_string(), None));
    }

    if let Some(page_ids) = page_ids {
        state
            .paper_scans_service
            .rebind_student_to_leaf(tenant_name.as_str(), exam_id, page_ids, student_id, target_student_id.unwrap())
            .await
            .map_err(|e| ApiResponse::error(e.to_string(), None))?;
    }

    Ok(ApiResponse::success((), None))
}
