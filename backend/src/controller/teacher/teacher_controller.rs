use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::teacher::import::types::TeacherImportRecord;
use crate::model::teacher::teacher::{
    Create<PERSON><PERSON>er<PERSON><PERSON><PERSON>, Teacher, ToggleStatusPayload, UpdateTeacherParams,
};
use crate::model::{FindAllParams, PageAllTeacherParams, TeacherImportResult, TeacherSummary};
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::utils::error::AppError;
use crate::web_server::AppState;
use axum::extract::Multipart;
use axum::http::HeaderMap;
use axum::routing::post;
use axum::{
    extract::{Path, State},
    response::Json,
    Router,
};
use sqlx::types::Uuid;
use std::collections::HashMap;

/// 创建教师管理路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/{id}/toggle-status", post(toggle_teacher_status))
        .route("/Summaries", post(get_teacher_summaries))
        .route("/findAll", post(find_all))
        .route("/pageAllTeacher", post(page_all_teacher))
        .route("/createTeacher", post(create_teacher))
        .route("/updateTeacher", post(update_teacher))
        .route("/import", post(import_teachers))
}

/// 切换教师状态
#[axum::debug_handler]
async fn toggle_teacher_status(
    State(state): State<AppState>,
    Path((tenant_name, teacher_id)): Path<(String, Uuid)>,
    _auth: AuthExtractor,
    Json(payload): Json<ToggleStatusPayload>,
) -> Result<ApiResponse<Teacher>, ApiResponse<()>> {
    match state.teacher_service
        .toggle_teacher_status(&tenant_name, teacher_id, payload.is_active)
        .await
    {
        Ok(teacher) => Ok(ApiResponse::success(teacher, None)),
        Err(_e) => {
            Err(ApiResponse::error("切换教师状态失败".to_string(), None))
        }
    }
}

/// 获取教师摘要列表
pub async fn get_teacher_summaries(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
    Json(params): Json<Option<HashMap<String, bool>>>,
) -> Result<ApiResponse<Vec<TeacherSummary>>, ApiResponse<()>> {
    let is_active = params.and_then(|p| p.get("is_active").copied());
    match state
        .teacher_service
        .get_teacher_summaries(&tenant_name, is_active)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(e) => Err(responses::error(&format!("{}", e), None)),
    }
}

pub async fn find_all(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
    Json(params): Json<FindAllParams>,
) -> Result<ApiResponse<Vec<Teacher>>, ApiResponse<()>> {
    state
        .teacher_service
        .find_all(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn page_all_teacher(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<PageAllTeacherParams>,
) -> Result<PaginatedApiResponse<Teacher>, PaginatedApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::paginated_error("权限不足！", None));
    }
    state
        .teacher_service
        .page_all_teacher(&tenant_name, &params)
        .await
        .map_err(|e| responses::paginated_error(&e, None))
        .map(|(list, total)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                total,
                None,
            )
        })
}

pub async fn create_teacher(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<CreateTeacherParams>,
) -> Result<ApiResponse<Teacher>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    println!("{:?}", tenant_id);
    state
        .teacher_service
        .create_teacher(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn update_teacher(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<UpdateTeacherParams>,
) -> Result<ApiResponse<Teacher>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .teacher_service
        .update_teacher(&tenant_name, &tenant_id, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}


/// Excel文件导入教师
pub async fn import_teachers(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    mut multipart: Multipart,
) -> Result<ApiResponse<TeacherImportResult>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        responses::error(&format!("解析上传文件失败: {}", e), None)
    })? {
        if field.name() == Some("file") {
            let data = field.bytes().await.map_err(|e| {
                responses::error(&format!("读取文件内容失败: {}", e), None)
            })?;

            // 使用新的import_service处理Excel文件导入
            match process_excel_import(&state, &tenant_name, &data).await {
                Ok(result) => return Ok(responses::success(result, None)),
                Err(e) => return Err(responses::error(&e.to_string(), None)),
            }
        }
    }

    Err(responses::error("未找到上传文件", None))
}

async fn process_excel_import(
    state: &AppState,
    tenant_name: &str,
    data: &[u8],
) -> Result<TeacherImportResult,  AppError> {
    use calamine::{open_workbook_from_rs, Reader, Xlsx};
    use std::io::Cursor;

    let cursor = Cursor::new(data);
    let mut workbook: Xlsx<_> = open_workbook_from_rs(cursor)
        .map_err(|e| AppError::InternalServerError(format!("无法打开Excel文件: {}", e)))?;
    let sheet_names = workbook.sheet_names().to_owned();
    if sheet_names.is_empty() {
        return Err(AppError::InternalServerError("Excel文件中没有工作表".to_string()));
    }
    let sheet_name = &sheet_names[0];
    let worksheet = workbook.worksheet_range(sheet_name)
        .map_err(|e| AppError::InternalServerError(format!("读取工作表失败: {}", e)))?;
    let mut records = Vec::new();

    // 跳过标题行，从第二行开始处理
    for row in worksheet.rows().skip(1) {
        if row.len() < 7 {
            continue; // 跳过字段不足的行
        }

        // 解析Excel行数据: 序号,科目,姓名,手机号,任教班级,职位,年级
        match parse_excel_row_to_teacher_record(row) {
            Ok(record) => {
                if !record.name.trim().is_empty() {
                    records.push(record);
                }
            }
            Err(_) => {
                // 跳过解析失败的行，由import_service处理错误
                continue;
            }
        }
    }

    // 使用import_service处理导入
   state.teacher_import_service
        .import_teachers(tenant_name, records, Some("2025".to_string())).await
        .map_err(|e| AppError::InternalServerError(e.to_string()))
}

fn parse_excel_row_to_teacher_record(row: &[calamine::Data]) -> Result<TeacherImportRecord, String> {
    use calamine::Data;
    
    let get_cell_string = |index: usize| -> String {
        if index < row.len() {
            match &row[index] {
                Data::String(s) => s.clone(),
                Data::Float(f) => f.to_string(),
                Data::Int(i) => i.to_string(),
                Data::Bool(b) => b.to_string(),
                _ => String::new(),
            }
        } else {
            String::new()
        }
    };

    let sequence = get_cell_string(0);
    let subject = get_cell_string(1);
    let name = get_cell_string(2);
    let phone = get_cell_string(3);
    let teaching_classes = get_cell_string(4);
    let position = get_cell_string(5);
    let grade = get_cell_string(6);

    if subject.trim().is_empty() {
        return Err("科目不能为空".to_string());
    }
    if name.trim().is_empty() {
        return Err("姓名不能为空".to_string());
    }
    if grade.trim().is_empty() {
        return Err("年级不能为空".to_string());
    }

    Ok(TeacherImportRecord {
        sequence: if sequence.trim().is_empty() { None } else { Some(sequence) },
        subject,
        name,
        phone: if phone.trim().is_empty() { None } else { Some(phone) },
        teaching_classes: if teaching_classes.trim().is_empty() { None } else { Some(teaching_classes.to_string()) },
        position: if position.trim().is_empty() { None } else { Some(position.to_string()) },
        grade,
    })
}
