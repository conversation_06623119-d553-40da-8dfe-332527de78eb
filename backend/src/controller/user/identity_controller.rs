use crate::model::user::auth::*;
use crate::service::user::identity_service::IdentityService;
use crate::utils::jwt;
use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::Json,
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;
use crate::middleware::auth_middleware::AuthExtractor;
use crate::utils::api_response::{responses, ApiResponse};

pub fn create_router() -> Router<Arc<IdentityService>> {
    Router::new()
        .route("/suggestions", get(get_identity_suggestions))
        .route("/list", get(get_identities))
        .route("/bind", post(bind_identity))
        .route("/switch", post(switch_identity))
        .route("/{identity_id}/verify", post(verify_identity))
}

async fn get_identity_suggestions(
    State(identity_service): State<Arc<IdentityService>>,
    headers: HeaderMap,
) -> Result<Json<IdentitySuggestionsResponse>, (StatusCode, Json<ErrorResponse>)> {
    let user_id = extract_user_id_from_headers(&headers)?;

    match identity_service.get_identity_suggestions(user_id).await {
        Ok(suggestions) => {
            info!("Retrieved {} identity suggestions for user {}", suggestions.len(), user_id);
            Ok(Json(IdentitySuggestionsResponse {
                success: true,
                data: IdentitySuggestionsData { suggestions },
            }))
        }
        Err(e) => {
            error!("Failed to get identity suggestions: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn get_identities(
    State(identity_service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
) -> ApiResponse<Vec<IdentityInfo>> {
    // TODO 这个返回结构前端其实相当难辨认，还需要优化连表查询具体学生/老师、职位等。
    match identity_service.get_identities(context.user_id,context.username).await {
        Ok(vec) => {
            responses::success(vec, Some("获取身份列表成功"))
        }
        Err(e) => responses::error(format!("获取用户身份失败：{}",e).as_str(), Some("GET_IDENTITY_FAILED"))

    }
}

async fn bind_identity(
    State(identity_service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Json(request): Json<BindIdentityRequest>,
) -> ApiResponse<()> {
    let user_id = context.user_id;
    match identity_service.bind_identity(user_id, request).await {
        Ok(()) => {
            responses::success((), Some("绑定成功"))
        }
        Err(e) => {
            responses::error(format!("绑定失败：{}",e).as_str(), Some("BIND_IDENTITY_FAILED"))
        }
    }
}

async fn switch_identity(
    State(identity_service): State<Arc<IdentityService>>,
    headers: HeaderMap,
    Json(request): Json<SwitchIdentityRequest>,
) -> Result<Json<SwitchIdentityResponse>, (StatusCode, Json<ErrorResponse>)> {
    let user_id = extract_user_id_from_headers(&headers)?;
    let session_id = extract_session_id_from_headers(&headers)?;
    let ip_address = extract_ip_address(&headers);
    let user_agent = extract_user_agent(&headers);

    match identity_service.switch_identity(
        user_id,
        session_id,
        request,
        ip_address,
        user_agent,
    ).await {
        Ok(response) => {
            info!("Identity switched successfully for user {}", user_id);
            Ok(Json(response))
        }
        Err(e) => {
            error!("Failed to switch identity: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn verify_identity(
    State(identity_service): State<Arc<IdentityService>>,
    headers: HeaderMap,
    Path(identity_id): Path<Uuid>,
) -> Result<Json<VerifyIdentityResponse>, (StatusCode, Json<ErrorResponse>)> {
    let verified_by = extract_user_id_from_headers(&headers)?;

    // TODO: Add authorization check to ensure user has permission to verify identities
    // This should typically be restricted to administrators

    match identity_service.verify_identity(identity_id, verified_by).await {
        Ok(_) => {
            info!("Identity {} verified by user {}", identity_id, verified_by);
            Ok(Json(VerifyIdentityResponse {
                success: true,
                message: "Identity verified successfully".to_string(),
            }))
        }
        Err(e) => {
            error!("Failed to verify identity: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

// Helper functions
fn extract_user_id_from_headers(
    headers: &HeaderMap,
) -> Result<Uuid, (StatusCode, Json<ErrorResponse>)> {
    let token = extract_bearer_token(headers)
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    success: false,
                    message: "Missing authorization token".to_string(),
                    error_code: "MISSING_TOKEN".to_string(),
                }),
            )
        })?;

    let claims = jwt::validate_token(&token).map_err(|e| {
        error!("Failed to validate token: {:?}", e);
        (
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                success: false,
                message: "Invalid or expired token".to_string(),
                error_code: "INVALID_TOKEN".to_string(),
            }),
        )
    })?;
    
    Uuid::parse_str(&claims.sub).map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                success: false,
                message: "Invalid user ID in token".to_string(),
                error_code: "INVALID_TOKEN_SUB".to_string(),
            }),
        )
    })
}

fn extract_session_id_from_headers(_headers: &HeaderMap) -> Result<Uuid, (StatusCode, Json<ErrorResponse>)> {
    // In a real implementation, this would be extracted from the session or token
    Ok(Uuid::new_v4())
}

fn extract_bearer_token(headers: &HeaderMap) -> Option<String> {
    headers
        .get("authorization")
        .and_then(|value| value.to_str().ok())
        .and_then(|auth_header| {
            if auth_header.starts_with("Bearer ") {
                Some(auth_header[7..].to_string())
            } else {
                None
            }
        })
}

fn extract_ip_address(headers: &HeaderMap) -> Option<std::net::IpAddr> {
    headers
        .get("x-forwarded-for")
        .or_else(|| headers.get("x-real-ip"))
        .and_then(|value| value.to_str().ok())
        .and_then(|s| s.split(',').next().unwrap_or(s).trim().parse().ok())
}

fn extract_user_agent(headers: &HeaderMap) -> Option<String> {
    headers
        .get("user-agent")
        .and_then(|value| value.to_str().ok())
        .map(|s| s.to_string())
}

fn map_auth_error(error: AuthError) -> (StatusCode, ErrorResponse) {
    let (status, message, code) = match error {
        AuthError::IdentityNotFound => (
            StatusCode::NOT_FOUND,
            "Identity not found".to_string(),
            "IDENTITY_NOT_FOUND".to_string(),
        ),
        AuthError::IdentityAlreadyBound => (
            StatusCode::CONFLICT,
            "Identity already bound".to_string(),
            "IDENTITY_EXISTS".to_string(),
        ),
        AuthError::InsufficientPermissions => (
            StatusCode::FORBIDDEN,
            "Insufficient permissions".to_string(),
            "INSUFFICIENT_PERMISSIONS".to_string(),
        ),
        AuthError::InvalidCredentials => (
            StatusCode::UNAUTHORIZED,
            "Invalid credentials".to_string(),
            "INVALID_CREDENTIALS".to_string(),
        ),
        AuthError::DatabaseError(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Internal server error".to_string(),
            "DATABASE_ERROR".to_string(),
        ),
        AuthError::JwtError(_) => (
            StatusCode::UNAUTHORIZED,
            "Invalid or expired token".to_string(),
            "INVALID_TOKEN".to_string(),
        ),
        _ => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Internal server error".to_string(),
            "INTERNAL_ERROR".to_string(),
        ),
    };

    (
        status,
        ErrorResponse {
            success: false,
            message,
            error_code: code,
        },
    )
}

// Response DTOs
#[derive(serde::Serialize)]
struct IdentitySuggestionsResponse {
    success: bool,
    data: IdentitySuggestionsData,
}

#[derive(serde::Serialize)]
struct IdentitySuggestionsData {
    suggestions: Vec<IdentityBindingSuggestion>,
}

#[derive(serde::Serialize)]
struct UserIdentitiesResponse {
    success: bool,
    data: UserIdentitiesData,
}

#[derive(serde::Serialize)]
struct UserIdentitiesData {
    identities: Vec<IdentityInfo>,
}

#[derive(serde::Serialize)]
struct VerifyIdentityResponse {
    success: bool,
    message: String,
}

#[derive(serde::Serialize)]
struct ErrorResponse {
    success: bool,
    message: String,
    error_code: String,
}