use crate::middleware::auth_middleware::AuthExtractor;
use crate::service::user::identity_service::IdentityService;
use crate::utils::api_response::{responses, ApiResponse};
use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{delete, get, post, put},
    Router,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;

// ===== 请求结构体 =====

/// 创建用户身份请求
#[derive(Debug, Clone, Deserialize)]
pub struct CreateUserIdentityRequest {
    pub user_id: Uuid,
    pub role_ids: Vec<Uuid>, // 支持多选角色
    pub target_type: String, // 'school', 'subject_group', 'grade', 'class', 'student'
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    pub tenant_id: Uuid,
}

/// 更新用户身份请求
#[derive(Debug, Deserialize)]
pub struct UpdateUserIdentityRequest {
    pub role_ids: Vec<Uuid>, // 支持多选角色
    pub target_type: Option<String>,
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
}

/// 批量操作用户身份请求
#[derive(Debug, Deserialize)]
pub struct BatchUserIdentityRequest {
    pub user_id: Uuid,
    pub identities: Vec<CreateUserIdentityRequest>,
}

/// 查询用户身份请求参数
#[derive(Debug, Deserialize)]
pub struct QueryUserIdentitiesParams {
    pub user_id: Option<Uuid>,
    pub role_id: Option<Uuid>,
    pub target_type: Option<String>,
    pub target_id: Option<Uuid>,
    pub tenant_id: Uuid,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

// ===== 响应结构体 =====

/// 用户身份响应
#[derive(Debug, Serialize)]
pub struct UserIdentityResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub roles: Vec<RoleInfo>, // 支持多角色显示
    pub target_type: String,
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 角色信息
#[derive(Debug, Serialize)]
pub struct RoleInfo {
    pub id: Uuid,
    pub name: String,
    pub code: String,
    pub description: Option<String>,
}

/// 用户身份列表响应
#[derive(Debug, Serialize)]
pub struct UserIdentitiesListResponse {
    pub identities: Vec<UserIdentityResponse>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
}

/// 批量操作响应
#[derive(Debug, Serialize)]
pub struct BatchOperationResponse {
    pub success_count: u32,
    pub failed_count: u32,
    pub failed_items: Vec<BatchFailedItem>,
}

#[derive(Debug, Serialize)]
pub struct BatchFailedItem {
    pub index: u32,
    pub error: String,
}

// ===== 控制器函数 =====

/// 创建用户身份
pub async fn create_user_identity(
    State(service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Json(request): Json<CreateUserIdentityRequest>,
) -> ApiResponse<UserIdentityResponse> {
    // 获取默认的 schema_name
    let schema_name = context.tenant_links.iter().find(|link| link.tenant_id == request.tenant_id).map(|link| link.schema_name.as_str()).unwrap_or("public");
    match service.create_user_identity_with_validation(request, schema_name).await {
        Ok(identity) => responses::success(identity, Some("用户身份创建成功")),
        Err(e) => responses::error(&format!("创建用户身份失败: {}", e), Some("CREATE_IDENTITY_FAILED")),
    }
}

/// 获取用户身份列表
pub async fn get_user_identities(
    State(service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Query(params): Query<QueryUserIdentitiesParams>,
) -> ApiResponse<UserIdentitiesListResponse> {
    let schema_name = context.tenant_links.iter().find(|link| link.tenant_id == params.tenant_id).map(|link| link.schema_name.as_str()).unwrap_or("public");
    match service.get_user_identities_with_validation(params, &schema_name).await {
        Ok(response) => responses::success(response, Some("获取用户身份列表成功")),
        Err(e) => responses::error(&format!("获取用户身份列表失败: {}", e), Some("GET_IDENTITIES_FAILED")),
    }
}

/// 根据ID获取用户身份
pub async fn get_user_identity_by_id(
    State(service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Path(identity_id): Path<Uuid>,
) -> ApiResponse<UserIdentityResponse> {
    let schema_name = context.tenant_links
        .first()
        .map(|link| link.schema_name.as_str())
        .unwrap_or("public");

    match service.get_user_identity_by_id(identity_id, schema_name).await {
        Ok(Some(identity)) => responses::success(identity, Some("获取用户身份成功")),
        Ok(None) => responses::error("用户身份不存在", Some("IDENTITY_NOT_FOUND")),
        Err(e) => responses::error(&format!("获取用户身份失败: {}", e), Some("GET_IDENTITY_FAILED")),
    }
}

/// 更新用户身份
pub async fn update_user_identity(
    State(service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Path(identity_id): Path<Uuid>,
    Json(request): Json<UpdateUserIdentityRequest>,
) -> ApiResponse<UserIdentityResponse> {
    let schema_name = context.tenant_links
        .first()
        .map(|link| link.schema_name.as_str())
        .unwrap_or("public");

    match service.update_user_identity_with_validation(identity_id, request, schema_name).await {
        Ok(identity) => responses::success(identity, Some("用户身份更新成功")),
        Err(e) => responses::error(&format!("更新用户身份失败: {}", e), Some("UPDATE_IDENTITY_FAILED")),
    }
}

/// 删除用户身份
pub async fn delete_user_identity(
    State(service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Path(identity_id): Path<Uuid>,
) -> ApiResponse<()> {
    let schema_name = context.tenant_links
        .first()
        .map(|link| link.schema_name.as_str())
        .unwrap_or("public");

    match service.delete_user_identity_by_id(identity_id, schema_name).await {
        Ok(()) => responses::success((), Some("用户身份删除成功")),
        Err(e) => responses::error(&format!("删除用户身份失败: {}", e), Some("DELETE_IDENTITY_FAILED")),
    }
}

/// 批量创建用户身份
pub async fn batch_create_user_identities(
    State(service): State<Arc<IdentityService>>,
    AuthExtractor(context): AuthExtractor,
    Json(request): Json<BatchUserIdentityRequest>,
) -> ApiResponse<BatchOperationResponse> {
    let schema_name = context.tenant_links
        .first()
        .map(|link| link.schema_name.as_str())
        .unwrap_or("public");

    match service.batch_create_user_identities(request, schema_name).await {
        Ok(response) => responses::success(response, Some("批量创建用户身份完成")),
        Err(e) => responses::error(&format!("批量创建用户身份失败: {}", e), Some("BATCH_CREATE_FAILED")),
    }
}

/// 创建路由
pub fn create_router() -> Router<Arc<IdentityService>> {
    Router::new()
        .route("/", post(create_user_identity))
        .route("/", get(get_user_identities))
        .route("/{id}", get(get_user_identity_by_id))
        .route("/{id}", put(update_user_identity))
        .route("/{id}", delete(delete_user_identity))
        .route("/batch", post(batch_create_user_identities))
}
