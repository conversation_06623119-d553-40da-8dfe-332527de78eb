use crate::model::paper::paper::{CreatePaperRequest, Paper, PaperQuery, UpdatePaperRequest};
use crate::service::paper::paper::PaperService;
use crate::utils::api_response::ApiResponse;
use crate::utils::error::AppError;
use axum::extract::{Path, Query, State};
use axum::routing::{get, post};
use axum::{Json, Router};
use std::sync::Arc;
use tracing::error;
use uuid::Uuid;

/**
 * 将会废弃，请使用 public_paper_controller
 */
pub fn create_router() -> Router<Arc<PaperService>> {
    Router::new()
        .route("/paper/list", get(list_papers))
        .route("/paper", post(create_paper))
        .route("/paper/{id}", get(get_paper).put(update_paper).delete(delete_paper))
}

pub async fn list_papers(State(service): State<Arc<PaperService>>, Query(_params): Query<PaperQuery>) -> Result<ApiResponse<Vec<Paper>>, AppError> {
    match service.get_papers().await {
        Ok(papers) => Ok(ApiResponse::success(papers, None)),
        Err(err) => {
            error!("Failed to list papers: {}", err);
            Err(AppError::InternalServerError("Failed to list papers".to_string()))
        }
    }
}

// GET /paper/:id
pub async fn get_paper(State(service): State<Arc<PaperService>>, Path(id): Path<Uuid>) -> Result<ApiResponse<Paper>, AppError> {
    match service.get_paper_by_id(id).await {
        Ok(paper) => Ok(ApiResponse::success(paper, None)),
        Err(_) => Err(AppError::NotFound("Paper not found".to_string())),
    }
}

// POST /paper
pub async fn create_paper(State(service): State<Arc<PaperService>>, Json(payload): Json<CreatePaperRequest>) -> Result<ApiResponse<Paper>, AppError> {
    match service.create_paper(payload).await {
        Ok(paper) => Ok(ApiResponse::success(paper, Some("Paper created successfully".to_string()))),
        Err(err) => {
            error!("Failed to create paper: {}", err);
            Err(AppError::InternalServerError("Failed to create paper".to_string()))
        }
    }
}

// PUT /paper/:id
pub async fn update_paper(State(service): State<Arc<PaperService>>, Path(id): Path<Uuid>, Json(payload): Json<UpdatePaperRequest>) -> Result<ApiResponse<Paper>, AppError> {
    match service.update_paper(id, payload).await {
        Ok(paper) => Ok(ApiResponse::success(paper, Some("Paper updated successfully".to_string()))),
        Err(_) => Err(AppError::NotFound("Paper not found".to_string())),
    }
}

// DELETE /paper/:id
pub async fn delete_paper(State(service): State<Arc<PaperService>>, Path(id): Path<Uuid>) -> Result<ApiResponse<()>, AppError> {
    match service.delete_paper(id).await {
        Ok(_) => Ok(ApiResponse::success((), Some("Paper deleted successfully".to_string()))),
        Err(_) => Err(AppError::NotFound("Paper not found".to_string())),
    }
}
