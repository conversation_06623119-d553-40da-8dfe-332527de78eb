use axum::extract::State;
use axum::routing::post;
use axum::{Json, Router};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::controller::paper::tenant_paper_controller::UpdatePaperContentParams;
use crate::model::paper::paper::Paper;
use crate::service::paper::public_paper_service::PublicPapersService;
use crate::utils::api_response::{responses, ApiResponse};
use crate::web_server::AppState;
static SCHEMA_NAME: &str = "public";
pub fn create_router() -> Router<AppState> {
    Router::new().route("/findByAnswerId", post(find_by_answer_id)).route("/updatePaperContent", post(update_paper_content))
}

pub async fn find_by_answer_id(State(state): State<AppState>, Json(params): <PERSON><PERSON><FindByAnswerIdParams>) -> Result<ApiResponse<Paper>, ApiResponse<()>> {
    let FindByAnswerIdParams { answer_card_id } = params;
    let result = PublicPapersService::new(state.db, SCHEMA_NAME.to_string()).find_by_answer_id(&answer_card_id).await;

    match result {
        Ok(paper) => {
            // for question_data in paper.paper_content.question_data_list.iter_mut() {
            //     for qid in question_data.question_content.iter_mut() {
            //         if let Value::Object(config) = &mut qid.config {
            //             let text = config.get_mut("text");
            //             if let Some(Value::String(text)) = text {
            //                 if text.contains("<img") && !text.contains("http") {
            //                     let t = ChapterService::handle_text_images(&state.storage_service, text.clone()).await;
            //                     match t {
            //                         Ok(replaced_text) => {
            //                             *text = replaced_text;
            //                         }
            //                         Err(err) => {
            //                             error!("图片处理异常：{}", err.to_string());
            //                         }
            //                     }
            //                 }
            //             }
            //         }
            //     }
            // }
            Ok(responses::success(paper, None))
        }
        Err(err) => Ok(responses::error(&err, None)),
    }
}

pub async fn update_paper_content(State(state): State<AppState>, Json(params): Json<UpdatePaperContentParams>) -> Result<ApiResponse<Paper>, ApiResponse<()>> {
    let UpdatePaperContentParams { paper_id, paper_content } = params;
    PublicPapersService::new(state.db, SCHEMA_NAME.to_string())
        .update_paper_content(&paper_id, &paper_content)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindByAnswerIdParams {
    pub answer_card_id: Uuid,
}
