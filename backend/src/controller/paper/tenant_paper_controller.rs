use axum::extract::{Path, State};
use axum::routing::post;
use axum::{Json, Router};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::model::paper::paper::Paper;
use crate::model::paper::paper_cache::PaperContentData;
use crate::service::paper::tenant_paper_service::TenantPapersService;
use crate::utils::api_response::{responses, ApiResponse};
use crate::web_server::AppState;

pub fn create_router() -> Router<AppState> {
    Router::new().route("/findById", post(find_by_id)).route("/updatePaperContent", post(update_paper_content))
}

pub async fn find_by_id(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<FindByIdParams>) -> Result<ApiResponse<Paper>, ApiResponse<()>> {
    let FindByIdParams { paper_id } = params;
    TenantPapersService::new(state.db, tenant_name)
        .find_by_id(&paper_id)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn update_paper_content(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<UpdatePaperContentParams>) -> Result<ApiResponse<Paper>, ApiResponse<()>> {
    let UpdatePaperContentParams { paper_id, paper_content } = params;
    TenantPapersService::new(state.db, tenant_name)
        .update_paper_content(&paper_id, &paper_content)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindByIdParams {
    pub paper_id: Uuid,
}
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdatePaperContentParams {
    pub paper_id: Uuid,
    pub paper_content: PaperContentData,
}
