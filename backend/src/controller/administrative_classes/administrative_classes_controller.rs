use std::collections::{HashMap, HashSet};
use axum::{
    extract::{Path, State},
    http::HeaderMap,
    routing::{get, post},
    <PERSON>son, Router,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{middleware::auth_middleware::AuthExtractor, model::{
        administrative_classes::administrative_classes::{
            AdministrativeClasses, AdministrativeClassesDetail, AdministrativeClassesStatistics, CreateAdministrativeClassesParams, DeleteAdministrativeClassesParams, FindAllStudentInClassParams, MoveStudentToAdministrativeClassesParams, PageStudentInClassParams, PageUserClassListParams, RemoveStudentFromAdministrativeClassesParams, UpdateAdministrativeClassesParams
        }, homework_students::homework_students::{HomeworkStudentStatus, TimeRange}, PageParams, Student
    }, service::{homework::homework_analysis_service::HomeworkAnalysis, homework_papers::homework_papers_service::HomeworkPapersService}, utils::api_response::{responses, ApiResponse, PaginatedApiResponse}, web_server::AppState
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getStatistics", get(get_statistics))
        .route("/pageUserClassList",post(page_user_class_list))
        .route("/createClasses", post(create_classes))
        .route("/findAllStudentInClass", post(find_all_student_in_class))
        .route("/updateClasses", post(update_classes))
        .route(
            "/moveStudentToAdministrativeClasses",
            post(move_student_to_administrative_classes),
        )
        .route(
            "/removeStudentFromAdministrativeClasses",
            post(remove_student_from_administrative_classes),
        )
        .route("/pageStudentInClass",post(page_student_in_class))
        .route("/deleteClass", post(delete_class))
        .route("/getClassStudentHomeworkStats",post(get_class_student_homework_stats))
}
pub async fn get_statistics(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
) -> Result<ApiResponse<AdministrativeClassesStatistics>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    match state
        .administrative_classes_service
        .get_statistics(&context, &tenant_id, &tenant_name, &context.user_id)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}
pub async fn create_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(params): Json<CreateAdministrativeClassesParams>,
) -> Result<ApiResponse<AdministrativeClasses>, ApiResponse<()>> {
    match state
        .administrative_classes_service
        .create_classes(&tenant_name, &admin_context.user_id, &params)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(msg.to_string().as_str(), None)),
    }
}

pub async fn find_all_student_in_class(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_context): AuthExtractor,
    Json(params): Json<FindAllStudentInClassParams>,
) -> Result<ApiResponse<Vec<Student>>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .find_all_student_in_class(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn update_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .update_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|_| responses::success_no_data(None))
}

pub async fn move_student_to_administrative_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<MoveStudentToAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .move_student_to_administrative_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|_| responses::success_no_data(None))
}

pub async fn remove_student_from_administrative_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<RemoveStudentFromAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .remove_student_from_administrative_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|_| responses::success_no_data(None))
}

pub async fn delete_class(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<DeleteAdministrativeClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .administrative_classes_service
        .delete_class(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|_| responses::success_no_data(None))
}

/**
 * 作者：朱若表
 * 说明：分页查询班级内的学生
 */
pub async fn page_student_in_class(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    _header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageStudentInClassParams>,
) -> Result<PaginatedApiResponse<Student>, PaginatedApiResponse<()>> {
    state
        .administrative_classes_service
        .page_student_in_class(&context,&tenant_name, &params)
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))
        .map(|(list, count)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                count,
                None,
            )
        })
}
/**
 * 作者：朱若彪
 * 说明：分页查询班级列表
 */
pub async fn page_user_class_list(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    Json(params): Json<PageUserClassListParams>,
) -> Result<PaginatedApiResponse<AdministrativeClassesDetail>, PaginatedApiResponse<()>> {
    //查询班级列表
    let mut class_list: Vec<AdministrativeClasses> = vec![];
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    let mut total_count=0;
    if context.is_admin_in_tenant(tenant_id) {
        let (mut list,count)= state
            .administrative_classes_service
            .page_all_class_list(&tenant_name, &params)
            .await
            .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
        class_list.append(&mut list);
        total_count=count;
    } else {
        class_list.append(
            &mut state
                .administrative_classes_service
                .get_user_class_list(&tenant_name, &context.user_id)
                .await
                .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?,
        );
    }
    //联查额外信息
    let mut teacher_id_set = HashSet::<Uuid>::new();
    let mut grade_level_code_set = HashSet::<String>::new();
    let mut class_id_set = HashSet::<Uuid>::new();
    class_list.iter().for_each(|c: &AdministrativeClasses| {
        if c.teacher_id.is_some() {
            teacher_id_set.insert(c.teacher_id.clone().unwrap());
        }
        if c.grade_level_code.is_some() {
            grade_level_code_set.insert(c.grade_level_code.clone().unwrap());
        }
        class_id_set.insert(c.id.clone());
    });
    //联查教师信息
    let teacher_list = state
        .teacher_service
        .find_all_by_id_in(&tenant_name, teacher_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    let mut teacher_id_to_name_map = HashMap::<Uuid, String>::new();
    teacher_list.iter().for_each(|teacher| {
        teacher_id_to_name_map.insert(teacher.id, teacher.teacher_name.clone());
    });
    //联查年级信息
    let grade_level_all_list = state
        .grade_service
        .get_all_grades()
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    let mut grade_level_code_to_name_map = HashMap::<String, String>::new();
    grade_level_all_list.iter().for_each(|item| {
        grade_level_code_to_name_map.insert(item.code.clone(), item.name.clone());
    });
    //联查班级人数统计信息
    let class_id_to_student_count_map = state
        .student_service
        .batch_count_by_class(&tenant_name, &class_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    Ok(responses::paginated_success(
        class_list.clone()
            .into_iter()
            .map(|classes| {
                let AdministrativeClasses {
                    id,
                    class_name,
                    code,
                    academic_year,
                    grade_level_code,
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                } = classes;
                AdministrativeClassesDetail {
                    id,
                    class_name,
                    code,
                    academic_year,
                    grade_level_code: grade_level_code.clone(),
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                    teacher_name: teacher_id.map_or(None, |teacher_id| {
                        teacher_id_to_name_map
                            .get(&teacher_id)
                            .map_or(None, |v| Some(v.clone()))
                    }),
                    grade_level_name: grade_level_code.map_or(None, |item| {
                        grade_level_code_to_name_map
                            .get(&item)
                            .map_or(None, |v| Some(v.clone()))
                    }),
                    total_student: class_id_to_student_count_map.get(&id).unwrap_or(&0).clone(),
                }
            })
            .collect(),
            params.page_params.get_page(),
            params.page_params.get_page_size(),
            total_count,
        None,
    ))
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClassStudentHomeworkParams {
    pub class_id: Uuid,
    pub subject_id: Option<Uuid>,
    pub time_range: Option<TimeRange>,
}
#[derive(Serialize)]
pub struct StudentHomeworkAnalyse{
    pub student_base_info: StudentBaseInfo,
    pub total_homework: i32,
    pub absent: i32,
    pub homework_summary: Vec<StudentHomeworkAnalysis>
}
#[derive(Debug, Serialize, Clone)]
pub struct StudentHomeworkAnalysis{
    pub homework_id: Uuid,
    pub status: HomeworkStudentStatus,
    pub score: f64,
    pub rank: i32,
    pub teaching_class_id: Uuid,
}
#[derive(Debug, Serialize)]
pub struct StudentBaseInfo{
    pub student_id: Uuid,
    pub student_number: String,
    pub student_name: String,
    pub status: String,
}
/**
 * 作者：朱若彪
 * 说明：获取班级内学生的作业完成情况
 */
pub async fn get_class_student_homework_stats(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<ClassStudentHomeworkParams>,
) -> Result<ApiResponse<Vec<StudentHomeworkAnalyse>>, ApiResponse<()>> {
    let ClassStudentHomeworkParams{class_id,subject_id,time_range}=params;
    //1.查询班级内的所有学生
    let (students,_)=state
        .administrative_classes_service
        .page_student_in_class(&context, &tenant_name, &PageStudentInClassParams{
            class_id: class_id,
            page_params: PageParams {
                page: None,
                page_size: None,
            },
            name_like: None,
            status: None,
            student_number: None,
        })
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    let student_id_list: Vec<Uuid> = students.iter().map(|student| student.id).collect();
    //2.查出班级内所有相关的作业
    let homework_students=state.homework_students_service.fetch_homework_students(&tenant_name, &student_id_list,subject_id,time_range)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    //用于处理返回数据
    let mut result: Vec<StudentHomeworkAnalyse> = Vec::new();
    //2.1没有相关作业，直接返回
    if homework_students.len()==0{
        for student in students.iter(){
            let base_info=StudentBaseInfo{
                student_id: student.id,
                student_number: student.student_number.clone(),
                student_name: student.student_name.clone(),
                status: student.status.clone(),
            };
            result.push(StudentHomeworkAnalyse{
                student_base_info: base_info,
                total_homework: 0,
                absent: 0,
                homework_summary: vec![],
            });
        }
        return Ok( responses::success(result, None));
    }
    //3.统计每一个学生的总作业数、缺考次数
    let student_homework_summary=state
        .homework_students_service
        .calculate_student_homework_summary(&homework_students)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    //4.对每一个作业获取评分标准
    let homework_paper_service = HomeworkPapersService::new(state.db.clone(), tenant_name.clone());
    let mut homework_criteria_map = HashMap::new();
    for homework in homework_students.iter() {
        let criteria_list = homework_paper_service.get_homework_criteria_list(homework.homework_id)
            .await
            .map_err(|e| ApiResponse::error(format!("读取试卷评分标准失败: {}", e), None))?;
        let criteria_ids: Vec<Uuid> = criteria_list.iter().map(|c| c.id).collect();
        homework_criteria_map.insert(homework.homework_id, criteria_ids);
    }
    //5.获取所有学生在每个作业下的分数、计算每个学生在每个作业的总分
    let homework_analysis_service=HomeworkAnalysis::new(state.db, tenant_name,state.storage_service);
    let student_homework_analysis=homework_analysis_service.get_student_homework_detail(&homework_criteria_map,&student_id_list,&homework_students).await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    //6.组织返回结果
    for student in students.iter(){
        let base_info=StudentBaseInfo{
            student_id: student.id,
            student_number: student.student_number.clone(),
            student_name: student.student_name.clone(),
            status: student.status.clone(),
        };
        let summary= student_homework_summary.iter().find(|s| s.student_id==student.id);
        let total_homework = summary.map_or(0, |s| s.total_homework as i32);
        let absent = summary.map_or(0, |s| s.unsubmitted_count as i32);
        let homework_summary = student_homework_analysis.get(&student.id).cloned().unwrap_or_default();
        result.push(StudentHomeworkAnalyse{
            student_base_info: base_info,
            total_homework,
            absent,
            homework_summary,
        });
    }
    Ok( responses::success(result, None))
}