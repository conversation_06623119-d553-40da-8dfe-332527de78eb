use crate::model::question::question_type::{CheckCodeRequest, ComposeBindRequest, ComposeCodeRequest, ComposeQuestionTypeQueryParams, ComposeSubjectGradeRequest, CreateQuestionTypeRequest, QuestionTypeQueryParams, QuestionTypeSummaryQuery, UpdateQuestionTypeRequest};
use crate::service::question::question_type_service::QuestionTypeService;
use crate::utils::api_response::responses;
use axum::extract::{Query, State};
use axum::response::IntoResponse;
use axum::routing::{get, post, put};
use axum::{<PERSON><PERSON>, Router};
use std::sync::Arc;

pub fn create_router() -> Router<Arc<QuestionTypeService>> {
    Router::new()
        .route("/", get(get_question_types))
        .route("/create", post(create_question_type))
        .route("/update", put(update_question_type))
        .route("/check-code", get(check_code_availability))
        .route("/summaries", get(get_question_type_summaries))

        .route("/compose", get(get_all_compose))
        .route("/compose/subject_grade", get(list_by_subject_grade))
        .route("/compose/type", get(list_by_type_code))
        .route("/compose/bind", post(bind_compose))
        .route("/compose/unbind", post(unbind_compose))
}

pub async fn get_question_types(
    State(service): State<Arc<QuestionTypeService>>,
    Query(params): Query<QuestionTypeQueryParams>,
) -> impl IntoResponse {
    service
        .get_question_types(params)
        .await
        .map(|page_result| {
            responses::paginated_success(
                page_result.data,
                page_result.page as i32,
                page_result.page_size as i32,
                page_result.total,
                Some("获取题型成功"),
            )
        })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                format!("获取题型信息失败：{}", e).as_str(),
                Some("QUERY_QUESTION_TYPE_FAILED"),
            )
        })
}

pub async fn get_question_type_summaries(
    State(service): State<Arc<QuestionTypeService>>,
    Query(params): Query<QuestionTypeSummaryQuery>,
) -> impl IntoResponse {
    service
        .get_question_type_summaries(params.is_active)
        .await
        .map(|result| responses::success(result, Some("获取题型成功")))
        .unwrap_or_else(|e| {
            responses::error(
                format!("获取题型信息失败：{}", e).as_str(),
                Some("QUERY_QUESTION_TYPE_FAILED"),
            )
        })
}

pub async fn create_question_type(
    State(service): State<Arc<QuestionTypeService>>,
    Json(payload): Json<CreateQuestionTypeRequest>,
) -> impl IntoResponse {
    match service.create_question_type(payload).await {
        Ok(result) => responses::success(result, Some("创建成功")),
        Err(e) => responses::error(
            format!("获取题型信息失败：{}", e).as_str(),
            Some("CREATE_QUESTION_TYPE_FAILED"),
        ),
    }
}

pub async fn update_question_type(
    State(service): State<Arc<QuestionTypeService>>,
    Json(payload): Json<UpdateQuestionTypeRequest>,
) -> impl IntoResponse {
    match service.update_question_type(payload).await {
        Ok(_) => responses::success_no_data(Some("更新成功")),
        Err(e) => responses::error(
            format!("更新题型信息失败：{}", e).as_str(),
            Some("UPDATE_QUESTION_TYPE_FAILED"),
        ),
    }
}

#[axum::debug_handler]
async fn check_code_availability(
    State(service): State<Arc<QuestionTypeService>>,
    Query(params): Query<CheckCodeRequest>,
) -> impl IntoResponse {
    if params.code.trim().is_empty() {
        return responses::error("学科代码不能为空", Some("CHECK_SUBJECT_FAILED"));
    }

    match service
        .is_code_available(&params.code)
        .await
    {
        Ok(bool) => responses::success(bool, Some("查询成功")),
        Err(e) => responses::error(
            format!("查询失败：{}", e).as_str(),
            Some("CHECK_SUBJECT_FAILED"),
        ),
    }
}

pub async fn get_all_compose(
    State(service): State<Arc<QuestionTypeService>>,
    Query(params): Query<ComposeQuestionTypeQueryParams>,
) -> impl IntoResponse {
    service
        .get_all_compose(params)
        .await
        .map(|page_result| {
            responses::paginated_success(
                page_result.data,
                page_result.page as i32,
                page_result.page_size as i32,
                page_result.total,
                Some("获取题型成功"),
            )
        })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                format!("获取题型失败：{}", e).as_str(),
                Some("QUERY_QUESTION_TYPE_FAILED"),
            )
        })
}

pub async fn list_by_subject_grade(
    State(service): State<Arc<QuestionTypeService>>,
    Query(params): Query<ComposeSubjectGradeRequest>,
) -> impl IntoResponse {
    service
        .list_by_subject_grade(params)
        .await
        .map(|result| responses::success(result, Some("获取题型成功")))
        .unwrap_or_else(|e| {
            responses::error(
                format!("获取题型失败：{}", e).as_str(),
                Some("QUERY_QUESTION_TYPE_FAILED"),
            )
        })
}

pub async fn list_by_type_code(
    State(service): State<Arc<QuestionTypeService>>,
    Query(params): Query<ComposeCodeRequest>,
) -> impl IntoResponse {
    service
        .list_by_question_type_code(params)
        .await
        .map(|result| responses::success(result, Some("获取题型成功")))
        .unwrap_or_else(|e| {
            responses::error(
                format!("获取题型信息失败：{}", e).as_str(),
                Some("QUERY_QUESTION_TYPE_FAILED"),
            )
        })
}

pub async fn bind_compose(
    State(service): State<Arc<QuestionTypeService>>,
    Json(payload): Json<ComposeBindRequest>,
) -> impl IntoResponse {
    match service.bind_compose(payload).await {
        Ok(res) => responses::success(res,Some("绑定成功")),
        Err(e) => responses::error(
            format!("绑定题型信息失败：{}", e).as_str(),
            Some("CREATE_COMPOSE_FAILED"),
        ),
    }
}

pub async fn unbind_compose(
    State(service): State<Arc<QuestionTypeService>>,
    Json(payload): Json<ComposeBindRequest>,
) -> impl IntoResponse {
    match service.unbind_compose(payload).await {
        Ok(_) => responses::success_no_data(Some("删除成功")),
        Err(e) => responses::error(
            format!("删除绑定信息失败：{}", e).as_str(),
            Some("REMOVE_COMPOSE_FAILED"),
        ),
    }
}