use axum::{
    extract::{Path, State},
    routing::post,
    Json, Router,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{
    model::homework_papers::homework_papers::{BindPapersToHomeworkParams, HomeworkPapers},
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};
use crate::{model::paper::paper::Paper, service::homework_papers::homework_papers_service::HomeworkPapersService};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/bindPapersToHomework", post(bind_papers_to_homework))
        .route("/bulkBindPapersToHomework", post(bulk_bind_papers_to_homework))
        .route("/findPaperByHomeworkId", post(find_paper_by_homework_id))
}

pub async fn bind_papers_to_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    <PERSON><PERSON>(params): <PERSON>son<BindPapersToHomeworkParams>,
) -> Result<ApiResponse<Vec<HomeworkPapers>>, ApiResponse<()>> {
    let homework_paper_service = HomeworkPapersService::new(state.db, tenant_name);
    homework_paper_service
        .bind_papers_to_homework(&params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

/// 批量绑定试卷到作业
pub async fn bulk_bind_papers_to_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<Vec<BindPapersToHomeworkParams>>,
) -> Result<ApiResponse<usize>, ApiResponse<()>> {
    let homework_paper_service = HomeworkPapersService::new(state.db, tenant_name);
    homework_paper_service
        .bulk_bind_papers_to_homework(&params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn find_paper_by_homework_id(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<FindPaperByHomeworkIdParams>,
) -> Result<ApiResponse<Vec<Paper>>, ApiResponse<()>> {
    let FindPaperByHomeworkIdParams { homework_id } = params;
    HomeworkPapersService::new(state.db, tenant_name)
        .get_homework_papers_by_homework_id(homework_id)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindPaperByHomeworkIdParams {
    homework_id: Uuid,
}
