use std::sync::Arc;
use axum::{
    extract::{J<PERSON>, Path, Query, State},
    http::StatusCode,
    routing::{delete, get, post, put},
    Router,
};
use chrono::NaiveDateTime;
use serde::{Deserialize};
use uuid::Uuid;

use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::PageParams;
use crate::model::tenant::tenant_user::{TenantUserVO};
use crate::service::tenant::TenantUserService;
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
/// 添加用户到租户的请求
#[derive(Debug, Deserialize)]
pub struct AddUserToTenantRequest {
    #[serde(rename = "userId")]
    pub user_id: Uuid,
    #[serde(rename = "accessType")]
    pub access_type: Option<String>, // 默认为 "member"
    #[serde(rename = "expiresAt")]
    pub expires_at: Option<NaiveDateTime>,
}

/// 更新用户权限的请求
#[derive(Debug, Deserialize)]
pub struct UpdateUserPermissionRequest {
    pub access_type: Option<String>,
    pub expires_at: Option<NaiveDateTime>,
}

/// 查询租户用户列表参数
#[derive(Debug, Deserialize)]
pub struct FindAllTenantUsersParams {
    pub user_name_like: Option<String>,
    pub user_email_like: Option<String>,
    pub access_type: Option<String>,
    pub page_params: PageParams,
}

/// 用户搜索参数
#[derive(Debug, Deserialize)]
pub struct SearchUsersQuery {
    pub keyword: Option<String>,
    #[serde(rename = "accessType")]
    pub access_type: Option<String>,
    pub page: Option<i64>,
    pub limit: Option<i64>,
}

/// 分页查询参数
#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<i64>,
    pub limit: Option<i64>,
}

/// 移除用户查询参数
#[derive(Debug, Deserialize)]
pub struct RemoveUserQuery {
    #[serde(rename = "hardDelete")]
    pub hard_delete: Option<bool>,
}
/// 获取租户用户列表
#[axum::debug_handler]
pub async fn get_tenant_users(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
    Query(pagination): Query<PaginationQuery>,
) -> Result<PaginatedApiResponse<TenantUserVO>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.get_tenant_users(tenant_id, pagination.page, pagination.limit).await {
        Ok((users, total)) => Ok(PaginatedApiResponse::success(
            users,
            pagination.page.unwrap_or(1) as i32,
            pagination.limit.unwrap_or(10) as i32,
            total,
            Some("Users retrieved successfully".to_string()),
        )),
        Err(e) => {
            eprintln!("Error retrieving tenant users: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            let status_code = StatusCode::INTERNAL_SERVER_ERROR;
            Err((status_code, Json(error_response)))
        }
    }
}

/// 添加用户到租户
#[axum::debug_handler]
pub async fn add_user_to_tenant(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
    Json(request): Json<AddUserToTenantRequest>,
) -> Result<ApiResponse<TenantUserVO>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.add_user_to_tenant(tenant_id, request).await {
        Ok(user) => Ok(ApiResponse::success(user, Some("User added to tenant successfully".to_string()))),
        Err(e) => {
            eprintln!("Error adding user to tenant: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            let status_code = StatusCode::INTERNAL_SERVER_ERROR;
            Err((status_code, Json(error_response)))
        }
    }
}

/// 更新用户权限
#[axum::debug_handler]
pub async fn update_user_permission(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path((tenant_id, user_id)): Path<(Uuid, Uuid)>,
    Json(request): Json<UpdateUserPermissionRequest>,
) -> Result<ApiResponse<TenantUserVO>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.update_user_permission(tenant_id, user_id, request).await {
        Ok(user) => Ok(ApiResponse::success(user, Some("User permission updated successfully".to_string()))),
        Err(e) => {
            eprintln!("Error updating user permission: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            let status_code = StatusCode::INTERNAL_SERVER_ERROR;
            Err((status_code, Json(error_response)))
        }
    }
}

/// 移除用户
#[axum::debug_handler]
pub async fn remove_user_from_tenant(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path((tenant_id, user_id)): Path<(Uuid, Uuid)>,
    Query(query): Query<RemoveUserQuery>,
) -> Result<ApiResponse<()>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.remove_user_from_tenant(tenant_id, user_id, query.hard_delete).await {
        Ok(_) => Ok(ApiResponse::<()>::success_without_data(Some("User removed from tenant successfully".to_string()))),
        Err(e) => {
            eprintln!("Error removing user from tenant: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            let status_code = StatusCode::INTERNAL_SERVER_ERROR;
            Err((status_code, Json(error_response)))
        }
    }
}

/// 搜索用户
#[axum::debug_handler]
pub async fn search_tenant_users(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
    Query(query): Query<SearchUsersQuery>,
) -> Result<ApiResponse<Vec<TenantUserVO>>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.search_tenant_users(tenant_id, query).await {
        Ok((users,_)) => Ok(ApiResponse::success(users, Some("Users searched successfully".to_string()))),
        Err(e) => {
            eprintln!("Error searching tenant users: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            let status_code = StatusCode::INTERNAL_SERVER_ERROR;
            Err((status_code, Json(error_response)))
        }
    }
}

/// 获取单个租户用户信息
#[axum::debug_handler]
pub async fn get_tenant_user(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path((tenant_id, user_id)): Path<(Uuid, Uuid)>,
) -> Result<ApiResponse<TenantUserVO>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.get_tenant_user(tenant_id, user_id).await {
        Ok(user) => Ok(ApiResponse::success(user, Some("Tenant user retrieved successfully".to_string()))),
        Err(e) => {
            eprintln!("Error retrieving tenant user: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            let status_code = StatusCode::INTERNAL_SERVER_ERROR;
            Err((status_code, Json(error_response)))
        }
    }
}

/// 获取租户用户统计信息
#[axum::debug_handler]
pub async fn get_tenant_user_stats(
    State(service): State<Arc<TenantUserService>>,
    AuthExtractor(_auth_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
) -> Result<ApiResponse<serde_json::Value>, (StatusCode, Json<ApiResponse<()>>)> {
    match service.get_tenant_user_stats(tenant_id).await {
        Ok(stats) => Ok(ApiResponse::success(stats, Some("Tenant user statistics retrieved successfully".to_string()))),
        Err(e) => {
            eprintln!("Error retrieving tenant user stats: {}", e);
            let error_response = ApiResponse::error(e.to_string(), None);
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)))
        }
    }
}


/// 租户用户管理路由
pub fn create_router() -> Router<Arc<TenantUserService>> {
    Router::new()
        // 获取租户用户列表
        .route("/{tenant_id}/users", get(get_tenant_users))
        // 添加用户到租户
        .route("/{tenant_id}/users", post(add_user_to_tenant))
        // 更新用户权限
        .route("/{tenant_id}/users/{user_id}", put(update_user_permission))
        // 移除用户
        .route("/{tenant_id}/users/{user_id}", delete(remove_user_from_tenant))
        // 搜索用户
        .route("/{tenant_id}/users/search", get(search_tenant_users))
        // 获取单个用户信息
        .route("/{tenant_id}/users/{user_id}/info", get(get_tenant_user))
        // 获取用户统计信息
        .route("/{tenant_id}/users/stats", get(get_tenant_user_stats))
}
