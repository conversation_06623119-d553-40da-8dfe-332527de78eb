use std::sync::Arc;
use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::tenant::tenant::{CreateTenantRequest, TenantQueryParams, TenantResponse, UpdateTenantRequest};

use crate::service::tenant::tenant_service::TenantService;
use crate::utils::api_response::{responses};

use axum::{
    extract::{Json, Path, State},
    http::StatusCode,
    response::IntoResponse,
    routing::{delete, get, post, put},
    Router,
};
use axum::extract::Query;
use uuid::Uuid;
use crate::model::question::question_type::QuestionTypeQueryParams;
use crate::utils::api_response::{ErrorResponse, SuccessResponse};

/// 创建租户
#[axum::debug_handler]
pub async fn create_tenant(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(admin_context): AuthExtractor,
    <PERSON><PERSON>(request): <PERSON><PERSON><CreateTenantRequest>,
) -> impl IntoResponse {
    // 使用管理员认证中间件提供的用户ID
    let creator_id = admin_context.user_id;

    match tenant_service.create_tenant(request, creator_id).await {
        Ok(tenant) => {
            responses::success(tenant, Some("Tenant created successfully"))
        }
        Err(e) => {
            eprintln!("Error creating tenant: {}", e);
            responses::error(format!("Failed to create tenant. {}", e).as_str(), Some("TENANT_CREATION_ERROR"))
        }
    }
}

/// 获取租户列表
#[axum::debug_handler]
pub async fn get_tenants(
    State(tenant_service): State<Arc<TenantService>>,
    Query(params): Query<TenantQueryParams>,
) -> impl IntoResponse {
    tenant_service.get_tenants(params).await.map(|page_result| {
        responses::paginated_success(
            page_result.data,
            page_result.page as i32,
            page_result.page_size as i32,
            page_result.total,
            Some("获取租户成功"),
        )
    })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                format!("获取租户信息失败：{}", e).as_str(),
                Some("QUERY_TENANT_FAILED"),
            )
        })
}

pub async fn get_tenants_summaries(
    State(tenant_service): State<Arc<TenantService>>,
) -> impl IntoResponse {
    match tenant_service.get_tenants_summaries().await {
        Ok(res) => {responses::success(res, Some("获取租户信息成功"))}
        Err(err) => {responses::error(format!("获取租户信息失败：{}", err).as_str(), Some("QUERY_TENANT_FAILED"))}
    }
}

pub async fn get_tenants_stats(
    State(tenant_service): State<Arc<TenantService>>,
) -> impl IntoResponse {
    match tenant_service.get_tenants_stats().await {
        Ok(res) => {responses::success(res, Some("获取租户统计信息成功"))}
        Err(err) => {responses::error(format!("获取租户统计信息失败：{}", err).as_str(), Some("TENANT_STATS_ERROR"))}
    }
}


/// 根据 ID 获取租户
#[axum::debug_handler]
pub async fn get_tenant_by_id(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
) -> Result<Json<SuccessResponse<TenantResponse>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.get_tenant_by_id(tenant_id).await {
        Ok(tenant) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(tenant),
            message: "Tenant retrieved successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error retrieving tenant: {}", e);
            let (status_code, _error_msg, user_msg) = if e.to_string().contains("not found") {
                (StatusCode::NOT_FOUND, "Tenant not found", "The requested tenant does not exist")
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error", "Failed to retrieve tenant")
            };

            let error_response = ErrorResponse {
                success: false,
                message: user_msg.to_string(),
                error_code: Some("TENANT_ERROR".to_string()),
            };
            Err((status_code, Json(error_response)))
        }
    }
}

/// 更新租户
#[axum::debug_handler]
pub async fn update_tenant(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
    Json(request): Json<UpdateTenantRequest>,
) -> Result<Json<SuccessResponse<TenantResponse>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.update_tenant(tenant_id, request).await {
        Ok(tenant) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(tenant),
            message: "Tenant updated successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error updating tenant: {}", e);
            let (status_code, _error_msg, user_msg) = if e.to_string().contains("not found") {
                (StatusCode::NOT_FOUND, "Tenant not found", "The requested tenant does not exist")
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error", "Failed to update tenant")
            };

            let error_response = ErrorResponse {
                success: false,
                message: user_msg.to_string(),
                error_code: Some("TENANT_ERROR".to_string()),
            };
            Err((status_code, Json(error_response)))
        }
    }
}

/// 删除租户
#[axum::debug_handler]
pub async fn delete_tenant(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
) -> Result<Json<SuccessResponse<()>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.delete_tenant(tenant_id).await {
        Ok(_) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(()),
            message: "Tenant deleted successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error deleting tenant: {}", e);
            let (status_code, _error_msg, user_msg) = if e.to_string().contains("not found") {
                (StatusCode::NOT_FOUND, "Tenant not found", "The requested tenant does not exist")
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error", "Failed to delete tenant")
            };

            let error_response = ErrorResponse {
                success: false,
                message: user_msg.to_string(),
                error_code: Some("TENANT_ERROR".to_string()),
            };
            Err((status_code, Json(error_response)))
        }
    }
}

/// 租户管理路由 (New service-based routes)
pub fn create_router() -> Router<Arc<TenantService>> {
    Router::new()
        .route("/", post(create_tenant))
        .route("/", get(get_tenants))
        .route("/summary", get(get_tenants_summaries))
        .route("/stats", get(get_tenants_stats))
        .route("/{id}", get(get_tenant_by_id))
        .route("/{id}", put(update_tenant))
        .route("/{id}", delete(delete_tenant))
}