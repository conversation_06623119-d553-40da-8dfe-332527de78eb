use axum::{
    extract::{Path, State},
    routing::{delete, get, post, put},
    J<PERSON>, Router,
};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    model::subject_groups::subject_group_members::{
        BatchAddMembersParams, CreateSubjectGroupMemberParams, SubjectGroupMemberDetail,
        SubjectGroupMemberStats, UpdateSubjectGroupMemberParams,
    },
    service::subject_groups::subject_group_members_service::SubjectGroupMembersService,
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/{subject_group_id}/members", get(get_members))
        .route("/{subject_group_id}/members", post(add_member))
        .route("/{subject_group_id}/members/batch", post(batch_add_members))
        .route("/{subject_group_id}/members/{member_id}", delete(remove_member))
        .route("/{subject_group_id}/members/{member_id}", put(update_member_role))
        .route("/{subject_group_id}/members/stats", get(get_member_stats))
}

/// 获取学科组成员列表
pub async fn get_members(
    State(state): State<AppState>,
    Path((tenant_name, subject_group_id)): Path<(String, Uuid)>,
    AuthExtractor(_auth_context): AuthExtractor,
) -> Result<ApiResponse<Vec<SubjectGroupMemberDetail>>, ApiResponse<()>> {
    match SubjectGroupMembersService::get_members(&state.db, &tenant_name, subject_group_id)
        .await
    {
        Ok(members) => Ok(responses::success(members, None)),
        Err(err) => Err(responses::error(&err.to_string(), None)),
    }
}

/// 添加单个成员
pub async fn add_member(
    State(state): State<AppState>,
    Path((tenant_name, subject_group_id)): Path<(String, Uuid)>,
    AuthExtractor(_auth_context): AuthExtractor,
    Json(mut params): Json<CreateSubjectGroupMemberParams>,
) -> Result<ApiResponse<String>, ApiResponse<()>> {
    // 确保subject_group_id一致
    params.subject_group_id = subject_group_id;

    match SubjectGroupMembersService::add_member(&state.db, &tenant_name, params).await {
        Ok(_member) => Ok(responses::success("成员添加成功".to_string(), None)),
        Err(err) => Err(responses::error(&err.to_string(), None)),
    }
}

/// 批量添加成员
pub async fn batch_add_members(
    State(state): State<AppState>,
    Path((tenant_name, subject_group_id)): Path<(String, Uuid)>,
    AuthExtractor(_auth_context): AuthExtractor,
    Json(params): Json<BatchAddMembersParams>,
) -> Result<ApiResponse<String>, ApiResponse<()>> {
    match SubjectGroupMembersService::batch_add_members(
        &state.db,
        &tenant_name,
        subject_group_id,
        params,
    )
    .await
    {
        Ok(members) => Ok(responses::success(
            format!("成功添加 {} 名成员", members.len()),
            None,
        )),
        Err(err) => Err(responses::error(&err.to_string(), None)),
    }
}

/// 移除成员
pub async fn remove_member(
    State(state): State<AppState>,
    Path((tenant_name, subject_group_id, member_id)): Path<(String, Uuid, Uuid)>,
    AuthExtractor(_auth_context): AuthExtractor,
) -> Result<ApiResponse<String>, ApiResponse<()>> {
    match SubjectGroupMembersService::remove_member(
        &state.db,
        &tenant_name,
        subject_group_id,
        member_id,
    )
    .await
    {
        Ok(_) => Ok(responses::success("成员移除成功".to_string(), None)),
        Err(err) => Err(responses::error(&err.to_string(), None)),
    }
}

/// 更新成员角色
pub async fn update_member_role(
    State(state): State<AppState>,
    Path((tenant_name, subject_group_id, member_id)): Path<(String, Uuid, Uuid)>,
    AuthExtractor(_auth_context): AuthExtractor,
    Json(params): Json<UpdateSubjectGroupMemberParams>,
) -> Result<ApiResponse<String>, ApiResponse<()>> {
    match SubjectGroupMembersService::update_member_role(
        &state.db,
        &tenant_name,
        subject_group_id,
        member_id,
        params,
    )
    .await
    {
        Ok(_member) => Ok(responses::success("成员角色更新成功".to_string(), None)),
        Err(err) => Err(responses::error(&err.to_string(), None)),
    }
}

/// 获取成员统计信息
pub async fn get_member_stats(
    State(state): State<AppState>,
    Path((tenant_name, subject_group_id)): Path<(String, Uuid)>,
    AuthExtractor(_auth_context): AuthExtractor,
) -> Result<ApiResponse<SubjectGroupMemberStats>, ApiResponse<()>> {
    match SubjectGroupMembersService::get_member_stats(
        &state.db,
        &tenant_name,
        subject_group_id,
    )
    .await
    {
        Ok(stats) => Ok(responses::success(stats, None)),
        Err(err) => Err(responses::error(&err.to_string(), None)),
    }
}
