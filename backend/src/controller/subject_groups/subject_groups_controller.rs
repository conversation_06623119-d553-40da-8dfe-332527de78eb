use std::collections::HashMap;

use axum::{
    extract::{Path, State},
    routing::post,
    Json, Router,
};
use uuid::Uuid;

use crate::{
    controller::subject_groups::subject_group_members_controller,
    middleware::auth_middleware::AuthExtractor,
    model::subject_groups::subject_groups::{
        CreateSubjectGroupsParams, SubjectGroups, SubjectGroupsDetail, UpdateSubjectGroupsParams,
    },
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/createSubjectGroups", post(create_subject_groups))
        .route("/updateSubjectGroups", post(update_subject_groups))
        .route("/findAll", post(find_all))
        // 新增路由
        .route("/deleteSubjectGroup", post(delete_subject_group))
        // 合并成员管理路由
        .merge(subject_group_members_controller::create_router())
}

pub async fn create_subject_groups(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
    Json(params): Json<CreateSubjectGroupsParams>,
) -> Result<ApiResponse<SubjectGroups>, ApiResponse<()>> {
    match state
        .subject_groups_service
        .create_subject_groups(&tenant_name, &params)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}

pub async fn update_subject_groups(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
    Json(params): Json<UpdateSubjectGroupsParams>,
) -> Result<ApiResponse<SubjectGroups>, ApiResponse<()>> {
    match state
        .subject_groups_service
        .update_subject_groups(&tenant_name, &params)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}

pub async fn find_all(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
) -> Result<ApiResponse<Vec<SubjectGroupsDetail>>, ApiResponse<()>> {
    match state.subject_groups_service.find_all(&tenant_name).await {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}

pub async fn delete_subject_group(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
    Json(params): Json<HashMap<String, Uuid>>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    let id = params.get("id").ok_or_else(|| responses::error("Missing 'id' in request body", None))?;
    match state.subject_groups_service.delete_subject_group(&tenant_name, *id).await {
        Ok(_) => Ok(responses::success((), None)),
        Err(e) => Err(responses::error(&e.to_string(), None)),
    }
}
