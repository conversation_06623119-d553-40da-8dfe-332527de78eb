use crate::service::homework::homework_analysis_service::{ClassDetailResponse, ClassSummaryResponse, HomeworkAnalysis};
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::extract::{Path, State};
use axum::routing::post;
use axum::{Json, Router};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/classes/{homework_id}", post(get_analysis_with_classes))
        .route("/class/detail", post(get_analysis_class_detail))
}
#[derive(Debug, Serialize, Deserialize, Clone)]
struct GetQuestionsRequest {
    pub tenant_name:String,
    pub homework_id: Uuid
}

async fn get_analysis_with_classes(State(state): State<AppState>, Path(params): Path<GetQuestionsRequest>) -> Result<ApiResponse<ClassSummaryResponse>, ApiResponse<String>> {
    let homework_paper_service = HomeworkPapersService::new(state.db.clone(), params.tenant_name.clone());
    let service = HomeworkAnalysis::new(state.db, params.tenant_name);
    let criteria_list = homework_paper_service.get_homework_criteria_list(params.homework_id).await.map_err(|e| ApiResponse::error(format!("读取试卷评分标准失败: {}", e), None))?;
    let vos = service.classes_summary(params.homework_id, criteria_list).await.map_err(|e| ApiResponse::error(format!("读取班级分数信息失败: {}", e), None))?;
    Ok(ApiResponse::success(vos, None))
}
#[derive(Deserialize)]
struct ClassDetailRequest {
    homework_id: Uuid,
    class_id: Uuid,
}
async fn get_analysis_class_detail(State(state): State<AppState>, Path(tenant_name): Path<String>, Json(params): Json<ClassDetailRequest>) -> Result<ApiResponse<ClassDetailResponse>, ApiResponse<String>> {
    let homework_paper_service = HomeworkPapersService::new(state.db.clone(), tenant_name.clone());
    let service = HomeworkAnalysis::new(state.db, tenant_name);
    let criteria_list = homework_paper_service.get_homework_criteria_list(params.homework_id).await.map_err(|e| ApiResponse::error(format!("读取试卷评分标准失败: {}", e), None))?;
    let details = service.classes_detail(params.homework_id, params.class_id, criteria_list).await.map_err(|e| ApiResponse::error(format!("获取班级详情失败: {}", e), None))?;
    Ok(ApiResponse::success(details, None))
}