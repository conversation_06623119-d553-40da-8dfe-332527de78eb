use std::collections::HashMap;

use axum::{
    extract::{Path, State},
    routing::post,
    Json, Router,
};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

use crate::{
    model::{
        homework_students::homework_students::{
            HomeworkStudents, PageStudentsByHomeworkIdParams,
        },
        StudentBaseInfo,
    },
    utils::api_response::{responses, ApiResponse, PaginatedApiResponse},
    web_server::AppState,
};
use crate::repository::homework_students::homework_students_repository::HomeworkStudentsRepository;
use crate::repository::students::class_repository::fetch_teaching_class_with_name_list;
use crate::service::homework::vo::HomeworkStudentsWithStudentBaseInfo;

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route(
            "/batchBindStudentsToHomework",
            post(batch_bind_students_to_homework),
        )
        .route("/findAllByHomeworkId", post(find_all_by_homework_id))
        .route(
            "/batchUnbindStudentsFromHomework",
            post(batch_unbind_students_from_homework),
        )
        .route("/pageStudentsByHomeworkId", post(page_students_by_homework_id))
        .route("/{homework_id}/homework_classes", post(homework_classes))
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct BatchBindStudentsToHomeworkParams {
    pub homework_id: Uuid,
    pub student_id_list: Vec<Uuid>,
    pub class_id: Uuid,
}
pub async fn batch_bind_students_to_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<BatchBindStudentsToHomeworkParams>,
) -> Result<ApiResponse<Vec<HomeworkStudents>>, ApiResponse<()>> {
    state
        .homework_students_service
        .batch_bind_students_to_homework(&tenant_name, params.homework_id, params.student_id_list,params.class_id)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct FindAllByHomeworkIdParams {
    pub homework_id: Uuid,
}
pub async fn find_all_by_homework_id(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<FindAllByHomeworkIdParams>,
) -> Result<ApiResponse<Vec<HomeworkStudentsWithStudentBaseInfo>>, ApiResponse<()>> {
    let list = HomeworkStudentsRepository::fetch_homework_students_by_homework_id(&state.db, tenant_name.as_str(), &params.homework_id)
        .await.map_err(|e| responses::error(&e, None))?;
    //联查学生信息
    let student_id_list = list
        .iter()
        .map(|item| item.student_id)
        .collect::<Vec<Uuid>>();
    let student_base_info_list = state
        .student_service
        .batch_get_student_base_info_by_id_list(&tenant_name, student_id_list)
        .await
        .map_err(|e| responses::error(&e, None))?;
    let mut student_id_to_info_map = HashMap::<Uuid, StudentBaseInfo>::new();
    for ele in student_base_info_list {
        student_id_to_info_map.insert(ele.id, ele);
    }
    Ok(responses::success(
        list.into_iter()
            .map(|item|
                HomeworkStudentsWithStudentBaseInfo::from(student_id_to_info_map.get(&item.student_id).cloned(), item)
            ).collect(),
        None,
    ))
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct BatchUnbindStudentsFromHomeworkParams {
    pub homework_id: Uuid,
    pub student_id_list: Vec<Uuid>,
}

pub async fn batch_unbind_students_from_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<BatchUnbindStudentsFromHomeworkParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .homework_students_service
        .batch_unbind_students_from_homework(&tenant_name, params.homework_id, params.student_id_list)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|()| responses::success_no_data(None))
}
#[derive(Serialize)]
struct HomeworkClassSummary {
    class_id: Uuid,
    class_name: String,
    student_count: i64,
}
pub async fn homework_classes(State(state): State<AppState>, Path((tenant_name, homework_id)): Path<(String, Uuid)>) -> Result<ApiResponse<Vec<HomeworkClassSummary>>, ApiResponse<()>> {
    let class_with_count_list = HomeworkStudentsRepository::fetch_class_ids_by_homework_id(&state.db, tenant_name.as_str(), &homework_id)
        .await.map_err(|e| ApiResponse::error(e.to_string(), None))?;
    let class_ids = class_with_count_list.iter().map(|c|c.0).collect::<Vec<Uuid>>();
    let class_count_map = class_with_count_list.into_iter().collect::<HashMap<Uuid,i64>>();
    let class_name_list = fetch_teaching_class_with_name_list(&state.db, tenant_name.as_str(), class_ids).await.map_err(|e| ApiResponse::error(e.to_string(), None))?;
    let ret: Vec<HomeworkClassSummary> = class_name_list.into_iter().map(|(class_id, class_name)| {
        let student_count = class_count_map.get(&class_id).cloned().unwrap_or(0);
        HomeworkClassSummary {
            class_id,
            class_name,
            student_count,
        }
    }).collect::<Vec<_>>();
    Ok(ApiResponse::success(ret, None))
}

/**
 * 作者：朱若彪
 * 说明：分页查询作业绑定的学生
 */
pub async fn page_students_by_homework_id(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageStudentsByHomeworkIdParams>,
) -> Result<PaginatedApiResponse<HomeworkStudentsWithStudentBaseInfo>, PaginatedApiResponse<()>> {
    let list = HomeworkStudentsRepository::fetch_homework_students_by_homework_id(&state.db, tenant_name.as_str(), &params.homework_id)
        .await.map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    // filter 不在班级的学生（临时做法，后续sql过滤）
    let list = if let Some(class_id) = params.administrative_class_id {
        list.into_iter().filter(|v| v.class_id == class_id).collect()
    } else {list};
    //联查学生信息
    let student_id_list = list.iter().map(|item| {
        item.student_id
    }).collect::<Vec<Uuid>>();
    let (student_base_info_list,count) = state
        .student_service
        .page_students_by_id_list(&tenant_name, student_id_list, &params)
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))?;
    let mut student_id_to_info_map = HashMap::<Uuid, StudentBaseInfo>::new();
    for ele in student_base_info_list {
        student_id_to_info_map.insert(ele.id, ele);
    }
    Ok(responses::paginated_success(
        list.into_iter()
            .map(|item| {
                HomeworkStudentsWithStudentBaseInfo::from(student_id_to_info_map.get(&item.student_id).cloned(), item)
            })
            //过滤掉学生信息为空的元素
            .filter(|item| item.student_base_info.is_some())
            .collect(),
        params.page_params.get_page(),
        params.page_params.get_page_size(),
        count,
        None,
    ))
}