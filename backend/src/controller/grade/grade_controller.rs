use crate::model::grade::grade::{
    CreateGradeLevel, GradeLevelQueryParams,
    UpdateGradeLevel
};
use crate::service::grade::grade_service::GradeService;
use crate::utils::api_response::{responses, ApiResponse};
use crate::utils::error::AppError;
use axum::response::IntoResponse;
use axum::{
    extract::{Path, Query, State},
    routing::{get, patch, post},
    Json, Router,
};
use serde::Deserialize;
use uuid::Uuid;

pub fn create_router() -> Router<GradeService> {
    Router::new()
        .route("/", get(get_grades))
        .route("/create", post(create_grade))
        .route("/summaries", get(get_grade_summaries))
        .route("/statistics", get(get_grade_statistics))
        .route("/orders", patch(update_grade_orders))
        .route("/check-code", get(check_code_availability))
        .route(
            "/{id}",
            get(get_grade_by_id).put(update_grade).delete(delete_grade),
        )
}

/// 获取年级列表（支持分页和查询）
#[axum::debug_handler]
async fn get_grades(
    State(grade_service): State<GradeService>,
    Query(params): Query<GradeLevelQueryParams>,
) -> impl IntoResponse {
    grade_service.get_grades(params).await.map(|page_result| {
        responses::paginated_success(
            page_result.data,
            page_result.page as i32,      // 转换类型
            page_result.page_size as i32, // 转换类型
            page_result.total,
            Some("获取年级信息成功"),
        )
    })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                &format!("获取年级信息失败：{}", e),
                Some("QUERY_GRADE_FAILED"),
            )
        })
}

/// 获取年级简要信息列表（用于下拉选择）
#[axum::debug_handler]
async fn get_grade_summaries(
    State(grade_service): State<GradeService>,
    Query(params): Query<GradeSummaryQuery>,
) -> impl IntoResponse {
    match grade_service.get_grade_summaries(params.is_active)
        .await
    {
        Ok(subject_summaries) => responses::success(subject_summaries, Some("获取年级成功")),
        Err(e) => responses::error(
        format!("获取年级信息失败：{}", e).as_str(),
        Some("QUERY_GRADE_FAILED"),
    ),
    }}

/// 获取单个年级详情
#[axum::debug_handler]
async fn get_grade_by_id(
    State(grade_service): State<GradeService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match grade_service.get_grade_by_id(id).await? {
        Some(grade) => Ok(Json(ApiResponse::success(grade, None))),
        None => Err(AppError::NotFound("Grade level not found".to_string())),
    }
}

/// 创建年级
#[axum::debug_handler]
async fn create_grade(
    State(grade_service): State<GradeService>,
    Json(payload): Json<CreateGradeLevel>,
) -> impl IntoResponse {
    // 验证输入
    if payload.code.trim().is_empty() {
        return responses::error("年级编码不能为空", Some("CREATE_GRADE_FAILED"));
    }
    if payload.name.trim().is_empty() {
        return responses::error("年级名称不能为空", Some("CREATE_GRADE_FAILED"));
    }
    if payload.order_level < 0 {
        return responses::error("排序值必须为非负数", Some("CREATE_GRADE_FAILED"));
    }
    match grade_service.create_grade(payload).await {
        Ok(subject) => responses::success(subject, Some("年级创建成功")),
        Err(e) => responses::error(
            format!("年级创建失败：{}", e).as_str(),
            Some("CREATE_GRADE_FAILED"),
        ),
    }
}

/// 更新年级
#[axum::debug_handler]
async fn update_grade(
    State(grade_service): State<GradeService>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateGradeLevel>,
) -> impl IntoResponse {
    // 验证输入
    if let Some(ref name) = payload.name {
        if name.trim().is_empty() {
            return responses::error("年级名称不能为空", Some("UPDATE_GRADE_FAILED"));
        }
    }
    if let Some(order_level) = payload.order_level {
        if order_level < 0 {
            return responses::error("排序值必须为非负数", Some("UPDATE_GRADE_FAILED"));
        }
    }
    match grade_service.update_grade(id, payload).await {
        Ok(grade) => {responses::success(grade, Some("年级更新成功"))}
        Err(e) => {responses::error(format!("年级更新失败：{}", e).as_str(),Some("UPDATE_GRADE_FAILED"))}
    }
}

/// 删除年级（软删除）
#[axum::debug_handler]
async fn delete_grade(
    State(grade_service): State<GradeService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match grade_service.delete_grade(id).await {
        Ok(_) => {responses::success_no_data(Some("删除年级成功"))}
        Err(e) => {responses::error(format!("删除年级失败：{}", e).as_str(),Some("DELETE_GRADE_FAILED"))}
    }
}

/// 获取年级统计信息
#[axum::debug_handler]
async fn get_grade_statistics(
    State(grade_service): State<GradeService>,
) -> impl IntoResponse {
    match grade_service.get_grade_statistics().await {
        Ok(grade) => {responses::success(grade, Some("获取年级统计信息成功"))}
        Err(e) => {responses::error(format!("获取年级统计信息失败：{}", e).as_str(),Some("QUERY_GRADE_FAILED"))}
    }
}

/// 批量更新年级排序
#[axum::debug_handler]
async fn update_grade_orders(
    State(grade_service): State<GradeService>,
    Json(payload): Json<UpdateGradeOrdersRequest>,
) -> impl IntoResponse {
    if payload.orders.is_empty() {
        return responses::error("排序列表不能为空", Some("UPDATE_GRADE_FAILED"));
    }

    match grade_service.update_grade_orders(payload.orders).await {
        Ok(grand) => {responses::success(grand, Some("更新年级排序成功"))},
        Err(e) => {responses::error(format!("更新年级排序失败：{}", e).as_str(),Some("UPDATE_GRADE_FAILED"))}
    }    
}

/// 检查年级代码是否可用
#[axum::debug_handler]
async fn check_code_availability(
    State(grade_service): State<GradeService>,
    Query(params): Query<CheckCodeQuery>,
) -> impl IntoResponse {
    if params.code.trim().is_empty() {
        return responses::error("年级代码不能为空", Some("CHECK_GRADE_FAILED"));
    }

    match grade_service.is_code_available(&params.code, params.exclude_id).await {
        Ok(bool) => {responses::success(bool, Some("查询成功"))}
        Err(e) => {responses::error(format!("查询失败：{}", e).as_str(),Some("CHECK_GRADE_FAILED"))}
    }
}

/// 年级简要信息查询参数
#[derive(Debug, Deserialize)]
struct GradeSummaryQuery {
    pub is_active: Option<bool>,
}

/// 批量更新年级排序请求
#[derive(Debug, Deserialize)]
struct UpdateGradeOrdersRequest {
    pub orders: Vec<(Uuid, i32)>,
}

/// 检查代码可用性查询参数
#[derive(Debug, Deserialize)]
struct CheckCodeQuery {
    pub code: String,
    pub exclude_id: Option<Uuid>,
}