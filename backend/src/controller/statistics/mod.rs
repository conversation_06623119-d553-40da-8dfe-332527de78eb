use crate::service::statistics::StatisticsService;
use crate::utils::api_response::ApiResponse;
use axum::extract::{Path, State};
use axum::routing::post;
use axum::Router;
use sqlx::PgPool;

pub fn create_router() -> Router<PgPool> {
    Router::new()
        .route("/subject_groups_statistics", post(subject_groups_statistics))
}

async fn subject_groups_statistics(State(state): State<PgPool>, Path(tenant_name): Path<String>) -> Result<ApiResponse<()>, ApiResponse<()>> {
    let service = StatisticsService { pool: state, schema_name: tenant_name };
    let _ret = service.subject_groups_statistics().await.map_err(|e| ApiResponse::error(e.to_string(), None))?;
    Ok(ApiResponse::error("None".to_string(), None))
}