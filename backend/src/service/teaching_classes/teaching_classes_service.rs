use std::collections::{HashMap, HashSet};

use sqlx::{postgres::PgRow, PgPool, Row};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthContext, model::{
        administrative_classes::administrative_classes::PageStudentInClassParams, teaching_classes::teaching_classes::{
            CreateTeachingClassesParams, DeleteTeachingClassesParams, FindAllStudentInClassParams, MoveStudentToTeachingClassesParams, PageUserClassListParams, RemoveStudentFromTeachingClassesParams, TeachingClasses, TeachingClassesStatistics, UpdateTeachingClassesParams
        }, Student
    }, repository::students::student_repository::StudentsRepository, utils::schema::connect_with_schema
};
use anyhow::{bail, Result};
use crate::model::administrative_classes::administrative_classes::AdministrativeClasses;
use crate::model::auth::UserIdentity;

#[derive(Clone)]
pub struct TeachingClassesService {
    db_pool: PgPool,
}

//教学班级管理服务
impl TeachingClassesService {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /**
     * 作者：张瀚
     * 说明：获取用户能查看的教学班列表
     */
    pub async fn get_user_class_list(
        &self,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<Vec<TeachingClasses>, String> {
        let mut result: Vec<TeachingClasses> = vec![];
        //自己担任认任课老师的班级列表
        let mut teacher_class_list = self.find_teacher_class_list(schema_name, user_id).await?;
        result.append(&mut teacher_class_list);
        //自己担任临时班主任的班级列表 TODO
        Ok(result)
    }

    /**
     * 作者：张瀚
     * 说明：获取所有的教学班（管理员功能）
     */
    pub async fn get_all_class_list(
        &self,
        schema_name: &String,
    ) -> Result<Vec<TeachingClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc order by tc.is_active desc , tc.created_at desc",
            schema_name
        ));
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }
    /**
     * 作者：朱若彪
     * 说明：分页查询教学班（管理员功能）
     */
    pub async fn page_all_class_list(
        &self,
        schema_name: &String,
        params: &PageUserClassListParams,   
    )->Result<(Vec<TeachingClasses>,i64), String>{
        let PageUserClassListParams { page_params, name_like, class_code, is_active, subject_group_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc where 1=1 ",
            schema_name
        ));
        //计算总班级数
        let mut count_builder = sqlx::QueryBuilder::new(format!(
            "SELECT count(*) from {}.teaching_classes tc where 1=1 ",
            schema_name
        ));
        if let Some(name_like) = name_like {
            builder.push(" and tc.class_name like ");
            builder.push_bind(format!("%{}%", name_like));

            count_builder.push(" and tc.class_name like ");
            count_builder.push_bind(format!("%{}%", name_like));
        }
        if let Some(class_code) = class_code {
            builder.push(" and tc.code = ");
            builder.push_bind(class_code);

            count_builder.push(" and tc.code = ");
            count_builder.push_bind(class_code);
        }
        if let Some(is_active) = is_active {
            builder.push(" and tc.is_active = ");
            builder.push_bind(is_active);

            count_builder.push(" and tc.is_active = ");
            count_builder.push_bind(is_active);
        }
        if let Some(subject_group_id) = subject_group_id {
            builder.push(" and tc.subject_group_id = ");
            builder.push_bind(subject_group_id);

            count_builder.push(" and tc.subject_group_id = ");
            count_builder.push_bind(subject_group_id);
        }
        builder.push(" order by tc.is_active desc , tc.created_at desc ");
        builder.push(" limit ");
        builder.push_bind(page_params.get_limit());
        builder.push(" offset ");
        builder.push_bind(page_params.get_offset());

        let classes = builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
    
        let count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        Ok(( classes, count))
    }

    /**
     * 作者：张瀚
     * 说明：查询自己担任班主任的班级列表
     */
    pub async fn find_teacher_class_list(
        &self,
        schema_name: &String,
        teacher_id: &Uuid,
    ) -> Result<Vec<TeachingClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc where tc.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        builder.push(" order by tc.is_active desc , tc.created_at desc");
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询指定的多个教学班内所有学生ID列表（去重）
     */
    pub async fn find_all_student_id_set_in_classes(
        &self,
        schema_name: &String,
        classes_id_list: &Vec<Uuid>,
    ) -> Result<HashSet<Uuid>, String> {
        if classes_id_list.len() == 0 {
            return Ok(HashSet::<Uuid>::new());
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select distinct stc.student_id from {}.student_teaching_classes stc where stc.class_id in (",
            schema_name
        ));
        for (index, item) in classes_id_list.iter().enumerate() {
            builder.push_bind(item);
            if index < classes_id_list.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) ");
            }
        }
        Ok(builder
            .build_query_scalar()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?
            .into_iter()
            .collect())
    }

    /**
     * 作者：张瀚
     * 说明：查询指定编码的班级
     */
    pub async fn find_by_code(
        &self,
        schema_name: &String,
        code: &String,
    ) -> Result<TeachingClasses, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc where tc.code =  ",
            schema_name
        ));
        builder.push_bind(code);
        Ok(builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：获取教学班统计数据
     */
    pub async fn get_statistics(
        &self,
        context: &AuthContext,
        tenant_id: &Option<Uuid>,
        schema_name: &String,
    ) -> Result<TeachingClassesStatistics, String> {
        //获取能查看的班级列表
        let class_list = match context.is_admin_in_tenant(tenant_id.clone()) {
            true => self.get_all_class_list(schema_name).await?,
            false => {
                self.get_user_class_list(schema_name, &context.user_id)
                    .await?
            }
        };
        //统计
        let mut teacher_id_set = HashSet::<Uuid>::new();
        let mut class_id_set = HashSet::<Uuid>::new();
        for classes in &class_list {
            if classes.teacher_id.is_some() {
                teacher_id_set.insert(classes.teacher_id.unwrap().clone());
            }
            class_id_set.insert(classes.id.clone());
        }
        let student_id_set = self
            .find_all_student_id_set_in_classes(schema_name, &class_id_set.into_iter().collect())
            .await
            .map_err(|e| e.to_string())?;
        let data = TeachingClassesStatistics {
            total_classes: class_list.len() as i32,
            total_teacher: teacher_id_set.len() as i32,
            total_students: student_id_set.len() as i32,
        };
        Ok(data)
    }

    /**
     * 作者：张瀚
     * 说明：创建行政班
     */
    pub async fn create_classes(
        &self,
        schema_name: &String,
        params: &CreateTeachingClassesParams,
    ) -> Result<TeachingClasses> {
        //创建班级
        let CreateTeachingClassesParams {
            class_name,
            code,
            academic_year,
            subject_group_id,
            teacher_id,
        } = params;
        let mut tx = self.db_pool.begin().await?;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.teaching_classes (class_name,code,academic_year,subject_group_id,teacher_id) VALUES (",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , ")
            .push_bind(code)
            .push(" , ")
            .push_bind(academic_year)
            .push(" , ")
            .push_bind(subject_group_id)
            .push(" , ")
            .push_bind(teacher_id)
            .push(" ) returning *");

        let class: TeachingClasses = builder
            .build_query_as()
            .fetch_one(&mut *tx)
            .await?;

        // Step 2: 查 teacher.user_id
        let user_id: Option<Uuid> = sqlx::query_scalar(&format!(
            "SELECT user_id FROM {}.teachers WHERE id = $1", schema_name
        ))
            .bind(teacher_id)
            .fetch_optional(&mut *tx)
            .await?;

        if let Some(uid) = user_id {
            // Step 3: 插入 user_identities
            let role_id:Option<Uuid>= sqlx::query_scalar("SELECT id FROM roles WHERE code = 'teacher'").fetch_optional(&mut *tx).await?;
            if role_id.is_none() {
                // 这里可以返回 Err 或者默认值
                bail!("创建班级时查找角色身份失败");
            }

            let new_identity: UserIdentity = sqlx::query_as(&format!(
                "INSERT INTO {}.user_identities (user_id, role_id, target_type, target_id)
             VALUES ($1, $2, 'teaching_class', $3)
             RETURNING *", schema_name
            ))
                .bind(uid)
                .bind(role_id.unwrap())
                .bind(class.id)
                .fetch_one(&mut *tx)
                .await?;
        }

        tx.commit().await?;

        Ok(class)
    }

    /**
     * 作者：张瀚
     * 说明：更新班级
     */
    pub async fn update_classes(
        &self,
        schema_name: &String,
        params: &UpdateTeachingClassesParams,
    ) -> Result<TeachingClasses> {
        let UpdateTeachingClassesParams {
            id,
            class_name,
            code,
            academic_year,
            subject_group_id,
            teacher_id,
            is_active,
        } = params;

        let mut tx = self.db_pool.begin().await?;

        // 先获取原有 user_identity 的 user_id
        let old_user_id_opt: Option<Uuid> = sqlx::query_scalar(&format!(
            "SELECT user_id FROM {}.user_identities WHERE target_type='teaching_class' AND target_id=$1",
            schema_name
        ))
            .bind(id)
            .fetch_optional(&mut *tx)
            .await?;

        let mut builder = sqlx::QueryBuilder::new(format!(
            "UPDATE {}.teaching_classes SET class_name = ",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , code = ")
            .push_bind(code)
            .push(" , academic_year = ")
            .push_bind(academic_year)
            .push(" , subject_group_id = ")
            .push_bind(subject_group_id)
            .push(" , teacher_id = ")
            .push_bind(teacher_id)
            .push(" , is_active = ")
            .push_bind(is_active)
            .push(" , updated_at = now() where id = ")
            .push_bind(id)
            .push("  returning * ");
        let class:TeachingClasses = builder.build_query_as()
            .fetch_one(&mut *tx)
            .await?;

        // 查新 teacher 的 user_id
        let user_id_opt: Option<Uuid> = sqlx::query_scalar(&format!(
            "SELECT user_id FROM {}.teachers WHERE id = $1",
            schema_name
        ))
            .bind(teacher_id)
            .fetch_optional(&mut *tx)
            .await?;

        match user_id_opt {
            Some(user_id) => {
                // user_id存在 → 更新原有 user_identity
                sqlx::query(&format!(
                    "UPDATE {}.user_identities SET user_id = $1, updated_at = now()
                 WHERE target_type = 'teaching_class' AND target_id = $2",
                    schema_name
                ))
                    .bind(user_id)
                    .bind(id)
                    .execute(&mut *tx)
                    .await?;
            }
            None => {
                // user_id不存在 → 删除原有 user_identity
                sqlx::query(&format!(
                    "DELETE FROM {}.user_identities WHERE target_type = 'teaching_class' AND target_id = $1",
                    schema_name
                ))
                    .bind(id)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        // 检查旧 user_id 是否还有其他 user_identities
        if let Some(old_user_id) = old_user_id_opt {
            let count: i64 = sqlx::query_scalar(
                &format!("SELECT COUNT(*) FROM {}.user_identities WHERE user_id = $1",
                         schema_name
                ))
                .bind(old_user_id)
                .fetch_one(&mut *tx)
                .await?;

            if count == 0 {
                sqlx::query(
                    "DELETE FROM public.user_tenant_links
                            WHERE user_id = $1
                        AND tenant_id = (SELECT id FROM public.tenants WHERE schema_name = $2)"
                )
                    .bind(old_user_id)
                    .bind(schema_name)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        tx.commit().await?;

        Ok(class)
    }

    /**
     * 作者：张瀚
     * 说明：删除班级
     */
    pub async fn delete_class(
        &self,
        schema_name: &String,
        params: &DeleteTeachingClassesParams,
    ) -> Result<(), String> {
        let DeleteTeachingClassesParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.teaching_classes set is_active = false where id = ",
            schema_name
        ));
        builder.push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：查询教学班内所有学生信息
     */
    pub async fn find_all_student_in_class(
        &self,
        schema_name: &String,
        params: &FindAllStudentInClassParams,
    ) -> Result<Vec<Student>, String> {
        let FindAllStudentInClassParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.* from {}.students s ,{}.student_teaching_classes stc where stc.class_id = ",
            schema_name, schema_name
        ));
        builder
            .push_bind(class_id)
            .push("  and stc.student_id = s.id order by s.created_at desc ");
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }
    /**
     * 作者：朱若彪
     * 说明：分页查询班级内所有学生
     */
    pub async fn page_student_in_class(
        &self,
        schema_name: &String,
        params: &PageStudentInClassParams,
    )->Result<(Vec<Student>,i64), String>{
        let PageStudentInClassParams{class_id,page_params,name_like,status,student_number}=params;
        let mut conn=connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        //查询学生列表
        let (list,total)=StudentsRepository::page_student_by_teaching_class_id(&mut conn,class_id,page_params,name_like,status,student_number)
            .await
            .map_err(|e| e.to_string())?;
        Ok((list,total))
    }
    /**
     * 作者：张瀚
     * 说明：把学生移动到行政班内
     */
    pub async fn move_student_to_teaching_classes(
        &self,
        schema_name: &String,
        params: &MoveStudentToTeachingClassesParams,
    ) -> Result<(), String> {
        let MoveStudentToTeachingClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.student_teaching_classes (student_id,class_id) VALUES ( ",
            schema_name
        ));
        builder
            .push_bind(student_id)
            .push(" , ")
            .push_bind(class_id)
            .push(" ) ");
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：把学生从行政班中移除
     */
    pub async fn remove_student_from_teaching_classes(
        &self,
        schema_name: &String,
        params: &RemoveStudentFromTeachingClassesParams,
    ) -> Result<(), String> {
        let RemoveStudentFromTeachingClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "DELETE FROM {}.student_teaching_classes WHERE student_id = ",
            schema_name
        ));
        builder
            .push_bind(student_id)
            .push(" and class_id = ")
            .push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：批量统计指定教学班级中的学生数量
     */
    pub async fn batch_count_by_class(
        &self,
        schema_name: &str,
        class_ids: &Vec<Uuid>,
    ) -> Result<HashMap<Uuid, i64>, String> {
        if class_ids.len() == 0 {
            return Ok(HashMap::<Uuid, i64>::new());
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.class_id,count(*) from {}.student_teaching_classes s where s.class_id in (",
            schema_name
        ));
        for (index, id) in class_ids.iter().enumerate() {
            builder.push_bind(id);
            if index < class_ids.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) group by s.class_id");
            }
        }
        let list: Vec<PgRow> = builder
            .build()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        let mut map = HashMap::<Uuid, i64>::new();
        for ele in list.iter() {
            let id: Uuid = ele.try_get("class_id").map_err(|e| e.to_string())?;
            let count: i64 = ele.try_get("count").map_err(|e| e.to_string())?;
            map.insert(id, count);
        }
        Ok(map)
    }
}
