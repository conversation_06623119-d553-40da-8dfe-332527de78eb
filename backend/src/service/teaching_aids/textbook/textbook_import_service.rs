use crate::controller::teaching_aids::teaching_aids_controller::TeachingAidsRouteState;
use crate::middleware::auth_middleware::AuthContext;
use crate::model::textbooks::{
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Cat<PERSON><PERSON><PERSON>, Chapter<PERSON>son, ImportTeachingAidRequest, QuestionJson, SectionJson, TeachingAidChapter2, Textbook, TextbookImportLog,
    TextbookJson,
};
use crate::service::storage::storage_service::UploadOptions;
use crate::service::storage::StorageService;
use crate::utils::error::AppError;
use bytes::Bytes;
use chrono::Utc;
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::fs;
use std::io::Cursor;
use std::path::PathBuf;
use std::sync::Arc;
use tempfile::{Builder as TempFileBuilder, TempDir};
use tracing::{error, info, log};
use uuid::Uuid;
use walkdir::WalkDir;
use zip::ZipArchive;

pub struct TextbookImportService {}

impl TextbookImportService {
    /// 导入ZIP格式的教辅
    pub async fn import_textbook_from_zip(
        state: Arc<TeachingAidsRouteState>,
        auth_context: &AuthContext,
        file_data: Bytes,
        file_name: String,
        metadata: Option<ImportTeachingAidRequest>,
    ) -> Result<TextbookImportLog, AppError> {
        log::info!("Importing textbook from zip");
        // _temp_dir 一定要返回，否则出了作用域，临时文件夹就被删除了
        let (_temp_dir, temp_path): (TempDir, PathBuf) = Self::extract_zip(file_data)?;

        let textbook_id = Uuid::new_v4();
        let image_url_map = Self::upload_images(&state.storage_service, &temp_path, textbook_id).await?;

        let textbook_json_path = temp_path.join("textbook.json");
        let chapters_json_path = temp_path.join("chapters.json");

        let textbook_data: TextbookJson = serde_json::from_str(&fs::read_to_string(textbook_json_path).unwrap())?;

        let subject_id = state
            .subject_service
            .get_subject_by_code(&textbook_data.subject)
            .await
            .map(|op| op.map(|subject| subject.id))
            .unwrap_or(None);

        let grade_id = state.grade_service.get_grade_by_code(&textbook_data.grade).await.map(|op| op.map(|grade| grade.id)).unwrap_or(None);

        // 封面处理
        let mut fixed_cover_path: Option<String> = None;
        if let Some(cover_path) = textbook_data.cover_path {
            let cover = cover_path.replace("/api/oss/", "");
            let option = image_url_map.get(&cover).clone();
            if let Some(option) = option {
                fixed_cover_path = Some(option.clone());
            }
        }

        let mut new_textbook = Textbook {
            id: textbook_id,
            title: textbook_data.title,
            subject_id,
            grade_level_id: grade_id,
            publisher: textbook_data.publisher,
            publication_year: textbook_data.publish_year,
            cover_path: fixed_cover_path,
            isbn: textbook_data.isbn,
            version: Some("1.0".to_string()),
            status: Some("published".to_string()),
            creator_id: Some(auth_context.user_id),
            created_at: Some(Utc::now()),
            updated_at: Some(Utc::now()),
        };

        // metadata 有数据则以此为准
        if let Some(metadata) = metadata {
            if !metadata.title.trim().is_empty() {
                new_textbook.title = metadata.title;
            }
            if new_textbook.grade_level_id.is_none() {
                new_textbook.grade_level_id = metadata.grade_level_id;
            }
            if new_textbook.subject_id.is_none() {
                new_textbook.subject_id = metadata.subject_id;
            }
            if new_textbook.publisher.is_none() {
                new_textbook.publisher = metadata.publisher;
            }
        }

        let mut chapters_content_str = fs::read_to_string(chapters_json_path).unwrap();

        // chapter 的图片 url 清洗
        chapters_content_str = chapters_content_str.replace("/api/oss/", "");
        chapters_content_str = chapters_content_str.replace("api/oss/", "");
        for (original_name, new_url) in &image_url_map {
            chapters_content_str = chapters_content_str.replace(original_name, new_url);
        }

        // 有部分题，做了一些骚操作
        chapters_content_str = chapters_content_str.replace("underline2", "underline");

        let chapters_data: Vec<ChapterJson> = serde_json::from_str(&chapters_content_str)?;

        // 1. 处理 chapter 的父子关系，为什么这么做？就是为了可以多次导入教辅，确保每次都是新生成的
        // 2. 处理 chapter 与 answer_sheet 的关系，为什么这么做？就是为了可以多次导入教辅和答题卡，确保每次都是新生成的
        //     2.1. 先收集好 chapter 中，所有 answer-sheet 节点的 answerSheetId
        //     2.2. 再逐个 replace chapter 中的 answerSheetId 和 answer_sheet 中的 answer_card.id 和 所有 card_id 为新的 Uuid

        // 2.1. 先收集好 chapter 中，所有 answer-sheet 节点的 answerSheetId
        let mut answer_sheet_id_map = HashMap::new();
        let mut parent_id_set = HashSet::new();
        for cd in &chapters_data {
            parent_id_set.insert(cd.id.clone());
            let Some(content) = &cd.content.content else { continue };
            for cj in content {
                let Some(type_) = &cj._type else { continue };
                if type_ != "answer-sheet" {
                    continue;
                }
                if let Some(attrs) = &cj.attrs {
                    if let Some(Value::String(id)) = &attrs.get("answerSheetId") {
                        // 生成一个新的 uuid
                        answer_sheet_id_map.insert(id.to_string(), Uuid::new_v4().to_string());
                    }
                }
            }
        }

        // 1. 父子结构，需要重新设置
        for parent_id in parent_id_set.into_iter() {
            chapters_content_str = chapters_content_str.replace(&parent_id, &Uuid::new_v4().to_string());
        }
        // 2.2.1 替换 chapter 中的 answerSheetId
        for map in &answer_sheet_id_map {
            chapters_content_str = chapters_content_str.replace(map.0, map.1);
        }

        let answer_sheets_path = temp_path.join("answer_sheets");
        let mut answer_sheets = Vec::new();
        if answer_sheets_path.is_dir() {
            for entry in fs::read_dir(answer_sheets_path).map_err(|e| AppError::InternalServerError(e.to_string()))? {
                let entry = entry.map_err(|e| AppError::InternalServerError(e.to_string()))?;
                let path = entry.path();

                if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("json") {
                    let mut file_content = fs::read_to_string(&path).map_err(|e| {
                        error!("读取 answer_sheet.json 文件失败， {:?}: {}", path, e);
                        AppError::InternalServerError(e.to_string())
                    })?;

                    // answer_sheet 的图片 url 清洗
                    file_content = file_content.replace("/api/oss/", "");
                    file_content = file_content.replace("api/oss/", "");
                    for (original_name, new_url) in &image_url_map {
                        file_content = file_content.replace(original_name, new_url);
                    }

                    for map in &answer_sheet_id_map {
                        if file_content.contains(map.0) {
                            // 2.2.2 替换 answer_sheet 中的 answer_card.id 和 所有 card_id
                            file_content = file_content.replace(map.0, map.1);

                            let mut answer_sheet_data: Value = serde_json::from_str(&file_content)?;
                            let content = &mut answer_sheet_data["content"];
                            let paper_content = &mut content["paper_content"];
                            let exam_name = paper_content.pointer("/answer_card/admissionTicketNumberInfoQuestionItemConfig/examName");
                            let mut title = "答题卡".to_string();
                            if let Some(Value::String(exam_name)) = exam_name {
                                info!("成功提取到答题卡名：{}", exam_name);
                                title = exam_name.to_string();
                            }
                            let answer_card_paper_id = paper_content.pointer_mut("/answer_card/paper_id");
                            let paper_id = Uuid::new_v4();
                            if let Some(Value::String(answer_card_paper_id)) = answer_card_paper_id {
                                *answer_card_paper_id = paper_id.to_string();
                            }
                            answer_sheets.push((paper_id, title, paper_content.clone()));
                            // 世界上没有两片相同的雪花，如果有，那一定是相同的。
                            break;
                        }
                    }
                }
            }
        }

        info!("Successfully parsed and processed textbook and chapter and answer_sheets JSON data.");

        let chapters_data: Vec<ChapterJson> = serde_json::from_str(&chapters_content_str)?;
        let new_chapters: Vec<TeachingAidChapter2> = chapters_data
            .into_iter()
            .map(|c| {
                let mut parent_id = None;
                if let Some(p_id) = &c.parent_id {
                    let uuid = Uuid::parse_str(p_id).unwrap();
                    parent_id = Some(uuid);
                }
                TeachingAidChapter2 {
                    id: Uuid::parse_str(&c.id).unwrap_or_else(|_| Uuid::new_v4()),
                    textbook_id,
                    parent_id,
                    chapter_number: c.sequence,
                    title: c.title,
                    description: None,
                    content: Some(serde_json::to_value(&c.content).unwrap()),
                    metadata: c.metadata,
                    creator_id: Some(auth_context.user_id),
                    created_at: Some(Utc::now()),
                    updated_at: Some(Utc::now()),
                }
            })
            .collect();

        state.teaching_aids_service.create_textbook_with_chapters(new_textbook, new_chapters, answer_sheets).await?;

        info!("Successfully committed textbook and chapter data to the database.");

        // todo 导入记录入库
        let import_log = TextbookImportLog {
            id: Uuid::new_v4(),
            filename: file_name.to_string(),
            status: "completed".to_string(),
            total_chapters: 0,
            total_exercises: 0,
            started_at: Utc::now(),
            completed_at: Utc::now(),
            created_at: Utc::now(),
        };

        Ok(import_log)
    }

    pub async fn import_textbook_from_zip_test(state: Arc<TeachingAidsRouteState>, file_data: Bytes, file_name: String, metadata: Option<ImportTeachingAidRequest>) -> Result<(), AppError> {
        let (_temp_dir, temp_path): (TempDir, PathBuf) = Self::extract_zip(file_data)?;

        let books_json_path = temp_path.join("books.json");
        let answers_json_path = temp_path.join("answers.json");
        let catalogs_json_path = temp_path.join("catalogs.json");
        let questions_json_path = temp_path.join("questions.json");
        let sections_json_path = temp_path.join("sections.json");

        let book_data: Vec<BookJson> = serde_json::from_str(&fs::read_to_string(books_json_path).unwrap())?;
        let catalogs_data: Vec<CatalogJson> = serde_json::from_str(&fs::read_to_string(catalogs_json_path).unwrap())?;
        let questions_data: Vec<QuestionJson> = serde_json::from_str(&fs::read_to_string(questions_json_path).unwrap())?;
        let answer_data: Vec<AnswerJson> = serde_json::from_str(&fs::read_to_string(answers_json_path).unwrap())?;
        let sections_data: Vec<SectionJson> = serde_json::from_str(&fs::read_to_string(sections_json_path).unwrap())?;

        // todo 这边处理图片，但是不应该有id的，得确认具体上传怎么改。
        let image_url_map = Self::upload_images_test(&state.storage_service, &temp_path).await?;
        // todo 是否需要后处理图片链接？

        let res = state
            .teaching_aids_service
            .create_textbook_with_chapters_test(book_data, catalogs_data, questions_data, answer_data, sections_data).await?;

        Ok(res)
    }


    async fn upload_images(storage_service: &Arc<dyn StorageService>, temp_path: &PathBuf, textbook_id: Uuid) -> Result<HashMap<String, String>, AppError> {        let mut image_url_map = HashMap::new();
        let images_path = temp_path.join("images");

        // 递归遍历目录并上传所有图片，保持目录结构
        if images_path.is_dir() {
            for entry in WalkDir::new(&images_path).into_iter().filter_map(Result::ok) {
                let path = entry.path();
                if path.is_file() {
                    // 获取相对于根目录的路径（保留目录结构）
                    let relative_path = path.strip_prefix(&images_path).unwrap();
                    let full_filename = relative_path.to_str().unwrap();

                    // 构建包含目录结构的完整文件名
                    let filename = full_filename.replace("\\", "/"); // 确保路径分隔符统一

                    let content = fs::read(path).unwrap();

                    let upload_options = UploadOptions {
                        preserve_filename: true,
                        prefix: Some(format!("/public/teaching-aids/textbook-imported/textbooks/{}/images", textbook_id)),
                    };

                    let file_info = storage_service.upload(&filename, Bytes::from(content), upload_options).await.map_err(|e| {
                        error!("Failed to upload image {}: {}", filename, e);
                        AppError::InternalServerError(format!("Failed to upload image: {}", filename))
                    })?;

                    // key 是不带 http 的
                    image_url_map.insert(format!("{}{}", "images/", filename), format!("{}{}", "/files", file_info.key));
                }
            }
        }
        info!("Successfully uploaded {} images.", image_url_map.len());
        Ok(image_url_map)
    }

    async fn upload_images_test(storage_service: &Arc<dyn StorageService>, temp_path: &PathBuf) -> Result<HashMap<String, String>, AppError> {
        let mut image_url_map = HashMap::new();
        let images_path = temp_path.join("images");

        // 递归遍历目录并上传所有图片，保持目录结构
        if images_path.is_dir() {
            for entry in WalkDir::new(&images_path).into_iter().filter_map(Result::ok) {
                let path = entry.path();
                if path.is_file() {
                    // 获取相对于根目录的路径（保留目录结构）
                    let relative_path = path.strip_prefix(&images_path).unwrap();
                    let full_filename = relative_path.to_str().unwrap();

                    // 构建包含目录结构的完整文件名
                    let filename = full_filename.replace("\\", "/"); // 确保路径分隔符统一

                    let content = fs::read(path).unwrap();

                    let upload_options = UploadOptions {
                        preserve_filename: true,
                        prefix: Some("/public/teaching-aids/textbook-imported/textbooks/images".to_string()),
                    };

                    let file_info = storage_service.upload(&filename, Bytes::from(content), upload_options).await.map_err(|e| {
                        error!("Failed to upload image {}: {}", filename, e);
                        AppError::InternalServerError(format!("Failed to upload image: {}", filename))
                    })?;

                    // key 是不带 http 的
                    image_url_map.insert(format!("{}{}", "images/", filename), format!("{}{}", "/files", file_info.key));
                }
            }
        }
        info!("Successfully uploaded {} images.", image_url_map.len());
        Ok(image_url_map)
    }


    fn extract_zip(file_data: Bytes) -> Result<(TempDir, PathBuf), AppError> {
        let mut archive = ZipArchive::new(Cursor::new(file_data)).map_err(|e| {
            error!("读取ZIP压缩包失败: {}", e);
            AppError::BadRequest("无效的ZIP格式".to_string())
        })?;

        // 创建一个临时目录
        let temp_dir = TempFileBuilder::new().prefix("textbook-import-").tempdir().map_err(|e| {
            error!("创建临时目录失败: {}", e);
            AppError::InternalServerError("创建临时目录失败".to_string())
        })?;
        let temp_path = temp_dir.path().to_path_buf();
        info!("Temporary directory created at: {:?}", temp_path);

        archive.extract(&temp_path).map_err(|e| {
            error!("提取文件失败: {}", e);
            AppError::InternalServerError("提取文件失败".to_string())
        })?;
        info!("Successfully extracted ZIP to temp directory.");
        Ok((temp_dir, temp_path))
    }
}
