use crate::controller::teaching_aids::teaching_aids_controller::TeachingAidsRouteState;
use crate::model::textbooks::{Chapter<PERSON><PERSON><PERSON><PERSON><PERSON>, TeachingAidChapter, TeachingAidChapter2};
use crate::utils::error::AppError;
use regex::Regex;
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::log::error;
use uuid::Uuid;
use crate::service::storage::StorageService;

pub struct ChapterService;

impl ChapterService {
    /// 根据 textbook_id 获取所有章节，章节的所有图片需通过 MinIO 获取到可访问的 url
    /// 已过时。后续可以删除。
    pub async fn get_chapters_by_textbook_id(state: Arc<TeachingAidsRouteState>, textbook_id: Uuid) -> Result<Vec<TeachingAidChapter2>, AppError> {
        let mut chapters = state.teaching_aids_service.get_chapters_by_textbook_id(textbook_id).await.map_err(|err| {
            error!("查询章节失败，textbook_id：{}，err：{}", textbook_id, err.to_string());
            AppError::InternalServerError(err.to_string())
        })?;

        for chapter in chapters.iter_mut() {
            let Some(ref content) = chapter.content else { continue };
            let ref mut chapter_content_json = serde_json::from_value::<ChapterContentJson>(content.clone()).map_err(|err| {
                error!("解析content失败：{}，当前章节：{}", err.to_string(), chapter.id.clone());
                AppError::InternalServerError(err.to_string())
            })?;
            let Some(ref mut contents) = chapter_content_json.content else { continue };
            for ref mut c in contents {
                if Some("question-node".to_string()) == c._type {
                    let Some(ref mut attrs) = c.attrs else { continue };
                    let id = attrs.get("id").cloned().unwrap_or_else(|| Value::String("".to_string()));
                    // 1、处理 当前 question
                    Self::handle_question_images(state.clone(), attrs).await?;
                    // 2、处理 subQuestions
                    if let Some(Value::Number(sub_questions_count)) = attrs.get_mut("subQuestionsCount") {
                        // 大于1的才是有小题的
                        if sub_questions_count.as_i64() <= Some(1i64) {
                            continue;
                        }
                        if let Some(Value::Array(sub_questions)) = attrs.get_mut("subQuestions") {
                            for sub_question in sub_questions {
                                let ref mut sub_attrs = serde_json::from_value::<HashMap<String, Value>>(sub_question.clone()).map_err(|err| {
                                    error!("解析小题失败：{}，当前章节：{}，当前试题id：{}", err.to_string(), chapter.id.clone(), id.clone());
                                    AppError::InternalServerError(err.to_string())
                                })?;
                                Self::handle_question_images(state.clone(), sub_attrs).await?;
                                *sub_question = serde_json::to_value(sub_attrs).map_err(|err| {
                                    error!("更新小题失败：{}，当前章节：{}，当前试题id：{}", err.to_string(), chapter.id.clone(), id.clone());
                                    AppError::InternalServerError(err.to_string())
                                })?;
                            }
                        }
                    }
                } else if Some("resizable-image".to_string()) == c._type || Some("image".to_string()) == c._type {
                    let Some(ref mut attrs) = c.attrs else { continue };
                    if let Some(Value::String(src)) = attrs.get_mut("src") {
                        // 生成预签名 URL
                        *src = Self::generate_presigned_url(&state.storage_service, src).await?;
                    }
                }
            }
            chapter.content = Some(serde_json::to_value(chapter_content_json).map_err(|err| {
                error!("更新content失败：{}，当前章节：{}", err.to_string(), chapter.id.clone());
                AppError::InternalServerError(err.to_string())
            })?);
        }

        Ok(chapters)
    }

    async fn handle_question_images(state: Arc<TeachingAidsRouteState>, attrs: &mut HashMap<String, Value>) -> Result<(), AppError> {
        // 1、处理 stem
        if let Some(Value::String(stem)) = attrs.get_mut("stem") {
            if stem.contains("underline2") {
                *stem = stem.replace("underline2", "underline");
            }
            if stem.contains("<img") {
                *stem = Self::handle_text_images(&state.storage_service, stem.clone()).await?;
            }
        }
        // 2、处理 material
        if let Some(Value::String(material)) = attrs.get_mut("material") {
            if material.contains("underline2") {
                *material = material.replace("underline2", "underline");
            }
            if material.contains("<img") {
                *material = Self::handle_text_images(&state.storage_service, material.clone()).await?;
            }
        }
        // 3、处理 choices
        if let Some(Value::Array(choices)) = attrs.get_mut("choices") {
            for choice in choices {
                if let Value::String(choice) = choice {
                    if choice.contains("underline2") {
                        *choice = choice.replace("underline2", "underline");
                    }
                    if choice.contains("<img") {
                        *choice = Self::handle_text_images(&state.storage_service, choice.clone()).await?;
                    }
                }
            }
        }
        Ok(())
    }

    /// 将 text 中的 <img> 标签中的 src 属性值替换为可访问的 MinIO url
    pub async fn handle_text_images(storage_service: &Arc<dyn StorageService>, text: String) -> Result<String, AppError> {
        // 匹配 <img ... src="value" ...> 或 <img ... src='value' ...>
        // 捕获组 1: src 前的属性, 组 2: src 值, 组 3: src 后的属性
        let re = Regex::new(r#"<img([^>]*)\s+src=["']([^"']+)["']([^>]*)>"#).map_err(|e| AppError::InternalServerError(format!("正则编译失败: {}", e)))?;

        let mut result = String::new();
        let mut last_end = 0;

        for cap in re.captures_iter(&text) {
            let full_match = cap.get(0).unwrap();
            // let src = &cap[2].trim().replace("images/public/teaching-aids", "public/teaching-aids");
            let src = &cap[2].trim();

            // 前缀文本
            result.push_str(&text[last_end..full_match.start()]);

            // 生成预签名 URL
            let presigned_url = Self::generate_presigned_url(storage_service, src).await?;

            // 重建 img 标签
            let before_src = &cap[1];
            let after_src = &cap[3];
            // 统一使用双引号包围 URL
            result.push_str(&format!(r#"<img{} src="{}"{}>"#, before_src, presigned_url, after_src));

            last_end = full_match.end();
        }

        // 添加剩余文本
        result.push_str(&text[last_end..]);

        Ok(result)
    }

    async fn generate_presigned_url(storage_service: &Arc<dyn StorageService>, key: &str) -> Result<String, AppError> {
        Ok(storage_service.generate_presigned_url(key).await.map_err(|err| {
            error!("生成预签名URL失败 (src={}): {}", key, err.to_string());
            AppError::InternalServerError(err.to_string())
        })?)
    }

    pub async fn get_answer_sheet_id_by_chapter_id(
        state: Arc<TeachingAidsRouteState>,
        chapter_id: Uuid,
    ) -> Result<Option<String>, AppError> {
        use sqlx::postgres::PgRow;
        use sqlx::Row;

        // 使用服务提供的方法获取章节内容
        let content = state.teaching_aids_service
            .get_chapter_content(chapter_id)
            .await?;

        let content_json: Value = match content {
            Some(content) => serde_json::from_value(content).map_err(|err| {
                error!("解析章节content失败，chapter_id: {}, error: {}", chapter_id, err);
                AppError::InternalServerError("解析章节内容失败".to_string())
            })?,
            None => return Ok(None),
        };

        if let Some(Value::Array(content_array)) = content_json.get("content") {
            for item in content_array {
                if let Some(Value::String(ty)) = item.get("type") {
                    if ty == "answer-sheet" {
                        if let Some(Value::Object(attrs)) = item.get("attrs") {
                            if let Some(Value::String(answer_sheet_id)) = attrs.get("answerSheetId") {
                                return Ok(Some(answer_sheet_id.clone()));
                            }
                        }
                    }
                }
            }
        }

        Ok(None)
    }
}
