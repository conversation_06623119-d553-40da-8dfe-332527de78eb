use crate::model::paper::paper::{CreatePaperRequest, Paper, PaperQuery};
use crate::model::public_resource::book::Book;
use crate::model::teaching_aids::textbook_paper::CreateTextbookPaperRequest;
use crate::model::textbooks::*;
use crate::model::{<PERSON><PERSON>ara<PERSON>, PageResult};
use crate::service::paper::paper::PaperService;
use crate::service::storage::StorageService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use crate::utils::error::AppError;
use crate::utils::error_handler::AppResult;
use anyhow::{bail, Result};
use axum::extract::Query;
use chrono::Utc;
use serde_json::Value;
use sqlx::{PgPool, Postgres, QueryBuilder};
use std::sync::Arc;
use tracing::{error, log};
use uuid::Uuid;

#[derive(Clone)]
pub struct TeachingAidsService {
    pool: PgPool,
    storage_service: Arc<dyn StorageService>,
    paper_service: Arc<PaperService>,
    textbook_paper_service: Arc<TextbookPaperService>,
}

impl TeachingAidsService {
    pub fn new(pool: PgPool, storage_service: Arc<dyn StorageService>, paper_service: Arc<PaperService>, textbook_paper_service: Arc<TextbookPaperService>) -> Self {
        Self {
            pool,
            storage_service,
            paper_service,
            textbook_paper_service,
        }
    }

    pub async fn create_textbook(&self, request: Textbook) -> AppResult<Textbook> {
        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
            "#,
            request.id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            request.created_at,
            request.updated_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(textbook)
    }

    pub async fn get_textbook(&self, id: Uuid) -> AppResult<TextbookVO> {
        let textbook = sqlx::query_as!(
            TextbookVO,
            r#"
                    SELECT t.*, s.name AS subject, g.name AS grade_level
                    FROM public.textbooks AS t
                    LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                    LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                    WHERE t.id = $1"#,
            id
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn get_textbooks(&self) -> AppResult<Vec<TextbookVO>> {
        // 直接映射到TextbookVO，通过SQL别名确保字段与结构体字段匹配
        let textbooks = sqlx::query_as!(
            TextbookVO,
            r#"
                SELECT
                    t.id AS "id!: Uuid",
                    t.title AS "title!: String",
                    t.subject_id,
                    s.name AS subject,
                    t.grade_level_id,
                    g.name AS grade_level,
                    t.publisher,
                    t.publication_year,
                    t.cover_path,
                    t.isbn,
                    t.version,
                    t.status,
                    t.creator_id,
                    t.created_at,
                    t.updated_at
                FROM public.textbooks AS t
                LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                "#
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(textbooks)
    }

    pub async fn get_textbooks_test(&self, params: BookQuery) -> Result<PageResult<Book>> {
        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>, // 改为可变引用
            params: &BookQuery,
        ) {
            let mut where_count = 0;
            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    let pattern = format!("%{}%", search.trim());

                    builder.push(" (b.id::text ILIKE ").push_bind(pattern.clone()).push(" OR b.title ILIKE ").push_bind(pattern).push(")");
                    where_count += 1;
                }
            }
            if let Some(subject_code) = &params.subject_code {
                if !subject_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" b.subject_code = ").push_bind(subject_code.to_string());
                    where_count += 1;
                }
            }
            if let Some(gl_code) = &params.grade_level_code {
                if !gl_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" b.grade_level_code = ").push_bind(gl_code.to_string());
                    where_count += 1;
                }
            }
        }

        let mut count_builder = QueryBuilder::new("SELECT COUNT(*) FROM public.books b");
        build_where_clause(&mut count_builder, &params);

        let mut query_builder = QueryBuilder::new("SELECT * FROM public.books b");
        build_where_clause(&mut query_builder, &params);

        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        query_builder.push(" LIMIT ").push_bind(page_size).push(" OFFSET ").push_bind(offset);

        let total: i64 = count_builder.build_query_scalar().fetch_one(&self.pool).await?;
        let data = query_builder.build_query_as::<Book>().fetch_all(&self.pool).await?;

        Ok(PageResult {
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
            data,
        })
    }

    pub async fn find_latest_textbooks(&self, limit: i64) -> AppResult<Vec<TextbookVO>> {
        let textbooks = sqlx::query_as!(
            TextbookVO,
            r#"
                SELECT
                    t.id AS "id!: Uuid",
                    t.title AS "title!: String",
                    t.subject_id,
                    s.name AS subject,
                    t.grade_level_id,
                    g.name AS grade_level,
                    t.publisher,
                    t.publication_year,
                    t.cover_path,
                    t.isbn,
                    t.version,
                    t.status,
                    t.creator_id,
                    t.created_at,
                    t.updated_at
                FROM public.textbooks AS t
                LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                order by t.created_at desc
                limit $1
                "#,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(textbooks)
    }

    pub async fn update_textbooks_test(
        &self,
        book_id: Uuid,
        request: BookUpdatePayload,
    ) -> Result<Book> {
        // 把中文逗号分割成 Vec<String>
        let authors_vec: Vec<String> = request
            .authors
            .split('，')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();

        let updated: Book = sqlx::query_as::<_, Book>(
            r#"
            UPDATE books
            SET
                title = $1,
                authors = $2,
                subject_code = $3,
                grade_level_code = $4,
                updated_at = NOW()
            WHERE id = $5
            RETURNING *
            "#
        )
            .bind(&request.title)
            .bind(&authors_vec)
            .bind(&request.subject_code)
            .bind(&request.grade_level_code)
            .bind(book_id)
            .fetch_one(&self.pool)
            .await?;

        Ok(updated)
    }


    pub async fn update_textbook(&self, id: Uuid, request: Textbook) -> AppResult<Textbook> {
        let now = Utc::now();

        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            UPDATE public.textbooks
            SET title = $2, subject_id = $3, grade_level_id = $4, publisher = $5, publication_year = $6, isbn = $7, version = $8, status = $9, creator_id = $10, updated_at = $11
            WHERE id = $1
            RETURNING *
            "#,
            id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            now
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn delete_textbook(&self, id: Uuid) -> AppResult<()> {
        let result = sqlx::query!("DELETE FROM public.textbooks WHERE id = $1", id).execute(&self.pool).await?;

        if result.rows_affected() == 0 {
            return Err(anyhow::anyhow!("Textbook not found"));
        }

        Ok(())
    }

    pub async fn create_textbook_with_chapters(&self, textbook: Textbook, chapters: Vec<TeachingAidChapter2>, answer_sheets: Vec<(Uuid, String, Value)>) -> AppResult<Textbook> {
        let mut tx = self.pool.begin().await?;

        let created_textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, cover_path, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            RETURNING *
            "#,
            textbook.id,
            textbook.title,
            textbook.subject_id,
            textbook.grade_level_id,
            textbook.publisher,
            textbook.publication_year,
            textbook.cover_path,
            textbook.isbn,
            textbook.version,
            textbook.status,
            textbook.creator_id,
            textbook.created_at,
            textbook.updated_at
        )
        .fetch_one(&mut *tx)
        .await?;

        for chapter in chapters {
            log::info!("Chapter ID: {}", chapter.id.clone());
            sqlx::query!(
                r#"
                INSERT INTO public.chapters (id, textbook_id, parent_id, chapter_number, title, content, metadata, creator_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                "#,
                chapter.id,
                textbook.id,
                chapter.parent_id,
                chapter.chapter_number,
                chapter.title,
                chapter.content,
                chapter.metadata,
                chapter.creator_id,
                chapter.created_at,
                chapter.updated_at,
            )
            .execute(&mut *tx)
            .await?;
        }

        let mut couter = 0;
        for (id, title, content) in answer_sheets {
            let create_paper_req = CreatePaperRequest {
                id: Some(id),
                paper_name: title,
                paper_content: serde_json::from_value(content)?,
            };
            let new_paper = self.paper_service.create_paper_tx(&mut tx, create_paper_req).await?;

            let create_textbook_paper_req = CreateTextbookPaperRequest {
                paper_id: new_paper.id,
                textbook_id: created_textbook.id,
                serial_number: couter,
            };
            self.textbook_paper_service.create_textbook_paper_tx(&mut tx, create_textbook_paper_req).await?;
            couter += 1;
        }

        tx.commit().await?;

        Ok(created_textbook)
    }

    pub async fn create_textbook_with_chapters_test(
        &self,
        books: Vec<BookJson>,
        catalogs: Vec<CatalogJson>,
        questions: Vec<QuestionJson>,
        answers: Vec<AnswerJson>,
        sections: Vec<SectionJson>,
    ) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        // 插入书籍
        for book in books {
            sqlx::query!(
                r#"
            INSERT INTO books (
                id, title, subject_code, grade_level_code, publisher, distributor,
                year, cover_path, isbn, edition, printing_version, authors, summary
            )
            VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13)
            ON CONFLICT (id) DO UPDATE SET
                title = EXCLUDED.title,
                subject_code = EXCLUDED.subject_code,
                grade_level_code = EXCLUDED.grade_level_code,
                publisher = EXCLUDED.publisher,
                distributor = EXCLUDED.distributor,
                year = EXCLUDED.year,
                cover_path = EXCLUDED.cover_path,
                isbn = EXCLUDED.isbn,
                edition = EXCLUDED.edition,
                printing_version = EXCLUDED.printing_version,
                authors = EXCLUDED.authors,
                summary = EXCLUDED.summary
            "#,
                book.id,
                book.title,
                book.subject_code,
                book.grade_level_code,
                book.publisher,
                book.distributor,
                book.year,
                book.cover_path,
                book.isbn,
                book.edition,
                book.printing_version,
                //     todo
                &book.authors.unwrap_or(vec![]),
                book.summary
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入目录
        for catalog in catalogs {
            sqlx::query!(
                r#"
            INSERT INTO catalogs (id, book_id, parent_id, section_id, serial, level, title)
            VALUES ($1,$2,$3,$4,$5,$6,$7)
            ON CONFLICT (id) DO UPDATE SET
                parent_id = EXCLUDED.parent_id,
                section_id = EXCLUDED.section_id,
                serial = EXCLUDED.serial,
                level = EXCLUDED.level,
                title = EXCLUDED.title
            "#,
                catalog.id,
                catalog.book_id,
                catalog.parent_id,
                catalog.section_id,
                catalog.serial,
                catalog.level,
                catalog.title,
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入题目
        for question in questions {
            sqlx::query!(
                r#"
            INSERT INTO questions (id, question_type_code, items, subject_code)
            VALUES ($1,$2,$3,$4)
            ON CONFLICT (id) DO UPDATE SET
                question_type_code = EXCLUDED.question_type_code,
                items = EXCLUDED.items,
                subject_code = EXCLUDED.subject_code
            "#,
                question.id,
                question.question_type_code,
                Value::Array(question.items), // serde_json::Value
                //     todo
                question.subject_code.unwrap_or("".to_string()),
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入答案
        for answer in answers {
            sqlx::query!(
                r#"
            INSERT INTO question_answers (id, question_id, answer_area_id, content, explanation)
            VALUES ($1,$2,$3,$4,$5)
            ON CONFLICT (id) DO UPDATE SET
                question_id = EXCLUDED.question_id,
                answer_area_id = EXCLUDED.answer_area_id,
                content = EXCLUDED.content,
                explanation = EXCLUDED.explanation
            "#,
                answer.id,
                answer.question_id,
                answer.answer_area_id,
                answer.content,
                answer.explanation,
            )
            .execute(&mut *tx)
            .await?;
        }

        // 插入 Section
        for section in sections {
            sqlx::query!(
                r#"
            INSERT INTO sections (id, answer_card_id, items, snapshots)
            VALUES ($1,$2,$3,$4)
            ON CONFLICT (id) DO UPDATE SET
                answer_card_id = EXCLUDED.answer_card_id,
                items = EXCLUDED.items,
                snapshots = EXCLUDED.snapshots
            "#,
                section.id,
                section.answer_card_id,
                Value::Array(section.items), // serde_json::Value
                &section.snapshots
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    pub async fn get_chapters_by_textbook_id(&self, textbook_id: Uuid) -> AppResult<Vec<TeachingAidChapter2>> {
        let chapters = sqlx::query_as!(
            TeachingAidChapter2,
            "SELECT id, textbook_id, parent_id, chapter_number, title, description, content, metadata, creator_id, created_at, updated_at FROM public.chapters WHERE textbook_id = $1",
            textbook_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(chapters)
    }

    pub async fn get_chapter_content(&self, chapter_id: Uuid) -> Result<Option<serde_json::Value>, AppError> {
        let result: Option<Option<serde_json::Value>> = sqlx::query_scalar!(r#"SELECT content FROM public.chapters WHERE id = $1"#, chapter_id)
            .fetch_optional(&self.pool)
            .await
            .map_err(|err| {
                error!("查询章节内容失败, chapter_id: {}, error: {}", chapter_id, err);
                AppError::InternalServerError("查询章节内容失败".to_string())
            })?;

        Ok(result.flatten())
    }
}
