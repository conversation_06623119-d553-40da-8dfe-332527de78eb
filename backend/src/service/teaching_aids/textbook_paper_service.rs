use crate::model::paper::paper::{Paper, PaperQuery};
use crate::model::teaching_aids::textbook_paper::{CreateTextbookPaperRequest, TextbookPaper, UpdateTextbookPaperRequest};
use crate::utils::error_handler::AppResult;
use sqlx::{PgPool, Postgres, Transaction};
use uuid::Uuid;

#[derive(<PERSON><PERSON>, Debug)]
pub struct TextbookPaperService {
    pool: PgPool,
}

impl TextbookPaperService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_textbook_papers(&self) -> AppResult<Vec<TextbookPaper>> {
        let textbook_papers = sqlx::query_as!(TextbookPaper, "SELECT * FROM public.textbook_papers ORDER BY serial_number")
            .fetch_all(&self.pool)
            .await?;

        Ok(textbook_papers)
    }

    // GET /textbook-papers/{id}
    pub async fn get_textbook_paper_by_id(&self, id: Uuid) -> AppResult<TextbookPaper> {
        let paper = sqlx::query_as!(TextbookPaper, "SELECT * FROM public.textbook_papers WHERE id = $1", id).fetch_one(&self.pool).await?;
        Ok(paper)
    }

    // POST /textbook-papers
    pub async fn create_textbook_paper(&self, data: CreateTextbookPaperRequest) -> AppResult<TextbookPaper> {
        let paper = sqlx::query_as!(
            TextbookPaper,
            "INSERT INTO public.textbook_papers (paper_id, textbook_id, serial_number) VALUES ($1, $2, $3) RETURNING *",
            data.paper_id,
            data.textbook_id,
            data.serial_number
        )
        .fetch_one(&self.pool)
        .await?;
        Ok(paper)
    }

    pub async fn create_textbook_paper_tx<'a>(&self, tx: &mut Transaction<'a, Postgres>, data: CreateTextbookPaperRequest) -> AppResult<TextbookPaper> {
        let paper = sqlx::query_as!(
            TextbookPaper,
            "INSERT INTO public.textbook_papers (paper_id, textbook_id, serial_number) VALUES ($1, $2, $3) RETURNING *",
            data.paper_id,
            data.textbook_id,
            data.serial_number
        )
        .fetch_one(&mut **tx)
        .await?;
        Ok(paper)
    }

    // PUT /textbook-papers/{id}
    pub async fn update_textbook_paper(&self, id: Uuid, data: UpdateTextbookPaperRequest) -> AppResult<TextbookPaper> {
        let paper = sqlx::query_as!(
            TextbookPaper,
            "UPDATE public.textbook_papers SET paper_id = COALESCE($1, paper_id), textbook_id = COALESCE($2, textbook_id), updated_at = now() WHERE id = $3 RETURNING *",
            data.paper_id,
            data.textbook_id,
            id
        )
        .fetch_one(&self.pool)
        .await?;
        Ok(paper)
    }

    // DELETE /textbook-papers/{id}
    pub async fn delete_textbook_paper(&self, id: Uuid) -> AppResult<()> {
        sqlx::query!("DELETE FROM public.textbook_papers WHERE id = $1", id).execute(&self.pool).await?;
        Ok(())
    }
    /**
     * 作者：朱若彪
     * 说明: 根据教辅id分页查询试卷信息
     */
    pub async fn get_papers_by_textbook_id(&self, textbook_id: Uuid, params: PaperQuery) -> AppResult<(Vec<Paper>, i64)> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let paper_name = params.paper_name;
        let offset = (page - 1) * page_size;
        let ret = crate::repository::papers::get_papers_by_textbook_id(textbook_id, &self.pool, page_size, paper_name, offset).await?;
        Ok(ret)
    }
}
