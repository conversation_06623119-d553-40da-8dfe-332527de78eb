use sqlx::PgPool;
use uuid::Uuid;

use crate::{model::scoring_criterias::scoring_criterias::{ScoringCriteria, UpdateScoringCriteriaParams}, repository::scoring_criterias::scoring_criterias_repository::ScoringCriteriasRepository, utils::schema::connect_with_schema};

#[derive(Clone)]
pub struct ScoringCriteriasService {
    pub db_pool: PgPool,
}
impl ScoringCriteriasService{
    pub fn new(pool: PgPool)->Self{
        Self{db_pool: pool}
    }
    pub async fn get_scoring_criteria_by_id(
        &self,
        schema_name: &String,
        scoring_criteria_id:Uuid
    )->Result<ScoringCriteria,String>{
        let mut conn=connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        let scoring_criteria=ScoringCriteriasRepository::get_by_id(&mut conn, &scoring_criteria_id)
            .await
            .map_err(|e| e.to_string())?;
        match scoring_criteria {
            Some(s) => Ok(s),
            None => Err("ScoringCriteria not found".to_string()),   
        }
    }
    pub async fn create_scoring_criteria(
        &self,
        params: &ScoringCriteria,
        schema_name: &String,
    )->Result<ScoringCriteria,String>{
        let mut conn=connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        //调用repository层的创建题目方法
        let scoring_criteria=ScoringCriteriasRepository::create(&mut conn, &params)
            .await
            .map_err(|e| e.to_string())?;
        Ok(scoring_criteria)
    }
    pub async fn update_scoring_criteria(
        &self,
        scoring_criteria_id: Uuid,
        schema_name: &String,
        params: &UpdateScoringCriteriaParams,
    ) -> Result<ScoringCriteria, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        if params.scoring_type.is_none() && params.mode.is_none() && params.criteria_name.is_none() && params.answer.is_none() && params.score.is_none() && params.ocr_work_id.is_none() && params.check_work_id.is_none() && params.question_tips.is_none(){
            return Err("No fields to update".to_string());
        };
        ScoringCriteriasRepository::update(&mut conn, &scoring_criteria_id, params)
            .await
            .map_err(|e| e.to_string())
            .and_then(|opt| opt.ok_or("ScoringCriteria not found or not updated".to_string()))
    }
    pub async fn delete_scoring_criteria(
        &self,
        schema_name: &String,
        scoring_criteria_id: Uuid
    ) -> Result<bool, String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        ScoringCriteriasRepository::delete(&mut conn, scoring_criteria_id).await
    }

}