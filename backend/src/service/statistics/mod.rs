use sqlx::PgPool;
use tracing::info;
use crate::repository::students::class_repository::fetch_teaching_class_with_name_list_by_group_ids;
use crate::repository::students::subject_group_repository;
pub struct StatisticsService {
    pub pool: PgPool,
    pub schema_name: String,
}
impl StatisticsService {
    // 学科组：作业数量 题卡页
    // 各班级的数量
    pub async fn subject_groups_statistics(&self) -> anyhow::Result<()> {
        let groups = subject_group_repository::fetch_subject_groups(&self.pool, self.schema_name.as_str(), Vec::new()).await?;
        let group_ids = groups.iter().map(|g| g.0).collect::<Vec<_>>();
        // 获取班级
        let classes = fetch_teaching_class_with_name_list_by_group_ids(&self.pool, &self.schema_name, &group_ids).await?;
        // 获取作业学生表，按考试、班级、考生状态分组获得数量（考试数量，缺考率）
        // 获取考试列表，统计考试数量，题卡扫描总页数/张数
        
        info!("Statistics for subject groups:{:?}", classes);
        Ok(())
    }
}