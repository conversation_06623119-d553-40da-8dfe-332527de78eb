use sqlx::PgPool;
use uuid::Uuid;

use crate::{model::answer_card::{self, answer_card::{AnswerCard, UpdateAnswerCardParams}}, repository::answer_card::answer_card_repository::AnswerCardRepository, utils::schema::{self, connect_with_schema}};
pub struct AnswerCardService{
    pub db_pool: PgPool,
}
impl AnswerCardService{
    pub fn new(pool: PgPool)->Self{
        Self{db_pool: pool}
    }
}
impl AnswerCardService{
    /**
     * 作者：朱若彪
     * 说明：answer_card表基础crud
     */
    pub async fn get_answer_card_by_id(
        &self,
        schema_name: &String,
        answer_card_id: Uuid
    )->Result<AnswerCard, String>{
        // 获取带指定schema的数据库连接
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        // 调用返回Option类型的方法
        match AnswerCardRepository::get_answer_card_by_id(&mut conn, answer_card_id).await.map_err(|e| e.to_string())? {
            Some(answer_card) => Ok(answer_card), 
            None => Err("未找到对应答题卡".to_string())  
        }
    }
    pub async fn get_answer_card_by_paper_id(
        &self,
        schema_name: &String,
        paper_id: Uuid
    )->Result<AnswerCard, String>{
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        match AnswerCardRepository::get_answer_card_by_paper_id(&mut conn, paper_id).await.map_err(|e| e.to_string())? {
            Some(answer_card) => Ok(answer_card),
            None => Err("未找到对应答题卡".to_string())
        }
    }
    pub async fn create_answer_card(
        &self,
        schema_name: &String,
        new_answer_card: &AnswerCard
    )->Result<AnswerCard, String>{
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        AnswerCardRepository::create_answer_card(&mut conn, new_answer_card).await.map_err(|e| e.to_string())
    }
    pub async fn update_answer_card(
        &self,
        schema_name: &String,
        answer_card_id: Uuid,
        update_params: &UpdateAnswerCardParams
    )->Result<AnswerCard, String>{
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        match AnswerCardRepository::update_answer_card(&mut conn, answer_card_id, &update_params).await.map_err(|e| e.to_string())?{
            Some(answer_card) => Ok(answer_card),
            None => Err("返回更新后的数据异常".to_string())
        }
    }
    pub async fn delete_answer_card(
        &self,
        schema_name: &String,
        answer_card_id: Uuid
    )->Result<bool, String>{
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        AnswerCardRepository::delete_answer_card(&mut conn, answer_card_id).await.map_err(|e| e.to_string())
    }
}