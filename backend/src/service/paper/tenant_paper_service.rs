use crate::model::paper::paper::Paper;
use crate::model::paper::paper_cache::PaperContentData;
use crate::repository::papers::papers_repository::PapersRepository;
use crate::utils::schema::connect_with_schema;
use sqlx::pool::PoolConnection;
use sqlx::{PgPool, Postgres};
use uuid::Uuid;

/**
 * 作者：张瀚
 * 说明：租户内papers表的操作服务
 */
#[derive(Clone)]
pub struct TenantPapersService {
    db_pool: PgPool,
    schema_name: String,
}

impl TenantPapersService {
    pub fn new(db_pool: PgPool, schema_name: String) -> Self {
        Self { db_pool, schema_name }
    }
    pub async fn get_conn(&self) -> Result<PoolConnection<Postgres>, String> {
        connect_with_schema(&self.db_pool, self.schema_name.as_str()).await.map_err(|e| e.to_string())
    }

    pub async fn find_by_id(&self, paper_id: &Uuid) -> Result<Paper, String> {
        let conn = self.get_conn().await?;
        PapersRepository::find_by_paper_id(conn, paper_id).await
    }

    pub async fn update_paper_content(&self, paper_id: &Uuid, paper_content: &PaperContentData) -> Result<Paper, String> {
        let conn = self.get_conn().await?;
        let paper_content_json = serde_json::to_value(paper_content).map_err(|e| e.to_string())?;
        PapersRepository::update_paper_content(conn, paper_id, &paper_content_json).await
    }
}
