use crate::model::paper::paper::{CreatePaperRequest, Paper, UpdatePaperRequest};
use crate::repository::papers as paper_repository;
use crate::utils::error_handler::AppResult;
use sqlx::{PgPool, Postgres, Transaction};
use uuid::Uuid;

#[derive(Clone)]
pub struct PaperService {
    pool: PgPool,
}

impl PaperService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_papers(&self) -> AppResult<Vec<Paper>> {
        paper_repository::get_papers(&self.pool).await
    }

    // GET /papers/{id}
    pub async fn get_paper_by_id(&self, id: Uuid) -> AppResult<Paper> {
        paper_repository::get_paper_by_id(&self.pool, id).await
    }

    // POST /papers
    pub async fn create_paper(&self, data: CreatePaperRequest) -> AppResult<Paper> {
        paper_repository::create_paper(&self.pool, data.paper_name, data.paper_content).await
    }

    pub async fn create_paper_tx<'a>(&self, tx: &mut Transaction<'a, Postgres>, data: CreatePaperRequest) -> AppResult<Paper> {
        paper_repository::create_paper_tx(tx, data.paper_name, data.paper_content).await
    }

    // PUT /papers/{id}
    pub async fn update_paper(&self, id: Uuid, data: UpdatePaperRequest) -> AppResult<Paper> {
        paper_repository::update_paper(&self.pool, id, data.paper_name, data.paper_content).await
    }

    // DELETE /papers/{id}
    pub async fn delete_paper(&self, id: Uuid) -> AppResult<()> {
        paper_repository::delete_paper(&self.pool, id).await
    }
}
