use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use serde::{Serialize, Deserialize};
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use uuid::Uuid;

use crate::service::permission::casbin_service::DataScope;

/// 权限缓存条目
#[derive(Debug, Clone, Serialize, Deserialize)]
struct PermissionCacheEntry {
    data_scopes: Vec<DataScope>,
    cached_at: u64, // Unix timestamp
    expires_at: u64, // Unix timestamp
}

impl PermissionCacheEntry {
    fn new(data_scopes: Vec<DataScope>, ttl_seconds: u64) -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        Self {
            data_scopes,
            cached_at: now,
            expires_at: now + ttl_seconds,
        }
    }

    fn is_expired(&self) -> bool {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        now > self.expires_at
    }
}

/// 权限缓存配置
#[derive(Debug, Clone)]
pub struct PermissionCacheConfig {
    /// 缓存过期时间（秒）
    pub ttl_seconds: u64,
    /// 最大缓存条目数
    pub max_entries: usize,
    /// 缓存清理间隔（秒）
    pub cleanup_interval_seconds: u64,
    /// 是否启用性能监控
    pub enable_metrics: bool,
}

impl Default for PermissionCacheConfig {
    fn default() -> Self {
        Self {
            ttl_seconds: 300, // 5分钟
            max_entries: 10000,
            cleanup_interval_seconds: 60, // 1分钟
            enable_metrics: true,
        }
    }
}

/// 缓存性能指标
#[derive(Debug, Default)]
pub struct CacheMetrics {
    pub hits: u64,
    pub misses: u64,
    pub evictions: u64,
    pub cleanup_runs: u64,
    pub total_entries: usize,
}

impl CacheMetrics {
    pub fn hit_rate(&self) -> f64 {
        if self.hits + self.misses == 0 {
            0.0
        } else {
            self.hits as f64 / (self.hits + self.misses) as f64
        }
    }
}

/// 内存权限缓存服务
/// 提供高性能的权限数据缓存，支持自动过期和清理
#[derive(Debug)]
pub struct PermissionCache {
    cache: Arc<RwLock<HashMap<String, PermissionCacheEntry>>>,
    config: PermissionCacheConfig,
    metrics: Arc<RwLock<CacheMetrics>>,
    last_cleanup: Arc<RwLock<Instant>>,
}

impl PermissionCache {
    pub fn new(config: PermissionCacheConfig) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            config,
            metrics: Arc::new(RwLock::new(CacheMetrics::default())),
            last_cleanup: Arc::new(RwLock::new(Instant::now())),
        }
    }

    /// 缓存用户权限范围
    pub async fn cache_user_permissions(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        permissions: Vec<DataScope>,
    ) -> Result<()> {
        let cache_key = self.build_cache_key(user_id, tenant_id, resource);
        let entry = PermissionCacheEntry::new(permissions, self.config.ttl_seconds);

        {
            let mut cache = self.cache.write().await;
            
            // 检查是否需要清理缓存
            if cache.len() >= self.config.max_entries {
                self.evict_expired_entries(&mut cache).await;
                
                // 如果仍然超过限制，移除最旧的条目
                if cache.len() >= self.config.max_entries {
                    self.evict_oldest_entries(&mut cache, self.config.max_entries / 10).await;
                }
            }
            
            cache.insert(cache_key.clone(), entry);
        }

        // 更新指标
        if self.config.enable_metrics {
            let mut metrics = self.metrics.write().await;
            metrics.total_entries = self.cache.read().await.len();
        }

        debug!("Cached permissions for user {} in tenant {} for resource {}", 
               user_id, tenant_id, resource);
        
        Ok(())
    }

    /// 获取缓存的用户权限范围
    pub async fn get_cached_permissions(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Option<Vec<DataScope>>> {
        let cache_key = self.build_cache_key(user_id, tenant_id, resource);
        
        // 定期清理过期条目
        self.cleanup_if_needed().await;

        let cache = self.cache.read().await;
        
        if let Some(entry) = cache.get(&cache_key) {
            if entry.is_expired() {
                // 条目已过期
                drop(cache);
                self.remove_cache_entry(&cache_key).await;
                
                if self.config.enable_metrics {
                    let mut metrics = self.metrics.write().await;
                    metrics.misses += 1;
                }
                
                debug!("Cache miss (expired) for user {} in tenant {} for resource {}", 
                       user_id, tenant_id, resource);
                return Ok(None);
            }

            // 缓存命中
            if self.config.enable_metrics {
                let mut metrics = self.metrics.write().await;
                metrics.hits += 1;
            }
            
            debug!("Cache hit for user {} in tenant {} for resource {}", 
                   user_id, tenant_id, resource);
            return Ok(Some(entry.data_scopes.clone()));
        }

        // 缓存未命中
        if self.config.enable_metrics {
            let mut metrics = self.metrics.write().await;
            metrics.misses += 1;
        }
        
        debug!("Cache miss for user {} in tenant {} for resource {}", 
               user_id, tenant_id, resource);
        Ok(None)
    }

    /// 清除用户权限缓存
    pub async fn clear_user_cache(&self, user_id: &Uuid, tenant_id: &str) -> Result<()> {
        let pattern = format!("permissions:{}:{}:", user_id, tenant_id);
        let mut cache = self.cache.write().await;
        
        let keys_to_remove: Vec<String> = cache
            .keys()
            .filter(|key| key.starts_with(&pattern))
            .cloned()
            .collect();
        
        let removed_count = keys_to_remove.len();
        
        for key in keys_to_remove {
            cache.remove(&key);
        }

        // 更新指标
        if self.config.enable_metrics {
            let mut metrics = self.metrics.write().await;
            metrics.evictions += removed_count as u64;
            metrics.total_entries = cache.len();
        }

        info!("Cleared {} cache entries for user {} in tenant {}", 
              removed_count, user_id, tenant_id);
        
        Ok(())
    }

    /// 清除租户权限缓存
    pub async fn clear_tenant_cache(&self, tenant_id: &str) -> Result<()> {
        let pattern = format!(":{}:", tenant_id);
        let mut cache = self.cache.write().await;
        
        let keys_to_remove: Vec<String> = cache
            .keys()
            .filter(|key| key.contains(&pattern))
            .cloned()
            .collect();
        
        let removed_count = keys_to_remove.len();
        
        for key in keys_to_remove {
            cache.remove(&key);
        }

        // 更新指标
        if self.config.enable_metrics {
            let mut metrics = self.metrics.write().await;
            metrics.evictions += removed_count as u64;
            metrics.total_entries = cache.len();
        }

        info!("Cleared {} cache entries for tenant {}", removed_count, tenant_id);
        
        Ok(())
    }

    /// 清除所有缓存
    pub async fn clear_all_cache(&self) -> Result<()> {
        let mut cache = self.cache.write().await;
        let removed_count = cache.len();
        cache.clear();

        // 更新指标
        if self.config.enable_metrics {
            let mut metrics = self.metrics.write().await;
            metrics.evictions += removed_count as u64;
            metrics.total_entries = 0;
        }

        info!("Cleared all {} cache entries", removed_count);
        
        Ok(())
    }

    /// 获取缓存性能指标
    pub async fn get_metrics(&self) -> CacheMetrics {
        if self.config.enable_metrics {
            let metrics = self.metrics.read().await;
            let mut result = metrics.clone();
            result.total_entries = self.cache.read().await.len();
            result
        } else {
            CacheMetrics::default()
        }
    }

    /// 构建缓存键
    fn build_cache_key(&self, user_id: &Uuid, tenant_id: &str, resource: &str) -> String {
        format!("permissions:{}:{}:{}", user_id, tenant_id, resource)
    }

    /// 移除单个缓存条目
    async fn remove_cache_entry(&self, cache_key: &str) {
        let mut cache = self.cache.write().await;
        cache.remove(cache_key);
        
        if self.config.enable_metrics {
            let mut metrics = self.metrics.write().await;
            metrics.evictions += 1;
            metrics.total_entries = cache.len();
        }
    }

    /// 清理过期条目（如果需要）
    async fn cleanup_if_needed(&self) {
        let should_cleanup = {
            let last_cleanup = self.last_cleanup.read().await;
            last_cleanup.elapsed().as_secs() >= self.config.cleanup_interval_seconds
        };

        if should_cleanup {
            let mut cache = self.cache.write().await;
            self.evict_expired_entries(&mut cache).await;
            
            let mut last_cleanup = self.last_cleanup.write().await;
            *last_cleanup = Instant::now();
            
            if self.config.enable_metrics {
                let mut metrics = self.metrics.write().await;
                metrics.cleanup_runs += 1;
                metrics.total_entries = cache.len();
            }
        }
    }

    /// 清理过期条目
    async fn evict_expired_entries(&self, cache: &mut HashMap<String, PermissionCacheEntry>) {
        let keys_to_remove: Vec<String> = cache
            .iter()
            .filter(|(_, entry)| entry.is_expired())
            .map(|(key, _)| key.clone())
            .collect();
        
        let removed_count = keys_to_remove.len();
        
        for key in keys_to_remove {
            cache.remove(&key);
        }

        if removed_count > 0 {
            debug!("Evicted {} expired cache entries", removed_count);
            
            if self.config.enable_metrics {
                let mut metrics = self.metrics.write().await;
                metrics.evictions += removed_count as u64;
            }
        }
    }

    /// 清理最旧的条目
    async fn evict_oldest_entries(&self, cache: &mut HashMap<String, PermissionCacheEntry>, count: usize) {
        let mut entries: Vec<(String, u64)> = cache
            .iter()
            .map(|(key, entry)| (key.clone(), entry.cached_at))
            .collect();
        
        // 按缓存时间排序，最旧的在前
        entries.sort_by_key(|(_, cached_at)| *cached_at);
        
        let to_remove = entries.into_iter().take(count).map(|(key, _)| key).collect::<Vec<_>>();
        let removed_count = to_remove.len();
        
        for key in to_remove {
            cache.remove(&key);
        }

        if removed_count > 0 {
            debug!("Evicted {} oldest cache entries", removed_count);
            
            if self.config.enable_metrics {
                let mut metrics = self.metrics.write().await;
                metrics.evictions += removed_count as u64;
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_basic_operations() {
        let config = PermissionCacheConfig::default();
        let cache = PermissionCache::new(config);
        
        let user_id = Uuid::new_v4();
        let tenant_id = "tenant_001";
        let resource = "exam";
        
        let permissions = vec![
            DataScope {
                resource: "exam".to_string(),
                scope_type: "class".to_string(),
                scope_value: "class_001".to_string(),
                actions: vec!["read".to_string()],
            }
        ];

        // 测试缓存
        cache.cache_user_permissions(&user_id, tenant_id, resource, permissions.clone()).await.unwrap();
        
        // 测试获取
        let cached = cache.get_cached_permissions(&user_id, tenant_id, resource).await.unwrap();
        assert!(cached.is_some());
        assert_eq!(cached.unwrap().len(), 1);
        
        // 测试清除
        cache.clear_user_cache(&user_id, tenant_id).await.unwrap();
        let cached_after_clear = cache.get_cached_permissions(&user_id, tenant_id, resource).await.unwrap();
        assert!(cached_after_clear.is_none());
    }

    #[tokio::test]
    async fn test_cache_expiration() {
        let mut config = PermissionCacheConfig::default();
        config.ttl_seconds = 1; // 1秒过期
        
        let cache = PermissionCache::new(config);
        
        let user_id = Uuid::new_v4();
        let tenant_id = "tenant_001";
        let resource = "exam";
        
        let permissions = vec![
            DataScope {
                resource: "exam".to_string(),
                scope_type: "class".to_string(),
                scope_value: "class_001".to_string(),
                actions: vec!["read".to_string()],
            }
        ];

        // 缓存权限
        cache.cache_user_permissions(&user_id, tenant_id, resource, permissions).await.unwrap();
        
        // 立即获取应该成功
        let cached = cache.get_cached_permissions(&user_id, tenant_id, resource).await.unwrap();
        assert!(cached.is_some());
        
        // 等待过期
        tokio::time::sleep(Duration::from_secs(2)).await;
        
        // 过期后获取应该返回None
        let cached_expired = cache.get_cached_permissions(&user_id, tenant_id, resource).await.unwrap();
        assert!(cached_expired.is_none());
    }
}
