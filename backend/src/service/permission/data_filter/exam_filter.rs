use std::sync::Arc;
use anyhow::Result;
use async_trait::async_trait;
use sqlx::{Postgres, QueryBuilder, PgPool};
use tracing::{debug, info, warn};
use uuid::Uuid;

use super::base::{DataFilter, FilterContext, FilterCondition, BaseDataFilter};
use super::casbin_query::CasbinQueryHelper;
use crate::service::permission::casbin_service::{CasbinPermissionService, DataScope};
use crate::service::permission::hierarchy_resolver::PermissionHierarchyResolver;

/// 考试数据过滤器
/// 根据用户的教育角色和权限范围过滤考试数据
#[derive(Clone)]
pub struct ExamDataFilter {
    query_helper: <PERSON><PERSON>binQueryHelper,
    hierarchy_resolver: Arc<PermissionHierarchyResolver>,
}

impl ExamDataFilter {
    pub fn new(
        query_helper: <PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON>el<PERSON>,
        hierarchy_resolver: Arc<PermissionHierarchyResolver>,
    ) -> Self {
        Self {
            query_helper,
            hierarchy_resolver,
        }
    }

    /// 获取查询助手的数据库连接池引用
    fn get_pool(&self) -> &PgPool {
        self.query_helper.get_pool()
    }

    /// 获取用户的考试权限范围
    async fn get_user_exam_scopes(&self, context: &FilterContext) -> Result<Vec<DataScope>> {
        // 使用层级解析器获取完整的权限范围
        self.hierarchy_resolver
            .resolve_user_permissions(&context.user_id, &context.tenant_id, "exam")
            .await
    }

    /// 构建考试过滤条件
    async fn build_exam_filter_conditions(
        &self,
        context: &FilterContext,
        exam_scopes: &[DataScope],
    ) -> Result<Vec<String>> {
        let mut conditions = Vec::new();

        for scope in exam_scopes {
            match scope.scope_type.as_str() {
                "all" | "school" => {
                    // 学校级权限：可以访问所有考试
                    info!("User {} has school-level exam access", context.user_id);
                    return Ok(vec!["1=1".to_string()]);
                },
                "subject_group" => {
                    // 学科组权限：只能访问本学科组相关的考试
                    let condition = self.build_subject_group_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                },
                "teaching_class" => {
                    // 教学班权限：只能访问特定班级的考试
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.exam_classes ec WHERE ec.exam_id = e.id AND ec.class_id = '{}')",
                        context.schema_name, scope.scope_value
                    ));
                },
                "grade" => {
                    // 年级权限：可以访问该年级所有班级的考试
                    let condition = self.build_grade_condition(context, &scope.scope_value).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                },
                "administrative_class" => {
                    // 行政班权限：访问该行政班参与的考试
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.exam_classes ec WHERE ec.exam_id = e.id AND ec.admin_class_id = '{}')",
                        context.schema_name, scope.scope_value
                    ));
                },
                "individual" => {
                    // 个人权限：只能访问自己创建或参与的考试
                    let condition = self.build_individual_condition(context).await?;
                    if let Some(cond) = condition {
                        conditions.push(cond);
                    }
                },
                _ => {
                    warn!("Unknown scope type for exam filter: {}", scope.scope_type);
                }
            }
        }

        Ok(conditions)
    }

    /// 构建学科组相关的过滤条件
    async fn build_subject_group_condition(
        &self,
        context: &FilterContext,
        subject_group_id: &str,
    ) -> Result<Option<String>> {
        // 查询该学科组下的所有教学班
        let query = format!(
            r#"
            SELECT id FROM {}.teaching_classes 
            WHERE subject_group_id = $1 AND is_active = true
            "#,
            context.schema_name
        );

        let class_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(Uuid::parse_str(subject_group_id)?)
            .fetch_all(self.get_pool())
            .await?;

        if class_ids.is_empty() {
            return Ok(None);
        }

        let class_id_strings: Vec<String> = class_ids
            .iter()
            .map(|id| format!("'{}'", id))
            .collect();

        Ok(Some(format!(
            "EXISTS (SELECT 1 FROM {}.exam_classes ec WHERE ec.exam_id = e.id AND ec.class_id IN ({}))",
            context.schema_name,
            class_id_strings.join(",")
        )))
    }

    /// 构建年级相关的过滤条件
    async fn build_grade_condition(
        &self,
        context: &FilterContext,
        grade_code: &str,
    ) -> Result<Option<String>> {
        // 查询该年级下的所有行政班
        let query = format!(
            r#"
            SELECT id FROM {}.administrative_classes 
            WHERE grade_level_code = $1 AND is_active = true
            "#,
            context.schema_name
        );

        let class_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(grade_code)
            .fetch_all(self.query_helper.get_pool())
            .await?;

        if class_ids.is_empty() {
            return Ok(None);
        }

        let class_id_strings: Vec<String> = class_ids
            .iter()
            .map(|id| format!("'{}'", id))
            .collect();

        Ok(Some(format!(
            "EXISTS (SELECT 1 FROM {}.exam_classes ec WHERE ec.exam_id = e.id AND ec.admin_class_id IN ({}))",
            context.schema_name,
            class_id_strings.join(",")
        )))
    }

    /// 构建个人相关的过滤条件
    async fn build_individual_condition(&self, context: &FilterContext) -> Result<Option<String>> {
        // 学生只能查看自己参与的考试
        if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
            return Ok(Some(format!(
                "EXISTS (SELECT 1 FROM {}.exam_students es WHERE es.exam_id = e.id AND es.student_id = '{}')",
                context.schema_name, student_id
            )));
        }

        // 教师可以查看自己创建的考试
        Ok(Some(format!("e.created_by = '{}'", context.user_id)))
    }

    /// 检查用户是否有考试管理权限
    async fn has_exam_management_permission(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        let user_identity = format!("user:{}", context.user_id);
        
        // 检查是否有考试管理权限
        casbin_service.enforce(&crate::service::permission::casbin_service::PermissionRequest {
            subject: user_identity,
            domain: context.tenant_id.clone(),
            object: "exam".to_string(),
            action: "manage".to_string(),
        }).await
    }

    /// 基于用户身份构建过滤条件
    async fn build_identity_based_conditions(&self, context: &FilterContext) -> Result<Vec<String>> {
        let mut conditions = Vec::new();

        // 根据用户身份构建基本的过滤条件
        match context.user_identity.as_str() {
            "teacher" => {
                // 教师只能查看自己相关的考试
                conditions.push(format!(
                    "EXISTS (SELECT 1 FROM {}.exam_teachers et WHERE et.exam_id = e.id AND et.teacher_user_id = '{}')",
                    context.schema_name, context.user_id
                ));
            },
            "student" => {
                // 学生只能查看自己班级的考试
                if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.exam_students es WHERE es.exam_id = e.id AND es.student_id = '{}')",
                        context.schema_name, student_id
                    ));
                }
            },
            _ => {
                // 其他身份默认无权限
                conditions.push("1=0".to_string());
            }
        }

        Ok(conditions)
    }
}

#[async_trait]
impl DataFilter for ExamDataFilter {
    /// 获取过滤条件
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 使用默认表别名
        self.get_filter_condition_with_alias(context, casbin_service, "e").await
    }

    async fn get_filter_condition_with_alias(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
        _table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        // 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            return Ok(None); // 不需要过滤
        }

        // 检查是否有考试管理权限
        if self.has_exam_management_permission(context, casbin_service).await? {
            return Ok(None); // 不需要过滤
        }

        // 获取用户的考试权限范围
        let exam_scopes = self.get_user_exam_scopes(context).await?;

        // 构建过滤条件
        let mut conditions = if !exam_scopes.is_empty() {
            self.build_exam_filter_conditions(context, &exam_scopes).await?
        } else {
            // 如果没有通过权限范围获取到条件，尝试基于身份构建
            self.build_identity_based_conditions(context).await?
        };

        if conditions.is_empty() {
            // 无权限，返回拒绝所有的条件
            return Ok(Some(FilterCondition {
                where_clause: "1=0".to_string(),
                parameters: vec![],
            }));
        }

        // 返回过滤条件
        Ok(Some(FilterCondition {
            where_clause: conditions.join(" OR "),
            parameters: vec![],
        }))
    }

    /// 应用过滤条件到查询构建器
    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        query_builder.push(" AND (");
        query_builder.push(&condition.where_clause);
        query_builder.push(")");
        Ok(())
    }

    /// 应用过滤条件到计数查询构建器
    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        count_builder.push(" AND (");
        count_builder.push(&condition.where_clause);
        count_builder.push(")");
        Ok(())
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        info!("Applying exam data filter for user {} in tenant {}", 
              context.user_id, context.tenant_id);

        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            info!("User {} is system admin, no exam filtering applied", context.user_id);
            return Ok(false);
        }

        // 2. 检查是否有考试管理权限
        if self.has_exam_management_permission(context, casbin_service).await? {
            info!("User {} has exam management permission, no filtering applied", context.user_id);
            return Ok(false);
        }

        // 3. 获取用户的考试权限范围
        let exam_scopes = self.get_user_exam_scopes(context).await?;
        
        if exam_scopes.is_empty() {
            warn!("User {} has no exam access permissions", context.user_id);
            // 无权限，返回空结果
            query_builder.push(" AND 1=0");
            count_builder.push(" AND 1=0");
            return Ok(true);
        }

        // 4. 构建过滤条件
        let conditions = self.build_exam_filter_conditions(context, &exam_scopes).await?;
        
        if conditions.is_empty() {
            warn!("No valid exam filter conditions for user {}", context.user_id);
            query_builder.push(" AND 1=0");
            count_builder.push(" AND 1=0");
            return Ok(true);
        }

        // 5. 应用过滤条件
        let filter_condition = if conditions.len() == 1 && conditions[0] == "1=1" {
            // 全权限，不需要过滤
            return Ok(false);
        } else {
            format!(" AND ({})", conditions.join(" OR "))
        };

        debug!("Applying exam filter condition: {}", filter_condition);
        query_builder.push(&filter_condition);
        count_builder.push(&filter_condition);

        info!("Successfully applied exam data filter for user {}", context.user_id);
        Ok(true)
    }
}