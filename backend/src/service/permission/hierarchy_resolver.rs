use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use anyhow::Result;
use sqlx::PgPool;
use uuid::Uuid;
use tracing::{debug, info, warn};

use crate::service::permission::casbin_service::{MultiTenantCasbinService, DataScope};
use crate::model::permission::casbin_policy::CasbinPolicyRecord;

/// 权限层级解析器
/// 负责解析用户的完整权限范围，包括角色继承和数据范围层级
#[derive(Clone)]
pub struct PermissionHierarchyResolver {
    pool: PgPool,
    casbin_service: Arc<MultiTenantCasbinService>,
}

impl PermissionHierarchyResolver {
    pub fn new(pool: PgPool, casbin_service: Arc<MultiTenantCasbinService>) -> Self {
        Self {
            pool,
            casbin_service,
        }
    }

    /// 解析用户的完整权限范围（包括继承权限）
    pub async fn resolve_user_permissions(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Vec<DataScope>> {
        info!("Resolving permissions for user {} in tenant {} for resource {}", 
              user_id, tenant_id, resource);

        // 1. 获取用户直接角色
        let direct_roles = self.get_user_direct_roles(user_id, tenant_id).await?;
        debug!("Direct roles for user {}: {:?}", user_id, direct_roles);

        // 2. 获取继承角色
        let inherited_roles = self.get_inherited_roles(&direct_roles, tenant_id).await?;
        debug!("Inherited roles for user {}: {:?}", user_id, inherited_roles);

        // 3. 合并所有角色
        let mut all_roles = direct_roles;
        all_roles.extend(inherited_roles);
        all_roles.sort();
        all_roles.dedup();

        // 4. 解析每个角色的数据范围
        let mut all_scopes = Vec::new();
        for role in &all_roles {
            let role_scopes = self.get_role_data_scopes(role, tenant_id, resource).await?;
            all_scopes.extend(role_scopes);
        }

        // 5. 合并和优化数据范围
        let merged_scopes = self.merge_data_scopes(all_scopes);
        
        info!("Resolved {} data scopes for user {} in resource {}", 
              merged_scopes.len(), user_id, resource);
        
        Ok(merged_scopes)
    }

    /// 获取用户的直接角色
    async fn get_user_direct_roles(&self, user_id: &Uuid, tenant_id: &str) -> Result<Vec<String>> {
        let query = format!(
            r#"
            SELECT DISTINCT r.code
            FROM "{schema}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1 AND ui.is_active = true
            "#,
            schema = self.get_schema_name(tenant_id)
        );

        let roles: Vec<String> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(roles)
    }

    /// 获取角色的继承角色（递归）
    async fn get_inherited_roles(&self, roles: &[String], tenant_id: &str) -> Result<Vec<String>> {
        let mut inherited_roles = HashSet::new();
        let mut to_process: Vec<String> = roles.to_vec();
        let mut processed = HashSet::new();

        while let Some(role) = to_process.pop() {
            if processed.contains(&role) {
                continue;
            }
            processed.insert(role.clone());

            // 查询该角色继承的其他角色
            let query = r#"
                SELECT v1 as inherited_role
                FROM public.casbin_policies
                WHERE ptype = 'g2' 
                AND v0 = $1 
                AND (tenant_id = $2 OR tenant_id = 'template')
            "#;

            let parent_roles: Vec<String> = sqlx::query_scalar(query)
                .bind(&role)
                .bind(tenant_id)
                .fetch_all(&self.pool)
                .await?;

            for parent_role in parent_roles {
                if !processed.contains(&parent_role) {
                    inherited_roles.insert(parent_role.clone());
                    to_process.push(parent_role);
                }
            }
        }

        Ok(inherited_roles.into_iter().collect())
    }

    /// 获取角色的数据范围
    async fn get_role_data_scopes(
        &self,
        role: &str,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Vec<DataScope>> {
        let query = r#"
            SELECT v0, v1, v2, v3, v4
            FROM public.casbin_policies
            WHERE ptype = 'p'
            AND v0 = $1
            AND (v1 = $2 OR v1 = '*')
            AND (v2 LIKE $3 OR v2 = '*')
            AND (v4 = 'allow' OR v4 IS NULL)
            AND (tenant_id = $2 OR tenant_id = 'template')
        "#;

        let resource_pattern = format!("{}%", resource);
        let policies: Vec<CasbinPolicyRecord> = sqlx::query_as(query)
            .bind(role)
            .bind(tenant_id)
            .bind(&resource_pattern)
            .fetch_all(&self.pool)
            .await?;

        let mut scopes = Vec::new();
        for policy in policies {
            if let Some(scope) = self.parse_policy_to_scope(&policy, resource).await? {
                scopes.push(scope);
            }
        }

        Ok(scopes)
    }

    /// 将策略解析为数据范围
    async fn parse_policy_to_scope(
        &self,
        policy: &CasbinPolicyRecord,
        resource: &str,
    ) -> Result<Option<DataScope>> {
        let object = policy.v2.as_deref().unwrap_or("");
        let action = policy.v3.as_deref().unwrap_or("");

        // 解析对象格式：resource:scope_type:scope_value
        let parts: Vec<&str> = object.split(':').collect();
        
        if parts.len() >= 2 && parts[0] == resource {
            let scope_type = parts[1].to_string();
            let scope_value = if parts.len() > 2 {
                parts[2].to_string()
            } else {
                "*".to_string()
            };

            return Ok(Some(DataScope {
                resource: resource.to_string(),
                scope_type,
                scope_value,
                actions: vec![action.to_string()],
            }));
        }

        // 处理通配符权限
        if object == "*" {
            return Ok(Some(DataScope {
                resource: resource.to_string(),
                scope_type: "all".to_string(),
                scope_value: "*".to_string(),
                actions: vec![action.to_string()],
            }));
        }

        Ok(None)
    }

    /// 合并和优化数据范围
    fn merge_data_scopes(&self, scopes: Vec<DataScope>) -> Vec<DataScope> {
        let mut scope_map: HashMap<String, DataScope> = HashMap::new();

        for scope in scopes {
            let key = format!("{}:{}:{}", scope.resource, scope.scope_type, scope.scope_value);
            
            if let Some(existing) = scope_map.get_mut(&key) {
                // 合并操作权限
                for action in scope.actions {
                    if !existing.actions.contains(&action) {
                        existing.actions.push(action);
                    }
                }
            } else {
                scope_map.insert(key, scope);
            }
        }

        let mut merged_scopes: Vec<DataScope> = scope_map.into_values().collect();
        
        // 处理层级关系优化
        merged_scopes = self.optimize_scope_hierarchy(merged_scopes);
        
        merged_scopes
    }

    /// 优化范围层级关系
    /// 如果有上级范围权限，则移除下级范围权限
    fn optimize_scope_hierarchy(&self, scopes: Vec<DataScope>) -> Vec<DataScope> {
        let mut optimized: Vec<DataScope> = Vec::new();
        
        // 按范围级别排序（从高到低）
        let scope_priority = |scope_type: &str| -> i32 {
            match scope_type {
                "all" => 0,
                "school" => 1,
                "subject_group" => 2,
                "grade" => 2,
                "teaching_class" => 3,
                "administrative_class" => 3,
                "individual" => 4,
                _ => 5,
            }
        };

        let mut sorted_scopes = scopes;
        sorted_scopes.sort_by(|a, b| {
            scope_priority(&a.scope_type).cmp(&scope_priority(&b.scope_type))
        });

        for scope in sorted_scopes {
            let mut should_add = true;
            
            // 检查是否已有更高级别的权限覆盖
            for existing in &optimized {
                if existing.resource == scope.resource {
                    let existing_priority = scope_priority(&existing.scope_type);
                    let current_priority = scope_priority(&scope.scope_type);
                    
                    if existing_priority < current_priority {
                        // 已有更高级别权限，检查是否覆盖当前权限
                        if self.is_scope_covered(existing, &scope) {
                            should_add = false;
                            break;
                        }
                    }
                }
            }
            
            if should_add {
                optimized.push(scope);
            }
        }

        optimized
    }

    /// 检查一个范围是否被另一个范围覆盖
    fn is_scope_covered(&self, parent: &DataScope, child: &DataScope) -> bool {
        if parent.resource != child.resource {
            return false;
        }

        match (parent.scope_type.as_str(), child.scope_type.as_str()) {
            ("all", _) => true,
            ("school", "subject_group" | "grade" | "teaching_class" | "administrative_class" | "individual") => true,
            ("subject_group", "teaching_class") => {
                // 需要检查具体的学科组是否包含该教学班
                // 这里简化处理，实际应该查询数据库
                true
            },
            ("grade", "administrative_class") => {
                // 需要检查具体的年级是否包含该行政班
                true
            },
            _ => false,
        }
    }

    /// 获取租户的schema名称
    fn get_schema_name(&self, tenant_id: &str) -> String {
        format!("tenant_{}", tenant_id)
    }
}