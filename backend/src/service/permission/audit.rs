use sqlx::PgPool;
use uuid::Uuid;
use anyhow::Result;
use serde_json::Value as JsonValue;
use tracing::{info, error, warn};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// 权限审计服务
/// 负责记录权限相关的操作和访问日志
#[derive(Debug, Clone)]
pub struct PermissionAuditService {
    pool: PgPool,
}

impl PermissionAuditService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 记录敏感数据访问
    pub async fn log_sensitive_data_access(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        action: &str,
        target_ids: &[Uuid],
        request_ip: Option<&str>,
        user_agent: Option<&str>,
        request_path: Option<&str>,
        response_status: Option<i32>,
        processing_time_ms: Option<i32>,
    ) -> Result<()> {
        let query = r#"
            INSERT INTO public.sensitive_data_access_log 
            (user_id, tenant_id, resource, action, target_ids, target_count, 
             request_ip, user_agent, request_path, response_status, processing_time_ms)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        "#;
        
        sqlx::query(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(resource)
            .bind(action)
            .bind(target_ids)
            .bind(target_ids.len() as i32)
            .bind(request_ip)
            .bind(user_agent)
            .bind(request_path)
            .bind(response_status)
            .bind(processing_time_ms)
            .execute(&self.pool)
            .await?;
            
        info!("Logged sensitive data access for user {} in tenant {} - resource: {}, action: {}, targets: {}", 
              user_id, tenant_id, resource, action, target_ids.len());
        Ok(())
    }

    /// 记录权限检查失败
    pub async fn log_permission_failure(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        action: &str,
        failure_reason: &str,
        failure_type: &str,
        request_context: Option<JsonValue>,
        user_roles: Option<&[String]>,
        attempted_scope: Option<&str>,
        request_ip: Option<&str>,
        user_agent: Option<&str>,
        request_path: Option<&str>,
    ) -> Result<()> {
        let query = r#"
            INSERT INTO public.permission_failure_log 
            (user_id, tenant_id, resource, action, failure_reason, failure_type,
             request_context, user_roles, attempted_scope, request_ip, user_agent, request_path)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        "#;
        
        sqlx::query(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(resource)
            .bind(action)
            .bind(failure_reason)
            .bind(failure_type)
            .bind(request_context)
            .bind(user_roles)
            .bind(attempted_scope)
            .bind(request_ip)
            .bind(user_agent)
            .bind(request_path)
            .execute(&self.pool)
            .await?;
            
        warn!("Logged permission failure for user {} in tenant {} - resource: {}, action: {}, reason: {}", 
              user_id, tenant_id, resource, action, failure_reason);
        Ok(())
    }

    /// 更新权限使用统计
    pub async fn update_permission_stats(
        &self,
        tenant_id: &str,
        user_id: &Uuid,
        resource: &str,
        action: &str,
        success: bool,
        processing_time_ms: i32,
    ) -> Result<()> {
        let query = "SELECT update_permission_usage_stats($1, $2, $3, $4, $5, $6)";
        
        sqlx::query(query)
            .bind(tenant_id)
            .bind(user_id)
            .bind(resource)
            .bind(action)
            .bind(success)
            .bind(processing_time_ms)
            .execute(&self.pool)
            .await?;
            
        Ok(())
    }

    /// 记录权限变更历史
    pub async fn log_permission_change(
        &self,
        tenant_id: &str,
        change_type: &str,
        target_type: &str,
        target_id: &str,
        old_value: Option<JsonValue>,
        new_value: Option<JsonValue>,
        change_reason: Option<&str>,
        operator_id: &Uuid,
        operator_identity: &str,
        approval_required: bool,
        effective_at: Option<DateTime<Utc>>,
        expires_at: Option<DateTime<Utc>>,
    ) -> Result<Uuid> {
        let query = r#"
            INSERT INTO public.permission_change_history 
            (tenant_id, change_type, target_type, target_id, old_value, new_value,
             change_reason, operator_id, operator_identity, approval_required, 
             effective_at, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING id
        "#;
        
        let change_id: Uuid = sqlx::query_scalar(query)
            .bind(tenant_id)
            .bind(change_type)
            .bind(target_type)
            .bind(target_id)
            .bind(old_value)
            .bind(new_value)
            .bind(change_reason)
            .bind(operator_id)
            .bind(operator_identity)
            .bind(approval_required)
            .bind(effective_at)
            .bind(expires_at)
            .fetch_one(&self.pool)
            .await?;
            
        info!("Logged permission change {} for {} {} in tenant {} by user {}", 
              change_type, target_type, target_id, tenant_id, operator_id);
        
        Ok(change_id)
    }

    /// 检测异常访问行为
    pub async fn detect_anomaly_access(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        anomaly_type: &str,
        severity: &str,
        description: &str,
        evidence: Option<JsonValue>,
        risk_score: i32,
    ) -> Result<Uuid> {
        let query = r#"
            INSERT INTO public.anomaly_access_detection 
            (user_id, tenant_id, anomaly_type, severity, description, evidence, risk_score)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
        "#;
        
        let anomaly_id: Uuid = sqlx::query_scalar(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(anomaly_type)
            .bind(severity)
            .bind(description)
            .bind(evidence)
            .bind(risk_score)
            .fetch_one(&self.pool)
            .await?;
            
        warn!("Detected anomaly access {} for user {} in tenant {} - severity: {}, risk: {}", 
              anomaly_type, user_id, tenant_id, severity, risk_score);
        
        Ok(anomaly_id)
    }

    /// 解决异常访问
    pub async fn resolve_anomaly(
        &self,
        anomaly_id: &Uuid,
        resolved_by: &Uuid,
        resolution_notes: &str,
    ) -> Result<()> {
        let query = r#"
            UPDATE public.anomaly_access_detection 
            SET is_resolved = true, resolved_by = $2, resolved_at = NOW(), resolution_notes = $3
            WHERE id = $1
        "#;
        
        sqlx::query(query)
            .bind(anomaly_id)
            .bind(resolved_by)
            .bind(resolution_notes)
            .execute(&self.pool)
            .await?;
            
        info!("Resolved anomaly {} by user {}", anomaly_id, resolved_by);
        Ok(())
    }

    /// 获取权限使用统计
    pub async fn get_permission_usage_stats(
        &self,
        tenant_id: &str,
        days: i32,
    ) -> Result<Vec<PermissionUsageStats>> {
        let query = r#"
            SELECT 
                resource,
                action,
                SUM(success_count) as total_success,
                SUM(failure_count) as total_failure,
                AVG(avg_processing_time_ms) as avg_processing_time,
                COUNT(DISTINCT user_id) as unique_users,
                MAX(last_access_at) as last_access_at
            FROM public.permission_usage_stats
            WHERE tenant_id = $1 AND stats_date >= CURRENT_DATE - INTERVAL '%d days'
            GROUP BY resource, action
            ORDER BY total_success + total_failure DESC
        "#;
        
        let formatted_query = query.replace("%d", &days.to_string());
        
        let stats: Vec<PermissionUsageStats> = sqlx::query_as(&formatted_query)
            .bind(tenant_id)
            .fetch_all(&self.pool)
            .await?;
            
        Ok(stats)
    }

    /// 获取异常访问统计
    pub async fn get_anomaly_stats(
        &self,
        tenant_id: &str,
        days: i32,
    ) -> Result<Vec<AnomalyStats>> {
        let query = r#"
            SELECT 
                anomaly_type,
                severity,
                COUNT(*) as total_count,
                COUNT(*) FILTER (WHERE NOT is_resolved) as unresolved_count,
                AVG(risk_score) as avg_risk_score,
                MAX(detected_at) as latest_detection
            FROM public.anomaly_access_detection
            WHERE tenant_id = $1 AND detected_at >= CURRENT_DATE - INTERVAL '%d days'
            GROUP BY anomaly_type, severity
            ORDER BY total_count DESC
        "#;
        
        let formatted_query = query.replace("%d", &days.to_string());
        
        let stats: Vec<AnomalyStats> = sqlx::query_as(&formatted_query)
            .bind(tenant_id)
            .fetch_all(&self.pool)
            .await?;
            
        Ok(stats)
    }

    /// 获取用户访问模式分析
    pub async fn analyze_user_access_pattern(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        days: i32,
    ) -> Result<UserAccessPattern> {
        // 获取用户访问的资源类型分布
        let resource_query = r#"
            SELECT resource, COUNT(*) as access_count
            FROM public.sensitive_data_access_log
            WHERE user_id = $1 AND tenant_id = $2 
            AND accessed_at >= CURRENT_DATE - INTERVAL '%d days'
            GROUP BY resource
            ORDER BY access_count DESC
        "#;
        
        let formatted_resource_query = resource_query.replace("%d", &days.to_string());
        
        let resource_access: Vec<(String, i64)> = sqlx::query_as(&formatted_resource_query)
            .bind(user_id)
            .bind(tenant_id)
            .fetch_all(&self.pool)
            .await?;

        // 获取用户访问时间分布
        let time_query = r#"
            SELECT 
                EXTRACT(hour FROM accessed_at) as hour,
                COUNT(*) as access_count
            FROM public.sensitive_data_access_log
            WHERE user_id = $1 AND tenant_id = $2 
            AND accessed_at >= CURRENT_DATE - INTERVAL '%d days'
            GROUP BY EXTRACT(hour FROM accessed_at)
            ORDER BY hour
        "#;
        
        let formatted_time_query = time_query.replace("%d", &days.to_string());
        
        let time_access: Vec<(i32, i64)> = sqlx::query_as(&formatted_time_query)
            .bind(user_id)
            .bind(tenant_id)
            .fetch_all(&self.pool)
            .await?;

        // 获取权限失败统计
        let failure_query = r#"
            SELECT failure_type, COUNT(*) as failure_count
            FROM public.permission_failure_log
            WHERE user_id = $1 AND tenant_id = $2 
            AND failed_at >= CURRENT_DATE - INTERVAL '%d days'
            GROUP BY failure_type
            ORDER BY failure_count DESC
        "#;
        
        let formatted_failure_query = failure_query.replace("%d", &days.to_string());
        
        let failure_stats: Vec<(String, i64)> = sqlx::query_as(&formatted_failure_query)
            .bind(user_id)
            .bind(tenant_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(UserAccessPattern {
            user_id: *user_id,
            tenant_id: tenant_id.to_string(),
            analysis_period_days: days,
            resource_access: resource_access.into_iter().collect(),
            hourly_access: time_access.into_iter().collect(),
            failure_stats: failure_stats.into_iter().collect(),
        })
    }

    /// 清理过期审计数据
    pub async fn cleanup_expired_audit_data(&self) -> Result<CleanupResult> {
        let query = "SELECT * FROM cleanup_audit_data()";
        
        let cleanup_results: Vec<(String, i64, i64)> = sqlx::query_as(query)
            .fetch_all(&self.pool)
            .await?;
            
        let mut total_deleted = 0;
        let mut total_archived = 0;
        let mut table_results = HashMap::new();
        
        for (table_name, deleted_count, archived_count) in cleanup_results {
            total_deleted += deleted_count;
            total_archived += archived_count;
            table_results.insert(table_name, (deleted_count, archived_count));
        }
        
        info!("Audit data cleanup completed - deleted: {}, archived: {}", 
              total_deleted, total_archived);
        
        Ok(CleanupResult {
            total_deleted,
            total_archived,
            table_results,
        })
    }
}

/// 权限使用统计
#[derive(Debug, sqlx::FromRow)]
pub struct PermissionUsageStats {
    pub resource: String,
    pub action: String,
    pub total_success: i64,
    pub total_failure: i64,
    pub avg_processing_time: f64,
    pub unique_users: i64,
    pub last_access_at: Option<DateTime<Utc>>,
}

/// 异常访问统计
#[derive(Debug, sqlx::FromRow)]
pub struct AnomalyStats {
    pub anomaly_type: String,
    pub severity: String,
    pub total_count: i64,
    pub unresolved_count: i64,
    pub avg_risk_score: f64,
    pub latest_detection: Option<DateTime<Utc>>,
}

/// 用户访问模式分析
#[derive(Debug)]
pub struct UserAccessPattern {
    pub user_id: Uuid,
    pub tenant_id: String,
    pub analysis_period_days: i32,
    pub resource_access: HashMap<String, i64>,
    pub hourly_access: HashMap<i32, i64>,
    pub failure_stats: HashMap<String, i64>,
}

/// 清理结果
#[derive(Debug)]
pub struct CleanupResult {
    pub total_deleted: i64,
    pub total_archived: i64,
    pub table_results: HashMap<String, (i64, i64)>, // table_name -> (deleted, archived)
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;

    async fn create_test_service() -> PermissionAuditService {
        // 这里应该创建测试用的数据库连接
        let pool: PgPool = todo!("Create test database pool");
        PermissionAuditService::new(pool)
    }

    #[tokio::test]
    async fn test_log_sensitive_data_access() {
        let service = create_test_service().await;
        let user_id = Uuid::new_v4();
        let target_ids = vec![Uuid::new_v4(), Uuid::new_v4()];
        
        let result = service.log_sensitive_data_access(
            &user_id,
            "tenant_001",
            "student",
            "read",
            &target_ids,
            Some("192.168.1.1"),
            Some("Mozilla/5.0"),
            Some("/api/v1/students"),
            Some(200),
            Some(15),
        ).await;
        
        // assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_permission_failure_logging() {
        let service = create_test_service().await;
        let user_id = Uuid::new_v4();
        
        let result = service.log_permission_failure(
            &user_id,
            "tenant_001",
            "exam",
            "read",
            "Insufficient permissions",
            "NO_PERMISSION",
            None,
            Some(&vec!["teacher".to_string()]),
            Some("class:123"),
            Some("192.168.1.1"),
            None,
            Some("/api/v1/exams"),
        ).await;
        
        // assert!(result.is_ok());
    }
}
