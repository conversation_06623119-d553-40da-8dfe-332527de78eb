use super::vo::score_detail_vo::ScoreDetailVo;
use crate::model::paper::paper_cache::ScoringCriteria;
use crate::repository::score::score_detail_repository::fetch_details_by_score_id;
use crate::repository::score::score_repository;
use crate::repository::score::score_repository::CriteriaSummary;
use crate::service::grading::vo::criteria_vo::CriteriaVo;
use crate::service::grading::vo::score_vo::ScoreVo;
use crate::service::storage::StorageService;
use sqlx::PgPool;
use std::collections::HashMap;
use std::sync::Arc;
use uuid::Uuid;

pub struct GradingQuestionService {
    pub db: PgPool,
    pub schema_name: String,
    pub storage: Arc<dyn StorageService>,
}

impl GradingQuestionService {
    pub fn new(db: PgPool, schema_name: String, storage: Arc<dyn StorageService>) -> Self {
        Self { db, schema_name, storage }
    }
    pub async fn get_criteria_list(&self, criteria_list: Vec<ScoringCriteria>) -> anyhow::Result<Vec<CriteriaVo>> {
        let criteria_ids: Vec<_> = criteria_list.iter().map(|c| c.id.clone()).collect();
        let criteria_summaries = score_repository::fetch_criteria_summary(&self.db, self.schema_name.as_str(), Some(criteria_ids), None).await?;
        let mut criteria_summary_map: HashMap<Uuid, Vec<CriteriaSummary>> = HashMap::new();
        criteria_summaries.into_iter().for_each(|summary| {
            criteria_summary_map.entry(summary.criteria_id.clone()).or_insert_with(Vec::new).push(summary);
        });
        let ret = criteria_list.into_iter().map(|criteria| {
            if let Some(s) = criteria_summary_map.remove(&criteria.id) {
                CriteriaVo::from(criteria, s)
            } else {
                CriteriaVo::from(criteria, Vec::new())
            }
        }).collect::<Vec<_>>();
        Ok(ret)
    }

    pub async fn get_scores(&self, _exam_id: Uuid, criteria_id: Uuid, page_size: i32, page: i32) -> anyhow::Result<(Vec<ScoreVo>, i64)> {
        let (scores, total) = score_repository::fetch_paged_scores_with_criteria_id(&self.db, self.schema_name.as_str(), criteria_id, page_size, (page - 1) * page_size).await?;
        let mut ret = Vec::with_capacity(scores.len());
        for score in scores {
            let mut blocks = score_repository::get_blocks_with_score_id(&self.db, self.schema_name.as_str(), score.id.clone()).await?;
            let details = fetch_details_by_score_id(&self.db, self.schema_name.as_str(), score.id.clone()).await?;
            let details = details.into_iter().map(|v| ScoreDetailVo::from_score_detail(v)).collect::<Vec<_>>();
            // let details = Vec::new();
            for block in &mut blocks {
                if let Some(url) = &block.answer_block_url {
                    if let Ok(url) = self.storage.generate_preview_images_url(url.as_str()).await {
                        block.answer_block_url = Some(url);
                    }
                }
            }
            ret.push(ScoreVo::from_score(score, blocks, details));
        }
        Ok((ret, total))
    }
}
