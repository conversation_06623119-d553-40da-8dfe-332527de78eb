use crate::model::grading::grading::{CreatePaperScanFileRequest, PaperScanPathRequest, EXAM_TYPE_HOMEWORK};
use crate::model::grading::paper_scan_block::PaperScanBlock;
use crate::model::grading::paper_scan_pages::{PaperScanPage, PaperScanPagesResponse};
use crate::model::grading::paper_scans::{PaperScan, PaperScanBatchRequest, PaperScanStatus};
use crate::model::paper::paper_cache::{PaperContentData, ScoringCriteriaTypeEnum, ScoringCriterion};
use crate::model::score::score_detail::{MatchMode, ScoreDetail, ScoreDetailStatus, ScoreReason, ScoringType};
use crate::model::score::{Score, ScoreStatus};
use crate::repository::paper_scan_page::paper_scan_page_repository::PaperScanPageRepository;
use crate::repository::paper_scans::paper_scans_repository::PaperScansRepository;
use crate::repository::score::score_detail_repository::fetch_details_by_score_id;
use crate::repository::score::score_repository::check_correct_by_ids;
use crate::repository::score::{score_detail_repository, score_repository};
use crate::service::paper_scans::paper_scans_service::PaperScansSplitSheetsAnswerBlockResponse;
use crate::service::storage::storage_service::UploadOptions;
use crate::service::task_queue::ai_grader_task::AiGraderTask;
use crate::service::task_queue::manual_tracer_task::ManualTracerTask;
use crate::service::task_queue::ocr_task::OcrTask;
use crate::service::task_queue::TaskQueueState;
use crate::utils::schema::connect_with_schema;
use crate::utils::score::f64_to_big_decimal;
use crate::web_server::AppState;
use anyhow::{anyhow, Result};
use axum::extract::Multipart;
use bigdecimal::{BigDecimal, Zero};
use sqlx::types::Json;
use sqlx::{Acquire, PgPool};
use std::cmp::min;
use std::collections::{HashMap, HashSet};
use tracing::info;
use tracing::log::error;
use uuid::Uuid;

#[derive(Clone)]
pub struct GradingService {
    pool: PgPool,
}
impl GradingService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn save_upload_paper_scans(&self, schema_name: String, request: &PaperScanBatchRequest) -> Result<Vec<PaperScanPagesResponse>> {
        let mut conn = connect_with_schema(&self.pool, schema_name.as_str()).await.map_err(|e| anyhow!("{}", e.to_string()))?;

        let mut tx = conn.begin().await?;
        let mut responses = Vec::new();

        // 保存纸张信息
        PaperScansRepository::save_tx(&mut tx, PaperScan::from(&request)).await?;

        let scans = request.scans.iter().clone();
        // 保存纸张中的每一页情况
        for scan_file in scans {
            let scan = PaperScanPageRepository::save_tx(&mut tx, PaperScanPage::from(&scan_file)).await?;
            responses.push(paper_scan_pages_to_response(scan));
        }
        // 提交事务
        tx.commit().await?;
        Ok(responses)
    }

    /// 处理图片上传
    pub async fn process_upload_payload(&self, state: AppState, params: &PaperScanPathRequest, mut payload: Multipart) -> Result<Vec<PaperScanBatchRequest>> {
        // 初始化化默认值
        let mut request: PaperScanBatchRequest = PaperScanBatchRequest::new(params.exam_id);
        request.batch_no = params.batch_no.clone();
        let mut scans = Vec::new();
        // 1. 循环提取处理所有参数 并且处理文件同步 Minio 服务端
        while let Some(field) = payload.next_field().await.map_err(|e| {
            error!("上传图片请求参数读取失败: {}", e.to_string());
            anyhow!("上传图片请求参数读取失败: {}", e)
        })? {
            let field_name = field.name().unwrap_or_default().to_string();

            println!("field_name: {}", field_name);
            // 检查参数并提取
            match field_name.as_str() {
                "file" => {
                    // 处理文件字段
                    if let Some(file_name) = field.file_name().map(|s| s.to_string()) {
                        let file_id = Uuid::new_v4();
                        let file_name_key = format!("{}_{}", file_id, file_name);
                        let prefix = format!("{}/{}/{}/{}/{}/{}", "tenants", params.tenant_name, "grading", "paper_scans", params.exam_id, params.batch_no.clone());
                        let bytes = field.bytes().await.map_err(|e| {
                            error!("上传图片请求参数提取文件流失败: {}", e.to_string());
                            anyhow!("上传图片请求参数提取文件流失败: {}", e)
                        })?;

                        // 上传到 MinIO
                        let file = state
                            .storage_service
                            .upload(
                                &file_name_key,
                                bytes,
                                UploadOptions {
                                    preserve_filename: false,
                                    prefix: Some(prefix),
                                },
                            )
                            .await
                            .map_err(|e| {
                                error!("同步到文件储存服务失败: {}", e.to_string());
                                anyhow!("同步到文件储存服务失败: {}", e)
                            })?;
                        // 将当前同步成功的文件填充到请求对象中
                        scans.push(CreatePaperScanFileRequest {
                            id: Uuid::new_v4(),
                            paper_scan_id: request.id,
                            paper_num: 0,
                            file_name,
                            file_url: file.key.clone(),
                            minio_bucket: Some("deep-mate".to_string()), //TODO 目前暂时先固定为 deep-mate
                            minio_object_key: Some(file.key.clone()),
                            file_size: file.size as i64,
                            scan_quality: 1,
                            is_duplicate: false,
                            is_blank: false,
                            is_abnormal: false,
                            abnormal_reason: None,
                            result: None,
                            create_at: Default::default(),
                            update_at: Default::default(),
                        })
                        // uploaded_files.push(file);
                    }
                }
                "student_id" => {
                    let value = field.text().await.unwrap_or_default();
                    request.student_id = Some(Uuid::parse_str(&value).unwrap_or_default());
                }
                "scan_method" => {
                    let value = field.text().await.unwrap_or_default();
                    request.scan_method = value.to_string();
                }
                "scan_device" => {
                    let value = field.text().await.unwrap_or_default();
                    request.scan_device = Some(value.to_string());
                }
                "exam_type" => {
                    // TODO 目前只不强制做类型绑定，由前端决定后面会修改
                    let value = field.text().await.unwrap_or_default();
                    request.exam_type = value.to_string();
                }
                "is_single_sided_multi_page" => {
                    let value = field.text().await.unwrap_or_default();
                    request.is_single_sided_multi_page = value.parse::<bool>().unwrap_or(false);
                }
                _ => {}
            }
        }

        //
        let mut leafs = Vec::new();
        if request.is_single_sided_multi_page {
            scans.into_iter().for_each(|mut s| {
                let mut req = request.clone();
                req.id = Uuid::new_v4();
                s.paper_scan_id = req.id;
                req.scans = vec![s];
                leafs.push(req);
            });
        } else {
            request.scans = scans;
            leafs.push(request);
        }
        Ok(leafs)
    }

    /// 智能试题分发评分
    /// 作者:萧达光
    pub async fn smart_dispatch_grading_score(
        &self,
        tenant_name: &str,
        task_queue_state: TaskQueueState,
        paper_scans_split_sheets_answer_block: PaperScansSplitSheetsAnswerBlockResponse,
        paper_content: PaperContentData,
    ) -> Result<()> {
        // 1.取出当前题卡评分标准信息
        let scoring_criteria_list = paper_content.answer_card.scoring_criteria_list;
        // 2.取出题块组与 评分标准的列表信息
        let block_group_scoring_criteria_list = paper_content.answer_card.answer_block_group_scoring_criteria_list;
        // 格式:评分标准ID => 题块组ID
        let mut block_group_criteria_map: HashMap<Uuid, _> = HashMap::new();
        block_group_scoring_criteria_list.into_iter().for_each(|item| {
            block_group_criteria_map.entry(item.scoring_criteria_id).or_insert(Vec::new()).push(item.block_group_id);
        });

        // 3.根据纸张ID取出当前纸张页面信息 将页面转换成 page_id->page_num 的map 结构
        let paper_id = paper_scans_split_sheets_answer_block.paper_id;

        info!("paper_id:{:?}", paper_id);

        // 4.将题块切图转换成 map 方便处理 格式:
        let paper_scan_blocks = paper_scans_split_sheets_answer_block.paper_scan_blocks;
        let paper_scan_blocks_map: HashMap<Uuid, Vec<PaperScanBlock>> = paper_scan_blocks.into_iter().into_iter().fold(HashMap::new(), |mut acc, block| {
            acc.entry(block.answer_block_group_id).or_default().push(block);
            acc
        });

        // 5. 将打分点转换成 map 方便处理 格式： 评分点id=>打分点实体
        let scores = paper_scans_split_sheets_answer_block.scores;
        let mut scores_map: HashMap<Uuid, Score> = scores
            .into_iter()
            .map(|item| {
                info!("criteria_id: {:?}", item.criteria_id);
                (item.criteria_id, item)
            })
            .collect();

        // 6.根据评分标准和评分组关系排序
        for scoring_criteria in scoring_criteria_list {
            // 如果没有找到对应的评分点信息则跳过
            if !scores_map.contains_key(&scoring_criteria.id) {
                continue;
            }

            // 根据评分标准获取题块组ID
            let block_group_ids = block_group_criteria_map.get(&scoring_criteria.id).unwrap();
            // 根据题块组ID拿到题信息
            let mut scan_blocks = Vec::new();
            for block_group_id in block_group_ids {
                if let Some(block_scan_blocks) = paper_scan_blocks_map.get(block_group_id) {
                    scan_blocks.extend(block_scan_blocks.clone());
                }
            }
            // 存在未绑定的题块，暂时跳过
            if scan_blocks.is_empty() {
                continue;
            }
            scan_blocks.sort_by(|a, b| a.serial_number.cmp(&b.serial_number));
            // 根据评分标准ID拿出评分点
            let score = scores_map.get_mut(&scoring_criteria.id).unwrap();
            let block_item = {
                if scan_blocks.is_empty() {
                    None
                } else {
                    let mut block_id = None;
                    let student_answers: Vec<_> = scan_blocks
                        .iter()
                        .filter_map(|block| {
                            block_id = Some(block.id);
                            block.answer_content.clone()
                        })
                        .collect();
                    let answer = if student_answers.is_empty() { None } else { Some(student_answers.join("")) };
                    let urls: Vec<_> = scan_blocks.into_iter().filter_map(|b| b.answer_block_url).collect();
                    Some((urls, answer, block_id))
                }
            };
            if block_item.is_some() {
                score_repository::update(&self.pool, tenant_name, score.id, None, Some(ScoreStatus::Distributed), None).await?;
            } else {
                score_repository::update(&self.pool, tenant_name, score.id, None, Some(ScoreStatus::Excepted), None).await?;
                // 跳过当前执行
                continue;
            }
            self.singe_scoring(tenant_name, score.id, &task_queue_state, &scoring_criteria, block_item, true).await?;
        }
        Ok(())
    }
    pub async fn check_correct_with_ids(&self, tenant_name: &str, score_ids: Vec<Uuid>) -> Result<u64> {
        check_correct_by_ids(&self.pool, tenant_name, score_ids).await
    }
    pub async fn check_by_id(&self, tenant_name: &str, score_id: Uuid, score: BigDecimal, reason: Option<String>) -> Result<u64> {
        let details = fetch_details_by_score_id(&self.pool, tenant_name, score_id).await?;
        let score_status = if let Some(detail) = details.first() {
            if score == detail.score {
                ScoreStatus::CheckedCorrect
            } else {
                ScoreStatus::CheckedError
            }
        } else {
            ScoreStatus::Done
        };
        let mut score_detail = ScoreDetail::default();
        score_detail.score = score.clone();
        score_detail.score_id = score_id;
        score_detail.scoring_type = Json(ScoringType::Check);
        score_detail.reason = Json(match reason {
            None => ScoreReason::None,
            Some(text) => ScoreReason::Text(text),
        });
        score_detail.status = ScoreDetailStatus::Done;
        let rows_affected = score_repository::update(&self.pool, tenant_name, score_id, Some(score), Some(score_status), None).await?;
        if rows_affected > 0 {
            score_detail_repository::insert(&self.pool, tenant_name, score_detail).await?;
        }
        Ok(rows_affected)
    }

    pub async fn redo_scoring(
        &self,
        tenant_name: &str,
        task_queue_state: TaskQueueState,
        criteria_list: Vec<ScoringCriterion>,
        student_ids: Vec<Uuid>,
        status_list: Vec<ScoreStatus>,
        redo_ocr: bool,
    ) -> Result<u64> {
        let mut criteria_ids = Vec::new();
        let mut criteria_map = HashMap::new();
        criteria_list.into_iter().for_each(|criteria| {
            criteria_ids.push(criteria.id);
            criteria_map.insert(criteria.id, criteria);
        });
        let score_items = score_repository::fetch_score_criteria_ids(&self.pool, tenant_name, criteria_ids, student_ids, status_list.clone()).await?;
        let redo_count = score_items.len() as u64;
        for (score_id, criteria_id) in score_items {
            if let Some(criteria) = criteria_map.get(&criteria_id) {
                self.singe_scoring(tenant_name, score_id, &task_queue_state, criteria, None, redo_ocr).await?;
            }
        }
        Ok(redo_count)
    }

    async fn singe_scoring(
        &self,
        tenant_name: &str,
        score_id: Uuid,
        task_queue_state: &TaskQueueState,
        criteria: &ScoringCriterion,
        block_info: Option<(Vec<String>, Option<String>, Option<Uuid>)>,
        redo_ocr: bool,
    ) -> Result<()> {
        let mut score_detail = ScoreDetail::default();
        score_detail.status = ScoreDetailStatus::GradingFailed;
        score_detail.score_id = score_id;
        match criteria.answer.clone() {
            None => {
                score_detail.reason = Json(ScoreReason::Text("未设置答案".to_string()));
                score_repository::update(&self.pool, tenant_name, score_id, None, Some(ScoreStatus::Excepted), None).await?;
                score_detail_repository::insert(&self.pool, tenant_name, score_detail).await?;
            }
            Some(standard_answer) => {
                let (urls, student_answer, block_id) = match block_info {
                    None => {
                        let score_blocks = score_repository::get_blocks_with_score_id(&self.pool, tenant_name, score_id).await?;
                        let mut block_id = None;
                        let student_answers: Vec<_> = score_blocks
                            .iter()
                            .filter_map(|block| {
                                block_id = Some(block.block_id);
                                block.answer_content.clone()
                            })
                            .collect();
                        let answer = if student_answers.is_empty() { None } else { Some(student_answers.join("")) };
                        let urls: Vec<_> = score_blocks.into_iter().filter_map(|b| b.answer_block_url).collect();
                        (urls, answer, block_id)
                    }
                    Some(item) => item,
                };

                match criteria.scoring_type {
                    ScoringCriteriaTypeEnum::Match => {
                        let (status, score) = if student_answer.is_none() {
                            (ScoreStatus::Excepted, None)
                        } else {
                            let student_answer = student_answer.unwrap_or_default();
                            score_detail.status = ScoreDetailStatus::Done;
                            score_detail.ocr = Some(student_answer.clone());
                            score_detail.reason = Json(ScoreReason::None);
                            if standard_answer.is_empty() {
                                score_detail.reason = Json(ScoreReason::Blank);
                                (ScoreStatus::Done, Some(BigDecimal::zero()))
                            } else if standard_answer == student_answer {
                                let score = f64_to_big_decimal(criteria.score);
                                score_detail.score = score.clone();
                                (ScoreStatus::Done, Some(score))
                            } else {
                                match criteria.mode.clone().unwrap_or_default().as_str() {
                                    "Partial" => {
                                        score_detail.scoring_type = Json(ScoringType::Match(MatchMode::Partial));
                                        // 获得半对得一半分
                                        let set_student_answer = student_answer.chars().collect::<HashSet<_>>();
                                        let set_standard_answer = standard_answer.chars().collect::<HashSet<_>>();
                                        if set_student_answer.is_subset(&set_standard_answer) {
                                            let s = criteria.score / 2f64;
                                            let score = f64_to_big_decimal(s);
                                            score_detail.score = score.clone();
                                            (ScoreStatus::Done, Some(score))
                                        } else {
                                            (ScoreStatus::Done, None)
                                        }
                                    }
                                    "Count" => {
                                        score_detail.scoring_type = Json(ScoringType::Match(MatchMode::Count));
                                        // 答案的每一项得s/n分
                                        let set_standard_answer = standard_answer.chars().collect::<HashSet<_>>();
                                        let answer_len = set_standard_answer.len();
                                        let vec_student_answer = student_answer.chars().collect::<Vec<_>>();
                                        let unit_score = criteria.score / answer_len as f64;
                                        let mut score = 0f64;
                                        for i in 0..min(answer_len, set_standard_answer.len()) {
                                            if set_standard_answer.contains(&vec_student_answer[i]) {
                                                score += unit_score;
                                            }
                                        }
                                        let score = f64_to_big_decimal(score);
                                        score_detail.score = score.clone();
                                        (ScoreStatus::Done, Some(score))
                                    }
                                    "Exact" | _ => {
                                        score_detail.scoring_type = Json(ScoringType::Match(MatchMode::Exact));
                                        (ScoreStatus::Done, Some(BigDecimal::zero()))
                                    }
                                }
                            }
                        };
                        score_repository::update(&self.pool, tenant_name, score_id, score, Some(status), None).await?;
                        score_detail_repository::insert(&self.pool, tenant_name, score_detail).await?;
                    }
                    ScoringCriteriaTypeEnum::AI => {
                        if redo_ocr || student_answer.is_none() {
                            let payload: OcrTask = OcrTask {
                                score_id,
                                tenant: tenant_name.to_string(),
                                ocr_workflow: criteria.ocr_work_id.unwrap_or_default(),
                                grade_workflow: criteria.check_work_id.unwrap_or_default(),
                                urls,
                                question_content: None,
                                standard_answer: standard_answer.clone(),
                                score: criteria.score,
                                scan_block_id: block_id,
                            };
                            task_queue_state.ocr.clone().add_task(payload).await?;
                        } else {
                            let task = AiGraderTask {
                                score_id,
                                tenant: tenant_name.to_string(),
                                grade_workflow: criteria.check_work_id.unwrap_or_default(),
                                answer: student_answer.unwrap_or_default(),
                                question_content: None,
                                standard_answer,
                                score: criteria.score,
                            };
                            task_queue_state.grader.clone().add_task(task).await?;
                        }
                    }
                    ScoringCriteriaTypeEnum::Manual => {
                        let payload: ManualTracerTask = ManualTracerTask {
                            score_id,
                            tenant: tenant_name.to_string(),
                            urls,
                            score: criteria.score,
                        };
                        task_queue_state.tracer.clone().add_task(payload).await?;
                    }
                }
            }
        }
        Ok(())
    }
}

fn paper_scan_pages_to_response(paper_scan_pages: PaperScanPage) -> PaperScanPagesResponse {
    PaperScanPagesResponse {
        id: paper_scan_pages.id,
        paper_scan_id: paper_scan_pages.paper_scan_id,
        page_num: paper_scan_pages.page_num,
        file_name: paper_scan_pages.file_name,
        file_url: paper_scan_pages.file_url,
        rectify_url: paper_scan_pages.rectify_url,
        minio_bucket: paper_scan_pages.minio_bucket,
        minio_object_key: paper_scan_pages.minio_object_key,
        file_size: paper_scan_pages.file_size,
        scan_quality: paper_scan_pages.scan_quality,
        is_duplicate: paper_scan_pages.is_duplicate,
        is_blank: paper_scan_pages.is_blank,
        is_abnormal: paper_scan_pages.is_abnormal,
        abnormal_reason: paper_scan_pages.abnormal_reason,
        result: paper_scan_pages.result,
        created_at: paper_scan_pages.created_at,
        updated_at: paper_scan_pages.updated_at,
    }
}
