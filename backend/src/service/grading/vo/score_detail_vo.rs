use bigdecimal::ToPrimitive;
use serde::Serialize;
use crate::model::score::score_detail::{ScoreDetail, ScoreDetailStatus, ScoreReason, ScoringType};

#[derive(Serialize, Debug, Clone)]
pub struct ScoreDetailVo {
    pub status: ScoreDetailStatus,
    pub score: f64,
    pub scoring_type: ScoringType,
    pub ocr: Option<String>,
    pub reason: ScoreReason,
}

impl ScoreDetailVo {
    pub fn from_score_detail(score_detail: ScoreDetail) -> Self {
        Self {
            status: score_detail.status,
            score: score_detail.score.to_f64().unwrap_or_default(),
            scoring_type: score_detail.scoring_type.0,
            ocr: score_detail.ocr,
            reason: score_detail.reason.0,
        }
    }
}