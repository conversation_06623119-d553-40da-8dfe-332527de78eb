use std::collections::HashMap;
use std::sync::Arc;
use bigdecimal::ToPrimitive;
use serde::Serialize;
use sqlx::PgPool;
use uuid::Uuid;
use crate::model::grading::paper_scan_block::PaperScanBlock;
use crate::model::paper::paper_cache::ScoringCriterion;
use crate::model::score::{Score, ScoreStatus};
use crate::repository::score::score_detail_repository::fetch_details_by_score_id;
use crate::repository::score::score_repository;
use crate::repository::score::score_repository::{ScoreBlock, ScoreWithBlockId};
use crate::repository::students::student_repository::StudentBase;
use crate::service::grading::vo::score_detail_vo::ScoreDetailVo;
use crate::service::storage::StorageService;

#[derive(Serialize)]
pub struct ScoreFullInfo {
    pub score: ScoreVo,
    pub criteria: ScoringCriterion,
}
#[derive(Serialize, Debug, Clone)]
pub struct ScoreVo {
    pub id: Uuid,
    pub criteria_id: Uuid,
    pub score: f32,
    pub status: ScoreStatus,
    pub blocks: Vec<ScoreBlock>,
    pub details: Vec<ScoreDetailVo>,
    pub student: Option<StudentBase>,
    pub answer: String,
}
impl ScoreVo {
    pub fn from_score(score: Score, blocks: Vec<ScoreBlock>, details: Vec<ScoreDetailVo>, student: Option<StudentBase>) -> Self {
        let answer = blocks.iter().map(|v| v.answer_content.clone()).flatten().collect::<String>(); 
        Self {
            id: score.id,
            student,
            criteria_id: score.criteria_id,
            score: score.score.to_f32().unwrap_or_default(),
            status: score.score_status,
            blocks,
            details,
            answer,
        }
    }
    pub fn from_block(mut items: Vec<(ScoreWithBlockId, PaperScanBlock)>, details: Vec<ScoreDetailVo>, student: Option<StudentBase>) -> Self {
        let mut blocks: Vec<ScoreBlock> = Vec::new();
        let mut id = Uuid::new_v4();
        let mut student_id = None;
        let mut criteria_id = Uuid::new_v4();
        let mut score = 0f32;
        let mut status = ScoreStatus::Done;
        items.sort_by(|a, b| a.1.serial_number.cmp(&b.1.serial_number));
        items.into_iter().for_each(|(s, block)| {
            blocks.push(ScoreBlock::from(block));
            id = s.id;
            student_id = s.student_id;
            criteria_id = s.criteria_id;
            score = s.score.to_f32().unwrap_or_default();
            status = s.score_status;
        });
        let answer = blocks.iter().map(|v| v.answer_content.clone()).flatten().collect::<String>();
        Self {
            id,
            student,
            criteria_id,
            score,
            status,
            blocks,
            details,
            answer,
        }
    }
}

pub async fn scores_to_score_vos(db: &PgPool, schema_name: &str, storage: Arc<dyn StorageService>, scores: Vec<Score>, student_info_map: HashMap<Uuid, StudentBase>) -> anyhow::Result<Vec<ScoreVo>> {
    let mut ret = Vec::with_capacity(scores.len());
    for score in scores {
        let mut blocks = score_repository::get_blocks_with_score_id(db, schema_name, score.id.clone()).await?;
        let details = fetch_details_by_score_id(db, schema_name, score.id.clone()).await?;
        let details = details.into_iter().map(|v| ScoreDetailVo::from_score_detail(v)).collect::<Vec<_>>();
        // let details = Vec::new();
        for block in &mut blocks {
            if let Some(url) = &block.answer_block_url {
                if let Ok(url) = storage.generate_preview_images_url(url.as_str()).await {
                    block.answer_block_url = Some(url);
                }
            }
        }
        let student = if let Some(sid) = score.student_id {
            student_info_map.get(&sid).cloned()
        } else { None };
        ret.push(ScoreVo::from_score(score, blocks, details, student));
    }
    Ok(ret)
}

pub fn split_score_vos_by_status(scores: Vec<ScoreVo>) -> (Vec<ScoreVo>, Vec<ScoreVo>, Vec<ScoreVo>) {
    let mut error_scored = Vec::new();
    let mut pending_scored = Vec::new();
    let mut scored = Vec::new();
    scores.into_iter().for_each(|score| {
        match score.status {
            ScoreStatus::Undistributed | ScoreStatus::Distributed => {pending_scored.push(score)}
            ScoreStatus::Excepted => {error_scored.push(score)}
            ScoreStatus::Done | ScoreStatus::CheckedError | ScoreStatus::CheckedCorrect => {scored.push(score)}
        }
    });
    (scored, error_scored, pending_scored)
}