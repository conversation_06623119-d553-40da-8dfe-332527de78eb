use crate::model::grading::paper_scans::PaperScanPageRecord;
use crate::repository::score::score_repository::ScoreBlock;
use crate::service::grading::vo::score_vo::ScoreFullInfo;
use chrono::{DateTime, Utc};
use serde::Serialize;
use uuid::Uuid;

#[derive(Serialize)]
pub struct SheetsVo {
    pub exam_type: String,
    pub batch_no: String,
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub created_at: DateTime<Utc>,
    pub pages: Vec<PageVo>,
    pub score_or_blocks: Vec<ScoreOrBlock>,
}

impl SheetsVo {
    pub fn from_scan_page(scan_pages: Vec<PaperScanPageRecord>, score_or_blocks: Vec<ScoreOrBlock>) -> Self {
        let mut exam_type= String::new();
        let mut batch_no= String::new();
        let mut student_id = None;
        let mut student_number = None;
        let mut created_at = Utc::now();
        let mut pages = Vec::new();
        for page in scan_pages {
            exam_type = page.exam_type;
            batch_no = page.batch_no;
            student_id = page.student_id;
            student_number = page.student_number;
            created_at = page.created_at;
            pages.push(PageVo {
                page_id: page.page_id,
                file_url: page.file_url,
                rectify_url: page.rectify_url,
                page_num: page.page_num,
                is_blank: page.is_blank,
                is_abnormal: page.is_abnormal,
            });
        }
        Self {
            exam_type,
            batch_no,
            student_id,
            student_number,
            created_at,
            pages,
            score_or_blocks,
        }
    }
}

#[derive(Serialize)]
pub struct PageVo {
    pub page_id: Uuid,
    pub file_url: Option<String>,
    pub rectify_url: Option<String>,
    pub page_num: i32,
    pub is_blank: bool,
    pub is_abnormal: bool,
}
#[derive(Serialize)]
pub enum ScoreOrBlock {
    Block(ScoreBlock),
    Score(ScoreFullInfo),
}