use bigdecimal::ToPrimitive;
use serde::Serialize;
use uuid::Uuid;
use crate::model::paper::paper_cache::{ScoringCriteria, ScoringCriteriaTypeEnum};
use crate::model::score::ScoreStatus;
use crate::repository::score::score_repository::CriteriaSummary;

#[derive(Serialize)]
pub struct CriteriaVo {
    pub id: Uuid,
    pub name: String,
    pub scoring_type: ScoringCriteriaTypeEnum,
    pub score: f64,
    pub answer: Option<String>,
    pub avg_score: f64,
    pub total_count: i64,
    pub success_count: i64,
}
impl CriteriaVo {
    pub fn from(criteria: ScoringCriteria, criteria_summaries: Vec<CriteriaSummary>) -> Self {
        let mut total_count = 0;
        let mut success_count = 0;
        let mut total_score = 0.0;
        for summary in criteria_summaries {
            total_count += summary.count;
            match summary.status {
                ScoreStatus::Done | ScoreStatus::CheckedCorrect | ScoreStatus::CheckedError => {
                    total_score += summary.total_score.to_f64().unwrap_or(0.0);
                    success_count += summary.count;
                },
                _ => {}
            }
        }
        let avg_score = if success_count > 0 {total_score / success_count as f64} else {0f64};
        Self {
            id: criteria.id,
            name: criteria.name.unwrap_or("-".to_string()),
            scoring_type: criteria.scoring_type,
            score: criteria.score,
            answer: criteria.answer,
            avg_score,
            total_count,
            success_count,
        }
    }
}