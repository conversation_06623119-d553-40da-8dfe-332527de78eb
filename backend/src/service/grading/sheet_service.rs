use crate::model::grading::paper_scan_block::PaperScanBlock;
use crate::repository::paper_scan_block::paper_scan_block_repository::PaperScansBlockRepository;
use crate::repository::paper_scans::paper_scans_repository::PaperScansRepository;
use crate::repository::score::score_detail_repository::fetch_details_by_score_id;
use crate::repository::score::score_repository;
use crate::repository::score::score_repository::{ScoreBlock, ScoreWithBlockId};
use crate::service::grading::vo::score_detail_vo::ScoreDetailVo;
use crate::service::grading::vo::score_vo::{ScoreFullInfo, ScoreVo};
use crate::service::grading::vo::sheet_vo::{ScoreOrBlock, SheetsVo};
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::service::storage::StorageService;
use anyhow::anyhow;
use sqlx::PgPool;
use std::collections::HashMap;
use std::sync::Arc;
use uuid::Uuid;
use crate::repository::students::student_repository::StudentsRepository;

pub async fn sheet_detail(db: PgPool, schema_name: &str, sheet_id: Option<Uuid>, student_id: Option<Uuid>, storage: Arc<dyn StorageService>) -> anyhow::Result<SheetsVo> {
    let mut spr = PaperScansRepository::get_sheet_pages_by_id(&db, schema_name, sheet_id, student_id).await?;
    if spr.is_empty() {
        return Err(anyhow!("sheet not found"));
    }
    let mut page_ids = Vec::new();
    let mut exam_id = Uuid::new_v4();
    for pr in &mut spr {
        if let Some(url) = &pr.file_url {
            if let Ok(url) = storage.generate_preview_images_url(url.as_str()).await {
                pr.file_url = Some(url);
            }
        }
        if let Some(url) = &pr.rectify_url {
            if let Ok(url) = storage.generate_preview_images_url(url.as_str()).await {
                pr.rectify_url = Some(url);
            }
        }
        page_ids.push(pr.page_id);
        exam_id = pr.exam_id;
    }
    let mut scan_blocks = PaperScansBlockRepository::get_blocks_by_page_ids(&db, schema_name, page_ids).await?;
    for b in &mut scan_blocks {
        if let Some(url) = &b.answer_block_url {
            if let Ok(url) = storage.generate_preview_images_url(url.as_str()).await {
                b.answer_block_url = Some(url);
            }
        }
    }
    let mut scan_block_map: HashMap<Uuid, PaperScanBlock> = scan_blocks.into_iter().map(|v| (v.id, v)).collect();
    let block_ids = scan_block_map.keys().cloned().collect::<Vec<_>>();
    let scores = score_repository::get_scores_with_block_ids(&db, schema_name, block_ids).await?;
    let mut score_block_map: HashMap<Uuid, Vec<(ScoreWithBlockId, PaperScanBlock)>> = HashMap::new();
    scores.into_iter().for_each(|score| {
        if let Some(scan_block) = scan_block_map.remove(&score.block_id) {
            score_block_map.entry(score.id.clone()).or_insert_with(Vec::new).push((score, scan_block,));
        }
    });
    let mut criteria_score_map = HashMap::new();
    for (k, v) in score_block_map.into_iter() {
        let details = fetch_details_by_score_id(&db, schema_name, k).await?;
        let details = details.into_iter().map(|v| ScoreDetailVo::from_score_detail(v)).collect::<Vec<_>>();
        let score_vo = ScoreVo::from_block(v, details, None);
        criteria_score_map.insert(score_vo.criteria_id, score_vo);
    }
    let mut block_group_map = HashMap::new();
    for (_k, v) in scan_block_map.into_iter() {
        block_group_map.insert(v.answer_block_group_id, ScoreBlock::from(v));
    }
    // 获取试卷结构，找到block排序
    let homework_paper_service = HomeworkPapersService::new(db.clone(), schema_name.to_string());
    let block_group_criteria_list = homework_paper_service.get_homework_block_group_criteria_list(exam_id).await?;
    let criteria_map = homework_paper_service.get_homework_criteria_map(exam_id).await?;
    let score_or_block_list = block_group_criteria_list.into_iter().filter_map(|criteria| {
        match criteria_score_map.remove(&criteria.scoring_criteria_id) {
            None => {
                match block_group_map.remove(&criteria.block_group_id) {
                    None => {None}
                    Some(b) => {
                        Some(ScoreOrBlock::Block(b))
                    }
                }
            },
            Some(s) => {
                if let Some(criteria) = criteria_map.get(&s.criteria_id).cloned() {
                    Some(ScoreOrBlock::Score(ScoreFullInfo { score: s, criteria, }))
                } else { None }
            }
        }
    }).collect::<Vec<_>>();
    let sid = spr.first().and_then(|v| v.student_id);
    let student = if let Some(student_id) = sid {
        StudentsRepository::fetch_students_by_ids(&db, schema_name, vec![student_id])
            .await?.pop()
    } else { None };
    let sheet_vo = SheetsVo::from_scan_page(spr, score_or_block_list, student);
    Ok(sheet_vo)
}