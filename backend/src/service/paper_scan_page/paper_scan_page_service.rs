use crate::model::grading::grading::ScanQueryParams;
use crate::model::grading::paper_scan_pages::PaperScanPage;
use crate::model::grading::paper_scans::PaperScanStatus;
use crate::repository::paper_scan_page::paper_scan_page_repository::{PaperScanPageRepository, PaperScanPageVo};
use crate::repository::paper_scans::paper_scans_repository::PaperScansRepository;
use crate::utils::api_response::ApiResponse;
use crate::utils::schema::connect_with_schema;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use std::collections::HashMap;
use uuid::Uuid;

/// 纸张页面服务类
#[derive(Clone)]
pub struct PaperScanPagesService {
    pool: PgPool,
}

impl PaperScanPagesService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 分页查询纸张上传列表
    /// 作者: 萧达光
    pub async fn find_paper_scan_page_list(&self, schema_name: &String, params: &ScanQueryParams) -> anyhow::Result<(Vec<PaperScansResult>, i64)> {
        let mut result = Vec::<PaperScansResult>::new();
        // 分页查询列表
        let (list, total) = PaperScansRepository::page_all_paper_scan(&self.pool, schema_name, params).await?;
        if !list.is_empty() && list.len() > 0 {
            let paper_scan_ids = list.iter().map(|p| p.id).collect::<Vec<Uuid>>();
            let paper_scan_pages = PaperScanPageRepository::find_paper_scan_pages_by_paper_scan_id(&self.pool, schema_name, paper_scan_ids).await?;
            let mut paper_scan_page_map: HashMap<Uuid, Vec<PaperScansPageInfoVo>> = paper_scan_pages.into_iter().fold(HashMap::new(), |mut map, psp| {
                map.entry(psp.paper_scan_id).or_default().push(PaperScansPageInfoVo::from(psp.clone()));
                map
            });
            // 合并纸张
            let mut pages_result_map: HashMap<Uuid, PaperScansResult> = HashMap::new();
            for item in list {
                let pages = paper_scan_page_map.remove(&item.id).unwrap_or(Vec::new());

                if pages_result_map.contains_key(&item.id) {
                    let t = pages_result_map.get_mut(&item.id).unwrap();
                    t.pages.extend(pages)
                } else {
                    pages_result_map.insert(
                        item.id,
                        PaperScansResult {
                            id: item.id.clone(),
                            student_id: item.student_id.clone(),
                            student_number: item.student_number.clone(),
                            batch_number: Some(item.batch_no.clone()),
                            pages: pages.clone(),
                            status: item.status,
                            created_at: item.created_at,
                        },
                    );
                }
            }

            // 响应结果
            let list = pages_result_map.values().cloned().collect::<Vec<PaperScansResult>>();

            result.extend(list);
        }

        Ok((result, total))
    }

    /// 更新纸张中的页码信息
    /// 作者: 萧达光
    pub async fn update_paper_scan_page_number(&self, tenant_name: &str, exam_id: Uuid, page_ids: Vec<Uuid>, page_number: i32) -> anyhow::Result<()> {
        // TODO:等待实现
        Ok(())
    }

    /// 重新识别纸张内容
    /// 作者: 萧达光
    /// 说明: 该操作会联动 (paper_scans、paper_scan_pages、score_blocks、score_details、scores、paper_scan_block) 这几个表中的数据
    pub async fn batch_recognize_scanned_pages(&self, tenant_name: &str, exam_id: Uuid, page_ids: Option<String>, leaf_ids: Option<Vec<Uuid>>) -> anyhow::Result<()> {
        // 1.判断当前考试是否已就诸状态
        // 2.根据条件查询对应的纸张或页数据
        // 3.调用重新识别题卡服务、重新识别题卡信息
        // 4.根据识别结果，移除对应的关联关系并且重构题块打分点等信息

        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperScansResult {
    pub id: Uuid,
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub batch_number: Option<String>,
    pub pages: Vec<PaperScansPageInfoVo>,
    pub status: PaperScanStatus,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperScansPageInfoVo {
    pub id: Uuid,
    pub file_name: String,
    pub page_num: i32,
    pub file_size: i64,
    pub url: String,
    pub rectify_url: Option<String>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub created_at: DateTime<Utc>,
}

impl From<PaperScanPage> for PaperScansPageInfoVo {
    fn from(item: PaperScanPage) -> Self {
        Self {
            id: item.id,
            file_name: item.file_name.clone(),
            page_num: item.page_num,
            file_size: item.file_size,
            url: item.file_url.clone(),
            rectify_url: item.rectify_url.clone(),
            is_duplicate: item.is_duplicate,
            is_blank: item.is_blank,
            is_abnormal: item.is_abnormal,
            abnormal_reason: item.abnormal_reason.clone(),
            created_at: item.created_at,
        }
    }
}
