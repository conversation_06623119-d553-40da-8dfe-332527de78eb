use crate::model::subject_groups::subject_group_members::{
    BatchAddMembersParams, CreateSubjectGroupMemberParams, SubjectGroupMember,
    SubjectGroupMemberDetail, SubjectGroupMemberStats,
    UpdateSubjectGroupMemberParams,
};
use crate::utils::error::AppError;
use chrono::Utc;
use sqlx::{PgPool, Row};
use uuid::Uuid;

pub struct SubjectGroupMembersService;

impl SubjectGroupMembersService {
    /// 获取学科组成员列表
    pub async fn get_members(
        pool: &PgPool,
        schema_name: &str,
        subject_group_id: Uuid,
    ) -> Result<Vec<SubjectGroupMemberDetail>, AppError> {
        let query = format!(
            r#"
            SELECT 
                sgm.id,
                sgm.subject_group_id,
                sgm.teacher_id,
                sgm.role_code,
                r.name as role_name,
                sgm.joined_at,
                sgm.is_active,
                sgm.created_at,
                sgm.updated_at,
                t.teacher_name,
                t.employee_id,
                t.title,
                t.email,
                t.phone
            FROM {}.subject_group_members sgm
            JOIN {}.teachers t ON sgm.teacher_id = t.id
            LEFT JOIN public.roles r ON sgm.role_code = r.code
            WHERE sgm.subject_group_id = $1
            ORDER BY 
                CASE sgm.role_code 
                    WHEN 'subject_group_leader' THEN 1 
                    WHEN 'subject_group_deputy_leader' THEN 2 
                    ELSE 3 
                END,
                sgm.joined_at ASC
            "#,
            schema_name, schema_name
        );

        let members = sqlx::query_as::<_, SubjectGroupMemberDetail>(&query)
            .bind(subject_group_id)
            .fetch_all(pool)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        Ok(members)
    }

    /// 添加单个成员
    pub async fn add_member(
        pool: &PgPool,
        schema_name: &str,
        params: CreateSubjectGroupMemberParams,
    ) -> Result<SubjectGroupMember, AppError> {
        // 检查是否已经是成员
        let existing_query = format!(
            "SELECT COUNT(*) FROM {}.subject_group_members WHERE subject_group_id = $1 AND teacher_id = $2 AND is_active = true",
            schema_name
        );

        let count: i64 = sqlx::query(&existing_query)
            .bind(params.subject_group_id)
            .bind(params.teacher_id)
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::DatabaseError(e))?
            .get(0);

        if count > 0 {
            return Err(AppError::BadRequest("教师已经是该学科组成员".to_string()));
        }

        // 如果是组长角色，检查是否已有组长
        if params.role_code == "subject_group_leader" {
            let leader_query = format!(
                "SELECT COUNT(*) FROM {}.subject_group_members WHERE subject_group_id = $1 AND role_code = 'subject_group_leader' AND is_active = true",
                schema_name
            );

            let leader_count: i64 = sqlx::query(&leader_query)
                .bind(params.subject_group_id)
                .fetch_one(pool)
                .await
                .map_err(|e| AppError::DatabaseError(e))?
                .get(0);

            if leader_count > 0 {
                return Err(AppError::BadRequest("该学科组已有组长".to_string()));
            }
        }

        let now = Utc::now();
        let id = Uuid::new_v4();

        let insert_query = format!(
            r#"
            INSERT INTO {}.subject_group_members 
            (id, subject_group_id, teacher_id, role_code, joined_at, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
            "#,
            schema_name
        );

        let member = sqlx::query_as::<_, SubjectGroupMember>(&insert_query)
            .bind(id)
            .bind(params.subject_group_id)
            .bind(params.teacher_id)
            .bind(params.role_code)
            .bind(now)
            .bind(true)
            .bind(now)
            .bind(now)
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        Ok(member)
    }

    /// 批量添加成员
    pub async fn batch_add_members(
        pool: &PgPool,
        schema_name: &str,
        subject_group_id: Uuid,
        params: BatchAddMembersParams,
    ) -> Result<Vec<SubjectGroupMember>, AppError> {
        let mut tx = pool
            .begin()
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        let mut added_members = Vec::new();

        for member_item in params.members {
            // 检查是否已经是成员
            let existing_query = format!(
                "SELECT COUNT(*) FROM {}.subject_group_members WHERE subject_group_id = $1 AND teacher_id = $2 AND is_active = true",
                schema_name
            );

            let count: i64 = sqlx::query(&existing_query)
                .bind(subject_group_id)
                .bind(member_item.teacher_id)
                .fetch_one(&mut *tx)
                .await
                .map_err(|e| AppError::DatabaseError(e))?
                .get(0);

            if count > 0 {
                continue; // 跳过已存在的成员
            }

            // 如果是组长角色，检查是否已有组长
            if member_item.role_code == "subject_group_leader" {
                let leader_query = format!(
                    "SELECT COUNT(*) FROM {}.subject_group_members WHERE subject_group_id = $1 AND role_code = 'subject_group_leader' AND is_active = true",
                    schema_name
                );

                let leader_count: i64 = sqlx::query(&leader_query)
                    .bind(subject_group_id)
                    .fetch_one(&mut *tx)
                    .await
                    .map_err(|e| AppError::DatabaseError(e))?
                    .get(0);

                if leader_count > 0 {
                    continue; // 跳过，已有组长
                }
            }

            let now = Utc::now();
            let id = Uuid::new_v4();

            let insert_query = format!(
                r#"
                INSERT INTO {}.subject_group_members 
                (id, subject_group_id, teacher_id, role_code, joined_at, is_active, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
                "#,
                schema_name
            );

            let member = sqlx::query_as::<_, SubjectGroupMember>(&insert_query)
                .bind(id)
                .bind(subject_group_id)
                .bind(member_item.teacher_id)
                .bind(member_item.role_code)
                .bind(now)
                .bind(true)
                .bind(now)
                .bind(now)
                .fetch_one(&mut *tx)
                .await
                .map_err(|e| AppError::DatabaseError(e))?;

            added_members.push(member);
        }

        tx.commit()
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        Ok(added_members)
    }

    /// 移除成员
    pub async fn remove_member(
        pool: &PgPool,
        schema_name: &str,
        subject_group_id: Uuid,
        member_id: Uuid,
    ) -> Result<(), AppError> {
        let query = format!(
            "UPDATE {}.subject_group_members SET is_active = false, updated_at = $1 WHERE id = $2 AND subject_group_id = $3",
            schema_name
        );

        let result = sqlx::query(&query)
            .bind(Utc::now())
            .bind(member_id)
            .bind(subject_group_id)
            .execute(pool)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("成员不存在".to_string()));
        }

        Ok(())
    }

    /// 更新成员角色
    pub async fn update_member_role(
        pool: &PgPool,
        schema_name: &str,
        subject_group_id: Uuid,
        member_id: Uuid,
        params: UpdateSubjectGroupMemberParams,
    ) -> Result<SubjectGroupMember, AppError> {
        // 如果要设置为组长，检查是否已有组长
        if let Some(role_code) = &params.role_code {
            if role_code == "subject_group_leader" {
                let leader_query = format!(
                    "SELECT COUNT(*) FROM {}.subject_group_members WHERE subject_group_id = $1 AND role_code = 'subject_group_leader' AND is_active = true AND id != $2",
                    schema_name
                );

                let leader_count: i64 = sqlx::query(&leader_query)
                    .bind(subject_group_id)
                    .bind(member_id)
                    .fetch_one(pool)
                    .await
                    .map_err(|e| AppError::DatabaseError(e))?
                    .get(0);

                if leader_count > 0 {
                    return Err(AppError::BadRequest("该学科组已有组长".to_string()));
                }
            }
        }

        let mut query_parts = Vec::new();
        let mut bind_count = 1;

        if params.role_code.is_some() {
            query_parts.push(format!("role_code = ${}", bind_count));
            bind_count += 1;
        }

        if params.is_active.is_some() {
            query_parts.push(format!("is_active = ${}", bind_count));
            bind_count += 1;
        }

        query_parts.push(format!("updated_at = ${}", bind_count));

        let query = format!(
            "UPDATE {}.subject_group_members SET {} WHERE id = ${} AND subject_group_id = ${} RETURNING *",
            schema_name,
            query_parts.join(", "),
            bind_count + 1,
            bind_count + 2
        );

        let mut query_builder = sqlx::query_as::<_, SubjectGroupMember>(&query);

        if let Some(role_code) = params.role_code {
            query_builder = query_builder.bind(role_code);
        }

        if let Some(is_active) = params.is_active {
            query_builder = query_builder.bind(is_active);
        }

        let member = query_builder
            .bind(Utc::now())
            .bind(member_id)
            .bind(subject_group_id)
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        Ok(member)
    }

    /// 获取成员统计信息
    pub async fn get_member_stats(
        pool: &PgPool,
        schema_name: &str,
        subject_group_id: Uuid,
    ) -> Result<SubjectGroupMemberStats, AppError> {
        let query = format!(
            r#"
            SELECT 
                COUNT(*) as total_members,
                COUNT(CASE WHEN role_code = 'subject_group_leader' THEN 1 END) as leaders,
                COUNT(CASE WHEN role_code = 'subject_group_deputy_leader' THEN 1 END) as deputy_leaders,
                COUNT(CASE WHEN role_code NOT IN ('subject_group_leader', 'subject_group_deputy_leader') THEN 1 END) as regular_members,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_members,
                COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_members
            FROM {}.subject_group_members 
            WHERE subject_group_id = $1
            "#,
            schema_name
        );

        let row = sqlx::query(&query)
            .bind(subject_group_id)
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::DatabaseError(e))?;

        let stats = SubjectGroupMemberStats {
            total_members: row.get("total_members"),
            leaders: row.get("leaders"),
            deputy_leaders: row.get("deputy_leaders"),
            regular_members: row.get("regular_members"),
            active_members: row.get("active_members"),
            inactive_members: row.get("inactive_members"),
        };

        Ok(stats)
    }
}
