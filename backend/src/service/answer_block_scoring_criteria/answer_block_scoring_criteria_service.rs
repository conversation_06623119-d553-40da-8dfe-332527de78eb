use sqlx::PgPool;
use uuid::Uuid;
use crate::model::answer_block_scoring_criteria::AnswerBlockScoringCriteria;
use crate::repository::answer_block_scoring_criteria::answer_block_scoring_criteria_repository::AnswerBlockScoringCriteriaRepository;
use crate::utils::schema::connect_with_schema;
pub struct AnswerBlockScoringCriteriaService{
    pub db_pool: PgPool,
}

impl AnswerBlockScoringCriteriaService{
    pub fn new(pool: PgPool)->Self{
        Self{db_pool: pool}
    }
}
impl AnswerBlockScoringCriteriaService{
    /// 查询
    pub async fn get_by_id(&self, schema_name: &String, id: Uuid) -> Result<AnswerBlockScoringCriteria, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        match AnswerBlockScoringCriteriaRepository::get_by_id(&mut conn, id).await? {
            Some(item) => Ok(item),
            None => Err("未找到对应记录".to_string()),
        }
    }
    /// 新增
    pub async fn create(&self, schema_name: &String, new_item: &AnswerBlockScoringCriteria) -> Result<AnswerBlockScoringCriteria, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        AnswerBlockScoringCriteriaRepository::create(&mut conn, new_item).await?.map_err(|e| e.to_string())
    }

    /// 修改
    pub async fn update(&self, schema_name: &String, id: Uuid, scoring_criteria_id: Option<Uuid>, block_group_id: Option<Uuid>) -> Result<AnswerBlockScoringCriteria, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        match AnswerBlockScoringCriteriaRepository::update(&mut conn, id, scoring_criteria_id, block_group_id).await? {
            Some(item) => Ok(item),
            None => Err("未找到或未更新记录".to_string()),
        }
    }

    /// 删除
    pub async fn delete(&self, schema_name: &String, id: Uuid) -> Result<bool, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        AnswerBlockScoringCriteriaRepository::delete(&mut conn, id).await
    }

}
    

