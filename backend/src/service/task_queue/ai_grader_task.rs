use super::ocr_task::OcrTask;
use super::{Task, TaskConfig};
use crate::model::score::score_detail::{ScoreDetail, ScoreDetailStatus, ScoreReason, ScoringType};
use crate::model::score::ScoreStatus;
use crate::repository::score::{score_detail_repository, score_repository};
use bigdecimal::BigDecimal;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use sqlx::types::Json;
use tracing::log;
use uuid::Uuid;
use crate::utils::score::f64_to_big_decimal;

#[derive(Clone, Deserialize, Serialize)]
pub struct AiGraderTask {
    pub score_id: Uuid,
    pub tenant: String,
    pub grade_workflow: Uuid,
    pub answer: String,
    pub question_content: Option<String>,
    pub standard_answer: String,
    pub score: f64,
}

impl AiGraderTask {
    pub fn from_ocr_task_and_answer(ocr_task: OcrTask, answer: String) -> Self {
        Self {
            score_id: ocr_task.score_id,
            tenant: ocr_task.tenant,
            grade_workflow: ocr_task.grade_workflow,
            answer,
            question_content: ocr_task.question_content,
            standard_answer: ocr_task.standard_answer,
            score: ocr_task.score,
        }
    }
}

#[derive(Serialize, Debug)]
struct Payload {
    #[serde(rename = "type")]
    name: String,
    texts: Vec<String>,
    params: Params,
}
#[derive(Serialize, Debug)]
struct Params {
    score: f64,
    #[serde(rename = "correctAnswer")]
    standard_answer: String,
    #[serde(rename = "originalQuestion")]
    question_content: Option<String>,
}

impl Task for AiGraderTask {
    fn get_id(&self) -> (Uuid, String) {
        (self.score_id.clone(), self.tenant.clone())
    }

    // 获取OCR结果，修改内容，添加到score任务队列
    async fn execute(&mut self, config: TaskConfig) -> Result<(), String> {
        let mut score_detail = ScoreDetail::default();
        score_detail.score_id = self.score_id.clone();
        score_detail.scoring_type = Json(ScoringType::AI);
        score_detail.ocr = Some(self.answer.clone());
        let (score, status) = match self.handle(config.http_client, config.host.as_str()).await {
            Ok((reason, s)) => {
                score_detail.score = s.clone();
                score_detail.status = ScoreDetailStatus::Done;
                score_detail.reason = Json(ScoreReason::Text(reason));
                (Some(s), ScoreStatus::Done)
            },
            Err(e) => {
                score_detail.status = ScoreDetailStatus::GradingFailed;
                score_detail.reason = Json(ScoreReason::Text(e));
                (None, ScoreStatus::Excepted)
            },
        };
        score_repository::update(&config.db, self.tenant.as_str(), self.score_id, score, Some(status), None).await.map_err(|e| e.to_string())?;
        score_detail_repository::insert(&config.db, self.tenant.as_str(), score_detail).await.map_err(|e| e.to_string())?;
        Ok(())
    }
}
impl AiGraderTask {
    async fn handle(&self, client: Client, host: &str) -> Result<(String, BigDecimal), String> {
        let url = format!("{}/api/run_workflow_json/{}", host, self.grade_workflow);
        let builder = client.post(url.as_str());
        let params = Params {
            score: self.score,
            standard_answer: self.standard_answer.clone(),
            question_content: self.question_content.clone(),
        };
        let payload = Payload {
            name: "text".to_string(),
            texts: vec![self.answer.clone()],
            params,
        };
        let response = builder.json(&payload).send().await.map_err(|e| e.to_string())?;
        if !response.status().is_success() {
            return Err(response.text().await.map_err(|e| e.to_string())?);
        }
        let data = response.json::<serde_json::Value>().await.map_err(|e| e.to_string())?;
        let response_str = format!("grader response [{}]: {:#?}", self.grade_workflow, data);
        let data = data.as_object().and_then(|d| d.get("results")).and_then(|v| v.as_array()).ok_or("results error")?;
        let contents: Vec<_> = data.into_iter().filter_map(|choice| {
            let reason = choice.get("reason").and_then(|v| v.as_str()).and_then(|content| Some(content.to_string()));
            let score = choice.get("score").and_then(|v| v.as_f64());
            let timeout = choice.get("timeout_feedback").and_then(|v| v.as_bool()).unwrap_or(false);
            if timeout {
                log::error!("timeout -> {:?}", choice);
                None
            } else {
                reason.zip(score)
            }
        }).collect();
        if contents.len() != 1 {
            log::error!("response: {:#?}", data);
            Err(response_str)
        } else {
            let sss = serde_json::to_string(&payload).map_err(|e| e.to_string())?;
            log::info!("{} -> {:#?}", url, sss);
            let score = f64_to_big_decimal(contents[0].1); // 保留两位小数
            Ok((contents[0].0.clone(), score))
        }
    }
}