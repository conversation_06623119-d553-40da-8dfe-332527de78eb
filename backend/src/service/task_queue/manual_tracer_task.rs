use std::cmp::Ordering;
use std::sync::Arc;
use bigdecimal::BigDecimal;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use tracing::log;
use sqlx::types::Json;
use uuid::Uuid;
use crate::model::score::ScoreStatus;
use crate::model::score::score_detail::{ScoreDetail, ScoreDetailStatus, ScoreReason, ScoringType};
use crate::repository::score::{score_detail_repository, score_repository};
use crate::service::storage::StorageService;
use crate::utils::score::f64_to_big_decimal;
use super::{gen_pub_urls, Task, TaskConfig};

#[derive(Clone, Deserialize, Serialize)]
pub struct ManualTracerTask {
    pub score_id: Uuid,
    pub tenant: String,
    pub urls: Vec<String>,
    pub score: f64,
}
#[derive(Serialize, Debug)]
struct Payload {
    images: Vec<String>,
}
#[derive(Deserialize, Debug)]
struct ResponseData {
    ret: Vec<ImageResp>,
}
#[derive(Deserialize, Debug)]
struct ImageResp {
    ocr_results: Vec<OcrResp>
}
#[derive(Deserialize, Debug)]
struct OcrResp {
    ocr_result: String,
    #[serde(rename = "type")]
    tracer_type: TraceType,
}
#[derive(Deserialize, Debug)]
enum TraceType {
    #[serde(rename = "wrong")]
    Wrong,
    #[serde(rename = "correct")]
    Correct,
    #[serde(rename = "score")]
    Score,
}
impl Task for ManualTracerTask {
    fn get_id(&self) -> (Uuid, String) {
        (self.score_id.clone(), self.tenant.clone())
    }

    // 获取OCR结果，修改内容，添加到score任务队列
    async fn execute(&mut self, config: TaskConfig) -> Result<(), String> {
        let mut score_detail = ScoreDetail::default();
        score_detail.score_id = self.score_id.clone();
        score_detail.scoring_type = Json(ScoringType::Manual);
        let (score, status) = match self.handle(config.http_client, config.host.as_str(), config.storage.clone()).await {
            Ok(s) => {
                score_detail.score = s.clone();
                score_detail.status = ScoreDetailStatus::Done;
                (Some(s), ScoreStatus::Done)
            },
            Err(e) => {
                score_detail.status = ScoreDetailStatus::GradingFailed;
                score_detail.reason = Json(ScoreReason::Text(e));
                (None, ScoreStatus::Excepted)
            }
        };
        score_repository::update(&config.db, self.tenant.as_str(), self.score_id, score, Some(status), None).await.map_err(|e| e.to_string())?;
        score_detail_repository::insert(&config.db, self.tenant.as_str(), score_detail).await.map_err(|e| e.to_string())?;
        Ok(())
    }
}

impl ManualTracerTask {

    async fn handle(&self, client: Client, host: &str, storage: Arc<dyn StorageService>) -> Result<BigDecimal, String> {
        let url = format!("{}/Question_Card_Det", host);
        let builder = client.post(url);
        let images = gen_pub_urls(&self.urls, storage).await.map_err(|e| e.to_string())?;
        let payload = Payload { images };
        let response = builder.json(&payload).send().await.map_err(|e| e.to_string())?;
        if !response.status().is_success() {
            return Err(response.text().await.map_err(|e| e.to_string())?);
        }
        let data: ResponseData = response.json().await.map_err(|e| e.to_string())?;
        log::info!("Got response: {:?}", data);
        let mut scores: Vec<f64> = Vec::new();
        let mut tracer_count = (0, 0, 0);
        data.ret.into_iter().for_each(|image| {
            image.ocr_results.into_iter().for_each(|ocr| {
                match ocr.tracer_type {
                    TraceType::Wrong => {tracer_count.2 += 1}
                    TraceType::Correct => {tracer_count.1 += 1}
                    TraceType::Score => {
                        tracer_count.0 += 1;
                        scores.push(ocr.ocr_result.parse::<f64>().unwrap_or(0.0));
                    }
                }
            })
        });
        let score = if tracer_count.0 > 0 {
            scores.sort_by(|a, b| b.partial_cmp(a).unwrap_or(Ordering::Less));
            let mut sum = 0f64;
            for score in scores {
                if sum + score < self.score {
                    sum += score;
                }
            }
            sum
        } else {
            let total = tracer_count.2 + tracer_count.1;
            if total == 0 {
                return Err("未检测到得分痕迹".to_string())
            } else {
                self.score * tracer_count.1 as f64 / total as f64
            }
        };
        Ok(f64_to_big_decimal(score))
    }
}