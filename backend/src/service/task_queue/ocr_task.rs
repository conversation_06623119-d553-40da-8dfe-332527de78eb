use std::sync::Arc;
use bigdecimal::{BigDecimal, Zero};
use reqwest::Client;
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use sqlx::types::Json;
use tracing::{info, log};
use uuid::Uuid;
use crate::model::score::ScoreStatus;
use crate::model::score::score_detail::{ScoreDetail, ScoreDetailStatus, ScoreReason};
use crate::repository::paper_scan_block::paper_scan_block_repository::PaperScansBlockRepository;
use crate::repository::score::{score_detail_repository, score_repository};
use crate::service::storage::StorageService;
use super::ai_grader_task::AiGraderTask;
use super::{gen_pub_urls, Task, TaskConfig, REDIS_HASH_QUEUE_PREFIX, REDIS_TASK_QUEUE_PREFIX};


#[derive(Clone, Deserialize, Serialize, Debug)]
pub struct OcrTask {
    pub score_id: Uuid,
    pub tenant: String,
    pub ocr_workflow: Uuid,
    pub grade_workflow: Uuid,
    pub urls: Vec<String>,
    pub question_content: Option<String>,
    pub standard_answer: String,
    pub score: f64,
    pub scan_block_id: Option<Uuid>,
}

#[derive(Serialize, Debug)]
struct Payload {
    #[serde(rename = "type")]
    name: String,
    images: Vec<ImagePayload>,
}
impl Payload {
    pub fn new(id: Uuid, urls: Vec<String>) -> Self {
        let images = urls.into_iter().map(|url| {
            ImagePayload { id: id.clone(), url, }
        }).collect::<Vec<_>>();
        Payload {
            name: "image".to_string(),
            images,
        }
    }
}
#[derive(Serialize, Debug)]
struct ImagePayload {
    id: Uuid,
    url: String,
}
impl Task for OcrTask {
    fn get_id(&self) -> (Uuid, String) {
        (self.score_id.clone(), self.tenant.clone())
    }

    // 获取OCR结果，修改内容，添加到score任务队列
    async fn execute(&mut self, config: TaskConfig) -> Result<(), String> {
        let mut score_detail = ScoreDetail {
            ..ScoreDetail::default()
        };
        match self.handle(config.http_client, config.host.as_str(), config.storage.clone()).await {
            Ok(content) => {
                info!("Task got content[{}]: {:?}", content.len(), content);
                // 写入content到数据库scan_block
                if let Some(bid) = self.scan_block_id {
                    PaperScansBlockRepository::update(&config.db, self.tenant.as_str(), bid, content.clone())
                        .await.ok();
                }

                if content.is_empty() {
                    score_detail.status = ScoreDetailStatus::Done;
                    score_detail.reason = Json(ScoreReason::Blank);
                    score_repository::update(&config.db, self.tenant.as_str(), self.score_id, Some(BigDecimal::zero()), Some(ScoreStatus::Done), None).await.map_err(|e| e.to_string())?;
                    score_detail_repository::insert(&config.db, self.tenant.as_str(), score_detail).await.map_err(|e| e.to_string())?;
                } else {
                    let grader_task = AiGraderTask::from_ocr_task_and_answer(self.clone(), content);
                    let queue_key = format!("{}{}{}", REDIS_TASK_QUEUE_PREFIX, super::GRADER_KEY, self.tenant);
                    let hash_key = format!("{}{}", REDIS_HASH_QUEUE_PREFIX, super::GRADER_KEY);
                    let task_json = serde_json::to_string(&grader_task).map_err(|e| e.to_string())?;
                    let mut conn = config.redis_pool.get().await.map_err(|e| e.to_string())?;
                    conn.rpush::<_, _, ()>(queue_key, self.score_id.to_string()).await.map_err(|e| e.to_string())?;
                    conn.hset::<_, _, _, ()>(hash_key, self.score_id.to_string(), task_json).await.map_err(|e| e.to_string())?;
                }
            }
            Err(e) => {
                score_detail.status = ScoreDetailStatus::OcrFailed;
                score_detail.reason = Json(ScoreReason::Text(e));
                score_repository::update(&config.db, self.tenant.as_str(), self.score_id, None, Some(ScoreStatus::Excepted), None).await.map_err(|e| e.to_string())?;
                score_detail_repository::insert(&config.db, self.tenant.as_str(), score_detail).await.map_err(|e| e.to_string())?;
            }
        }
        Ok(())
    }
}

impl OcrTask {
    async fn handle(&mut self, client: Client, host: &str, storage: Arc<dyn StorageService>) -> Result<String, String> {
        let url = format!("{}/api/run_workflow_json/{}", host, self.ocr_workflow);
        let builder = client.post(url.as_str());
        let urls = gen_pub_urls(&self.urls, storage).await.map_err(|e| e.to_string())?;
        let payload = Payload::new(self.score_id.clone(), urls);
        log::info!("{}", serde_json::to_string(&payload).unwrap());
        let response = builder.json(&payload).send().await.map_err(|e| e.to_string())?;
        if !response.status().is_success() {
            return Err(response.text().await.map_err(|e| e.to_string())?);
        }
        let data = response.json::<serde_json::Value>().await.map_err(|e| e.to_string())?;
        log::info!("{:?}", data);
        let data = data.as_object().and_then(|d| d.get("results")).and_then(|v| v.as_array()).ok_or("results error")?;
        let contents: Vec<_> = data.into_iter().map(|choice| {
            let timeout = choice.get("timeout_feedback").and_then(|v| v.as_bool()).unwrap_or(false);
            let content = choice.get("content").and_then(|v| v.as_str()).unwrap_or("");
            (content, timeout)
        }).collect();
        let (content, timeout) = contents.iter().fold((String::new(), false), |mut acc, &s| {
            acc.0.push_str(s.0);
            acc.1 = acc.1 || s.1;
            acc
        });
        log::info!("{} -> {}", url, content);
        if !timeout {
            Ok(content)
        } else {
            Err("timeout reached".to_owned())
        }
    }
}