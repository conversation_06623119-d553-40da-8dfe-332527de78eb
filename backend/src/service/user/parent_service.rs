use crate::model::user::auth::*;
use crate::repository::user::parent::ParentRepository;
use chrono::{DateTime, Utc};
use sqlx::PgPool;
use std::sync::Arc;
use tracing::info;
use uuid::Uuid;

pub struct ParentService {
    db: PgPool,
    repository: Arc<ParentRepository>,
}

impl ParentService {
    pub fn new(db: PgPool, repository: Arc<ParentRepository>) -> Self {
        Self { db, repository }
    }

    pub async fn link_student(
        &self,
        parent_user_id: Uuid,
        request: LinkStudentRequest,
    ) -> AuthResult<LinkStudentResponse> {
        // Find student by phone number
        let student_user = self.repository.find_user_by_phone(&request.student_phone_number).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?
            .ok_or(AuthError::UserNotFound)?;

        // Get tenant schema name
        let schema_name = self.repository.get_tenant_schema(request.tenant_id).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?
            .ok_or(AuthError::IdentityNotFound)?;

        // Find student's identity in the specified tenant
        let student_identity = self.repository.find_student_identity(
            student_user.id,
            &schema_name,
        ).await
        .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?
        .ok_or(AuthError::IdentityNotFound)?;

        // Check if relationship already exists
        if self.repository.relationship_exists(parent_user_id, student_user.id, request.tenant_id).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))? {
            return Err(AuthError::IdentityAlreadyBound);
        }

        // Create parent-student relationship
        let relation_id = Uuid::new_v4();

        // Default access permissions for parents
        let default_permissions = serde_json::json!({
            "view_grades": true,
            "view_attendance": true,
            "receive_notifications": true,
            "contact_teachers": false,
            "view_homework": true,
            "view_exam_results": true
        });

        self.repository.create_parent_student_relation(
            relation_id,
            parent_user_id,
            student_user.id,
            request.tenant_id,
            student_identity.id,
            &request.relationship_type,
            &request.verification_method,
            request.additional_info,
            default_permissions,
        ).await
        .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        // Create verification steps based on method
        let verification_steps = self.create_verification_steps(&request.verification_method);

        Ok(LinkStudentResponse {
            success: true,
            message: "Student link request submitted".to_string(),
            data: LinkStudentData {
                relation_id,
                verification_status: "pending".to_string(),
                verification_steps,
            },
        })
    }

    pub async fn get_linked_students(&self, parent_user_id: Uuid) -> AuthResult<LinkedStudentsResponse> {
        let students_data = self.repository.get_linked_students(parent_user_id).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        let students: Vec<LinkedStudent> = students_data
            .into_iter()
            .map(|data| LinkedStudent {
                relation_id: data.relation_id,
                student_user_id: data.student_user_id,
                student_name: data.student_name,
                tenant_id: data.tenant_id,
                tenant_name: data.tenant_name,
                relationship_type: data.relationship_type,
                verification_status: data.verification_status,
                access_permissions: data.access_permissions,
                verified_at: data.verified_at,
            })
            .collect();

        Ok(LinkedStudentsResponse {
            success: true,
            data: LinkedStudentsData { students },
        })
    }

    pub async fn verify_parent_student_relationship(
        &self,
        relation_id: Uuid,
        verified_by: Uuid,
        verification_method: &str,
    ) -> AuthResult<()> {
        self.repository.verify_parent_student_relationship(
            relation_id,
            verified_by,
            verification_method,
        ).await
        .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        Ok(())
    }

    pub async fn update_access_permissions(
        &self,
        relation_id: Uuid,
        permissions: serde_json::Value,
    ) -> AuthResult<()> {
        self.repository.update_access_permissions(relation_id, permissions).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        Ok(())
    }

    pub async fn get_parent_permissions(
        &self,
        parent_user_id: Uuid,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> AuthResult<Option<serde_json::Value>> {
        let permissions = self.repository.get_parent_permissions(
            parent_user_id,
            student_user_id,
            tenant_id,
        ).await
        .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        Ok(permissions)
    }

    pub async fn deactivate_relationship(&self, relation_id: Uuid) -> AuthResult<()> {
        self.repository.deactivate_relationship(relation_id).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        Ok(())
    }

    pub async fn get_student_parents(
        &self,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> AuthResult<Vec<ParentInfo>> {
        let parents_data = self.repository.get_student_parents(student_user_id, tenant_id).await
            .map_err(|e| AuthError::DatabaseError(sqlx::Error::Protocol(e.to_string())))?;

        let parent_info: Vec<ParentInfo> = parents_data
            .into_iter()
            .map(|data| ParentInfo {
                relation_id: data.relation_id,
                parent_user_id: data.parent_user_id,
                parent_phone: data.parent_phone,
                relationship_type: data.relationship_type,
                verification_status: data.verification_status,
                verified_at: data.verified_at,
            })
            .collect();

        Ok(parent_info)
    }



    fn create_verification_steps(&self, verification_method: &str) -> Vec<VerificationStep> {
        match verification_method {
            "phone_verification" => vec![
                VerificationStep {
                    step: "student_confirmation".to_string(),
                    description: "Student needs to confirm the relationship".to_string(),
                    status: "pending".to_string(),
                },
                VerificationStep {
                    step: "phone_verification".to_string(),
                    description: "Phone number verification required".to_string(),
                    status: "waiting".to_string(),
                },
            ],
            "admin_approval" => vec![
                VerificationStep {
                    step: "admin_review".to_string(),
                    description: "School administrator approval required".to_string(),
                    status: "pending".to_string(),
                },
            ],
            "document_upload" => vec![
                VerificationStep {
                    step: "document_upload".to_string(),
                    description: "Upload relationship verification documents".to_string(),
                    status: "pending".to_string(),
                },
                VerificationStep {
                    step: "document_review".to_string(),
                    description: "Document review by school administrator".to_string(),
                    status: "waiting".to_string(),
                },
            ],
            _ => vec![
                VerificationStep {
                    step: "manual_verification".to_string(),
                    description: "Manual verification required".to_string(),
                    status: "pending".to_string(),
                },
            ],
        }
    }
}

#[derive(Debug, serde::Serialize)]
pub struct ParentInfo {
    pub relation_id: Uuid,
    pub parent_user_id: Uuid,
    pub parent_phone: String,
    pub relationship_type: String,
    pub verification_status: String,
    pub verified_at: Option<DateTime<Utc>>,
}