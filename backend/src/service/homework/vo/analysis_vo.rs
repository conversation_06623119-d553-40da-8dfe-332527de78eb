use std::collections::HashMap;
use serde::Serialize;
use crate::model::paper::paper_cache::{ScoringCriteriaTypeEnum, ScoringCriterion};
use crate::repository::students::student_repository::StudentBase;
use crate::service::grading::vo::score_vo::{split_score_vos_by_status, ScoreVo};

#[derive(Debug, Serialize)]
pub struct ClassAnswerAnalysisVo {
    pub done_students: Vec<StudentBase>,
    pub error_students: Vec<StudentBase>,
    pub unsubmitted_students: Vec<StudentBase>,
    pub content_answer_analysis_list: Vec<ContentAnswerAnalysis>
}
#[derive(Debug, Serialize)]
pub enum ContentAnswerAnalysis {
    Text(String),
    AnswerAnalysis(AnswerAnalysis),
}
#[derive(Debug, Serialize)]
pub struct AnswerAnalysis {
    criterion: ScoringCriterion,
    error_scored: Vec<ScoreVo>,
    pending_scored: Vec<ScoreVo>,
    avg_score: Option<f32>,
    scored_map: HashMap<String, Vec<ScoreVo>>,
}

impl AnswerAnalysis {
    pub fn blank_criterion(criterion: ScoringCriterion) -> AnswerAnalysis {
        AnswerAnalysis {
            criterion,
            error_scored: vec![],
            pending_scored: vec![],
            avg_score: None,
            scored_map: Default::default(),
        }
    }
    
    pub fn from_scores(criterion: ScoringCriterion, scores: Vec<ScoreVo>) -> AnswerAnalysis {
        let (scored, error_scored, pending_scored) = split_score_vos_by_status(scores);
        let mut total_score = 0f32;
        let scored_count = scored.len();
        let mut scored_map: HashMap<String, Vec<ScoreVo>> = HashMap::new();
        match criterion.scoring_type {
            ScoringCriteriaTypeEnum::Match => {
                scored.into_iter().for_each(|s| {
                    total_score += s.score;
                    scored_map.entry(s.answer.clone()).or_insert(vec![]).push(s);
                });
            }
            ScoringCriteriaTypeEnum::AI | ScoringCriteriaTypeEnum::Manual => {
                scored.into_iter().for_each(|s| {
                    total_score += s.score;
                    scored_map.entry(format!("{:.1}", s.score)).or_insert(vec![]).push(s);
                });
            }
        }
        let avg_score = if scored_count > 0 {
            Some(total_score / scored_count as f32)
        } else { None };
        AnswerAnalysis {
            criterion,
            error_scored,
            pending_scored,
            avg_score,
            scored_map,
        }
    }
}