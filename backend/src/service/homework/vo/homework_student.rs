use crate::model::homework_students::homework_students::HomeworkStudentStatus;
use crate::repository::homework_students::homework_students_repository::HomeworkStudentBase;
use crate::repository::students::student_repository::StudentBase;

pub fn homework_students_to_student_vos(homework_students: Vec<HomeworkStudentBase>) -> anyhow::Result<(Vec<StudentBase>, Vec<StudentBase>, Vec<StudentBase>)> {
    let mut unsubmitted_students = Vec::new();
    let mut error_students = Vec::new();
    let mut done_students = Vec::new();
    homework_students.into_iter().for_each(|s| {
        let student = StudentBase {
            student_id: s.student_id,
            student_number: s.student_number,
            student_name: s.student_name,
        };
        match s.status {
            HomeworkStudentStatus::Unsubmitted => {unsubmitted_students.push(student);},
            HomeworkStudentStatus::Error => {error_students.push(student);},
            HomeworkStudentStatus::Done => {done_students.push(student);},
        }
    });
    Ok((done_students, error_students, unsubmitted_students))
}