use crate::model::classes::classes::ClassesSummary;
use crate::model::homework_students::homework_students::HomeworkStudentStatus;
use crate::model::paper::paper_cache::ScoringCriteria;
use crate::model::score::{Score, ScoreStatus};
use crate::repository::homework_students::homework_students_repository::{HomeworkStudentBase, HomeworkStudentsRepository};
use crate::repository::score::score_repository;
use crate::repository::score::score_repository::fetch_all_scores;
use crate::repository::students::class_repository::fetch_teaching_classes_summary;
use bigdecimal::ToPrimitive;
use serde::Serialize;
use sqlx::PgPool;
use std::collections::HashMap;
use uuid::Uuid;
use crate::service::homework::vo::AnalysisSummary;

pub struct HomeworkAnalysis {
    db: PgPool,
    tenant_name: String,
}

impl HomeworkAnalysis {
    pub fn new(db: PgPool, tenant_name: String) -> HomeworkAnalysis {
        HomeworkAnalysis { db, tenant_name }
    }

    pub async fn classes_summary(&self, homework_id: Uuid, criteria_list: Vec<ScoringCriteria>) -> anyhow::Result<ClassSummaryResponse> {
        let students = HomeworkStudentsRepository::fetch_homework_students_by_homework_class(&self.db, self.tenant_name.as_str(), homework_id, None).await?;
        // info!("students: {:?}", students);
        let mut student_class_map = HashMap::new();
        students.into_iter().for_each(|student| {
            student_class_map.entry(student.class_id).or_insert_with(Vec::new).push(student);
        });
        let mut total_score = 0f64;
        let criteria_ids: Vec<_> = criteria_list
            .iter()
            .map(|c| {
                total_score += c.score;
                c.id.clone()
            })
            .collect();

        let mut summary = AnalysisSummary::default();
        summary.total_score = total_score;
        let mut criteria_score_map: HashMap<Uuid, (i64, f64)> = HashMap::new();
        let mut class_vos = Vec::new();
        for (k, v) in student_class_map {
            let mut class_vo = ClassAnalysisVo::default();
            class_vo.class = fetch_teaching_classes_summary(&self.db, self.tenant_name.as_str(), k).await?;
            class_vo.total_score = total_score;
            let mut student_ids = Vec::new();
            v.iter().for_each(|student| match student.status {
                HomeworkStudentStatus::Unsubmitted => {
                    class_vo.absent_student_count += 1;
                }
                HomeworkStudentStatus::Error => {
                    class_vo.error_student_count += 1;
                }
                HomeworkStudentStatus::Done => {
                    student_ids.push(student.student_id);
                    class_vo.scoring_student_count += 1;
                }
            });
            summary.absent_student_count += class_vo.absent_student_count;
            summary.error_student_count += class_vo.error_student_count;
            summary.scoring_student_count += class_vo.scoring_student_count;
            let criteria_summaries = score_repository::fetch_criteria_summary(&self.db, self.tenant_name.as_str(), Some(criteria_ids.clone()), Some(student_ids)).await?;
            let mut criteria_summary_map: HashMap<Uuid, (i64, i64, f64)> = HashMap::new();
            criteria_summaries.into_iter().for_each(|summary| {
                let ss = match summary.status {
                    ScoreStatus::Done|ScoreStatus::CheckedCorrect|ScoreStatus::CheckedError => {
                        let score = summary.total_score.to_f64().unwrap_or(0.0);
                        (summary.count, summary.count, score)
                    },
                    _ => (summary.count, 0, 0f64)
                };
                criteria_summary_map
                    .entry(summary.criteria_id)
                    .and_modify(|value| {
                        value.0 += ss.0;
                        value.1 += ss.1;
                        value.2 += ss.2;
                    })
                    .or_insert(ss);
            });
            for (sk, sv) in criteria_summary_map {
                class_vo.total_score_count += sv.0;
                class_vo.done_score_count += sv.1;
                if sv.1 > 0 {
                    class_vo.avg_score += sv.2 / sv.1 as f64;
                }
                // 计算各个小题对应的总分和数量，用于求平均分
                criteria_score_map
                    .entry(sk)
                    .and_modify(|value| {
                        value.0 += sv.1;
                        value.1 += sv.2;
                    })
                    .or_insert((sv.1, sv.2));
            }
            summary.total_score_count += class_vo.total_score_count;
            summary.done_score_count += class_vo.done_score_count;
            class_vos.push(class_vo);
        }
        for (_k, v) in criteria_score_map {
            if v.0 > 0 {
                summary.avg_score += v.1 / v.0 as f64;
            }
        }
        Ok(ClassSummaryResponse { summary, details: class_vos })
    }
    pub async fn classes_detail(&self, homework_id: Uuid, class_id: Uuid, criteria_list: Vec<ScoringCriteria>) -> anyhow::Result<ClassDetailResponse> {
        let mut total_score = 0f64;
        let criteria_ids: Vec<_> = criteria_list
            .iter()
            .map(|c| {
                total_score += c.score;
                c.id.clone()
            })
            .collect();
        let mut summary = AnalysisSummary::default();
        summary.total_score = total_score;
        let mut sum_stu_score = 0f64;

        let students = HomeworkStudentsRepository::fetch_students_by_homework_class(&self.db, self.tenant_name.as_str(), homework_id, Some(class_id)).await?;
        let mut student_scores = Vec::new();
        for student in students {
            let scores = match student.status {
                HomeworkStudentStatus::Done => {
                    summary.scoring_student_count += 1;
                    let scores = fetch_all_scores(&self.db, self.tenant_name.as_str(), Some(criteria_ids.clone()), Some(student.student_id)).await?;
                    let stu_score = scores.iter().fold(0f64, |acc, s| match s.score_status {
                        ScoreStatus::Done|ScoreStatus::CheckedCorrect|ScoreStatus::CheckedError => acc + s.score.to_f64().unwrap_or(0f64),
                        _ => acc,
                    });
                    sum_stu_score += stu_score;
                    Some(scores)
                }
                HomeworkStudentStatus::Error => {
                    summary.error_student_count += 1;
                    None
                }
                HomeworkStudentStatus::Unsubmitted => {
                    summary.absent_student_count += 1;
                    None
                }
            };
            student_scores.push(HomeworkStudentScoresVo { student, scores });
        }
        summary.avg_score = if summary.scoring_student_count > 0 {
            sum_stu_score / summary.scoring_student_count as f64
        } else {
            0f64
        };
        Ok(ClassDetailResponse {
            summary,
            criteria_list,
            student_scores,
        })
    }
}
#[derive(Serialize)]
pub struct ClassSummaryResponse {
    pub summary: AnalysisSummary,
    pub details: Vec<ClassAnalysisVo>,
}

#[derive(Serialize)]
pub struct ClassDetailResponse {
    pub summary: AnalysisSummary,
    criteria_list: Vec<ScoringCriteria>,
    student_scores: Vec<HomeworkStudentScoresVo>,
}

#[derive(Serialize)]
pub struct HomeworkStudentScoresVo {
    pub student: HomeworkStudentBase,
    pub scores: Option<Vec<Score>>,
}
#[derive(Serialize, Default)]
pub struct ClassAnalysisVo {
    class: ClassesSummary,
    scoring_student_count: i64,
    absent_student_count: i64,
    error_student_count: i64,
    total_score_count: i64,
    done_score_count: i64,
    avg_score: f64,
    total_score: f64,
}
