use crate::model::grading::paper_scans::PaperScanStatus;
use crate::model::homework_students::homework_students::HomeworkStudentStatus;
use crate::model::score::ScoreStatus;
use crate::model::PageParams;
use crate::repository::paper_scans::paper_scans_repository::PaperScansRepository;
use crate::repository::score::score_repository;
use crate::service::homework::vo::HomeworkSummary;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::service::permission::casbin_service::CasbinPermissionService;
use crate::service::permission::data_filter::{DataFilterManager, FilterContext};
use crate::{
    middleware::auth_middleware::AuthContext,
    model::{
        homework::homework::{CreateHomeworkParams, Homework, HomeworkStatistics, PageHomeworkParams, UpdateHomeworkParams, HOMEWORK_STATUS_DONE, HOMEWORK_STATUS_DRAFT},
        Student, StudentBaseInfo,
    },
    repository::{homework::homework_repository::HomeworkRepository, homework_students::homework_students_repository::HomeworkStudentsRepository},
    utils::schema::connect_with_schema,
};
use anyhow::Result as AnyhowResult;
use bigdecimal::ToPrimitive;
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, PgPool, Result};
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

#[derive(Clone)]
pub struct HomeworkService {
    db_pool: PgPool,
    data_filter_manager: Option<Arc<DataFilterManager>>,
    casbin_service: Option<Arc<dyn CasbinPermissionService>>,
}

impl HomeworkService {
    pub fn new(db_pool: PgPool) -> Self {
        // 创建默认的数据过滤器管理器
        let data_filter_manager = Arc::new(DataFilterManager::create_default(db_pool.clone()));

        // 创建 MenuService 用于初始化 CasbinService
        let menu_service = Arc::new(crate::service::menu::MenuService::new(db_pool.clone()));

        // 创建 CasbinService（使用默认模型路径）
        let casbin_service = tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async { crate::service::permission::MultiTenantCasbinService::new(db_pool.clone(), "config/rbac_model.conf".to_string(), menu_service).await })
        });

        match casbin_service {
            Ok(service) => {
                // 使用 new_with_permission_filter 创建完整的服务实例
                Self::new_with_permission_filter(db_pool, data_filter_manager, Arc::new(service))
            }
            Err(_) => {
                // 如果 CasbinService 初始化失败，回退到基础版本
                Self {
                    db_pool,
                    data_filter_manager: Some(data_filter_manager),
                    casbin_service: None,
                }
            }
        }
    }

    pub fn new_with_permission_filter(db_pool: PgPool, data_filter_manager: Arc<DataFilterManager>, casbin_service: Arc<dyn CasbinPermissionService>) -> Self {
        Self {
            db_pool,
            data_filter_manager: Some(data_filter_manager),
            casbin_service: Some(casbin_service),
        }
    }

    /**
     * 检查用户是否为租户管理员或超级管理员
     * 这些角色应该有权访问所有数据（角色继承）
     */
    fn has_admin_privileges(&self, context: &AuthContext, schema_name: &str) -> bool {
        // 检查是否为超级管理员（跨租户权限）
        if context.is_super_admin() {
            info!("User {} is super admin, granting full access", context.user_id);
            return true;
        }

        // 检查是否为当前租户的租户管理员
        if context.has_role("tenant_admin") {
            // 进一步检查是否在当前schema/租户中有租户管理员角色
            let has_tenant_admin_in_schema = context.roles
                .iter()
                .any(|role| {
                    role.identity_type.contains("tenant_admin")
                    && role.is_verified
                    && role.schema_name == *schema_name
                });

            if has_tenant_admin_in_schema {
                info!("User {} is tenant admin in schema {}, granting full access", context.user_id, schema_name);
                return true;
            }
        }

        false
    }

    /**
     * 获取用户可访问的作业ID列表
     * 集成权限过滤逻辑和角色继承
     */
    async fn get_accessible_homework_ids(&self, context: &AuthContext, schema_name: &str) -> Result<Vec<Uuid>, String> {
        // 检查用户是否为租户管理员或超级管理员（角色继承）
        if self.has_admin_privileges(context, schema_name) {
            info!("User {} has admin privileges, returning all homework", context.user_id);
            let query = format!("SELECT id FROM {}.homework", schema_name);
            let homework_ids: Vec<Uuid> = sqlx::query_scalar(&query).fetch_all(&self.db_pool).await.map_err(|e| format!("Database error: {}", e))?;
            return Ok(homework_ids);
        }

        // 如果没有配置权限过滤器，返回空列表（表示无权限）
        let (filter_manager, casbin_service) = match (&self.data_filter_manager, &self.casbin_service) {
            (Some(fm), Some(cs)) => (fm, cs),
            _ => {
                warn!("Permission filtering not configured for homework service");
                return Ok(vec![]);
            }
        };

        // 构建过滤上下文
        let filter_context = FilterContext {
            user_id: context.user_id,
            user_identity: context.username.clone(),
            tenant_id: context.get_default_tenant_id(),
            schema_name: schema_name.to_string(),
            resource: "homework".to_string(),
            action: "read".to_string(),
        };

        // 使用数据过滤器构建查询
        let mut query_builder = sqlx::QueryBuilder::new("SELECT h.id FROM ");
        query_builder.push(schema_name);
        query_builder.push(".homework h WHERE 1=1");

        let mut count_builder = sqlx::QueryBuilder::new("SELECT COUNT(*) FROM ");
        count_builder.push(schema_name);
        count_builder.push(".homework h WHERE 1=1");

        // 应用权限过滤
        let filter_applied = filter_manager
            .apply_data_filter(&filter_context, &mut query_builder, &mut count_builder, casbin_service.as_ref(), "h")
            .await
            .map_err(|e| {
                error!("Failed to apply homework permission filter: {}", e);
                format!("Permission filter error: {}", e)
            })?;

        if !filter_applied {
            // 如果没有应用过滤器，说明用户有全部权限
            info!("User {} has full homework access", context.user_id);
            let query = format!("SELECT id FROM {}.homework", schema_name);
            let homework_ids: Vec<Uuid> = sqlx::query_scalar(&query).fetch_all(&self.db_pool).await.map_err(|e| format!("Database error: {}", e))?;
            return Ok(homework_ids);
        }

        // 执行过滤后的查询
        let homework_ids: Vec<Uuid> = query_builder.build_query_scalar().fetch_all(&self.db_pool).await.map_err(|e| {
            error!("Failed to execute filtered homework query: {}", e);
            format!("Database error: {}", e)
        })?;

        Ok(homework_ids)
    }
}

impl HomeworkService {
    /**
     * 作者：张瀚
     * 说明：获取作业管理统计数据
     */
    pub async fn get_statistics(&self, schema_name: &String) -> Result<HomeworkStatistics, String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name).await.map_err(|e| e.to_string())?;
        //查询能看的作业列表
        let list = sqlx::query_as::<_, Homework>("SELECT * FROM homework h order by h.created_at desc")
            .fetch_all(&mut *conn)
            .await
            .map_err(|e| e.to_string())?;

        //统计
        let total_homeworks = list.len() as i32;
        let mut completed_homeworks = 0;
        let mut in_progress_homeworks = 0;
        let mut total_students = 0;
        let mut homework_id_list: Vec<Uuid> = vec![];
        for ele in list {
            if ele.homework_status == HOMEWORK_STATUS_DONE {
                completed_homeworks += 1;
            } else if ele.homework_status != HOMEWORK_STATUS_DRAFT {
                //非完成和草稿的都是进行中
                in_progress_homeworks += 1;
            }
            homework_id_list.push(ele.id);
        }
        if homework_id_list.len() > 0 {
            let mut builder = sqlx::QueryBuilder::new("select distinct hs.student_id from homework_students hs where hs.homework_id in ( ");
            for (i, id) in homework_id_list.iter().enumerate() {
                builder.push_bind(id);
                if i < homework_id_list.len() - 1 {
                    builder.push(" , ");
                }
            }
            let student_id_list: Vec<Uuid> = builder.push(" )").build_query_scalar().fetch_all(&mut *conn).await.map_err(|e| e.to_string())?;
            total_students = student_id_list.len() as i32;
        }
        Ok(HomeworkStatistics {
            total_homeworks,
            completed_homeworks,
            in_progress_homeworks,
            total_students,
        })
    }

    pub async fn create_homework(&self, _context: &AuthContext, schema_name: &String, params: &CreateHomeworkParams) -> Result<Homework, String> {
        let CreateHomeworkParams {
            homework_name,
            homework_status,
            subject_group_id,
            description,
        } = params;
        let bean = sqlx::query_as::<_, Homework>(&format!(
            "INSERT INTO {}.homework (homework_name, homework_status, subject_group_id, description ) VALUES ($1, $2, $3, $4) RETURNING *",
            schema_name
        ))
        .bind(homework_name)
        .bind(homework_status)
        .bind(subject_group_id)
        .bind(description)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())?;
        //TODO:创建关联关系
        Ok(bean)
    }

    pub async fn update_homework(&self, _context: &AuthContext, schema_name: &String, params: &UpdateHomeworkParams) -> Result<Homework, String> {
        let UpdateHomeworkParams {
            id,
            homework_name,
            homework_status,
            subject_group_id,
            description,
        } = params;
        sqlx::query_as::<_, Homework>(&format!(
            "UPDATE {}.homework SET homework_name = $1, homework_status = $2, subject_group_id = $3, description = $4, updated_at = NOW() WHERE ID = $5 RETURNING *",
            schema_name
        ))
        .bind(homework_name)
        .bind(homework_status)
        .bind(subject_group_id)
        .bind(description)
        .bind(id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：分页查询作业
     */
    pub async fn page_homework(&self, context: &AuthContext, schema_name: &String, params: &PageHomeworkParams) -> Result<(Vec<PageHomeworkResult>, i64), String> {
        let PageHomeworkParams {
            page_params,
            name,
            status,
            subject_group_id,
        } = params;
        let mut conn = connect_with_schema(&self.db_pool, &schema_name).await.map_err(|e| e.to_string())?;

        // 应用权限过滤获取可访问的作业ID列表
        let accessible_homework_ids = self.get_accessible_homework_ids(context, schema_name).await?;

        info!("User {} can access {} homework items", context.user_id, accessible_homework_ids.len());

        let (list, total) = HomeworkRepository::page_all_homework(&mut conn, page_params, Some(accessible_homework_ids), name, status, subject_group_id)
            .await
            .map_err(|e| e.to_string())?;
        //联查学生信息
        let mut new_list: Vec<PageHomeworkResult> = vec![];
        for (_, homework) in list.into_iter().enumerate() {
            //查询学生列表
            let student_list = HomeworkStudentsRepository::find_student_list_by_homework_id(&mut conn, &homework.id).await.map_err(|e| e.to_string())?;
            new_list.push(PageHomeworkResult {
                homework,
                student_list: student_list
                    .into_iter()
                    .map(|student| {
                        StudentBaseInfo {
                            id: student.id,
                            student_number: student.student_number,
                            student_name: student.student_name,
                            gender: student.gender,
                            phone: student.phone,
                            administrative_class_id: student.administrative_class_id,
                            user_id: student.user_id,
                            status: student.status,
                            created_at: student.created_at,
                            updated_at: student.updated_at,
                        }
                    })
                    .collect(),
            });
        }
        Ok((new_list, total))
    }

    /**
     * 说明：分页查询所有作业
     * 直接在数据库层面应用权限过滤，性能更优
     * 支持角色继承（租户管理员和超级管理员可访问所有数据）
     */
    pub async fn page_all_homework_with_data_filter(
        &self,
        context: &AuthContext,
        schema_name: &String,
        page_params: &PageParams,
        name: &Option<String>,
        status: &Option<String>,
        subject_group_id: &Option<Uuid>,
    ) -> Result<(Vec<PageHomeworkResult>, i64), String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name).await.map_err(|e| e.to_string())?;

        // 检查用户是否为租户管理员或超级管理员（角色继承）
        if self.has_admin_privileges(context, schema_name) {
            info!("User {} has admin privileges, returning all homework with pagination", context.user_id);

            // 直接查询所有作业，不应用权限过滤
            let (list, total) = HomeworkRepository::page_all_homework(&mut conn, page_params, None, name, status, subject_group_id)
                .await
                .map_err(|e| e.to_string())?;

            // 联查学生信息
            let mut new_list: Vec<PageHomeworkResult> = vec![];
            for (_, homework) in list.into_iter().enumerate() {
                let student_list = HomeworkStudentsRepository::find_student_list_by_homework_id(&mut conn, &homework.id).await.map_err(|e| e.to_string())?;
                new_list.push(PageHomeworkResult {
                    homework,
                    student_list: student_list
                        .into_iter()
                        .map(|student| {
                            StudentBaseInfo {
                                id: student.id,
                                student_number: student.student_number,
                                student_name: student.student_name,
                                gender: student.gender,
                                phone: student.phone,
                                administrative_class_id: student.administrative_class_id,
                                user_id: student.user_id,
                                status: student.status,
                                created_at: student.created_at,
                                updated_at: student.updated_at,
                            }
                        })
                        .collect(),
                });
            }
            return Ok((new_list, total));
        }

        // 检查权限过滤器是否可用
        let (filter_manager, casbin_service) = match (&self.data_filter_manager, &self.casbin_service) {
            (Some(fm), Some(cs)) => (fm, cs),
            _ => {
                warn!("Permission filtering not configured, return empty data");
                return Ok((vec![],0));
            }
        };

        // 获取用户在当前schema下的所有角色
        let user_roles: Vec<String> = context.roles
            .iter()
            .filter(|role| role.schema_name == *schema_name && role.is_verified)
            .map(|role| role.identity_type.clone())
            .collect();

        if user_roles.is_empty() {
            info!("No roles found for user: {} in tenant: {}", context.username, schema_name);
            return Ok((vec![],0));
        }

        // 构建过滤上下文
        let filter_context = FilterContext {
            user_id: context.user_id,
            user_identity: user_roles.join(","),
            tenant_id: context.get_default_tenant_id(),
            schema_name: schema_name.clone(),
            resource: "homework".to_string(),
            action: "read".to_string(),
        };

        info!("Applying data filter for user {} in schema {}", context.user_id, schema_name);

        // 直接使用带过滤器的repository方法
        let (list, total) = HomeworkRepository::page_all_homework_with_filter(
            &mut conn,
            page_params,
            name,
            status,
            subject_group_id,
            &filter_context,
            filter_manager.as_ref(),
            casbin_service.as_ref(),
        )
        .await?;

        info!("Retrieved {} homework items for user {}", list.len(), context.user_id);

        // 联查学生信息
        let mut new_list: Vec<PageHomeworkResult> = vec![];
        for homework in list.into_iter() {
            let student_list = HomeworkStudentsRepository::find_student_list_by_homework_id(&mut conn, &homework.id).await.map_err(|e| e.to_string())?;

            new_list.push(PageHomeworkResult {
                homework,
                student_list: student_list
                    .into_iter()
                    .map(|student| {
                        StudentBaseInfo {
                            id: student.id,
                            student_number: student.student_number,
                            student_name: student.student_name,
                            gender: student.gender,
                            phone: student.phone,
                            administrative_class_id: student.administrative_class_id,
                            user_id: student.user_id,
                            status: student.status,
                            created_at: student.created_at,
                        }
                    })
                    .collect(),
            });
        }

        Ok((new_list, total))
    }

    /**
     * 作者：朱若彪
     * 说明：根据id查询作业
     */
    pub async fn get_homework_by_id(&self, _context: &AuthContext, schema_name: &String, id: Uuid) -> anyhow::Result<Homework> {
        let homework = HomeworkRepository::fetch_homework_by_id(&self.db_pool, schema_name, id).await?;
        Ok(homework)
    }
    pub async fn homework_summary(&self, schema_name: &str, id: Uuid) -> anyhow::Result<HomeworkSummary> {
        let mut summary = HomeworkSummary::default();
        let student_summaries = HomeworkStudentsRepository::fetch_homework_student_summary(&self.db_pool, schema_name, id).await?;
        student_summaries.into_iter().for_each(|s| {
            summary.student.total_count += s.count;
            match s.status {
                HomeworkStudentStatus::Unsubmitted => {
                    summary.student.absent_count += s.count;
                }
                HomeworkStudentStatus::Error => {
                    summary.student.error_count += s.count;
                }
                HomeworkStudentStatus::Done => {}
            }
        });
        let paper_service = HomeworkPapersService::new(self.db_pool.clone(), schema_name.to_string());
        let (criteria_ids, total_score) = paper_service.get_homework_criteria_ids(id).await?;
        let paper_id = paper_service
            .get_homework_papers_by_homework_id(id)
            .await
            .map_err(|e| anyhow::anyhow!(e))?
            .into_iter()
            .map(|p| p.id)
            .collect::<Vec<_>>()
            .pop();
        if paper_id.is_some() {
            summary.paper_id = paper_id;
            summary.criteria.score = total_score;
            summary.criteria.count = criteria_ids.len() as i64;
            let criteria_summaries = score_repository::fetch_criteria_summary(&self.db_pool, schema_name, Some(criteria_ids), None).await?;
            let paper_scans_status_summary_record = PaperScansRepository::calculate_scan_statistics(&self.db_pool, schema_name, id).await?;

            paper_scans_status_summary_record.into_iter().for_each(|s| {
                summary.scan.total_count += s.total;
                match s.status {
                    PaperScanStatus::Undistributed => summary.scan.undistributed_count += s.total,
                    PaperScanStatus::Unbound => summary.scan.unbounded_count += s.total,
                    PaperScanStatus::Duplicate => summary.scan.duplication_count += s.total,
                    PaperScanStatus::Error => summary.scan.error_count += s.total,
                    PaperScanStatus::Done => {}
                }
            });

            // 计算平均分
            criteria_summaries.into_iter().for_each(|criteria| {
                summary.scoring.total_count += criteria.count;
                match criteria.status {
                    ScoreStatus::Undistributed | ScoreStatus::Distributed | ScoreStatus::Excepted => {}
                    ScoreStatus::Done | ScoreStatus::CheckedCorrect | ScoreStatus::CheckedError => {
                        summary.scoring.scored_count += criteria.count;
                        if criteria.count > 0 {
                            summary.scoring.avg_score += criteria.total_score.to_f64().unwrap_or(0f64) / (criteria.count as f64);
                        }
                    }
                }
            });
        }
        Ok(summary)
    }
    pub async fn delete_homework(&self, _context: &AuthContext, schema_name: &String, id: &Uuid) -> Result<Homework, String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name).await.map_err(|e| e.to_string())?;
        let deleted_homework = HomeworkRepository::delete_homework(&mut conn, id).await.map_err(|e| e.to_string())?;
        Ok(deleted_homework)
    }
}

#[derive(Debug, FromRow, Clone, Serialize, Deserialize)]
pub struct PageHomeworkResult {
    //数据库字段
    pub homework: Homework,
    //联查的学生数据
    pub student_list: Vec<StudentBaseInfo>,
}
