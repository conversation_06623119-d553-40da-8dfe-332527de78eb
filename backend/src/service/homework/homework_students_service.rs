use sqlx::PgPool;
use tracing_subscriber::fmt::time;
use std::collections::{HashMap, HashSet};
use uuid::Uuid;

use crate::model::homework_students::homework_students::{HomeworkStudentStatus, StudentHomeworkSummary, TimeRange};
use crate::repository::homework_students::homework_students_repository::HomeworkStudentsRepository;
use crate::repository::paper_scan_page::paper_scan_page_repository::PaperScanPageRepository;
use crate::repository::score::score_repository::fetch_all_scores;
use crate::service::homework::vo::homework_student;
use crate::service::homework_papers::homework_papers_service::HomeworkPapersService;
use crate::{model::homework_students::homework_students::HomeworkStudents, utils::schema::connect_with_schema};
use bigdecimal::ToPrimitive;
#[derive(Clone)]
pub struct HomeworkStudentsService {
    db_pool: PgPool,
}

impl HomeworkStudentsService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

impl HomeworkStudentsService {
    /**
     * 作者：张瀚
     * 说明：批量绑定学生到作业中
     */
    pub async fn batch_bind_students_to_homework(&self, schema_name: &String, homework_id: Uuid, student_id_list: Vec<Uuid>, class_id: Uuid) -> Result<Vec<HomeworkStudents>, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await.map_err(|e| e.to_string())?;
        //先查询已有关联
        let refs: Vec<HomeworkStudents> = HomeworkStudentsRepository::fetch_homework_students_by_homework_id(&self.db_pool, schema_name, &homework_id).await?;
        //已经有的关联不要重复，所以要去掉
        let mut student_id_set: HashSet<Uuid> = student_id_list.clone().into_iter().collect();
        for ele in refs {
            student_id_set.remove(&ele.student_id);
        }
        //新建剩余的
        if student_id_set.len() == 0 {
            return Ok(vec![]);
        }
        let mut builder = sqlx::QueryBuilder::new("INSERT INTO homework_students (homework_id,student_id,class_id) VALUES ");
        for (i, ele) in student_id_set.iter().enumerate() {
            builder.push(" ( ").push_bind(homework_id).push(" , ").push_bind(ele).push(" , ").push_bind(class_id).push(" ) ");
            if i < student_id_set.len() - 1 {
                builder.push(" , ");
            }
        }
        builder.push("RETURNING *");
        builder.build_query_as().fetch_all(&mut *conn).await.map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：查询某个作业涉及的所有学生列表
     */


    /// 说明: 查询某个作业涉及的所有学生人数
    /// 作者: 萧达光
    pub async fn count_homework_student_by_homework_id(&self, schema_name: &String, homework_id: &Uuid) -> anyhow::Result<i64> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await?;

        let count: i64 = sqlx::query_scalar(" SELECT COUNT(1) FROM homework_students WHERE homework_id = $1 ")
            .bind(homework_id)
            .fetch_one(&mut *conn)
            .await?;

        Ok(count)
    }
    /**
     * 作者：张瀚
     * 说明：从作业中批量解绑学生
     */
    pub async fn batch_unbind_students_from_homework(&self, schema_name: &String, homework_id: Uuid, student_id_list: Vec<Uuid>) -> Result<(), String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await.map_err(|e| e.to_string())?;
        if student_id_list.len() == 0 {
            return Ok(());
        }
        let mut builder = sqlx::QueryBuilder::new("DELETE FROM homework_students WHERE homework_id = ");
        builder.push_bind(homework_id).push(" and student_id in (");
        for (i, id) in student_id_list.iter().enumerate() {
            builder.push_bind(id);
            if i < student_id_list.len() - 1 {
                builder.push(" , ");
            }
        }
        builder.push(" )").build().execute(&mut *conn).await.map_err(|e| e.to_string())?;
        Ok(())
    }

    pub async fn update_homework_student_status(&self, schema_name: &str, homework_id: Uuid, student_id: Uuid, total_page: i32) -> anyhow::Result<()> {
        let pages = PaperScanPageRepository::fetch_scan_pages_by_student_id(&self.db_pool, schema_name, student_id, homework_id).await?;
        let status = if pages.is_empty() {
            HomeworkStudentStatus::Unsubmitted
        } else if pages.len() != total_page as usize {
            HomeworkStudentStatus::Error
        } else {
            let pages = pages.into_iter().map(|p| p.page_num).collect::<HashSet<_>>();
            if (1..=total_page).all(|x| pages.contains(&x)) {
                HomeworkStudentStatus::Done
            } else {
                HomeworkStudentStatus::Error
            }
        };
        HomeworkStudentsRepository::update_homework_student_status(&self.db_pool, schema_name, student_id, status).await?;
        Ok(())
    }
    /**
     * 作者：朱若彪
     * 作用：查出行政班内所有学生的相关作业
     */
    pub async fn fetch_homework_students(&self, schema_name: &str, student_id_list: &Vec<Uuid>, subject_group_id: Option<Uuid>,time_range: Option<TimeRange>) -> Result<Vec<HomeworkStudents>,String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name).await.map_err(|e| e.to_string())?;
        if student_id_list.len() == 0 {
            return  Ok(vec![]);
        }
        let (start, end) = if let Some(tr) = time_range {
            let (start, end) = tr.to_timestamp_range();
            (Some(start), Some(end))
        } else {
            (None, None)
        };
        let homework_students = HomeworkStudentsRepository::fetch_homework_by_student_ids(
            &mut conn, &student_id_list, subject_group_id, start, end
        ).await.map_err(|e| e.to_string())?;
        Ok(homework_students)
    }
    /**
     * 作者：朱若彪
     * 说明：统计每一个学生的总作业数、缺考次数
     */
    pub async fn calculate_student_homework_summary(&self, homework_students: &Vec<HomeworkStudents>) -> Result<Vec<StudentHomeworkSummary>, String> {
        if homework_students.len() == 0 {
            return Err("没有相关作业数据".to_string());
        }
        let mut summary_map: HashMap<Uuid, (i32, i32)> = HashMap::new();
        for hs in homework_students {
            let entry = summary_map.entry(hs.student_id).or_insert((0, 0));
            entry.0 += 1; // 总作业数
            if hs.status == HomeworkStudentStatus::Unsubmitted {
                entry.1 += 1; // 缺考次数
            }
        }
        let result = summary_map.into_iter().map(|(student_id, (total, absent))| StudentHomeworkSummary {
            student_id,
            total_homework: total,
            unsubmitted_count: absent,
        }).collect();
        Ok(result)
    }
    /**
     * 作者：朱若彪
     * 说明：计算学生作业总分
     */
    pub async fn calculate_student_homework_score(&self, schema_name: &String, homework_id: &Uuid, student_id_list: &Option<Vec<Uuid>>) -> Result<(), String> {
        //查询作业下的所有评分标准
        let homework_paper_service = HomeworkPapersService::new(self.db_pool.clone(), schema_name.clone());
        let criteria_list= homework_paper_service.get_homework_criteria_list(*homework_id)
            .await
            .map_err(|e| e.to_string())?;
        let criteria_ids: Vec<Uuid>= criteria_list.iter().map(|c|c.id).collect();
        // 2. 获取需要计算的学生列表
        let target_student_ids: Vec<Uuid> = if let Some(list) = student_id_list {
            list.clone()
        } else {
            // 查询该作业下所有学生
            let homework_students = HomeworkStudentsRepository::get_homework_student_by_id(&self.db_pool, homework_id)
                .await
                .map_err(|_| "未找到该作业的学生关联信息".to_string())?;
            homework_students.iter().map(|hs| hs.student_id).collect()
        };
        // 查询该学生所有评分标准的得分
        let scores = fetch_all_scores(
             &self.db_pool,
             schema_name,
             Some(criteria_ids.clone()),   // 所有评分标准
             Some(target_student_ids.clone())
         ).await.map_err(|e| e.to_string())?;
         // student_id -> 总分
         let mut student_total_score: HashMap<Uuid, f64> = HashMap::new();
         for score in scores {
             if let Some(student_id) = score.student_id {
                 let entry = student_total_score.entry(student_id).or_insert(0.0);
                 *entry += score.score.to_f64().unwrap_or(0.0);
             }
         }
         // 遍历 student_total_score，更新 homeworkstudent 表的 scores 字段
         for (student_id, total_score) in student_total_score {
             HomeworkStudentsRepository::update_homework_student_score(
                 &self.db_pool,
                schema_name,
                 homework_id,
                 &student_id,
                 total_score
             ).await.map_err(|e| e.to_string())?;
         }
        Ok(())
    }


}
