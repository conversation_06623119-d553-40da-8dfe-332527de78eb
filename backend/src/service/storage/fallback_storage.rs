use crate::service::storage::storage_service::{FileInfo, StorageResult, StorageService, UploadOptions};
use anyhow::anyhow;
use async_trait::async_trait;
use bytes::Bytes;

/// 回退存储服务实现 - 当MinIO不可用时使用
pub struct FallbackStorageService;

impl FallbackStorageService {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl StorageService for FallbackStorageService {
    async fn upload(
        &self,
        _filename: &str,
        _content: Bytes,
        _options: UploadOptions
    ) -> StorageResult<FileInfo> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }

    async fn download(&self, _key: &str) -> StorageResult<Bytes> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }

    async fn delete(&self, _key: &str) -> StorageResult<()> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }

    async fn delete_batch(&self, _keys: Vec<&str>) -> StorageResult<Vec<String>> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }

    async fn exists(&self, _key: &str) -> StorageResult<bool> {
        Ok(false) // 假设文件不存在，避免阻塞其他功能
    }

    async fn get_info(&self, _key: &str) -> StorageResult<FileInfo> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }

    async fn generate_presigned_url(&self, _key: &str) -> StorageResult<String> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }

    async fn generate_preview_images_url(&self, _key: &str) -> StorageResult<String> {
        Err(anyhow!("Storage service is not available - MinIO connection failed"))
    }
}
