use anyhow::Result;
use async_trait::async_trait;
use bytes::Bytes;
use serde::{Deserialize, Serialize};

/// 通用存储服务结果类型
pub type StorageResult<T> = Result<T>;

/// 文件信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileInfo {
    pub key: String,
    pub size: u64,
    pub content_type: String,
    pub url: String,
}

/// 上传选项
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct UploadOptions {
    pub preserve_filename: bool,
    pub prefix: Option<String>,
}

/// 通用存储服务接口
#[async_trait]
pub trait StorageService: Send + Sync {
    /// 上传文件
    async fn upload(&self, filename: &str, content: Bytes, options: UploadOptions) -> StorageResult<FileInfo>;

    /// 下载文件
    async fn download(&self, key: &str) -> StorageResult<Bytes>;

    /// 删除文件
    async fn delete(&self, key: &str) -> StorageResult<()>;

    /// 批量删除文件
    async fn delete_batch(&self, keys: Vec<&str>) -> StorageResult<Vec<String>>;

    /// 检查文件是否存在
    async fn exists(&self, key: &str) -> StorageResult<bool>;

    /// 获取文件信息
    async fn get_info(&self, key: &str) -> StorageResult<FileInfo>;

    /// 生成预签名下载URL
    async fn generate_presigned_url(&self, key: &str) -> StorageResult<String>;

    /// 生成预签名图片预览URL (解决生成网络地址,图片自动下载问题)
    /// 作者萧达光
    async fn generate_preview_images_url(&self, key: &str) -> StorageResult<String>;
}
