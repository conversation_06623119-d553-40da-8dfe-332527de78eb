use crate::model::auth::{Auth<PERSON><PERSON><PERSON>, AuthResult};
use async_trait::async_trait;
use rand::Rng;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;
use tracing::{error, info, warn};
use crate::utils::mask::mask_phone;

#[async_trait]
pub trait SmsProvider: Send + Sync {
    async fn send_sms(&self, phone: &str, message: &str) -> AuthResult<SmsDeliveryResult>;
    fn provider_name(&self) -> &'static str;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SmsDeliveryResult {
    pub message_id: String,
    pub status: String,
    pub cost: Option<f64>,
    pub provider: String,
}

#[derive(Debug, Clone)]
pub struct SmsTemplate {
    pub template_id: String,
    pub content: String,
    pub variables: Vec<String>,
}

pub struct SmsService {
    providers: Vec<Box<dyn SmsProvider>>,
    templates: <PERSON>h<PERSON>ap<String, SmsTemplate>,
    rate_limiter: RwLock<HashMap<String, Vec<chrono::DateTime<chrono::Utc>>>>,
    delivery_stats: RwLock<HashMap<String, SmsStats>>,
}

#[derive(Debug, Default, Clone)]
struct SmsStats {
    sent: u64,
    delivered: u64,
    failed: u64,
    total_cost: f64,
}

impl SmsService {
    pub fn new() -> Self {
        let mut templates = HashMap::new();
        
        // Registration verification template
        templates.insert(
            "registration".to_string(),
            SmsTemplate {
                template_id: "REG_001".to_string(),
                content: "【Deep-Mate】您的注册验证码是：{code}，5分钟内有效，请勿泄露。".to_string(),
                variables: vec!["code".to_string()],
            },
        );
        
        // Login verification template
        templates.insert(
            "login".to_string(),
            SmsTemplate {
                template_id: "LOGIN_001".to_string(),
                content: "【Deep-Mate】您的登录验证码是：{code}，5分钟内有效，请勿泄露。".to_string(),
                variables: vec!["code".to_string()],
            },
        );
        
        // Password reset template
        templates.insert(
            "reset".to_string(),
            SmsTemplate {
                template_id: "RESET_001".to_string(),
                content: "【Deep-Mate】您的密码重置验证码是：{code}，5分钟内有效，请勿泄露。".to_string(),
                variables: vec!["code".to_string()],
            },
        );

        Self {
            providers: vec![
                Box::new(AliyunSmsProvider::new()),
                Box::new(TencentSmsProvider::new()),
            ],
            templates,
            rate_limiter: RwLock::new(HashMap::new()),
            delivery_stats: RwLock::new(HashMap::new()),
        }
    }

    pub fn generate_verification_code(&self) -> String {
        let mut rng = rand::rng();
        format!("{:06}", rng.random_range(100000..999999))
    }

    pub async fn send_verification_code(
        &self,
        phone: &str,
        code_type: &str,
        code: &str,
    ) -> AuthResult<SmsDeliveryResult> {
        // Check rate limiting
        if !self.check_rate_limit(phone).await {
            return Err(AuthError::TooManyAttempts);
        }

        // Get template
        let template = self.templates.get(code_type)
            .ok_or_else(|| AuthError::SmsServiceError("Invalid code type".to_string()))?;

        // Format message
        let message = template.content.replace("{code}", code);

        // Try providers in order until one succeeds
        let mut last_error = None;
        for provider in &self.providers {
            match provider.send_sms(phone, &message).await {
                Ok(result) => {
                    self.update_rate_limit(phone).await;
                    self.update_delivery_stats(&result, true).await;
                    info!(
                        "SMS sent successfully via {} to {}: {}",
                        provider.provider_name(),
                        mask_phone(phone),
                        result.message_id
                    );
                    return Ok(result);
                }
                Err(e) => {
                    warn!(
                        "SMS provider {} failed for {}: {:?}",
                        provider.provider_name(),
                        mask_phone(phone),
                        e
                    );
                    last_error = Some(e);
                }
            }
        }

        // All providers failed
        if let Some(error) = last_error {
            self.update_delivery_stats(
                &SmsDeliveryResult {
                    message_id: "failed".to_string(),
                    status: "failed".to_string(),
                    cost: None,
                    provider: "all_failed".to_string(),
                },
                false,
            ).await;
            error!("All SMS providers failed for {}", mask_phone(phone));
            Err(error)
        } else {
            Err(AuthError::SmsServiceError("No SMS providers available".to_string()))
        }
    }

    async fn check_rate_limit(&self, phone: &str) -> bool {
        let mut limiter = self.rate_limiter.write().await;
        let now = chrono::Utc::now();
        let window_start = now - chrono::Duration::minutes(1);

        let attempts = limiter.entry(phone.to_string()).or_insert_with(Vec::new);
        
        // Remove old attempts
        attempts.retain(|&time| time > window_start);
        
        // Check if under limit (5 attempts per minute)
        attempts.len() < 5
    }

    async fn update_rate_limit(&self, phone: &str) {
        let mut limiter = self.rate_limiter.write().await;
        let now = chrono::Utc::now();
        let attempts = limiter.entry(phone.to_string()).or_insert_with(Vec::new);
        attempts.push(now);
    }

    async fn update_delivery_stats(&self, result: &SmsDeliveryResult, success: bool) {
        let mut stats = self.delivery_stats.write().await;
        let provider_stats = stats.entry(result.provider.clone()).or_insert_with(SmsStats::default);
        
        provider_stats.sent += 1;
        if success {
            provider_stats.delivered += 1;
        } else {
            provider_stats.failed += 1;
        }
        
        if let Some(cost) = result.cost {
            provider_stats.total_cost += cost;
        }
    }
}

// Aliyun SMS Provider (placeholder for real implementation)
#[allow(dead_code)]
pub struct AliyunSmsProvider {
    access_key_id: String,
    access_key_secret: String,
    sign_name: String,
}

#[allow(dead_code)]
impl AliyunSmsProvider {
    pub fn new() -> Self {
        Self {
            access_key_id: std::env::var("ALIYUN_ACCESS_KEY_ID").unwrap_or_default(),
            access_key_secret: std::env::var("ALIYUN_ACCESS_KEY_SECRET").unwrap_or_default(),
            sign_name: std::env::var("ALIYUN_SMS_SIGN_NAME").unwrap_or_default(),
        }
    }
}

#[async_trait]
impl SmsProvider for AliyunSmsProvider {
    async fn send_sms(&self, _phone: &str, _message: &str) -> AuthResult<SmsDeliveryResult> {
        // TODO: Implement actual Aliyun SMS API integration
        Err(AuthError::SmsServiceError("Aliyun SMS not implemented".to_string()))
    }

    fn provider_name(&self) -> &'static str {
        "AliyunSmsProvider"
    }
}

// Tencent SMS Provider (placeholder for real implementation)
#[allow(dead_code)]
pub struct TencentSmsProvider {
    secret_id: String,
    secret_key: String,
    app_id: String,
}

#[allow(dead_code)]
impl TencentSmsProvider {
    pub fn new() -> Self {
        Self {
            secret_id: std::env::var("TENCENT_SECRET_ID").unwrap_or_default(),
            secret_key: std::env::var("TENCENT_SECRET_KEY").unwrap_or_default(),
            app_id: std::env::var("TENCENT_SMS_APP_ID").unwrap_or_default(),
        }
    }
}

#[async_trait]
impl SmsProvider for TencentSmsProvider {
    async fn send_sms(&self, _phone: &str, _message: &str) -> AuthResult<SmsDeliveryResult> {
        // TODO: Implement actual Tencent SMS API integration
        Err(AuthError::SmsServiceError("Tencent SMS not implemented".to_string()))
    }

    fn provider_name(&self) -> &'static str {
        "TencentSmsProvider"
    }
}