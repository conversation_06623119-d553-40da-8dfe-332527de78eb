use sqlx::{Postgres};
use crate::model::base::{PageParams, PageResult};
use crate::model::tenant::member::{TeacherVO};
use anyhow::{Result};
use sqlx::pool::PoolConnection;

pub async fn search_teacher(
    mut conn: PoolConnection<Postgres>,
    params: PageParams,
) -> Result<PageResult<TeacherVO>> {    
    let offset = params.get_offset();

    let rows: Vec<TeacherVO> = sqlx::query_as::<_, TeacherVO>(
        r#"
        SELECT
            t.id,                -- TeacherVO.id
            m.name,              -- TeacherVO.name
            m.status,            -- TeacherVO.status
            t.teacher_no,        -- TeacherVO.teacher_no
            m.created_at         -- TeacherVO.created_at
        FROM teacher AS t
        JOIN member  AS m ON m.id = t.member_id
        ORDER BY m.created_at DESC
        LIMIT $1 OFFSET $2
        "#,
    )
        .bind(params.page_size)
        .bind(offset)
        .fetch_all(&mut *conn)
        .await?;

    let (total,): (i64,) = sqlx::query_as("SELECT COUNT(*) FROM teacher")
        .fetch_one(&mut *conn)
        .await?;

    Ok(PageResult {
        data: rows,
        total,
        page: params.page.unwrap_or(1),
        page_size: params.page_size.unwrap_or(10),
        total_pages: 0,
    })
}