use crate::model::homework::homework::Homework;
use crate::model::paper::paper_cache::{AdmissionTicketQRCodeMsgBean, AnswerBlockScoringCriteria, AnswerCardData, ScoringCriteria};
use crate::repository::homework_papers::homework_papers_repository::HomeworkPapersRepository;
use crate::repository::papers::papers_repository::PapersRepository;
use crate::{
    model::{
        homework_papers::homework_papers::{BindPapersToHomeworkParams, HomeworkPapers},
        paper::{paper::Paper, paper_cache::PaperContentData},
    },
    utils::schema::connect_with_schema,
};
use anyhow::anyhow;
use serde_json::Value;
use sqlx::PgPool;
use std::collections::{HashMap, HashSet};
use uuid::Uuid;

#[derive(Clone)]
pub struct HomeworkPapersService {
    db_pool: PgPool,
    schema_name: String,
}
impl HomeworkPapersService {
    pub fn new(db_pool: PgPool, schema_name: String) -> Self {
        Self { db_pool, schema_name }
    }
}
impl HomeworkPapersService {
    /**
     * 说明：
     */
    pub async fn bind_papers_to_homework(&self, params: &BindPapersToHomeworkParams) -> Result<Vec<HomeworkPapers>, String> {
        let mut conn = connect_with_schema(&self.db_pool, self.schema_name.as_str()).await.map_err(|e| e.to_string())?;
        let BindPapersToHomeworkParams { homework_id, paper_id } = params;
        //复制试卷到租户内,先查询出来
        let paper = sqlx::query_as::<_, Paper>("SELECT * FROM public.papers p where p.id = $1")
            .bind(&paper_id)
            .fetch_one(&mut *conn)
            .await
            .map_err(|e| e.to_string())?;
        //修改内容
        let paper_content: PaperContentData = paper.paper_content.0;
        let PaperContentData { question_data_list, answer_card } = paper_content;
        let AnswerCardData {
            id: _,
            paper_id,
            dpi,
            width,
            height,
            x,
            y,
            right,
            bottom,
            page_orientation_is_vertical,
            bucket_size,
            show_page_index,
            show_pos_point,
            pos_point_width,
            pos_point_height,
            mut block_group_list,
            mut question_item_block_group_list,
            mut answer_block_group_scoring_criteria_list,
            mut scoring_criteria_list,
            page_total,
            admissionTicketNumberInfoQuestionItemConfig,
        } = answer_card;
        //更换ID为新的
        let mut old_id_to_new_id_map: HashMap<Uuid, Uuid> = HashMap::new();
        let new_answer_card_id = Uuid::new_v4();
        block_group_list.iter_mut().for_each(|item| {
            let new_id = Uuid::new_v4();
            old_id_to_new_id_map.insert(item.id.clone(), new_id);
            item.id = new_id;
            item.card_id = new_answer_card_id;
            //下级也要同步更新
            item.block_list.iter_mut().for_each(|block| block.group_id = new_id);
        });
        scoring_criteria_list.iter_mut().for_each(|item| {
            let new_id = Uuid::new_v4();
            old_id_to_new_id_map.insert(item.id.clone(), new_id);
            item.id = new_id;
        });
        answer_block_group_scoring_criteria_list.iter_mut().for_each(|item| {
            item.block_group_id = old_id_to_new_id_map.get(&item.block_group_id).map_or(item.block_group_id, |x| x.clone());
            item.scoring_criteria_id = old_id_to_new_id_map.get(&item.scoring_criteria_id).map_or(item.scoring_criteria_id, |x| x.clone());
            item.id = Uuid::new_v4();
        });
        //构建映射
        question_item_block_group_list.iter_mut().for_each(|item| {
            item.block_group_id = old_id_to_new_id_map.get(&item.block_group_id).map_or(item.block_group_id, |x| x.clone());
        });

        //插入到当前租户内
        let new_paper_content = serde_json::to_value(PaperContentData {
            question_data_list,
            answer_card: AnswerCardData {
                id: new_answer_card_id,
                paper_id,
                dpi,
                width,
                height,
                x,
                y,
                right,
                bottom,
                page_orientation_is_vertical,
                bucket_size,
                show_page_index,
                show_pos_point,
                pos_point_width,
                pos_point_height,
                block_group_list,
                question_item_block_group_list,
                answer_block_group_scoring_criteria_list,
                scoring_criteria_list,
                page_total,
                admissionTicketNumberInfoQuestionItemConfig,
            },
        })
        .map_err(|e| e.to_string())?;
        let new_paper = sqlx::query_as::<_, Paper>("INSERT INTO papers (paper_name,paper_content) values ($1,$2) RETURNING *")
            .bind(&paper.paper_name)
            .bind(&new_paper_content)
            .fetch_one(&mut *conn)
            .await
            .map_err(|e| e.to_string())?;
        //修改答题卡的默认试卷ID
        let new_paper_id = new_paper.id;
        let PaperContentData { question_data_list, mut answer_card } = new_paper.paper_content.0;
        answer_card.paper_id = new_paper_id.clone();
        //二维码内容
        let mut admission_ticket_number_info_question_item_config = answer_card.admissionTicketNumberInfoQuestionItemConfig.clone();
        let old_msg_op = admission_ticket_number_info_question_item_config.qrcodeMsg;
        match old_msg_op {
            Some(old_msg) => {
                let old_value: Value = serde_json::from_str(&old_msg).unwrap_or(serde_json::json!({}));
                let mut old_bean: AdmissionTicketQRCodeMsgBean = serde_json::from_value(old_value).unwrap_or(AdmissionTicketQRCodeMsgBean {
                    cardId: None,
                    paperId: None,
                    studentNumber: None,
                    studentName: None,
                    studentClassName: None,
                });
                old_bean.paperId = Some(new_paper_id.clone());
                admission_ticket_number_info_question_item_config.qrcodeMsg = Some(serde_json::to_string(&old_bean).unwrap_or("{}".to_string()));
            }
            None => {
                admission_ticket_number_info_question_item_config.qrcodeMsg = Some(
                    serde_json::to_string(&AdmissionTicketQRCodeMsgBean {
                        cardId: None,
                        paperId: Some(new_paper_id.clone()),
                        studentNumber: None,
                        studentName: None,
                        studentClassName: None,
                    })
                    .unwrap_or("{}".to_string()),
                );
            }
        }
        answer_card.admissionTicketNumberInfoQuestionItemConfig = admission_ticket_number_info_question_item_config;
        //更新
        let paper_content_json = serde_json::to_value(PaperContentData { question_data_list, answer_card }).map_err(|e| e.to_string())?;
        PapersRepository::update_paper_content(&mut conn, &new_paper_id, &paper_content_json).await.map_err(|e| e.to_string())?;
        //删除其他试卷关联
        sqlx::query("DELETE FROM homework_papers hp WHERE hp.homework_id = $1")
            .bind(&homework_id)
            .execute(&mut *conn)
            .await
            .map_err(|e| e.to_string())?;
        //新建
        let mut builder = sqlx::QueryBuilder::new("insert into homework_papers (homework_id,paper_id) values");
        builder
            .push("(")
            .push_bind(homework_id)
            .push(",")
            .push_bind(new_paper.id)
            .push(") ")
            .push("RETURNING *")
            .build_query_as()
            .fetch_all(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }

    pub async fn get_homework_papers_by_homework_id(&self, homework_id: Uuid) -> Result<Vec<Paper>, String> {
        let papers = HomeworkPapersRepository::fetch_paper_by_homework_id(&self.db_pool, self.schema_name.as_str(), homework_id).await?;
        Ok(papers)
    }

    pub async fn find_homework_list_by_paper_id(&self, paper_id: Uuid) -> Result<Vec<Homework>, String> {
        let conn = connect_with_schema(&self.db_pool, self.schema_name.as_str()).await.map_err(|e| e.to_string())?;
        let list = HomeworkPapersRepository::find_homework_list_by_paper_id(conn, paper_id).await.map_err(|e| e.to_string())?;
        Ok(list)
    }

    pub async fn bulk_bind_papers_to_homework(&self, params: &Vec<BindPapersToHomeworkParams>) -> Result<usize, String> {
        let mut result = 0;
        for param in params {
            match self.bind_papers_to_homework(param).await {
                Ok(res) => {
                    result += res.len();
                }
                Err(err) => {
                    return Err(err);
                }
            }
        }
        Ok(result)
    }

    // 作业评分列表
    pub async fn get_homework_criteria_list(&self, homework_id: Uuid) -> anyhow::Result<Vec<ScoringCriteria>> {
        let papers = self.get_homework_papers_by_homework_id(homework_id).await.map_err(|e| anyhow!(e))?;
        let mut criteria_list = Vec::new();
        let mut set_ids = HashSet::new();
        papers.iter().for_each(|paper| {
            paper.paper_content.0.answer_card.scoring_criteria_list.iter().for_each(|criteria| {
                if !set_ids.contains(&criteria.id) {
                    set_ids.insert(criteria.id);
                    criteria_list.push(criteria.clone());
                }
            });
        });
        Ok(criteria_list)
    }
    pub async fn get_homework_criteria_ids(&self, homework_id: Uuid) -> anyhow::Result<(Vec<Uuid>, f64)> {
        let list = self.get_homework_criteria_list(homework_id).await?;
        let mut total_score = 0.0;
        let set_ids = list.into_iter().map(|criteria| {
            total_score += criteria.score;
            criteria.id
        }).collect::<Vec<_>>();
        Ok((set_ids, total_score))
    }

    pub async fn get_homework_criteria_map(&self, homework_id: Uuid) -> anyhow::Result<HashMap<Uuid, ScoringCriteria>> {
        let list = self.get_homework_criteria_list(homework_id).await?;
        let mut map: HashMap<Uuid, ScoringCriteria> = HashMap::new();
        list.into_iter().for_each(|criteria| {
            map.entry(criteria.id).or_insert(criteria);
        });
        Ok(map)
    }

    // 获取题块和评分列表关系
    pub async fn get_homework_block_group_criteria_list(&self, homework_id: Uuid) -> anyhow::Result<Vec<AnswerBlockScoringCriteria>> {
        let papers = self.get_homework_papers_by_homework_id(homework_id).await.map_err(|e| anyhow!(e))?;
        let mut items = Vec::new();
        papers.iter().for_each(|paper| {
            paper.paper_content.0.answer_card.answer_block_group_scoring_criteria_list.iter().for_each(|criteria| {
                items.push(criteria.clone());
            });
        });
        Ok(items)
    }
}
