use std::collections::HashMap;
use std::sync::Arc;

use crate::model::homework_students::homework_students::PageStudentsByHomeworkIdParams;
use crate::model::{CreateStudentParams, CreateStudentProfileLevel, CreateStudentProfileTag, CreateStudentTeachingClass, FindAllStudentParams, Student, StudentBaseInfo, StudentDetail, StudentProfileLevel, StudentProfileTag, StudentTeachingClass, StudentVo, UpdateStudentParams};
use crate::repository::students::student_repository::StudentsRepository;
use crate::service::permission::{
    CasbinPermissionService, DataFilterManager, FilterContext
};
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use sqlx::postgres::PgRow;
use sqlx::{PgPool, Row};
use tracing::info;
use uuid::Uuid;

#[derive(Clone)]
pub struct StudentService {
    db_pool: PgPool,
    data_filter_manager: Option<Arc<DataFilterManager>>,
}

impl StudentService {
    pub fn new(db_pool: PgPool) -> Self {
        Self {
            db_pool,
            data_filter_manager: None,
        }
    }

    pub fn with_data_filter_manager(db_pool: PgPool, data_filter_manager: Arc<DataFilterManager>) -> Self {
        Self {
            db_pool,
            data_filter_manager: Some(data_filter_manager),
        }
    }

    /// 创建带有默认过滤器的学生服务
    pub fn with_default_filter(db_pool: PgPool) -> Self {
        let filter_manager = DataFilterManager::create_default(db_pool.clone());
        Self::with_data_filter_manager(db_pool, Arc::new(filter_manager))
    }

    /**
     * 作者：张瀚
     * 说明：分页查询学生列表，支持通用权限过滤
     */
    pub async fn page_all_student(
        &self,
        schema_name: &String,
        params: &FindAllStudentParams,
        user_id: Option<Uuid>, // 当前登录用户ID，用于权限过滤
        tenant_id: Option<String>, // 租户ID
        casbin_service: Option<&dyn CasbinPermissionService>, // Casbin权限服务
    ) -> Result<(Vec<StudentVo>, i64), String> {
        let FindAllStudentParams {
            name_like,
            student_number,
            phone,
            page_params,
        } = params;

        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.*, gl.name as grade_level_name, ac.class_name \
             from {}.students s \
             left join {}.administrative_classes ac on s.administrative_class_id = ac.id \
             left join public.grade_levels gl on ac.grade_level_code = gl.code \
             where 1 = 1 ",
            schema_name, schema_name
        ));
        let mut count_builder = sqlx::QueryBuilder::new(format!(
            "select count(*) from {}.students s where 1 = 1 ",
            schema_name
        ));

        // 预先获取class_filter以避免生命周期问题
        let class_filter = if let Some(user_id) = user_id {
            self.get_class_teacher_class_ids(schema_name, user_id).await?
        } else {
            None
        };

        // 应用通用数据过滤器
        if let (Some(user_id), Some(tenant_id), Some(casbin_service), Some(filter_manager)) =
            (user_id, tenant_id, casbin_service, &self.data_filter_manager) {

            info!("Applying data filter for user_id: {}, tenant_id: {}, schema: {}",
                  user_id, tenant_id, schema_name);

            let filter_context = FilterContext {
                user_id,
                tenant_id: tenant_id.clone(),
                user_identity: format!("user:{}", user_id),
                resource: "student".to_string(),
                action: "read".to_string(),
                schema_name: schema_name.clone(),
            };

            match filter_manager.apply_data_filter(
                &filter_context,
                &mut builder,
                &mut count_builder,
                casbin_service,
                "s", // 学生表别名
            ).await {
                Ok(filter_applied) => {
                    info!("Data filter applied: {}, for user {} in tenant {}",
                          filter_applied, user_id, tenant_id);
                },
                Err(e) => {
                    tracing::error!("Failed to apply data filter: {}", e);
                    // 回退到旧的权限检查逻辑
                    if let Some(class_ids) = &class_filter {
                        if !class_ids.is_empty() {
                            builder.push(" and s.administrative_class_id IN (");
                            count_builder.push(" and s.administrative_class_id IN (");

                            for (i, class_id) in class_ids.iter().enumerate() {
                                if i > 0 {
                                    builder.push(", ");
                                    count_builder.push(", ");
                                }
                                builder.push_bind(class_id);
                                count_builder.push_bind(class_id);
                            }

                            builder.push(") ");
                            count_builder.push(") ");
                        } else {
                            // 如果班主任没有负责任何班级，返回空结果
                            return Ok((Vec::new(), 0));
                        }
                    }
                }
            }
        } else if user_id.is_some() {
            // 回退到旧的权限检查逻辑（当没有提供完整参数时）
            if let Some(class_ids) = &class_filter {
                if !class_ids.is_empty() {
                    builder.push(" and s.administrative_class_id IN (");
                    count_builder.push(" and s.administrative_class_id IN (");

                    for (i, class_id) in class_ids.iter().enumerate() {
                        if i > 0 {
                            builder.push(", ");
                            count_builder.push(", ");
                        }
                        builder.push_bind(class_id);
                        count_builder.push_bind(class_id);
                    }

                    builder.push(") ");
                    count_builder.push(") ");
                } else {
                    // 如果班主任没有负责任何班级，返回空结果
                    return Ok((Vec::new(), 0));
                }
            }
        }

        // 添加业务查询条件
        if let Some(value) = name_like {
            if !value.is_empty() {
                builder.push(format!(" and s.student_name like '%{}%' ", value));
                count_builder.push(format!(" and s.student_name like '%{}%' ", value));
            }
        }
        if let Some(value) = student_number {
            if !value.is_empty() {
                builder.push(" and s.student_number = ").push_bind(value);
                count_builder
                    .push(" and s.student_number = ")
                    .push_bind(value);
            }
        }
        if let Some(value) = phone {
            if !value.is_empty() {
                builder.push(" and s.phone = ").push_bind(value);
                count_builder.push(" and s.phone = ").push_bind(value);
            }
        }

        // 添加排序和分页
        builder
            .push(" order by s.created_at desc limit ")
            .push_bind(page_params.get_limit())
            .push(" offset ")
            .push_bind(page_params.get_offset());
        info!("Executing query: {}", builder.sql());
        // 执行查询
        let count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        let list: Vec<StudentVo> = builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        Ok((list, count))
    }


    /**
     * 获取班主任负责的行政班ID列表
     */
    async fn get_class_teacher_class_ids(
        &self,
        schema_name: &str,
        user_id: Uuid,
    ) -> Result<Option<Vec<Uuid>>, String> {
        // 1. 根据user_id查找教师记录
        let teacher_query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            schema_name
        );
        let teacher_id: Option<Uuid> = sqlx::query_scalar(&teacher_query)
            .bind(user_id)
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;

        if let Some(teacher_id) = teacher_id {
            // 2. 根据教师ID查找其负责的行政班
            let class_query = format!(
                "SELECT id FROM {}.administrative_classes WHERE teacher_id = $1 AND is_active = true",
                schema_name
            );
            let class_ids: Vec<Uuid> = sqlx::query_scalar(&class_query)
                .bind(teacher_id)
                .fetch_all(&self.db_pool)
                .await
                .map_err(|e| e.to_string())?;

            Ok(Some(class_ids))
        } else {
            // 用户不是教师，可能是管理员等其他角色，不进行过滤
            Ok(None)
        }
    }

    /**
     * 作者：张瀚
     * 说明：批量统计指定教学班级中的学生数量
     */
    pub async fn batch_count_by_class(
        &self,
        schema_name: &str,
        class_ids: &Vec<Uuid>,
    ) -> Result<HashMap<Uuid, i64>, String> {
        if class_ids.len() == 0 {
            return Ok(HashMap::<Uuid, i64>::new());
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.administrative_class_id,count(*) from {}.students s where s.administrative_class_id in (",
            schema_name
        ));
        for (index, id) in class_ids.iter().enumerate() {
            builder.push_bind(id);
            if index < class_ids.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) group by s.administrative_class_id");
            }
        }
        let list: Vec<PgRow> = builder
            .build()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        let mut map = HashMap::<Uuid, i64>::new();
        for ele in list.iter() {
            let id: Uuid = ele
                .try_get("administrative_class_id")
                .map_err(|e| e.to_string())?;
            let count: i64 = ele.try_get("count").map_err(|e| e.to_string())?;
            map.insert(id, count);
        }
        Ok(map)
    }

    /**
     * 作者：张瀚
     * 说明：创建学生
     */
    pub async fn create_student(
        &self,
        schema_name: &str,
        params: &CreateStudentParams,
    ) -> Result<Student, String> {
        let CreateStudentParams {
            student_number,
            student_name,
            gender,
            birth_date,
            id_number,
            phone,
            email,
            address,
            guardian_name,
            guardian_phone,
            guardian_relation,
            administrative_class_id,
            user_id,
            enrollment_date,
            status,
            profile_level,
            profile_tags,
            notes,
        } = params;
        sqlx::query_as::<_, Student>(&format!(
            "INSERT INTO {}.students (
                student_number, student_name, gender, birth_date, id_number, phone, email, address,
                guardian_name, guardian_phone, guardian_relation, administrative_class_id, user_id, enrollment_date, status, profile_level, profile_tags, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            RETURNING *", schema_name)
        )
        .bind(student_number)
        .bind(student_name)
        .bind(gender)
        .bind(birth_date)
        .bind(id_number)
        .bind(phone)
        .bind(email)
        .bind(address)
        .bind(guardian_name)
        .bind(guardian_phone)
        .bind(guardian_relation)
        .bind(administrative_class_id)
        .bind(user_id)
        .bind(enrollment_date)
        .bind(status.as_deref().unwrap_or("active"))
        .bind(profile_level)
        .bind(profile_tags)
        .bind(notes)
        .fetch_one(&self.db_pool)
        .await.map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：更新学生
     */
    pub async fn update_student(
        &self,
        schema_name: &str,
        params: &UpdateStudentParams,
    ) -> Result<Student, String> {
        let UpdateStudentParams {
            student_number,
            student_name,
            gender,
            birth_date,
            id_number,
            phone,
            email,
            address,
            guardian_name,
            guardian_phone,
            guardian_relation,
            administrative_class_id,
            user_id,
            enrollment_date,
            status,
            profile_level,
            profile_tags,
            notes,
            id,
        } = params;
        sqlx::query_as::<_, Student>(&format!(
            "UPDATE {}.students SET 
                student_number = $1,
                student_name = $2,
                gender = $3,
                birth_date = $4,
                id_number = $5,
                phone = $6,
                email = $7,
                address = $8,
                guardian_name = $9,
                guardian_phone = $10,
                guardian_relation = $11,
                administrative_class_id = $12,
                user_id = $13,
                enrollment_date = $14,
                status = $15,
                profile_level = $16,
                profile_tags = $17,
                notes = $18,
                updated_at = NOW()
            WHERE id = $19
            RETURNING *",
            schema_name
        ))
        .bind(student_number)
        .bind(student_name)
        .bind(gender)
        .bind(birth_date)
        .bind(id_number)
        .bind(phone)
        .bind(email)
        .bind(address)
        .bind(guardian_name)
        .bind(guardian_phone)
        .bind(guardian_relation)
        .bind(administrative_class_id)
        .bind(user_id)
        .bind(enrollment_date)
        .bind(status.as_deref().unwrap_or("active"))
        .bind(profile_level)
        .bind(profile_tags)
        .bind(notes)
        .bind(id)
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：批量查询学生基本信息（用于别的联查）
     */
    pub async fn batch_get_student_base_info_by_id_list(
        &self,
        schema_name: &String,
        id_list: Vec<Uuid>,
    ) -> Result<Vec<StudentBaseInfo>, String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        if id_list.len() == 0 {
            return Ok(vec![]);
        }
        let mut builder = sqlx::QueryBuilder::new("SELECT * FROM students WHERE id IN ( ");
        for (i, id) in id_list.iter().enumerate() {
            builder.push_bind(id);
            if i < id_list.len() - 1 {
                builder.push(" , ");
            }
        }
        builder
            .push(" )")
            .build_query_as()
            .fetch_all(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }
    /**
     * 作者：朱若彪
     * 说明：分页查询学生基本信息（用于别的联查）
     */
    pub async fn page_students_by_id_list(
        &self,
        schema_name: &String,
        id_list: Vec<Uuid>,
        params: &PageStudentsByHomeworkIdParams,
    )->Result<(Vec<StudentBaseInfo>,i64), String>{
        let PageStudentsByHomeworkIdParams{homework_id,page_params,name_like,student_number,administrative_class_id}=params;
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e|e.to_string())?;
        let(list,total)=StudentsRepository::page_students_by_homework_id(&mut conn,id_list,page_params,name_like,student_number)
            .await
            .map_err(|e|e.to_string())?;
        Ok((list,total))
    }
    /// Get student by student_number
    pub async fn get_student_by_student_number(&self, schema_name: &str,student_number:&str) ->Result<Option<Student>,AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let student = sqlx::query_as::<_, Student>("SELECT * FROM students WHERE student_number = $1")
            .bind(student_number)
            .fetch_optional(&mut *conn)
            .await?;
        Ok(student)
    }

    /// Get student by ID
    pub async fn get_student_by_id(
        &self,
        schema_name: &str,
        id: Uuid,
    ) -> Result<Option<Student>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let student = sqlx::query_as::<_, Student>("SELECT * FROM students WHERE id = $1")
            .bind(id)
            .fetch_optional(&mut *conn)
            .await?;
        Ok(student)
    }

    /// Get student detail with related information
    pub async fn get_student_detail(
        &self,
        schema_name: &str,
        id: Uuid,
    ) -> Result<Option<StudentDetail>, AppError> {
        let student = self.get_student_by_id(schema_name, id).await?;

        if let Some(student) = student {
            let safe_schema = validate_schema_name(schema_name)?;
            let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

            // Get class name from administrative_classes table in the specified schema
            let class_name = if let Some(class_id) = student.administrative_class_id {
                sqlx::query_scalar::<_, String>(&format!(
                    "SELECT class_name FROM {}.administrative_classes WHERE id = $1",
                    safe_schema
                ))
                .bind(class_id)
                .fetch_optional(&mut *conn)
                .await?
            } else {
                None
            };

            // Get grade level name from public.grade_levels table
            let grade_level_name = if let Some(administrative_class_id) = student.administrative_class_id {
                sqlx::query_scalar::<_, String>(
                    "SELECT name FROM public.grade_levels WHERE id = $1",
                )
                .bind(administrative_class_id)
                .fetch_optional(&self.db_pool)
                .await?
            } else {
                None
            };

            // Get teaching classes
            let teaching_classes = sqlx::query_as::<_, StudentTeachingClass>(
                "SELECT * FROM student_teaching_classes WHERE student_id = $1",
            )
            .bind(id)
            .fetch_all(&mut *conn)
            .await?;

            // Get profile levels
            let profile_levels = sqlx::query_as::<_, StudentProfileLevel>(
                "SELECT * FROM student_profile_levels WHERE student_id = $1 ORDER BY assessment_date DESC"
            )
                .bind(id)
                .fetch_all(&mut *conn)
                .await?;

            // Get profile tags
            let profile_tags = sqlx::query_as::<_, StudentProfileTag>(
                "SELECT * FROM student_profile_tags WHERE student_id = $1 ORDER BY created_at DESC",
            )
            .bind(id)
            .fetch_all(&mut *conn)
            .await?;

            let student_detail = StudentDetail {
                student,
                class_name,
                grade_level_name,
                teaching_classes,
                profile_levels,
                profile_tags,
            };

            Ok(Some(student_detail))
        } else {
            Ok(None)
        }
    }

    /// Delete a student
    pub async fn delete_student(&self, schema_name: &str, id: Uuid) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        sqlx::query("DELETE FROM students WHERE id = $1")
            .bind(id)
            .execute(&mut *conn)
            .await?;

        Ok(())
    }

    /// Add student to teaching class
    pub async fn add_teaching_class(
        &self,
        schema_name: &str,
        payload: CreateStudentTeachingClass,
    ) -> Result<StudentTeachingClass, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;
        let teaching_class = sqlx::query_as::<_, StudentTeachingClass>(
            "INSERT INTO student_teaching_classes (student_id, class_id) VALUES ($1, $2) RETURNING *"
        )
        .bind(payload.student_id)
        .bind(payload.class_id)
        .fetch_one(&mut *conn)
        .await?;

        Ok(teaching_class)
    }

    /// Remove student from teaching class
    pub async fn remove_teaching_class(
        &self,
        schema_name: &str,
        student_id: Uuid,
        class_id: Uuid,
    ) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        sqlx::query("DELETE FROM student_teaching_classes WHERE student_id = $1 AND class_id = $2")
            .bind(student_id)
            .bind(class_id)
            .execute(&mut *conn)
            .await?;

        Ok(())
    }

    /// Add or update student profile level
    pub async fn update_profile_level(
        &self,
        schema_name: &str,
        payload: CreateStudentProfileLevel,
    ) -> Result<StudentProfileLevel, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let profile_level = sqlx::query_as::<_, StudentProfileLevel>(
            "INSERT INTO student_profile_levels (student_id, subject, level, level_description, assessment_date, assessed_by)
             VALUES ($1, $2, $3, $4, $5, $6)
             ON CONFLICT (student_id, subject, assessment_date)
             DO UPDATE SET level = EXCLUDED.level, level_description = EXCLUDED.level_description, assessed_by = EXCLUDED.assessed_by, updated_at = NOW()
             RETURNING *"
        )
        .bind(payload.student_id)
        .bind(&payload.subject)
        .bind(&payload.level)
        .bind(&payload.level_description)
        .bind(payload.assessment_date)
        .bind(payload.assessed_by)
        .fetch_one(&mut *conn)
        .await?;

        Ok(profile_level)
    }

    /// Add or update student profile tag
    pub async fn update_profile_tag(
        &self,
        schema_name: &str,
        payload: CreateStudentProfileTag,
    ) -> Result<StudentProfileTag, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let profile_tag = sqlx::query_as::<_, StudentProfileTag>(
            "INSERT INTO student_profile_tags (student_id, tag_name, tag_value, tag_category, created_by)
             VALUES ($1, $2, $3, $4, $5)
             ON CONFLICT (student_id, tag_name)
             DO UPDATE SET tag_value = EXCLUDED.tag_value, tag_category = EXCLUDED.tag_category, updated_at = NOW()
             RETURNING *"
        )
        .bind(payload.student_id)
        .bind(&payload.tag_name)
        .bind(&payload.tag_value)
        .bind(&payload.tag_category)
        .bind(payload.created_by)
        .fetch_one(&mut *conn)
        .await?;

        Ok(profile_tag)
    }

    /// Remove student profile tag
    pub async fn remove_profile_tag(
        &self,
        schema_name: &str,
        student_id: Uuid,
        tag_name: &str,
    ) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        sqlx::query("DELETE FROM student_profile_tags WHERE student_id = $1 AND tag_name = $2")
            .bind(student_id)
            .bind(tag_name)
            .execute(&mut *conn)
            .await?;

        Ok(())
    }

    /// Get students count by class
    pub async fn get_students_count_by_class(
        &self,
        schema_name: &str,
        class_id: Uuid,
    ) -> Result<i64, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM students WHERE administrative_class_id = $1 AND status = 'active'")
            .bind(class_id)
            .fetch_one(&mut *conn)
            .await?;

        Ok(count)
    }

    /// Get students count by grade level
    pub async fn get_students_count_by_grade(
        &self,
        schema_name: &str,
        grade_level_id: Uuid,
    ) -> Result<i64, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM students WHERE grade_level_id = $1 AND status = 'active'",
        )
        .bind(grade_level_id)
        .fetch_one(&mut *conn)
        .await?;

        Ok(count)
    }
}

#[cfg(test)]
mod tests {
    // 注意：这些测试需要数据库连接，在实际环境中运行
    #[tokio::test]
    #[ignore] // 需要数据库连接
    async fn test_create_with_default_filter() {
        // 这里需要一个测试数据库连接
        // let pool = PgPool::connect("postgresql://...").await.unwrap();
        // let service = StudentService::with_default_filter(pool);
        // assert!(service.data_filter_manager.is_some());
    }
}
