use crate::middleware::auth_middleware::{AuthContext, UserTenantLinks};
use crate::model::user::auth::*;
use crate::model::RoleCategory;
use crate::repository::auth::{AuthRepository, PhoneVerificationRepository, UserSessionRepository};
use crate::repository::user::user_repository::UserRepository;
use crate::service::sms::SmsService;
use crate::utils::jwt;
use crate::utils::password::PasswordService;
use anyhow::Result;
use chrono::{Duration, Utc};
use regex::Regex;
use sqlx::PgPool;
use std::net::IpAddr;
use std::sync::Arc;
use tracing::{info, warn};
use uuid::Uuid;
use crate::utils::mask::mask_phone;

pub struct AuthService {
    db: PgPool,
    sms_service: Arc<SmsService>,
    password_service: Arc<PasswordService>,
    phone_regex: Regex,
}

impl AuthService {
    pub fn new(db: PgPool, sms_service: Arc<SmsService>, password_service: Arc<PasswordService>) -> Self {
        let phone_regex = Regex::new(r"^\+?[1-9]\d{1,14}$").unwrap();

        Self {
            db,
            sms_service,
            password_service,
            phone_regex,
        }
    }

    pub async fn get_current_user(&self, user: AuthContext) -> Result<CurrentUserData> {
        let tenants = self.get_user_tenant_infos(user.user_id).await?;
        Ok(CurrentUserData {
            user_id: user.user_id,
            username: user.username,
            phone_number: user.phone_number,
            phone_verified: user.phone_verified.unwrap_or(false),
            tenants,
        })
    }

    pub async fn get_user_tenant_infos(&self, user_id: Uuid) -> Result<Vec<UserTenantInfo>> {
        let mut returns: Vec<UserTenantInfo> = vec![];
        let user_tenant_links = AuthRepository::get_user_tenant_links(&self.db, user_id).await?;
        
        for utl in user_tenant_links {
            let mut uti = UserTenantInfo {
                tenant_id: utl.tenant_id,
                schema_name: utl.schema_name.clone(),
                tenant_name: utl.name,
                user_switchable_infos: vec![],
                user_hit_info: None,
            };
            
            match utl.access_type.as_str() {
                "admin" => uti.user_switchable_infos.push("admin".to_string()),
                "member" => {
                    let roles = AuthRepository::get_user_roles_in_tenant(&self.db, user_id, &utl.schema_name).await?;

                    if AuthRepository::has_role_category(&roles, RoleCategory::EndUser) {
                        let is_parent = AuthRepository::check_is_parent(&self.db, user_id, utl.tenant_id, &utl.schema_name).await?;
                        if is_parent {
                            uti.user_switchable_infos.push("parent".to_string())
                        } else {
                            uti.user_switchable_infos.push("student".to_string())
                        }
                    };
                    
                    if AuthRepository::has_role_category(&roles, RoleCategory::ClassGrade) {
                        uti.user_switchable_infos.push("teacher".to_string())
                    }
                }
                _ => {}
            }

            returns.push(uti);
        }
        Ok(returns)
    }

    pub async fn send_verification_code(&self, phone_number: &str, code_type: &str) -> AuthResult<SendVerificationCodeResponse> {
        // Validate phone number
        let normalized_phone = self.normalize_phone_number(phone_number)?;

        // Check if user exists for login/reset codes
        if code_type == "login" || code_type == "reset" {
            let user_exists = UserRepository::phone_exists(&self.db, &normalized_phone)
                .await
                .map_err(|e| AuthError::DatabaseError(e))?;
            if !user_exists {
                return Err(AuthError::UserNotFound);
            }
        }

        // Check if phone is already registered for registration codes
        if code_type == "registration" {
            let user_exists = UserRepository::phone_exists(&self.db, &normalized_phone)
                .await
                .map_err(|e| AuthError::DatabaseError(e))?;
            if user_exists {
                return Err(AuthError::PhoneAlreadyRegistered);
            }
        }

        // Clean up expired codes
        PhoneVerificationRepository::cleanup_expired_codes(&self.db, &normalized_phone)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        // Check for existing valid code
        if let Some(existing_code) = PhoneVerificationRepository::get_valid_code(&self.db, &normalized_phone, code_type)
            .await
            .map_err(|e| AuthError::DatabaseError(e))? {
            let expires_in = (existing_code.expires_at - Utc::now()).num_seconds();
            return Ok(SendVerificationCodeResponse {
                success: true,
                message: "Verification code already sent".to_string(),
                data: VerificationCodeData {
                    expires_in,
                    can_resend_after: 60, // Can resend after 1 minute
                },
            });
        }

        // Generate new verification code
        let code = self.sms_service.generate_verification_code();
        let expires_at = Utc::now() + Duration::minutes(5);

        // Save code to database
        PhoneVerificationRepository::save_verification_code(&self.db, &normalized_phone, &code, code_type, expires_at)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        // Send SMS
        let delivery_result = self.sms_service.send_verification_code(&normalized_phone, code_type, &code).await?;

        info!(
            "Verification code sent to {} via {}: {}",
            mask_phone(&normalized_phone),
            delivery_result.provider,
            delivery_result.message_id
        );

        Ok(SendVerificationCodeResponse {
            success: true,
            message: "Verification code sent successfully".to_string(),
            data: VerificationCodeData {
                expires_in: 300,      // 5 minutes
                can_resend_after: 60, // 1 minute
            },
        })
    }

    pub async fn register(&self, request: RegisterRequest) -> AuthResult<RegisterResponse> {
        let normalized_phone = self.normalize_phone_number(&request.phone_number)?;

        // Validate password strength
        self.password_service.validate_password_strength(&request.password)?;

        // Verify the verification code
        self.verify_code(&normalized_phone, &request.verification_code, "registration").await?;

        // Check if user already exists
        if UserRepository::phone_exists(&self.db, &normalized_phone)
            .await
            .map_err(|e| AuthError::DatabaseError(e))? {
            return Err(AuthError::PhoneAlreadyRegistered);
        }

        // Hash password
        let (password_hash, salt) = self.password_service.hash_password(&request.password)?;

        // Create user
        let user_id = Uuid::new_v4();
        let now = Utc::now();
        let username = request.username.clone();
        UserRepository::create_user_with_auth(
            &self.db,
            user_id,
            &normalized_phone,
            true,
            Some(now),
            &password_hash,
            &salt,
            username,
        )
        .await
        .map_err(|e| AuthError::DatabaseError(e))?;

        // Mark verification code as used
        PhoneVerificationRepository::mark_code_as_verified(&self.db, &normalized_phone, &request.verification_code)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        // Generate tokens
        let (access_token, refresh_token) = self.generate_tokens(user_id, None).await?;

        // Create session
        UserSessionRepository::create_session(
            &self.db,
            user_id,
            &access_token,
            &refresh_token,
            None,
            request.device_info.as_ref(),
            None,
            None,
        )
        .await
        .map_err(|e| AuthError::DatabaseError(e))?;

        info!("User registered successfully: {}", mask_phone(&normalized_phone));

        Ok(RegisterResponse {
            success: true,
            message: "Registration successful".to_string(),
            data: AuthData {
                user_id,
                access_token,
                refresh_token,
                expires_in: 3600,
                user_profile: UserProfile {
                    username: request.username,
                    phone_number: normalized_phone,
                    phone_verified: true,
                    created_at: now,
                },
            },
        })
    }

    pub async fn login(&self, request: LoginRequest) -> AuthResult<LoginResponse> {
        // Prioritize username login over phone number login
        let user = match (&request.username, &request.phone_number) {
            (Some(username), _) => {
                // Username-based login (primary method)
                UserRepository::get_user_by_username_with_auth(&self.db, username)
                    .await
                    .map_err(|e| AuthError::DatabaseError(e))?
                    .ok_or(AuthError::UserNotFound)?
            }
            (None, Some(phone_number)) => {
                // Phone-based login (fallback method)
                let normalized_phone = self.normalize_phone_number(phone_number)?;
                UserRepository::get_user_by_phone_with_auth(&self.db, &normalized_phone)
                    .await
                    .map_err(|e| AuthError::DatabaseError(e))?
                    .ok_or(AuthError::UserNotFound)?
            }
            (None, None) => {
                // No identifier provided
                return Err(AuthError::InvalidCredentials);
            }
        };

        // Check if account is locked
        if let Some(locked_until) = user.locked_until {
            if locked_until > Utc::now() {
                return Err(AuthError::AccountLocked);
            }
        }

        // Verify password
        if !self.password_service.verify_password(&request.password, &user.password_hash, &user.salt)? {
            UserRepository::increment_failed_attempts(&self.db, user.id, 15)
                .await
                .map_err(|e| AuthError::DatabaseError(e))?;
            return Err(AuthError::InvalidCredentials);
        }

        // Verify SMS code if provided (optional 2FA)
        if let Some(verification_code) = &request.verification_code {
            self.verify_code(&user.phone_number, verification_code, "login").await?;
            PhoneVerificationRepository::mark_code_as_verified(&self.db, &user.phone_number, verification_code)
                .await
                .map_err(|e| AuthError::DatabaseError(e))?;
        }

        // Reset failed attempts and update last login
        UserRepository::reset_failed_attempts_and_update_login(&self.db, user.id)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        // Get user identities
        let identities = AuthRepository::get_user_identities(&self.db, user.id)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;
        let primary_identity = identities.iter().find(|i| i.is_primary).cloned();

        // Generate tokens
        let (access_token, refresh_token) = self.generate_tokens(user.id, primary_identity.as_ref().map(|i| i.identity_id)).await?;

        // Create session
        let _available_identity_ids: Vec<Uuid> = identities.iter().map(|i| i.identity_id).collect();
        UserSessionRepository::create_session(
            &self.db,
            user.id,
            &access_token,
            &refresh_token,
            primary_identity.as_ref().map(|i| i.identity_id),
            request.device_info.as_ref(),
            None,
            None,
        )
        .await
        .map_err(|e| AuthError::DatabaseError(e))?;

        // Log successful login with appropriate identifier
        let identifier = if request.username.is_some() {
            format!("username: {}", user.username)
        } else {
            format!("phone: {}", mask_phone(&user.phone_number))
        };
        info!("User logged in successfully with {}", identifier);

        Ok(LoginResponse {
            success: true,
            message: "Login successful".to_string(),
            data: LoginData {
                user_id: user.id,
                access_token,
                refresh_token,
                expires_in: 3600,
                available_identities: identities,
                current_identity: primary_identity,
            },
        })
    }

    pub async fn refresh_token(&self, refresh_token: &str) -> AuthResult<(String, String)> {
        // Validate refresh token
        let claims = jwt::validate_token(refresh_token)?;

        // Get session
        let session = UserSessionRepository::get_session_by_refresh_token(&self.db, refresh_token)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?
            .ok_or(AuthError::InvalidCredentials)?;

        if !session.is_active || session.refresh_expires_at < Utc::now() {
            return Err(AuthError::InvalidCredentials);
        }

        // Generate new tokens
        let user_id = Uuid::parse_str(&claims.sub).map_err(|_| AuthError::JwtError(jsonwebtoken::errors::Error::from(jsonwebtoken::errors::ErrorKind::InvalidToken)))?;
        let (new_access_token, new_refresh_token) = self.generate_tokens(user_id, session.current_identity_id).await?;

        // Update session with new tokens
        UserSessionRepository::update_session_tokens(&self.db, session.id, &new_access_token, &new_refresh_token)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        Ok((new_access_token, new_refresh_token))
    }

    pub async fn logout(&self, session_token: &str) -> AuthResult<()> {
        UserSessionRepository::logout_session(&self.db, session_token)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;
        Ok(())
    }

    pub async fn change_password(&self, user_id: Uuid, request: ChangePasswordRequest) -> AuthResult<ChangePasswordResponse> {
        // Get current user data
        let user = UserRepository::get_user_by_id_with_auth(&self.db, user_id)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?
            .ok_or(AuthError::UserNotFound)?;

        // Verify current password
        if !self.password_service.verify_password(&request.current_password, &user.password_hash, &user.salt)? {
            return Err(AuthError::InvalidCredentials);
        }

        // Validate new password strength
        self.password_service.validate_password_strength(&request.new_password)?;

        // Check if new password is different from current password
        if self.password_service.verify_password(&request.new_password, &user.password_hash, &user.salt)? {
            return Err(AuthError::PasswordValidationError("New password must be different from current password".to_string()));
        }

        // Verify SMS code if provided (optional extra security)
        if let Some(verification_code) = &request.verification_code {
            self.verify_code(&user.phone_number, verification_code, "password_change").await?;
            PhoneVerificationRepository::mark_code_as_verified(&self.db, &user.phone_number, verification_code)
                .await
                .map_err(|e| AuthError::DatabaseError(e))?;
        }

        // Hash new password
        let (new_password_hash, new_salt) = self.password_service.hash_password(&request.new_password)?;

        // Update password in database
        UserRepository::update_password_and_reset_security(&self.db, user_id, &new_password_hash, &new_salt)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        // Invalidate all existing sessions for security (force re-login)
        UserSessionRepository::logout_all_user_sessions(&self.db, user_id)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?;

        info!("Password changed successfully for user: {}", user_id);

        Ok(ChangePasswordResponse {
            success: true,
            message: "Password changed successfully".to_string(),
            data: Some(ChangePasswordData {
                password_changed_at: Utc::now(),
                requires_relogin: true,
            }),
        })
    }

    // Helper methods
    fn normalize_phone_number(&self, phone: &str) -> AuthResult<String> {
        let cleaned = phone.trim().replace(" ", "").replace("-", "");

        if !self.phone_regex.is_match(&cleaned) {
            return Err(AuthError::InvalidPhoneNumber);
        }

        // Add country code if missing (assuming China +86)
        if cleaned.starts_with("1") && cleaned.len() == 11 {
            Ok(format!("+86{}", cleaned))
        } else if cleaned.starts_with("+") {
            Ok(cleaned)
        } else {
            Ok(format!("+{}", cleaned))
        }
    }


    async fn verify_code(&self, phone: &str, code: &str, code_type: &str) -> AuthResult<()> {
        let verification_code = PhoneVerificationRepository::verify_and_increment_attempts(&self.db, phone, code, code_type)
            .await
            .map_err(|e| AuthError::DatabaseError(e))?
            .ok_or(AuthError::InvalidVerificationCode)?;

        if verification_code.expires_at < Utc::now() {
            return Err(AuthError::VerificationCodeExpired);
        }

        if verification_code.attempts >= verification_code.max_attempts {
            return Err(AuthError::TooManyAttempts);
        }

        Ok(())
    }






    async fn generate_tokens(&self, user_id: Uuid, _identity_id: Option<Uuid>) -> AuthResult<(String, String)> {
        let access_token = jwt::generate_base_token(user_id)?;
        let refresh_token = jwt::generate_base_token(user_id)?; //FIXME: should be a refresh token
        Ok((access_token, refresh_token))
    }


}
