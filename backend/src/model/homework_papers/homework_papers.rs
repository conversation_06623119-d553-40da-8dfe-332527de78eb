use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct HomeworkPapers {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub paper_id: Uuid,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct BindPapersToHomeworkParams {
    pub homework_id: Uuid,
    pub paper_id: Uuid,
}
