use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct QuestionType {
    pub id: Uuid,
    pub code: String,                  // e.g. SINGLE_CHOICE
    pub type_name: String,            // e.g. 单选题
    pub description: Option<String>,  // 可为空
    pub is_active: Option<bool>,      // 可使用 DEFAULT TRUE
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 试题类型查询参数
#[derive(Debug, Deserialize, Clone)]
pub struct QuestionTypeQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub search: Option<String>,
    pub is_active: Option<bool>,
    pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}

#[derive(Debug, Deserialize)]
pub struct QuestionTypeSummaryQuery {
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize,FromRow)]
pub struct QuestionTypeVO {
    pub code: String,
    pub type_name: String,
    pub description: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, FromRow, Serialize)]
pub struct QuestionTypeSummary {
    pub code: String,
    pub type_name: String,
    pub is_active: bool,
}
#[derive(Debug, Deserialize)]
pub struct CreateQuestionTypeRequest {
    pub code: String,
    pub type_name: String,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateQuestionTypeRequest {
    pub code: String,
    pub type_name: Option<String>,
    pub description: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct CheckCodeRequest {
    pub code: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ComposeQuestionType {
    pub question_type_code: String,
    pub subject_code: String,
    pub grade_level_code: String,
    pub created_at: Option<DateTime<Utc>>,
}

/// compose查询参数
#[derive(Debug, Deserialize, Clone)]
pub struct ComposeQuestionTypeQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,

    pub subject_code: Option<String>,
    pub grade_level_code: Option<String>,
    pub question_type_code: Option<String>,
    pub education_stage_code: Option<String>,
}


#[derive(Serialize, Deserialize,FromRow)]
pub struct ComposeQuestionTypeVO {
    pub question_type_name: Option<String>,  // LEFT JOIN 可能为 NULL
    pub question_type_code: String,
    pub subject_code: String,
    pub grade_level_code: String,
    pub subject_name: Option<String>,  // LEFT JOIN 可能为 NULL
    pub grade_level_name: Option<String>,  // LEFT JOIN 可能为 NULL
    pub education_stage_code: Option<String>,  // LEFT JOIN 可能为 NULL
    pub education_stage_name: Option<String>,  // LEFT JOIN 可能为 NULL
}

#[derive(Deserialize)]
pub struct ComposeSubjectGradeRequest {
    pub subject_code: Option<String>,
    pub grade_level_code: Option<String>,
}

#[derive(Deserialize)]
pub struct ComposeCodeRequest {
    pub question_type_code: String,
}

#[derive(Deserialize)]
pub struct ComposeBindRequest {
    #[serde(rename = "questionTypeCode")]
    pub question_type_code: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "gradeLevelCode")]
    pub grade_level_code: String,
}