pub mod question;
pub mod question_type;
pub mod question_answer;
pub mod book;
mod catalog;
pub mod section;
pub mod vo;
pub mod answer_card;

#[cfg(test)]
mod tests {
    use crate::model::public_resource::book::Book;
    use crate::model::public_resource::catalog::Catalog;
    use crate::model::public_resource::question::Question;
    use crate::model::public_resource::section::{QuestionGroupData, SectionQuestionRef, SectionUnit};
    use crate::model::public_resource::vo::section_vo::SectionRef;
    use uuid::Uuid;

    #[test]
    fn test_gen_book() {
        let q1_1 = Question::default_choice();
        let a1_1 = q1_1.gen_answers();
        let q1_2 = Question::default_choice();
        let a1_2 = q1_2.gen_answers();
        let q2_1 = Question::default_inline();
        let a2_1 = q2_1.gen_answers();
        let q2_2 = Question::default_wrap();
        let a2_2 = q2_2.gen_answers();
        let q3_1 = Question::default_multiline();
        let a3_1 = q3_1.gen_answers();
        let q3_2 = Question::default_illustration();
        let a3_2 = q3_2.gen_answers();
        let q4_1 = Question::default_ydlj();
        let a4_1 = q4_1.gen_answers();
        let q5_1 = Question::default_composition();
        let a5_1 = q5_1.gen_answers();
        let section = SectionRef {
            id: Uuid::new_v4(),
            answer_card_id: None,
            snapshots: Some(vec!["/snapshots/1/1.jpg".to_string()]),
            items: vec![
                SectionUnit::Text("想一想".to_string()),
                SectionUnit::Text("关于数据库表名是否应该使用复数形式（如 catalogs vs catalog），\n这是一个在软件开发和数据库设计中长期存在的讨论。结论如下：✅ 推荐做法：使用单数 catalog，而非复数 catalogs".to_string()),
                SectionUnit::QuestionGroup(QuestionGroupData {
                    text: Some("一、 选择题".to_string()),
                    questions: vec![
                        SectionQuestionRef::from(q1_1.id, "1".to_string(), 5.0, &a1_1),
                        SectionQuestionRef::from(q1_2.id, "2".to_string(), 5.0, &a1_2),
                    ],
                }),
                SectionUnit::QuestionGroup(QuestionGroupData {
                    text: Some("二、 填空题".to_string()),
                    questions: vec![
                        SectionQuestionRef::from(q2_1.id, "3".to_string(), 5.0, &a2_1),
                        SectionQuestionRef::from(q2_2.id, "4".to_string(), 5.0, &a2_2),],
                }),
                SectionUnit::QuestionGroup(QuestionGroupData {
                    text: Some("三、 解答题".to_string()),
                    questions: vec![
                        SectionQuestionRef::from(q3_1.id, "5".to_string(), 10.0, &a3_1),
                        SectionQuestionRef::from(q3_2.id, "6".to_string(), 10.0, &a3_2),],
                }),
                SectionUnit::QuestionGroup(QuestionGroupData {
                    text: Some("四、 阅读理解".to_string()),
                    questions: vec![
                        SectionQuestionRef::from(q4_1.id, "7".to_string(), 20.0, &a4_1),],
                }),
                SectionUnit::QuestionGroup(QuestionGroupData {
                    text: Some("作文".to_string()),
                    questions: vec![
                        SectionQuestionRef::from(q5_1.id, "".to_string(), 10.0, &a5_1),],
                }),
            ],
        };
        let book = Book::default();
        let mut catalog1 = Catalog::default();
        catalog1.book_id = book.id;
        catalog1.title = "第一章".to_string();
        let mut catalog2 = Catalog::default();
        catalog2.book_id = book.id;
        catalog2.title = "1.1".to_string();
        catalog2.serial = 1;
        catalog1.level = 2;
        catalog2.section_id = Some(section.id);
        println!("book: {}", serde_json::to_string_pretty(&vec![book]).unwrap());
        println!("catalog: {}", serde_json::to_string_pretty(&vec![catalog1, catalog2]).unwrap());
        println!("section: {}", serde_json::to_string_pretty(&vec![section]).unwrap());
        let questions = vec![q1_1, q1_2, q2_1, q2_2, q3_1, q3_2, q4_1, q5_1];
        println!("questions: {}", serde_json::to_string_pretty(&questions).unwrap());
        let mut answers = Vec::new();
        answers.extend(a1_1);
        answers.extend(a1_2);
        answers.extend(a2_1);
        answers.extend(a2_2);
        answers.extend(a3_1);
        answers.extend(a3_2);
        answers.extend(a4_1);
        answers.extend(a5_1);
        println!("answers: {}", serde_json::to_string_pretty(&answers).unwrap());
        assert_eq!(1 + 1, 2);
    }
}