use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, FromRow, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuestionAnswer {
    pub id: Uuid,
    pub question_id: Uuid,
    pub answer_area_id: u16,
    pub content: String,
    pub explanation: Option<String>, // 解析
    pub updated_at: DateTime<Utc>,
    // difficulty: u8, // 难度
    // knowledge_points: Vec<String>, //知识点
    // tips: Vec<String>, // 解题思路
    // reference: Vec<String>, // 相关学习内容
}

impl QuestionAnswer {
    pub fn new(question_id: Uuid, answer_area_id: u16, content: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            question_id,
            answer_area_id,
            content,
            explanation: None,
            updated_at: Default::default(),
        }
    }
}