use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// 不存在section的目录，点击直接渲染下级小节内容，如果没有下级小节则显示占位没有内容
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct Catalog {
    pub id: Uuid,
    pub book_id: Uuid,
    pub parent_id: Option<Uuid>,
    pub serial: u16,
    pub level: u16,
    pub title: String,
    pub section_id: Option<Uuid>,
    pub updated_at: Option<DateTime<Utc>>,
}
impl Catalog {
    pub fn default() -> Self {
        Self {
            id: Uuid::new_v4(),
            book_id: Default::default(),
            parent_id: None,
            serial: 1,
            level: 1,
            title: "".to_string(),
            section_id: None,
            updated_at: None,
        }
    }
}