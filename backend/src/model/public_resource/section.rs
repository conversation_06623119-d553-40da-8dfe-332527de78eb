use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use sqlx::types::Json;
use uuid::Uuid;
use crate::model::public_resource::question_answer::QuestionAnswer;

#[derive(Debug, FromRow)]
pub struct Section {
    pub id: Uuid,
    pub answer_card_id: Option<Uuid>,
    pub items: Json<Vec<SectionUnit<SectionQuestionRef>>>,
    pub snapshots: Option<Vec<String>>,
    pub updated_at: DateTime<Utc>,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "key", content = "value", rename_all = "camelCase")]
pub enum SectionUnit<T> {
    Text(String),
    QuestionGroup(QuestionGroupData<T>),
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuestionGroupData<T> {
    pub text: Option<String>,
    pub questions: Vec<T>,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SectionQuestionRef {
    pub question_id: Uuid,
    pub question_number: String,
    pub answer_refs: Vec<QuestionAnswerAreaRef>,
}

impl SectionQuestionRef {
    pub fn from(question_id: Uuid, question_number: String, score: f64, answer_list: &Vec<QuestionAnswer>) -> Self {
        let size = answer_list.len();
        let answer_refs = answer_list.into_iter().enumerate().map(|(index, v) | {
            let answer_area_number = if size > 1 {
                Some(format!("{}.{}", question_number, index + 1))
            } else { None };
            QuestionAnswerAreaRef {
                score: score / size as f64,
                answer_area_id: v.answer_area_id,
                answer_area_number,
                answer_ids: vec![],
            }
        }).collect();
        Self {
            question_id,
            question_number,
            answer_refs,
        }
    }
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuestionAnswerAreaRef {
    pub score:f64,
    pub answer_area_id: u16,
    pub answer_area_number: Option<String>,
    pub answer_ids: Vec<Uuid>,
}