use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use sqlx::types::Json;
use uuid::Uuid;
use super::question_answer::QuestionAnswer as Answer;

#[derive(Debug, Serialize, Deserialize, <PERSON>lone, FromRow)]
#[serde( rename_all = "camelCase")]
pub(crate) struct Question {
    pub id: Uuid,
    pub question_type_code: String,
    pub items: Json<Vec<QuestionUnit>>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "key", content = "value", rename_all = "camelCase")]
enum QuestionUnit {
    Content(ContentData), // 结束不换行，不可分割
    Choice(ChoiceData), // 换行
    Illustration(IllustrationData), // 插图, 环绕/内嵌
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
struct ContentData {
    content: String,
    answer_area: Option<AnswerArea>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
struct ChoiceData {
    choices: Vec<String>,
    answer_area: AnswerArea,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
struct IllustrationData {
    url: String,
    width: u32,
    height: u32,
    answer_area: Vec<AnswerArea>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
struct AnswerArea {
    id: u16,
    value: AnswerAreaValue
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "key", content = "value", rename_all = "camelCase")]
enum AnswerAreaValue {
    Choices,
    TrueOrFalse,
    MultiLine(u16),
    Inline(u16),
    Composition(u16),
    Wrap,
    ImageLocation((f64, f64, f64, f64)),
}

impl Question {
    pub fn gen_answers(&self) -> Vec<Answer> {
        let mut answers: Vec<Answer> = Vec::new();
        for item in &self.items.0 {
            match item {
                QuestionUnit::Content(c) => {
                    if let Some(ref a) = c.answer_area {
                        answers.push(Answer::new(self.id, a.id, "my_answer".to_string()));
                    }
                }
                QuestionUnit::Choice(c) => {
                    answers.push(Answer::new(self.id, c.answer_area.id, "A".to_string()));
                }
                QuestionUnit::Illustration(i) => {
                    for x in i.answer_area.iter() {
                        answers.push(Answer::new(self.id, x.id, "xxx".to_string()));
                    }
                }
            }
        }
        answers
    }
    pub fn default_choice() -> Question {
        let content = ContentData {
            content: "执行以下代码后，控制台的输出结果是什么".to_string(),
            answer_area: None,
        };
        let choices = ChoiceData {
            choices: vec!["0 1 2".to_string(), "1 2 3".to_string(), "3 3 3".to_string(), "1 2 2".to_string()],
            answer_area: AnswerArea { id: 0, value: AnswerAreaValue::Choices },
        };
        let qu1 = QuestionUnit::Content(content);
        let qu2 = QuestionUnit::Choice(choices);
        let items = vec![qu1, qu2];
        Question {
            id: Uuid::new_v4(),
            question_type_code: "".to_string(),
            items: Json(items),
            updated_at: Default::default(),
        }
    }
    pub fn default_wrap() -> Question {
        let content1 = ContentData {
            content: "设计一个“试题答案”实体时".to_string(),
            answer_area: None,
        };
        let content2 = ContentData {
            content: "执行".to_string(),
            answer_area: Some(AnswerArea { id: 0, value: AnswerAreaValue::Wrap }),
        };
        let qu1 = QuestionUnit::Content(content1);
        let qu2 = QuestionUnit::Content(content2);
        let items = vec![qu1, qu2];
        Question {
            id: Uuid::new_v4(),
            question_type_code: "选择".to_string(),
            items: Json(items),
            updated_at: Default::default(),
        }
    }
    pub fn default_inline() -> Question {
        let content = ContentData {
            content: "执行以下代码后，控制台的输出结果是".to_string(),
            answer_area: Some(AnswerArea { id: 0, value: AnswerAreaValue::Inline(4) }),
        };
        let qu1 = QuestionUnit::Content(content);
        let qu2 = QuestionUnit::Content(ContentData {
            content: ",无需引入外部库，只需使用".to_string(),
            answer_area: Some(AnswerArea { id: 0, value: AnswerAreaValue::MultiLine(2) }),
        });
        let items = vec![qu1, qu2];
        Question {
            id: Uuid::new_v4(),
            question_type_code: "填空".to_string(),
            items: Json(items),
            updated_at: Default::default(),
        }
    }
    pub fn default_multiline() -> Question {
        let content = ContentData {
            content: "执行以下代码后，控制台的输出结果是什么？".to_string(),
            answer_area: Some(AnswerArea { id: 0, value: AnswerAreaValue::MultiLine(8) }),
        };
        let qu1 = QuestionUnit::Content(content);
        let items = vec![qu1];
        Question {
            id: Uuid::new_v4(),
            question_type_code: "简答".to_string(),
            items: Json(items),
            updated_at: Default::default(),
        }
    }
    pub fn default_composition() -> Question {
        let content = ContentData {
            content: "请写一篇以祖国为主题的议论文，要求不少于800字。".to_string(),
            answer_area: Some(AnswerArea { id: 0, value: AnswerAreaValue::Composition(1200) }),
        };
        let qu1 = QuestionUnit::Content(content);
        let items = vec![qu1];
        Question {
            id: Default::default(),
            question_type_code: "作文".to_string(),
            items: Json(items),
            updated_at: Default::default(),
        }
    }
    pub fn default_illustration() -> Question {
        let content = ContentData {
            content: "这是最简洁、最常用的模式，序列化后得到 \"active\" 这样的字符串，清晰且易于前端解析。".to_string(),
            answer_area: None,
        };
        let qu1 = QuestionUnit::Content(content);
        let qu2 = QuestionUnit::Illustration(IllustrationData {
            url: "/a/a1.jpg".to_string(),
            width: 200,
            height: 100,
            answer_area: vec![AnswerArea {
                id: 0, value: AnswerAreaValue::ImageLocation((20.0, 20.0, 60.0, 30.0))
            },AnswerArea {
                id: 1, value: AnswerAreaValue::ImageLocation((20.0, 60.0, 60.0, 30.0))
            }],
        });
        let content = ContentData {
            content: "\n的序列化行为取决于它的 枚举类型（单元、元组、结构体）以及 serde 的默认规则。".to_string(),
            answer_area: None,
        };
        let qu3 = QuestionUnit::Content(content);
        let items = vec![qu1,qu2,qu3];
        Question {
            id: Uuid::new_v4(),
            question_type_code: "实验".to_string(),
            items: Json(items),
            updated_at: Default::default(),
        }
    }
    pub fn default_ydlj() -> Question {
        let content = ContentData {
            content: r#"    <p align="center">(A) </p>What makes a good reader? You can find the answer in Readers Club. Today, we have some books for you. Come and join the fun!

    The Old Man and the Sea won the Pulitzer Prize for fiction in 1953. A year later, Hemingway won the Nobel Prize for Literature. The Old Man and the Sea is generally considered by many to be his greatest achievement. The leading character in this book is a person who can face difficulties and never give up.

    Hardback ￥21.70 Paperback ￥16.00

    Heart, written by Edmondo De Amicis from Italy, is a diary of an Italian boy Eric. In his diary, he writes about the greatest love in the world: love for the nation, for the society, and also teachers’ love, classmates’ love, parents’ love, children’s love.

    Hardback ￥27.20 Paperback ￥16.40

    Peter Pan, created by Scottish novelist and playwright J.M. Barrie. Peter Pan is a young boy who can fly and never grows up. He spends his never-ending childhood having adventures(冒险) on the mythical island of Neverland as the leader of the Lost Boys, playing happily with children both inside and outside Neverland.

    Hardback ￥22.70 Paperback ￥14.60

    Celebrity Biography, also known as “Three Giants”, written by Romain Rolland, about three great men in different fields: Beethoven, Michelangelo and Leo Tolstoy. They all suffered(遭受) a lot in body and spirit, but never lost confidence.

    Hardback ￥26.20 Paperback ￥15.80

    Club members will get a discount(折扣) of 10% for hardback books and 20% for paperback books. If you buy any of the two hardback books, e-books will be free for you.

    "#.to_string(),
            answer_area: None,
        };
        Question {
            id: Uuid::new_v4(),
            question_type_code: "阅读理解".to_string(),
            items: Json(vec![
                QuestionUnit::Content(content),
                QuestionUnit::Content(ContentData {
                    content: "Who won the Nobel Prize for literature?".to_string(),
                    answer_area: None,
                }),
                QuestionUnit::Choice(ChoiceData {
                    choices: vec!["Hemingway".to_string(), "Leo Tolstoy.".to_string(), "Peter Pan".to_string()],
                    answer_area: AnswerArea { id: 0, value: AnswerAreaValue::Choices } }),
                QuestionUnit::Content(ContentData {
                    content: "Which word can be used to describe the topic of the book Heart?".to_string(),
                    answer_area: None,
                }),
                QuestionUnit::Choice(ChoiceData {
                    choices: vec!["Love".to_string(), "Success".to_string(), "Peter Pan".to_string()],
                    answer_area: AnswerArea { id: 0, value: AnswerAreaValue::Choices } }),
            ]),
            updated_at: Default::default(),
        }
    }
}