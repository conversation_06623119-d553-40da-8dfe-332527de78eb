use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::public_resource::question::Question;
use crate::model::public_resource::section::SectionUnit;
use crate::model::public_resource::vo::section_vo::SectionQuestionDetail;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PaperDetail {
    pub id: Uuid,
    pub title: String,
    pub answer_card_id: Option<Uuid>,
    pub subject_code: Option<String>,
    pub grade_level_code: Option<String>,
    pub paper_contents: Vec<SectionUnit::<SectionQuestionDetail>>,
}