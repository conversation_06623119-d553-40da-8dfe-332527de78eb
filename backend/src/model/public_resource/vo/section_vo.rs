use crate::model::public_resource::question::Question;
use crate::model::public_resource::question_answer::QuestionAnswer;
use crate::model::public_resource::section::{SectionQuestionRef, SectionUnit};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SectionRef {
    pub id: Uuid,
    pub answer_card_id: Option<Uuid>,
    pub snapshots: Option<Vec<String>>,
    pub items: Vec<SectionUnit<SectionQuestionRef>>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SectionDetail {
    pub id: Uuid,
    pub answer_card_id: Option<Uuid>,
    pub snapshots: Option<Vec<String>>,
    pub items: Vec<SectionUnit<SectionQuestionDetail>>,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SectionQuestionDetail {
    pub question_id: Uuid,
    pub question: Option<Question>,
    pub question_number: String,
    pub answer_area_details: Vec<QuestionAnswerAreaDetail>
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
struct QuestionAnswerAreaDetail {
    pub score:f64,
    pub answer_area_id: u16,
    pub answer_area_number: Option<String>,
    pub answers: Vec<QuestionAnswer>
}

impl SectionQuestionDetail {
    pub fn from(question: Question, number: String, score: f64) -> Self {
        let answer_list = question.gen_answers();
        let size = answer_list.len();
        let answers = answer_list.into_iter().enumerate().map(|(index, a)| {
            let answer_area_number = if size > 1 {
                Some(format!("{}.{}", number, index + 1))
            } else { None };
            QuestionAnswerAreaDetail {
                score: score / size as f64,
                answer_area_id: a.answer_area_id,
                answer_area_number,
                answers: vec![a],
            }
        }).collect();
        Self {
            question_id: question.id,
            question: Some(question),
            answer_area_details: answers,
            question_number: number,
        }
    }
}