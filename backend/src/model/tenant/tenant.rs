use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Tenant {
    pub id: Uuid,
    pub name: String,
    #[serde(rename = "tenantType")]
    pub tenant_type: String,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    pub domain: Option<String>,
    pub status: Option<String>, // 'active', 'inactive', 'suspended'
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdBy")]
    pub created_by: Option<Uuid>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TenantStats {
    pub total: Option<i64>,
    pub active: Option<i64>,
    pub deleted: Option<i64>,
    pub schools: Option<i64>,
    pub standard: Option<i64>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct TenantQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub search: Option<String>,
    pub is_active: Option<bool>,
    pub status:Option<String>,
    pub tenant_type: Option<String>,
    pub order_by: Option<String>, // 排序字段：name, schema_name, tenant_type, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTenantRequest {
    pub name: String,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    #[serde(rename = "tenantType")]
    pub tenant_type: Option<String>,
    pub domain: Option<String>,
    pub settings: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTenantRequest {
    pub name: Option<String>,
    pub domain: Option<String>,
    pub status: Option<String>,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "tenantType")]
    pub tenant_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantResponse {
    pub id: Uuid,
    pub name: String,
    #[serde(rename = "tenantType")]
    pub tenant_type: String,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    pub domain: Option<String>,
    pub status: Option<String>,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

impl From<Tenant> for TenantResponse {
    fn from(tenant: Tenant) -> Self {
        Self {
            id: tenant.id,
            name: tenant.name,
            tenant_type: tenant.tenant_type,
            schema_name: tenant.schema_name,
            domain: tenant.domain,
            status: tenant.status,
            settings: tenant.settings,
            created_at: tenant.created_at,
            updated_at: tenant.updated_at,
        }
    }
}