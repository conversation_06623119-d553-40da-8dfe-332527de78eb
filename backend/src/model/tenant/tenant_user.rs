use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use sqlx::FromRow;

/// 租户用户关联模型，对应 user_tenant_links 表
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TenantUser {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub access_type: String,
    pub granted_by: Option<Uuid>,
    pub granted_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub last_accessed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 租户用户视图对象，用于 API 响应
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TenantUserVO {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub access_type: String,
    pub granted_by: Option<Uuid>,
    pub granted_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub last_accessed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    // 关联信息
    pub username: Option<String>,
    pub phone_number: Option<String>,
    pub granted_by_name: Option<String>,
}

// 类型转换实现
impl From<TenantUser> for TenantUserVO {
    fn from(tenant_user: TenantUser) -> Self {
        Self {
            id: tenant_user.id,
            user_id: tenant_user.user_id,
            tenant_id: tenant_user.tenant_id,
            access_type: tenant_user.access_type,
            granted_by: tenant_user.granted_by,
            granted_at: tenant_user.granted_at,
            expires_at: tenant_user.expires_at,
            last_accessed_at: tenant_user.last_accessed_at,
            created_at: tenant_user.created_at,
            updated_at: tenant_user.updated_at,
            username: None,
            phone_number: None,
            granted_by_name: None,
        }
    }
}
