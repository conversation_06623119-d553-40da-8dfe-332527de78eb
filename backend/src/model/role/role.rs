use serde::{Deserialize, Serialize};
use sqlx::{FromRow};
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// 角色分类枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, sqlx::Type)]
// todo debug: 这里存在问题，有时候会导致查询 /me 接口出错，可能问题是数据连接池？
//  2025年8月18日 改为使用public.role_category时正常
//  2025年8月19日 在绑定时使用时候又出现问题，    "message": "用户信息查询失败：error occurred while decoding column \"category\": mismatched types; Rust type `deep_mate::model::role::role::RoleCategory` (as SQL type `public.role_category`) is not compatible with SQL type `role_category`",
//  2025年8月19日 在/me查询时候又遇到问题，用户信息查询失败：error occurred while decoding column \"category\": mismatched types; Rust type `deep_mate::model::role::role::RoleCategory` (as SQL type `role_category`) is not compatible with SQL type `public.role_category`
#[sqlx(type_name = "role_category", rename_all = "lowercase")]
pub enum RoleCategory {
    #[serde(rename = "system")]
    System,        // 系统级用户
    #[serde(rename = "tenant")]
    Tenant,        // 租户级用户
    #[serde(rename = "school")]
    School,        // 学校管理用户
    #[serde(rename = "business")]
    Business,      // 业务操作用户
    #[serde(rename = "class_grade")]
    #[sqlx(rename = "class_grade")]
    ClassGrade,    // 班级/年级用户
    #[serde(rename = "end_user")]
    #[sqlx(rename = "end_user")]
    EndUser,       // 终端用户
}

/// 角色级别枚举
#[derive(Debug, Copy, Clone, Serialize, Deserialize, PartialEq, sqlx::Type)]
#[repr(i32)]
pub enum RoleLevel {
    SuperAdmin = 1,      // 系统超级管理员
    TenantAdmin = 2,     // 租户管理员
    Principal = 3,       // 校长
    AcademicDirector = 4, // 教导主任
    SubjectLeader = 5,   // 学科组长
    GradeLeader = 6,     // 年级长
    ClassTeacher = 7,    // 班主任
    Teacher = 8,         // 任课老师
    Student = 9,         // 学生
    Parent = 10,         // 家长
}

/// 角色数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub code: String,
    pub description: Option<String>,
    pub category: RoleCategory,
    pub level: RoleLevel,
    pub is_system: bool,
    pub is_active: bool,
    pub tenant_id: Option<Uuid>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 角色创建请求
#[derive(Debug, Deserialize)]
pub struct CreateRoleRequest {
    pub name: String,
    pub code: String,
    pub description: Option<String>,
    pub category: RoleCategory,
    pub level: RoleLevel,
    pub permissions: Vec<Uuid>,
    pub is_active: bool,
    pub tenant_id: Option<Uuid>,
}

/// 角色更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateRoleRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub permissions: Option<Vec<Uuid>>,
    pub is_active: Option<bool>,
}

/// 角色查询参数
#[derive(Debug, Deserialize)]
pub struct RoleQueryParams {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub search: Option<String>,
    pub category: Option<RoleCategory>,
    pub level: Option<RoleLevel>,
    pub is_active: Option<bool>,
    pub tenant_id: Option<Uuid>,
}

/// 分配角色请求
#[derive(Debug, Deserialize)]
pub struct AssignRoleRequest {
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub role_id: Uuid,
    pub target_type: Option<String>,
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    pub display_name: Option<String>,
}

/// 角色响应VO（包含权限信息）
#[derive(Debug, Serialize)]
pub struct RoleVO {
    pub id: Uuid,
    pub name: String,
    pub code: String,
    pub description: Option<String>,
    pub category: RoleCategory,
    pub level: RoleLevel,
    pub is_system: bool,
    pub is_active: bool,
    pub tenant_id: Option<Uuid>,
    pub permissions: Vec<crate::model::role::permission::Permission>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 角色统计信息
#[derive(Debug, Serialize)]
pub struct RoleStatistics {
    pub total_roles: i64,
    pub active_roles: i64,
    pub system_roles: i64,
    pub tenant_roles: i64,
    pub user_count_by_role: std::collections::HashMap<String, i64>,
}

/// 用户身份响应VO（包含角色信息）- PRD 6.3.2 compliant
#[derive(Debug, Serialize)]
pub struct UserIdentityVO {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Uuid, // Passed from context, not stored in user_identities table
    pub role: RoleVO,
    pub target_type: String, // 'school', 'subject_group', 'grade', 'class', 'student'
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Role {
    /// 检查是否是系统预设角色
    pub fn is_system_role(&self) -> bool {
        self.is_system
    }

    /// 获取角色完整显示名称
    pub fn full_display_name(&self) -> String {
        format!("{} ({})", self.name, self.code)
    }
}

impl RoleLevel {
    /// 从整数转换为角色级别
    pub fn from_i32(value: i32) -> Option<Self> {
        match value {
            1 => Some(RoleLevel::SuperAdmin),
            2 => Some(RoleLevel::TenantAdmin),
            3 => Some(RoleLevel::Principal),
            4 => Some(RoleLevel::AcademicDirector),
            5 => Some(RoleLevel::SubjectLeader),
            6 => Some(RoleLevel::GradeLeader),
            7 => Some(RoleLevel::ClassTeacher),
            8 => Some(RoleLevel::Teacher),
            9 => Some(RoleLevel::Student),
            10 => Some(RoleLevel::Parent),
            _ => None,
        }
    }

    /// 转换为整数
    pub fn to_i32(&self) -> i32 {
        *self as i32
    }
}

/// 预定义的系统角色
pub struct SystemRoles;

impl SystemRoles {
    pub const SUPER_ADMIN: &'static str = "super_admin";
    pub const SYSTEM_AGENT: &'static str = "system_agent";
    pub const OPERATOR: &'static str = "operator";
    pub const TENANT_ADMIN: &'static str = "tenant_admin";
    pub const PRINCIPAL: &'static str = "principal";
    pub const ACADEMIC_DIRECTOR: &'static str = "academic_director";
    pub const SUBJECT_LEADER: &'static str = "subject_leader";
    pub const EXAM_MANAGER: &'static str = "exam_manager";
    pub const GRADING_MANAGER: &'static str = "grading_manager";
    pub const SCAN_OPERATOR: &'static str = "scan_operator";
    pub const GRADER: &'static str = "grader";
    pub const GRADE_LEADER: &'static str = "grade_leader";
    pub const CLASS_TEACHER: &'static str = "class_teacher";
    pub const TEACHER: &'static str = "teacher";
    pub const STUDENT: &'static str = "student";
    pub const PARENT: &'static str = "parent";
}