use serde::{Deserialize, Serialize};
use sqlx::prelude::FromRow;
use uuid::Uuid;

/// 分页查询参数
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
}

impl PageParams {
    /// 获取页码，默认为1
    pub fn get_page(&self) -> i32 {
        self.page.unwrap_or(1) as i32
    }

    /// 获取每页大小，默认为10
    pub fn get_page_size(&self) -> i32 {
        self.page_size.unwrap_or(10) as i32
    }

    /// 计算偏移量
    pub fn get_offset(&self) -> i64 {
        let page = self.page.unwrap_or(1);
        let page_size = self.page_size.unwrap_or(10);
        (page - 1) * page_size
    }

    /// 获取限制数量
    pub fn get_limit(&self) -> i64 {
        self.page_size.unwrap_or(10)
    }
}

/// 分页结果
#[derive(Debug, Serialize, Deserialize)]
pub struct PageResult<T> {
    pub data: Vec<T>,
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
    pub total_pages: i64,
}

/**
 * 对于参数仅有一个id字符串字段的参数的情况下的通用参数
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IdStringParams {
    pub id: String,
}

/**
 * 作者：张瀚
 * 说明：对于常用的数据库查询结果是一个ID字段和一个字符串字段时的通用返回对象，例如查询名称一类的
 */
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct DBUuidAndStringResult {
    pub id: Uuid,
    pub value: String,
}
