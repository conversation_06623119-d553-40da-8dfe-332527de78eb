use sqlx::FromRow;
use serde::{Serialize, Deserialize};

/// Casbin策略记录
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct CasbinPolicyRecord {
    pub id: i64,
    pub ptype: String,
    pub v0: Option<String>,    // subject (角色名或用户身份)
    pub v1: Option<String>,    // domain (租户schema_name)
    pub v2: Option<String>,    // object (资源:变量定义)
    pub v3: Option<String>,    // action (操作类型)
    pub v4: Option<String>,    // effect (allow/deny)
    pub v5: Option<String>,    // 扩展字段
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: Option<chrono::DateTime<chrono::Utc>>,
}