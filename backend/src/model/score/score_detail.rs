use bigdecimal::{BigDecimal, Zero};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use sqlx::types::Json;
use uuid::Uuid;
use qc_sqlx_derive::QcSqlxEnum;

#[derive(Debug, FromRow)]
pub struct ScoreDetail {
    pub id: Uuid,
    pub score_id: Uuid,
    pub status: ScoreDetailStatus,
    pub score: BigDecimal,
    pub scoring_type: <PERSON><PERSON><ScoringType>,
    pub created_at: DateTime<Utc>,
    pub ocr: Option<String>,
    pub reason: Json<ScoreReason>,
}

impl ScoreDetail {
    pub fn default() -> Self {
        Self {
            id: Uuid::new_v4(),
            score_id: Uuid::new_v4(),
            status: ScoreDetailStatus::OcrFailed,
            score: BigDecimal::zero(),
            scoring_type: <PERSON><PERSON>(ScoringType::AI),
            created_at: Default::default(),
            ocr: None,
            reason: <PERSON><PERSON>(ScoreReason::None),
        }
    }
}
#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum ScoreReason {
    None,
    Blank,
    Text(String),
    Composition
}
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScoringType {
    Match(MatchMode), // 题卡匹配
    AI,     // 自动AI阅卷
    Manual, // 老师手阅(登分)
    Online, // 在线分发阅卷
    Check, // 核查
}
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "PascalCase")]
pub enum MatchMode {
    Exact,
    Partial,
    Count,
}
#[derive(Clone, Debug, PartialEq, Eq, QcSqlxEnum, Serialize)]
#[serde(rename_all = "PascalCase")]
pub enum ScoreDetailStatus {
    OcrFailed,
    GradingFailed,
    Done
}