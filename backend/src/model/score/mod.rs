pub mod score_detail;

use bigdecimal::Zero;
use serde::{Deserialize, Serialize};
use qc_sqlx_derive::QcSqlxEnum;
use sqlx::types::BigDecimal;
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, FromRow,Clone, Serialize)]
pub struct Score {
    pub id: Uuid,
    pub criteria_id: Uuid,
    pub student_id: Option<Uuid>,
    pub score: BigDecimal,
    pub score_status: ScoreStatus,
}
impl Score {
    pub fn default() -> Self {
        Self {
            id: Uuid::new_v4(),
            criteria_id: Default::default(),
            student_id: None,
            score: BigDecimal::zero(),
            score_status: ScoreStatus::Excepted,
        }
    }
    pub fn new(id:Uuid, criteria_id:Uuid, student_id:Option<Uuid>)->Self{
        Self {
            id,
            criteria_id,
            student_id,
            score_status: ScoreStatus::Undistributed, //默认为未分发
            score: BigDecimal::zero(),
        }
    }
}
#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq, QcSqlxEnum, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub enum ScoreStatus {
    Undistributed,
    Distributed,
    Excepted,
    Done,
    CheckedError,
    CheckedCorrect,
}