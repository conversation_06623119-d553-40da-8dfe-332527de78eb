use std::fmt::{Display, Formatter,Result};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::types::Json;
use sqlx::{FromRow, Type};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
#[sqlx(type_name = "VARCHAR", rename_all = "lowercase")]
#[serde(rename_all = "lowercase")]
pub enum WorkflowCategory {
    Ocr,
    Correction,
}

impl Display for WorkflowCategory {
    fn fmt(&self, f: &mut Formatter<'_>) -> Result {
        let s = match self {
            WorkflowCategory::Ocr => "ocr",
            WorkflowCategory::Correction => "correction",
        };
        write!(f, "{}", s)
    }
}


#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Workflow {
    pub id: Uuid,
    pub workflow_name: String,
    pub workflow_type: WorkflowCategory,
    pub description: Option<String>,
    pub config: serde_json::Value,
    pub is_active: bool,
    pub version: i32,
    pub created_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct WorkflowSetting {
    pub id: Uuid,
    pub workflow_id: Uuid,
    pub subject_codes: Vec<String>,
    pub grade_level_codes: Vec<String>,
    pub question_type_codes: Vec<String>,
    pub schema_names: Vec<String>,
    pub priority: i32,
    pub is_default: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct WorkflowSettingQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,

    pub subject_code: Option<String>,
    pub grade_level_code: Option<String>,
    pub question_type_code: Option<String>,
    pub schema_name: Option<String>,
    pub workflow_type: Option<WorkflowCategory>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct WorkflowSettingSummaryQueryParams {
    pub subject_code: Option<String>,
    pub grade_level_code: Option<String>,
    pub question_type_code: Option<String>,
    pub schema_name: Option<String>,
    pub workflow_type: Option<WorkflowCategory>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct WorkflowQueryParams {
    pub workflow_type: WorkflowCategory,
}

#[derive(Debug, Deserialize, Clone)]
pub struct WorkflowSettingCreateParams {
    pub workflow_setting_id: Option<Uuid>,

    pub subject_codes: Option<Vec<String>>,
    pub grade_level_codes: Option<Vec<String>>,
    pub question_type_codes: Option<Vec<String>>,
    pub schema_names: Option<Vec<String>>,
    pub workflow_id: Uuid,
    
    pub priority: Option<i32>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct WorkflowSettingDeleteParams {
    pub workflow_setting_id: Uuid,
}


#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct WorkflowSettingVO {
    pub id: Uuid,

    pub workflow_name: String,
    pub workflow_id: Uuid,
    pub workflow_type: WorkflowCategory,
    pub description: String,

    pub subject_codes: Json<Vec<String>>,
    pub grade_level_codes: Json<Vec<String>>,
    pub question_type_codes: Json<Vec<String>>,
    pub schema_names: Json<Vec<String>>,
    pub priority: i32,

    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct WorkflowSummaryVO {
    pub workflow_name: String,
    pub workflow_id: Uuid,
    pub workflow_type: WorkflowCategory,
    pub description: String,
}

#[derive(Debug, Deserialize)]
pub struct WorkflowApiResponseItem {
    pub id: Uuid,
    pub name: String,
    pub info: String, // 注意是 String，不是对象
    pub category: WorkflowCategory,
}

#[derive(Debug, Deserialize)]
pub struct InfoObject {
    pub description: String,
}
