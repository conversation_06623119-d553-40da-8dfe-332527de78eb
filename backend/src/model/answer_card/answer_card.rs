use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use sqlx::types::Json;
use chrono::{DateTime, Utc};
use crate::model::paper::paper_cache::AdmissionTicketNumberInfoQuestionItemConfig;
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct AnswerCard{
    pub id: Uuid,
    pub paper_id: Uuid,
    pub dpi: i32,
    pub width: f32,
    pub height: f32,
    //左边距
    pub x: f32,
    //上边距
    pub y: f32,
    //右边距
    pub right: f32,
    //下边距
    pub bottom: f32,
    //是否纵向
    pub page_orientation_is_vertical: bool,
    //分栏数量
    pub bucket_size: i32,
    //是否显示页码
    pub show_page_index: bool,
    //是否显示定位点,定位点位于页面内部四个顶点，上面两个是横向，下面两个竖向
    pub show_pos_point: bool,
    //定位点宽度，指左上角那个
    pub pos_point_width: f32,
    //定位点高度度，指左上角那个
    pub pos_point_height: f32,
     //总页码
    pub page_total: i32,
    //答题卡信息
    pub admission_ticket_number_info_question_item_config: Json<AdmissionTicketNumberInfoQuestionItemConfig>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}
pub struct UpdateAnswerCardParams{
    pub dpi: Option<i32>,
    pub width: Option<f32>,
    pub height: Option<f32>,
    pub x: Option<f32>,
    pub y: Option<f32>,
    pub right: Option<f32>,
    pub bottom: Option<f32>,
    pub page_orientation_is_vertical: Option<bool>,
    pub bucket_size: Option<i32>,
    pub show_page_index: Option<bool>,
    pub show_pos_point: Option<bool>,
    pub pos_point_width: Option<f32>,
    pub pos_point_height: Option<f32>,
    pub page_total: Option<i32>,
    pub admission_ticket_number_info_question_item_config: Option<Json<AdmissionTicketNumberInfoQuestionItemConfig>>,
}


