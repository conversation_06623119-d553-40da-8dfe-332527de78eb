use crate::model::{RoleCategory};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub phone_number: String,
    pub phone_verified: bool,
    pub phone_verified_at: Option<DateTime<Utc>>,
    pub password_hash: String,
    pub salt: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_at: Option<DateTime<Utc>>,
    pub is_active: bool,
    pub failed_login_attempts: i32,
    pub locked_until: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PhoneVerificationCode {
    pub id: Uuid,
    pub phone_number: String,
    pub verification_code: String,
    pub code_type: String,
    pub expires_at: DateTime<Utc>,
    pub attempts: i32,
    pub max_attempts: i32,
    pub verified: bool,
    pub verified_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct UserIdentity {
    pub id: Uuid,
    pub user_id: Uuid,
    pub role_id: Uuid,
    pub target_type: String, // 'school', 'subject_group', 'grade', 'class', 'student'
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}


#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Roles {
    pub name: String,
    pub code: String,
    pub description: String,
    pub category: RoleCategory,
    pub level: i32,
}


#[derive(Serialize)]
pub struct CurrentUserData {
    pub user_id: Uuid,
    pub username: String,
    pub phone_number: String,
    pub phone_verified: bool,
    // pub current_identity: Option<IdentityInfo>,
    // pub available_identities: Vec<IdentityInfo>,
    pub tenants: Vec<UserTenantInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserTenantInfo {
    pub tenant_id: Uuid,
    pub schema_name: String,
    pub tenant_name: String,
    pub user_switchable_infos: Vec<String>,
    pub user_hit_info: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct IdentityBindingSuggestion {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub suggested_identity_type: String,
    pub suggested_target_id: Uuid,
    pub suggested_target_name: String,
    pub match_confidence: Option<f64>,
    pub match_criteria: Option<serde_json::Value>,
    pub status: String,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub processed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ParentStudentRelation {
    pub id: Uuid,
    pub parent_user_id: Uuid,
    pub student_user_id: Uuid,
    pub student_tenant_id: Uuid,
    pub student_identity_id: Uuid,
    pub relationship_type: String,
    pub verification_status: String,
    pub verification_method: Option<String>,
    pub verification_data: Option<serde_json::Value>,
    pub verified_by: Option<Uuid>,
    pub verified_at: Option<DateTime<Utc>>,
    pub access_permissions: Option<serde_json::Value>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct UserSession {
    pub id: Uuid,
    pub user_id: Uuid,
    pub session_token: String,
    pub refresh_token: String,
    pub current_identity_id: Option<Uuid>,
    pub available_identities: Option<serde_json::Value>,
    pub device_info: Option<serde_json::Value>,
    pub ip_address: Option<std::net::IpAddr>,
    pub user_agent: Option<String>,
    pub expires_at: DateTime<Utc>,
    pub refresh_expires_at: DateTime<Utc>,
    pub last_activity_at: DateTime<Utc>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct IdentitySwitchLog {
    pub id: Uuid,
    pub user_id: Uuid,
    pub session_id: Uuid,
    pub from_identity_id: Option<Uuid>,
    pub to_identity_id: Uuid,
    pub switch_reason: Option<String>,
    pub ip_address: Option<std::net::IpAddr>,
    pub user_agent: Option<String>,
    pub switched_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct SendVerificationCodeRequest {
    pub phone_number: String,
    pub code_type: String,
}

#[derive(Debug, Serialize)]
pub struct SendVerificationCodeResponse {
    pub success: bool,
    pub message: String,
    pub data: VerificationCodeData,
}

#[derive(Debug, Serialize)]
pub struct VerificationCodeData {
    pub expires_in: i64,
    pub can_resend_after: i64,
}

#[derive(Debug, Deserialize)]
pub struct RegisterRequest {
    pub username: String,
    pub phone_number: String,
    pub verification_code: String,
    pub password: String,
    pub device_info: Option<serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct RegisterResponse {
    pub success: bool,
    pub message: String,
    pub data: AuthData,
}

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: Option<String>,
    pub phone_number: Option<String>,
    pub password: String,
    pub verification_code: Option<String>,
    pub device_info: Option<serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub success: bool,
    pub message: String,
    pub data: LoginData,
}

#[derive(Debug, Serialize)]
pub struct AuthData {
    pub user_id: Uuid,
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
    pub user_profile: UserProfile,
}

#[derive(Debug, Serialize)]
pub struct LoginData {
    pub user_id: Uuid,
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
    pub available_identities: Vec<IdentityInfo>,
    pub current_identity: Option<IdentityInfo>,
}

#[derive(Debug, Serialize)]
pub struct UserProfile {
    pub username: String,
    pub phone_number: String,
    pub phone_verified: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IdentityInfo {
    pub identity_id: Uuid,
    pub tenant_id: Uuid,
    pub tenant_name: Option<String>,
    pub identity_type: String,
    pub display_name: Option<String>,
    pub is_primary: bool,
}

#[derive(Debug, Deserialize)]
pub struct BindIdentityRequest {
    pub tenant_id: Uuid,
    pub identity_type: String,
    pub extend_info: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct BindIdentityResponse {
    pub success: bool,
    pub message: String,
    pub data: BindIdentityData,
}

#[derive(Debug, Serialize)]
pub struct BindIdentityData {
    pub identity_id: Uuid,
    pub verification_status: String,
    pub estimated_approval_time: String,
}

#[derive(Debug, Deserialize)]
pub struct SwitchIdentityRequest {
    pub identity_id: Uuid,
    pub switch_reason: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct SwitchIdentityResponse {
    pub success: bool,
    pub message: String,
    pub data: SwitchIdentityData,
}

#[derive(Debug, Serialize)]
pub struct SwitchIdentityData {
    pub new_access_token: String,
    pub current_identity: IdentityInfo,
    pub expires_in: i64,
}

#[derive(Debug, Deserialize)]
pub struct LinkStudentRequest {
    pub student_phone_number: String,
    pub student_name: String,
    pub tenant_id: Uuid,
    pub relationship_type: String,
    pub verification_method: String,
    pub additional_info: Option<serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct LinkStudentResponse {
    pub success: bool,
    pub message: String,
    pub data: LinkStudentData,
}

#[derive(Debug, Serialize)]
pub struct LinkStudentData {
    pub relation_id: Uuid,
    pub verification_status: String,
    pub verification_steps: Vec<VerificationStep>,
}

#[derive(Debug, Serialize)]
pub struct VerificationStep {
    pub step: String,
    pub description: String,
    pub status: String,
}

#[derive(Debug, Serialize)]
pub struct LinkedStudentsResponse {
    pub success: bool,
    pub data: LinkedStudentsData,
}

#[derive(Debug, Serialize)]
pub struct LinkedStudentsData {
    pub students: Vec<LinkedStudent>,
}

#[derive(Debug, Serialize)]
pub struct LinkedStudent {
    pub relation_id: Uuid,
    pub student_user_id: Uuid,
    pub student_name: String,
    pub tenant_id: Uuid,
    pub tenant_name: String,
    pub relationship_type: String,
    pub verification_status: String,
    pub access_permissions: Option<serde_json::Value>,
    pub verified_at: Option<DateTime<Utc>>,
}

// Change Password DTOs
#[derive(Debug, Deserialize)]
pub struct ChangePasswordRequest {
    pub current_password: String,
    pub new_password: String,
    pub verification_code: Option<String>, // Optional SMS verification for extra security
}

#[derive(Debug, Serialize)]
pub struct ChangePasswordResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<ChangePasswordData>,
}

#[derive(Debug, Serialize)]
pub struct ChangePasswordData {
    pub password_changed_at: DateTime<Utc>,
    pub requires_relogin: bool,
}

// Error types
#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("Invalid phone number format")]
    InvalidPhoneNumber,
    #[error("Invalid verification code")]
    InvalidVerificationCode,
    #[error("Verification code expired")]
    VerificationCodeExpired,
    #[error("Too many verification attempts")]
    TooManyAttempts,
    #[error("User not found")]
    UserNotFound,
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Account locked")]
    AccountLocked,
    #[error("Phone number already registered")]
    PhoneAlreadyRegistered,
    #[error("Identity not found")]
    IdentityNotFound,
    #[error("Identity already bound")]
    IdentityAlreadyBound,
    #[error("Insufficient permissions")]
    InsufficientPermissions,
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    #[error("SMS service error: {0}")]
    SmsServiceError(String),
    #[error("Password validation error: {0}")]
    PasswordValidationError(String),
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
}

impl AuthError {
    /// 获取错误代码，用于前端识别错误类型
    pub fn error_code(&self) -> &'static str {
        match self {
            AuthError::InvalidPhoneNumber => "INVALID_PHONE",
            AuthError::InvalidVerificationCode => "INVALID_CODE",
            AuthError::VerificationCodeExpired => "CODE_EXPIRED",
            AuthError::TooManyAttempts => "TOO_MANY_ATTEMPTS",
            AuthError::UserNotFound => "USER_NOT_FOUND",
            AuthError::InvalidCredentials => "INVALID_CREDENTIALS",
            AuthError::AccountLocked => "ACCOUNT_LOCKED",
            AuthError::PhoneAlreadyRegistered => "PHONE_EXISTS",
            AuthError::IdentityNotFound => "IDENTITY_NOT_FOUND",
            AuthError::IdentityAlreadyBound => "IDENTITY_ALREADY_BOUND",
            AuthError::InsufficientPermissions => "INSUFFICIENT_PERMISSIONS",
            AuthError::DatabaseError(_) => "DATABASE_ERROR",
            AuthError::SmsServiceError(_) => "SMS_SERVICE_ERROR",
            AuthError::PasswordValidationError(_) => "PASSWORD_VALIDATION_ERROR",
            AuthError::JwtError(_) => "INVALID_TOKEN",
        }
    }

    /// 获取 HTTP 状态码
    pub fn status_code(&self) -> axum::http::StatusCode {
        use axum::http::StatusCode;
        match self {
            AuthError::InvalidPhoneNumber => StatusCode::BAD_REQUEST,
            AuthError::InvalidVerificationCode => StatusCode::BAD_REQUEST,
            AuthError::VerificationCodeExpired => StatusCode::BAD_REQUEST,
            AuthError::TooManyAttempts => StatusCode::TOO_MANY_REQUESTS,
            AuthError::UserNotFound => StatusCode::NOT_FOUND,
            AuthError::InvalidCredentials => StatusCode::UNAUTHORIZED,
            AuthError::AccountLocked => StatusCode::LOCKED,
            AuthError::PhoneAlreadyRegistered => StatusCode::CONFLICT,
            AuthError::IdentityNotFound => StatusCode::NOT_FOUND,
            AuthError::IdentityAlreadyBound => StatusCode::CONFLICT,
            AuthError::InsufficientPermissions => StatusCode::FORBIDDEN,
            AuthError::DatabaseError(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AuthError::SmsServiceError(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AuthError::PasswordValidationError(_) => StatusCode::BAD_REQUEST,
            AuthError::JwtError(_) => StatusCode::UNAUTHORIZED,
        }
    }
}

pub type AuthResult<T> = Result<T, AuthError>;
