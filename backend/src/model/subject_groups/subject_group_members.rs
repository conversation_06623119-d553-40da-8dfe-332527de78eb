use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::prelude::FromRow;
use uuid::Uuid;

/// 学科组成员模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SubjectGroupMember {
    pub id: Uuid,
    pub subject_group_id: Uuid,
    pub teacher_id: Uuid,
    pub role_code: String,  // 改为使用角色代码
    pub joined_at: DateTime<Utc>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 学科组成员详细信息（包含教师信息）
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SubjectGroupMemberDetail {
    pub id: Uuid,
    pub subject_group_id: Uuid,
    pub teacher_id: Uuid,
    pub role_code: String,  // 改为使用角色代码
    pub role_name: Option<String>,  // 添加角色名称字段
    pub joined_at: DateTime<Utc>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    // 教师信息
    pub teacher_name: String,
    pub employee_id: String,
    pub title: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
}

/// 创建学科组成员参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSubjectGroupMemberParams {
    pub subject_group_id: Uuid,
    pub teacher_id: Uuid,
    pub role_code: String,  // 改为使用角色代码
}

/// 批量添加成员参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchAddMembersParams {
    pub members: Vec<BatchMemberItem>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchMemberItem {
    pub teacher_id: Uuid,
    pub role_code: String,  // 改为使用角色代码
}

/// 更新学科组成员参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateSubjectGroupMemberParams {
    pub role_code: Option<String>,  // 改为使用角色代码
    pub is_active: Option<bool>,
}

/// 学科组成员统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubjectGroupMemberStats {
    pub total_members: i64,
    pub leaders: i64,
    pub deputy_leaders: i64,
    pub regular_members: i64,
    pub active_members: i64,
    pub inactive_members: i64,
}
