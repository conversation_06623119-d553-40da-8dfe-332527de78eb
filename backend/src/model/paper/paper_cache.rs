use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperContentData {
    //题目部分
    pub question_data_list: Vec<QuestionData>,
    //答题卡部分
    pub answer_card: AnswerCardData,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct QuestionData {
    pub id: Uuid,
    pub original_question_id: Option<Uuid>,
    //无题型的是非题目类型，类似标题一类的
    pub question_type: Option<String>,
    pub question_content: Vec<QuestionItemData>,
}

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct QuestionItemData {
    pub id: Uuid,
    pub question_id: Uuid,
    pub item_type: QuestionItemTypeEnum,
    pub config: Value,
    //可选项，有答案的题块才有
    pub scoring_criteria: Option<ScoringCriteria>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum QuestionItemTypeEnum {
    #[serde(rename = "Text")]
    Text,
    #[serde(rename = "Stem")]
    Stem,
    #[serde(rename = "Choice")]
    Choice,
    #[serde(rename = "TrueOrFalse")]
    TrueOrFalse,
    #[serde(rename = "FillInBlank")]
    FillInBlank,
    #[serde(rename = "Textarea")]
    Textarea,
    #[serde(rename = "AdmissionTicketNumberInfo")]
    AdmissionTicketNumberInfo,
    #[serde(rename = "ReadAndScan")]
    ReadAndScan,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScoringCriteriaTypeEnum {
    #[serde(rename = "Match")]
    Match,
    #[serde(rename = "AI")]
    AI,
    #[serde(rename = "Manual")]
    Manual,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AnswerCardData {
    pub id: Uuid,
    pub paper_id: Uuid,
    pub dpi: i32,
    pub width: f32,
    pub height: f32,
    //左边距
    pub x: f32,
    //上边距
    pub y: f32,
    //右边距
    pub right: f32,
    //下边距
    pub bottom: f32,
    //是否纵向
    pub page_orientation_is_vertical: bool,
    //分栏数量
    pub bucket_size: i32,
    //是否显示页码
    pub show_page_index: bool,
    //是否显示定位点,定位点位于页面内部四个顶点，上面两个是横向，下面两个竖向
    pub show_pos_point: bool,
    //定位点宽度，指左上角那个
    pub pos_point_width: f32,
    //定位点高度度，指左上角那个
    pub pos_point_height: f32,
    //题块组列表
    pub block_group_list: Vec<AnswerBlockGroupData>,
    //题块关联题块区域
    pub question_item_block_group_list: Vec<QuestionItemBlockGroupData>,
    //题块区域关联评分标准
    pub answer_block_group_scoring_criteria_list: Vec<AnswerBlockScoringCriteria>,
    //评分标准
    pub scoring_criteria_list: Vec<ScoringCriteria>,
    //总页码
    pub page_total: i32,
    //答题卡信息
    pub admissionTicketNumberInfoQuestionItemConfig: AdmissionTicketNumberInfoQuestionItemConfig,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AnswerBlockGroupData {
    pub id: Uuid,
    pub card_id: Uuid,
    pub serial_number: i32,
    pub block_list: Vec<AnswerBlockData>,
    pub group_type: String,
    pub mode: String,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AnswerBlockData {
    pub id: Uuid,
    pub group_id: Uuid,
    pub page_number: i32,
    pub width: f32,
    pub height: f32,
    pub x: f32,
    pub y: f32,
    pub serial_number: i32,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct QuestionItemBlockGroupData {
    pub id: Uuid,
    pub question_item_id: Uuid,
    pub block_group_id: Uuid,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AnswerBlockScoringCriteria {
    pub id: Uuid,
    pub scoring_criteria_id: Uuid,
    pub block_group_id: Uuid,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ScoringCriteria {
    pub id: Uuid,
    pub scoring_type: ScoringCriteriaTypeEnum,
    pub mode: Option<String>,
    #[serde(rename = "criteriaName")]
    pub name: Option<String>,
    pub answer: Option<String>,
    pub score: f64,
    pub ocr_work_id: Option<Uuid>,
    pub check_work_id: Option<Uuid>,
    #[serde(rename = "questionTips")]
    pub question_tips: Option<String>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TextQuestionItemConfig {
    #[serde(rename = "questionItemType")]
    pub question_item_type: QuestionItemTypeEnum,
    pub style: Value,
    #[serde(rename = "questionNo")]
    pub question_no: Option<String>,
    pub text: Option<String>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StemQuestionItemConfig {
    #[serde(rename = "questionItemType")]
    pub question_item_type: QuestionItemTypeEnum,
    pub style: Value,
    #[serde(rename = "questionNo")]
    pub question_no: Option<String>,
    pub text: Option<String>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TrueOrFalseQuestionItemConfig {
    pub questionItemType: QuestionItemTypeEnum,
    pub style: Value,
    pub questionNo: Option<String>,
    pub text: Option<String>,
}
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct FillInBlankQuestionItemConfig {
    pub questionItemType: QuestionItemTypeEnum,
    pub style: Value,
    pub questionNo: Option<String>,
    pub text: Option<String>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ChoiceQuestionInfoQuestionItemConfig {
    pub questionItemType: QuestionItemTypeEnum,
    pub style: Value,
    pub questionNo: Option<String>,
    pub text: Option<String>,
    pub choices: Vec<String>,
    pub isHorizontal: bool,
}
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TextareaQuestionItemConfig {
    pub questionItemType: QuestionItemTypeEnum,
    pub inputMode: String,
    pub answerAreaBlockNumber: i32,
    pub lineTotal: i32,
    pub style: Value,
    pub questionNo: Option<String>,
    pub text: Option<String>,
}
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AdmissionTicketNumberInfoQuestionItemConfig {
    pub questionItemType: QuestionItemTypeEnum,
    pub style: Value,
    pub questionNo: Option<String>,
    pub text: Option<String>,
    pub isHorizontal: bool,
    pub examName: String,
    pub modelName: String,
    pub size: i32,
    pub showQRCode: bool,
    pub needPaintCard: bool,
    pub qrcodeMsg: Option<String>,
    pub fillText: String,
}

impl AdmissionTicketNumberInfoQuestionItemConfig {
    /**
     * 作者：张瀚
     * 说明：获取字符串内容转出来的对象
     */
    pub fn get_qrcode_msg_bean(&self) -> AdmissionTicketQRCodeMsgBean {
        match &self.qrcodeMsg {
            Some(msg) => {
                let value: Value = serde_json::from_str(&msg).unwrap_or(serde_json::json!({}));
                serde_json::from_value(value).unwrap_or(AdmissionTicketQRCodeMsgBean {
                    cardId: None,
                    paperId: None,
                    studentNumber: None,
                    studentName: None,
                    studentClassName: None,
                })
            }
            None => AdmissionTicketQRCodeMsgBean {
                cardId: None,
                paperId: None,
                studentNumber: None,
                studentName: None,
                studentClassName: None,
            },
        }
    }
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AdmissionTicketQRCodeMsgBean {
    pub cardId: Option<String>,
    pub paperId: Option<Uuid>,
    pub studentNumber: Option<i32>,
    pub studentName: Option<String>,
    pub studentClassName: Option<String>,
}
