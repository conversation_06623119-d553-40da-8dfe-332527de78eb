use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use sqlx::types::Json;
use uuid::Uuid;

use crate::model::paper::paper_cache::PaperContentData;

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct Paper {
    pub id: Uuid,
    pub paper_name: String,
    pub answer_card_id: Option<Uuid>, // 答题卡ID
    pub subject_code: Option<String>, // 学科
    pub grade_level_code: Option<String>, // 年级
    pub section_id: Option<Uuid>, // 小节内容ID
    pub paper_content: Json<PaperContentData>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct PaperQuery {
    pub schema_name: Option<String>,

    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub paper_name: Option<String>,

    pub search: Option<String>,
    pub order_by: Option<String>, // 排序字段：paper_name, id, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}

// 新增：用于创建 Paper 的请求体
#[derive(Debug, Deserialize)]
pub struct CreatePaperRequest {
    pub id: Option<Uuid>,
    pub paper_name: String,
    pub paper_content: PaperContentData,
}

// 新增：用于更新 Paper 的请求体
#[derive(Debug, Deserialize)]
pub struct UpdatePaperRequest {
    pub paper_name: Option<String>,
    pub paper_content: Option<PaperContentData>,
}
