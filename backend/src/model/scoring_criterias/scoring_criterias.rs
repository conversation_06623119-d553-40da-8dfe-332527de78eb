use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, Type};
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct ScoringCriteria {
    pub id: Uuid,
    pub scoring_type: ScoringCriteriaTypeEnum,
    pub mode: Option<String>,
    pub criteria_name: Option<String>,
    pub answer: Option<String>,
    pub score: f64,
    pub ocr_work_id: Option<Uuid>,
    pub check_work_id: Option<Uuid>,
    pub question_tips: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Type, Serialize, Deserialize)]
pub enum ScoringCriteriaTypeEnum {
    #[serde(rename = "Match")]
    Match,
    #[serde(rename = "AI")]
    AI,
    #[serde(rename = "Manual")]
    Manual,
}
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateScoringCriteriaParams {
    pub scoring_type: Option<String>,
    pub mode: Option<String>,
    pub criteria_name: Option<String>,
    pub answer: Option<String>,
    pub score: Option<f64>,
    pub ocr_work_id: Option<Uuid>,
    pub check_work_id: Option<Uuid>,
    pub question_tips: Option<String>,
}
