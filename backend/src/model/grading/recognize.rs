use std::fmt;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::paper::paper_cache::AnswerCardData;

// 考试
#[derive(Deserialize,Serialize, Debug)]
pub struct RecognizeRequest {
    pub mark: AnswerCardData,
    pub sheets: Vec<SheetRequest>,
}
#[derive(Deserialize, Serialize,Debug)]
pub struct SheetRequest {
    pub uuid: String,
    pub bucket: String,
    pub pages: Vec<PageRequest>,
}
#[derive(Deserialize, Serialize, Debug)]
pub struct PageRequest {
    pub page_id: String,
    pub url: String,
}
impl PageRequest{
    pub fn new(page_id: String, url: String) -> Self {
        Self { page_id, url }
    }
}

#[derive(Deserialize,Serialize, Debug,Clone)]
pub struct SheetResponse {
    pub uuid: Uuid,
    pub number: String,
    pub page_containers: Vec<PageContainer>,
}
#[derive(Deserialize,Serialize, Debug,<PERSON>lone)]
pub struct PageContainer {
    pub page_id: Uuid,
    pub page_data: PageData,
}
#[derive(Debug,Deserialize, Serialize,Clone)]
pub enum Error {
    MarkInfoError(String),
    LoadImageError(String),
    CheckBlankError(String),
    LocationError(String),
    ImageHasherError(String),
    QrCodeError(String),
    PageNumberError(String),
    StudentNumberError(String),
    FillQuestionError(String),
    HandwriteQuestionError(String),
    SaveImageError(String),
}
impl fmt::Display for Error {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            Error::MarkInfoError(msg) => write!(f, "MarkInfoError: {}", msg),
            Error::LoadImageError(msg) => write!(f, "LoadImageError: {}", msg),
            Error::CheckBlankError(msg) => write!(f, "CheckBlankError: {}", msg),
            Error::LocationError(msg) => write!(f, "LocationError: {}", msg),
            Error::ImageHasherError(msg) => write!(f, "ImageHasherError: {}", msg),
            Error::QrCodeError(msg) => write!(f, "QrCodeError: {}", msg),
            Error::PageNumberError(msg) => write!(f, "PageNumberError: {}", msg),
            Error::StudentNumberError(msg) => write!(f, "StudentNumberError: {}", msg),
            Error::FillQuestionError(msg) => write!(f, "FillQuestionError: {}", msg),
            Error::HandwriteQuestionError(msg) => write!(f, "HandwriteQuestionError: {}", msg),
            Error::SaveImageError(msg) => write!(f, "SaveImageError: {}", msg),
        }
    }
}

#[derive(Deserialize,Serialize, Debug,Clone)]
pub enum PageData {
    Error(Error),
    Blank,
    Value(PageValue)
}
#[derive(Deserialize,Serialize, Debug,Clone)]
pub struct BlockData {
    pub block_id: Uuid,
    pub value: BlockValue,
    pub url: Option<String>,
}
#[derive(Deserialize,Serialize, Debug,Clone)]
pub enum BlockValue {
    Objective(String),
    Objectives(Vec<String>),
    Subjective,
}

#[derive(Deserialize,Serialize, Debug,Clone)]
pub struct PageValue {
    pub norm_card_url: String,
    pub page: u8,
    pub blocks: Vec<BlockData>,
}
