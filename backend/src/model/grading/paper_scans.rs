use crate::model::grading::grading::{CreatePaperScanFileRequest, PaperScanException, StudentIdException, EXAM_TYPE_HOMEWORK};
use crate::model::grading::paper_scan_pages::UpdatePaperScanPages;
use chrono::{DateTime, Local, Utc};
use qc_sqlx_derive::QcSqlxEnum;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::postgres::{PgHasArrayType, PgTypeInfo};
use sqlx::FromRow;
use std::ops::Not;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScan {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub exam_type: String, // 'homework','exam'
    pub batch_no: String,
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub scan_method: String, // 'image_upload', 'scanner_direct', 'third_party'
    pub scan_device: Option<String>,
    pub status: PaperScanStatus,
    pub result: Option<Value>,
    pub duplicate_num: i32,
    pub blank_num: i32,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub needs_review: bool,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 试卷扫描状态枚举定义
/// 状态说明:[Undistributed:未发布,Unbound:未绑定学生信息,Duplicate:重复页,Error:异常错误,Done:已完成]
#[derive(Debug, Clone, QcSqlxEnum, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "PascalCase")]
pub enum PaperScanStatus {
    Undistributed,
    Unbound,
    Duplicate,
    Error,
    Done,
}
impl Default for PaperScanStatus {
    fn default() -> Self {
        PaperScanStatus::Undistributed // 明确指定默认值
    }
}
impl PaperScan {
    pub fn new(id: Uuid, exam_id: Uuid, exam_type: String, batch_no: String, scan_method: String, status: PaperScanStatus) -> Self {
        Self {
            id,
            exam_id,
            exam_type,
            batch_no,
            student_id: None,
            student_number: None,
            scan_method,
            scan_device: None,
            status,
            result: None,
            duplicate_num: 0,
            blank_num: 0,
            is_abnormal: false,
            abnormal_reason: None,
            needs_review: false,
            reviewed_by: None,
            reviewed_at: None,
            created_at: Local::now().into(),
            updated_at: Local::now().into(),
        }
    }
    pub fn from(request: &PaperScanBatchRequest) -> Self {
        Self::new(
            request.id,
            request.exam_id,
            request.exam_type.clone(),
            request.batch_no.clone(),
            request.scan_method.clone(),
            request.status.clone(),
        )
    }
}
#[derive(Default, Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScanPageRecord {
    pub id: Uuid,
    pub page_id: Uuid,
    pub exam_id: Uuid,
    pub exam_type: String,
    pub batch_no: String,
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub status: PaperScanStatus,
    pub file_url: Option<String>,
    pub page_num: i32,
    pub rectify_url: Option<String>,
    pub file_name: Option<String>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScansStatusSummaryRecord {
    pub status: PaperScanStatus,
    pub total: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UpdatePaperScan {
    pub id: Uuid,
    pub exam_type: String, // 'homework','exam'
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub status: PaperScanStatus,
    pub result: Option<Value>,
    pub duplicate_num: i32,
    pub blank_num: i32,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub updated_at: DateTime<Utc>,
    pub scan_pages: Vec<UpdatePaperScanPages>,
}

impl UpdatePaperScan {
    pub fn new(
        id: Uuid,
        exam_type: String,
        student_id: Option<Uuid>,
        student_number: Option<String>,
        status: PaperScanStatus,
        result: Option<Value>,
        duplicate_num: i32,
        blank_num: i32,
        is_abnormal: bool,
        abnormal_reason: Option<String>,
        updated_at: DateTime<Utc>,
        scan_pages: Vec<UpdatePaperScanPages>,
    ) -> Self {
        Self {
            id,
            exam_type,
            student_id,
            student_number,
            status,
            result,
            duplicate_num,
            blank_num,
            is_abnormal,
            abnormal_reason,
            updated_at,
            scan_pages,
        }
    }
    pub fn error(id: Uuid, exam_type: String, result: Option<Value>, abnormal_reason: Option<String>, scan_pages: Vec<UpdatePaperScanPages>) -> Self {
        Self::new(id, exam_type, None, None, PaperScanStatus::Error, result, 0, 0, true, abnormal_reason, Local::now().into(), scan_pages)
    }
}

#[derive(Debug, Serialize)]
pub struct PaperScanResponse {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub student_name: String,
    pub front_page_url: Option<String>,
    pub back_page_url: Option<String>,
    pub scan_quality: i32,
    pub scan_method: String,
    pub scan_device: Option<String>,
    pub duplicate_num: i32,
    pub blank_num: i32,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub needs_review: bool,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub file_size: i64,
    pub scan_timestamp: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub exceptions: Vec<PaperScanException>,
    pub student_id_exceptions: Vec<StudentIdException>,
}

// Request types
#[derive(Debug, Deserialize, Clone)]
pub struct PaperScanBatchRequest {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub exam_type: String,
    pub batch_no: String,
    pub student_id: Option<Uuid>,
    pub scan_method: String,
    pub scan_device: Option<String>,
    pub status: PaperScanStatus,
    pub result: Option<Value>,
    pub is_single_sided_multi_page: bool, //是否是单面多页面
    pub scans: Vec<CreatePaperScanFileRequest>,
}

impl PaperScanBatchRequest {
    pub fn new(exam_id: Uuid) -> Self {
        Self {
            id: Uuid::new_v4(),
            exam_id,
            exam_type: EXAM_TYPE_HOMEWORK.to_string(), // 默认为作业类型考试
            batch_no: String::default(),
            student_id: None,
            scan_method: "scanner_direct".to_string(),
            scan_device: None,
            status: PaperScanStatus::Undistributed,
            result: None,
            is_single_sided_multi_page: false,
            scans: vec![],
        }
    }
}
