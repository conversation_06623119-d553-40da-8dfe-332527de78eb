use crate::model::grading::grading::CreatePaperScanFileRequest;
use chrono::{DateTime, Local, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;

/// 试卷纸张页信息
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct PaperScanPage {
    pub id: Uuid,
    pub paper_scan_id: Uuid,
    pub page_num: i32,
    pub file_name: String,
    pub file_url: String,
    pub rectify_url: Option<String>,
    pub minio_bucket: Option<String>,
    pub minio_object_key: Option<String>,
    pub file_size: i64,
    pub scan_quality: Option<i32>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub result: Option<Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl PaperScanPage {
    pub fn new(
        id: Uuid,
        paper_scan_id: Uuid,
        page_num: i32,
        file_name: String,
        file_url: String,
        rectify_url: Option<String>,
        minio_bucket: Option<String>,
        minio_object_key: Option<String>,
        file_size: i64,
        scan_quality: Option<i32>,
        is_duplicate: bool,
        is_blank: bool,
        is_abnormal: bool,
        abnormal_reason: Option<String>,
        result: Option<Value>,
        created_at: DateTime<Utc>,
        updated_at: DateTime<Utc>,
    ) -> Self {
        Self {
            id,
            paper_scan_id,
            page_num,
            file_name,
            file_url,
            rectify_url,
            minio_bucket,
            minio_object_key,
            file_size,
            scan_quality,
            is_duplicate,
            is_blank,
            is_abnormal,
            abnormal_reason,
            result,
            created_at,
            updated_at,
        }
    }

    pub fn from(request: &CreatePaperScanFileRequest) -> Self {
        Self::new(
            request.id,
            request.paper_scan_id,
            request.paper_num,
            request.file_name.clone(),
            request.file_url.clone(),
            None,
            request.minio_bucket.clone(),
            request.minio_object_key.clone(),
            request.file_size,
            Some(request.scan_quality),
            request.is_duplicate,
            request.is_blank,
            request.is_abnormal,
            request.abnormal_reason.clone(),
            request.result.clone(),
            Local::now().into(),
            Local::now().into(),
        )
    }
}

#[derive(Debug, Serialize)]
pub struct PaperScanPagesResponse {
    pub id: Uuid,
    pub paper_scan_id: Uuid,
    pub page_num: i32,
    pub file_name: String,
    pub file_url: String,
    pub rectify_url: Option<String>,
    pub minio_bucket: Option<String>,
    pub minio_object_key: Option<String>,
    pub file_size: i64,
    pub scan_quality: Option<i32>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub result: Option<Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScanPagesRecord {
    pub exam_id: Uuid,
    pub paper_scan_id: Uuid,
    pub paper_scan_pages_id: Uuid,
    pub page_num: i32,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UpdatePaperScanPages {
    pub id: Uuid,
    pub student_id: Option<Uuid>,
    pub student_number: Option<String>,
    pub paper_scan_id: Uuid,
    pub page_num: Option<i32>,
    pub rectify_url: Option<String>,
    pub duplicate_num: i32,
    pub blank_num: i32,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub result: Option<Value>,
    pub updated_at: DateTime<Utc>,
}

impl UpdatePaperScanPages {
    pub fn new(
        id: Uuid,
        student_id: Option<Uuid>,
        student_number: Option<String>,
        paper_scan_id: Uuid,
        page_num: Option<i32>,
        rectify_url: Option<String>,
        duplicate_num: i32,
        blank_num: i32,
        is_duplicate: bool,
        is_blank: bool,
        is_abnormal: bool,
        abnormal_reason: Option<String>,
        result: Option<Value>,
    ) -> Self {
        Self {
            id,
            student_id,
            student_number,
            paper_scan_id,
            page_num,
            rectify_url,
            duplicate_num,
            blank_num,
            is_duplicate,
            is_blank,
            is_abnormal,
            abnormal_reason,
            result,
            updated_at: Local::now().into(),
        }
    }
}
