use serde::{Deserialize, Serialize};
use crate::model::Teacher;

/// 教师导入记录结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TeacherImportRecord {
    pub sequence: Option<String>,        // 序号
    pub subject: String,                 // 科目
    pub name: String,                    // 姓名
    pub phone: Option<String>,           // 手机号
    pub teaching_classes: Option<String>, // 任教班级
    pub position: Option<String>,        // 职位
    pub grade: String,                   // 年级
}

/// 导入教师参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ImportTeachersParams {
    pub file: Vec<u8>, // Excel文件内容
}

/// 教师导入结果
#[derive(Debug, Serialize, Deserialize)]
pub struct TeacherImportResult {
    pub success: usize,
    pub failed: usize,
    pub errors: Vec<TeacherImportError>,
    pub success_count: i32,
    pub created_teachers: Vec<Teacher>,
}

/// 教师导入错误
#[derive(Debug, Serialize, Deserialize)]
pub struct TeacherImportError {
    pub row: usize,
    pub error: String,
    pub data: serde_json::Value,
}

impl Default for TeacherImportResult {
    fn default() -> Self {
        Self {
            success: 0,
            failed: 0,
            errors: Vec::new(),
            success_count: 0,
            created_teachers: vec![],
        }
    }
}

impl TeacherImportResult {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn add_success(&mut self) {
        self.success += 1;
    }

    pub fn add_error(&mut self, row: usize, error: String, data: serde_json::Value) {
        self.failed += 1;
        self.errors.push(TeacherImportError { row, error, data });
    }
}
