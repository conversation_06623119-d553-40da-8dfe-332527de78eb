use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::prelude::FromRow;
use uuid::Uuid;

use crate::model::PageParams;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdministrativeClassesStatistics {
    pub total_classes: i32,
    pub total_teacher: i32,
    pub total_students: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct AdministrativeClasses {
    pub id: Uuid,
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub grade_level_code: Option<String>,
    pub teacher_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub is_active: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct AdministrativeClassesDetail {
    pub id: Uuid,
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub grade_level_code: Option<String>,
    pub teacher_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub is_active: bool,
    //额外信息
    pub teacher_name: Option<String>,
    pub grade_level_name: Option<String>,
    pub total_student: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAdministrativeClassesParams {
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub grade_level_code: Option<String>,
    pub teacher_id: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindAllStudentInClassParams {
    pub class_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageUserClassListParams {
    pub page_params: PageParams,
    pub name_like: Option<String>,
    pub class_code: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageStudentInClassParams {
    pub class_id: Uuid,
    pub page_params: PageParams,
    pub name_like: Option<String>,
    pub status: Option<String>,
    pub student_number: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAdministrativeClassesParams {
    pub id: Uuid,
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub grade_level_code: Option<String>,
    pub teacher_id: Option<Uuid>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MoveStudentToAdministrativeClassesParams {
    pub class_id: Uuid,
    pub student_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoveStudentFromAdministrativeClassesParams {
    pub class_id: Uuid,
    pub student_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeleteAdministrativeClassesParams {
    pub class_id: Uuid,
}
