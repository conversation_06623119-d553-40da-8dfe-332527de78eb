use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct GradeLevel {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub description: Option<String>,
    pub order_level: i32,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct GradeLevelVO {
    pub id: Uuid,
    pub code: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub education_stage_code: Option<String>,
    pub name: String,
    pub description: Option<String>,
    pub order_level: i32,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct GradeLevelSummary {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub is_active: bool,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CreateGradeLevel {
    pub code: String,
    pub name: String,
    pub description: Option<String>,
    pub order_level: i32,
    pub education_stage_code: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct UpdateGradeLevel {
    pub name: Option<String>,
    pub description: Option<String>,
    pub order_level: Option<i32>,
    pub is_active: Option<bool>,
    pub education_stage_code: Option<Option<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GradeLevelQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub search: Option<String>,
    pub is_active: Option<bool>,
    pub order_by: Option<String>,
    pub order_direction: Option<String>,
    #[serde(rename = "stage_code")]
    pub stage_code: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct GradeLevelStatistics {
    pub total_grades: i64,
    pub active_grades: i64,
    pub inactive_grades: i64,
    pub usage_stats: Vec<GradeLevelUsageStats>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct GradeLevelUsageStats {
    pub grade_id: Uuid,
    pub grade_name: String,
    pub student_count: i64,
    pub class_count: i64,
    pub exam_count: Option<i64>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CheckCodeResponse {
    pub is_available: bool,
}