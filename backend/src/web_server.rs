use crate::config::config::GlobalConfig;
use crate::controller::file::file_controller;
use crate::middleware::auth_middleware::auth_middleware;
use crate::routes_builder::{create_protected_routes, create_public_routes};
use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;
use crate::service::classes::classes_service::ClassesService;
use crate::service::education_stage::EducationStageService;
use crate::service::grade::grade_service::GradeService;
use crate::service::homework::homework_service::HomeworkService;
use crate::service::homework::homework_students_service::HomeworkStudentsService;
use crate::service::menu::MenuService;
use crate::service::paper::paper::PaperService;
use crate::service::paper_scan_page::paper_scan_page_service::PaperScanPagesService;
use crate::service::paper_scans::paper_scans_service::PaperScansService;
use crate::service::permission::MultiTenantCasbinService;
use crate::service::question::question_type_service::QuestionTypeService;
use crate::service::role::role_service::RoleService;
use crate::service::storage::StorageService;
use crate::service::student::{StudentService, StudentImportService};
use crate::service::subject::SubjectService;
use crate::service::subject_groups::subject_groups_service::SubjectGroupsService;
use crate::service::task_queue::TaskQueueState;
use crate::service::teacher::teacher_service::TeacherService;
use crate::service::teacher::import_service::TeacherImportService;
use crate::service::teaching_aids::teaching_aids_service::TeachingAidsService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use crate::service::teaching_classes::teaching_classes_service::TeachingClassesService;
use crate::service::tenant::user::tenant_user_service::TenantUserService;
use crate::service::auth::auth_service::AuthService;
use crate::service::user::{IdentityService, UserService};
use crate::service::user::parent_service::ParentService;
use crate::service::sms::SmsService;
use crate::service::tenant::TenantService;
use crate::utils::password::PasswordService;
use crate::service::workflow::workflow_service::WorkflowService;
use axum::{middleware, Router};
use std::sync::Arc;
use tower_http::cors::{Any, CorsLayer};

#[derive(Clone)]
pub struct AppState {
    pub db: sqlx::PgPool,
    pub config: Arc<GlobalConfig>,
    pub storage_service: Arc<dyn StorageService>,
    pub auth_service: Arc<AuthService>,
    pub identity_service: Arc<IdentityService>,
    pub parent_service: Arc<ParentService>,
    pub sms_service: Arc<SmsService>,
    pub password_service: Arc<PasswordService>,
    pub tenant_service: Arc<TenantService>,
    pub casbin_service: Arc<MultiTenantCasbinService>,
    pub classes_service: Arc<ClassesService>,
    pub administrative_classes_service: Arc<AdministrativeClassesService>,
    pub teaching_classes_service: Arc<TeachingClassesService>,
    pub subject_groups_service: Arc<SubjectGroupsService>,
    pub user_service: UserService,
    pub role_service: RoleService,
    pub subject_service: SubjectService,
    pub grade_service: GradeService,
    pub student_service: StudentService,
    pub student_import_service: StudentImportService,
    pub teacher_service: TeacherService,
    pub teacher_import_service: TeacherImportService,
    pub homework_service: Arc<HomeworkService>,
    pub homework_students_service: Arc<HomeworkStudentsService>,
    pub education_stage_service: EducationStageService,
    pub teaching_aids_service: Arc<TeachingAidsService>,
    pub question_type_service: Arc<QuestionTypeService>,
    pub workflow_service: Arc<WorkflowService>,
    pub paper_service: Arc<PaperService>,
    pub textbook_paper_service: Arc<TextbookPaperService>,
    pub task_queue: TaskQueueState,
    pub paper_scans_service: PaperScansService,
    pub paper_scan_page_service: PaperScanPagesService,
    pub menu_service: Arc<MenuService>,
    pub tenant_user_service: Arc<TenantUserService>,
}

/// 构建 Router，并注入数据库连接池
pub fn build_app(state: AppState) -> Router {
    // Public routes (no auth required)
    let public_auth_routes = create_public_routes(&state);

    let protected_auth_routes = create_protected_routes(&state).route_layer(middleware::from_fn_with_state(state.db.clone(), auth_middleware));

    Router::new()
        .with_state(state.clone())
        .nest("/files", file_controller::create_public_router().with_state(state))
        .nest("/api/v1", public_auth_routes)
        .nest("/api/v1", protected_auth_routes)
        .layer(CorsLayer::new().allow_origin(Any).allow_methods(Any).allow_headers(Any))
}
