use crate::utils::api_response::ApiResponse;
use crate::utils::error::AppError;
use anyhow::Context;
use axum::{
    extract::rejection::JsonRejection,
    response::{IntoResponse, Response},
};
use tracing::{error, warn};

pub type AppResult<T> = anyhow::Result<T>;
pub type AnyhowResult<T> = anyhow::Result<T>;

/// 统一的错误处理器，用于将各种错误转换为 HTTP 响应
pub struct ErrorHandler;

impl ErrorHandler {
    /// 处理 JSON 解析错误
    pub fn handle_json_rejection(rejection: JsonRejection) -> AppError {
        match rejection {
            JsonRejection::JsonDataError(err) => {
                warn!("JSON data error: {}", err);
                AppError::InvalidInput(format!("Invalid JSON data: {}", err))
            }
            JsonRejection::JsonSyntaxError(err) => {
                warn!("JSON syntax error: {}", err);
                AppError::InvalidInput("Invalid JSON syntax".to_string())
            }
            JsonRejection::MissingJsonContentType(_) => {
                warn!("Missing JSON content type");
                AppError::InvalidInput("Content-Type must be application/json".to_string())
            }
            JsonRejection::BytesRejection(err) => {
                error!("Bytes rejection error: {}", err);
                AppError::InvalidInput("Failed to read request body".to_string())
            }
            _ => {
                error!("Unknown JSON rejection: {:?}", rejection);
                AppError::InvalidInput("Invalid request format".to_string())
            }
        }
    }

    /// 记录错误并返回适当的响应
    pub fn log_and_respond(error: &AppError, context: &str) -> Response {
        match error {
            AppError::DatabaseError(db_err) => {
                error!("Database error in {}: {}", context, db_err);
            }
            AppError::InternalServerError(message) => {
                error!("Internal server error in {}: {}", context, message);
            }
            _ => {
                warn!("Application error in {}: {}", context, error);
            }
        }

        // 创建统一的API响应
        let api_response: ApiResponse<()> = ApiResponse::from_error(error);
        api_response.into_response()
    }

    /// 记录错误并返回API响应格式
    pub fn log_and_respond_api<T>(error: &AppError, context: &str) -> ApiResponse<T> {
        match error {
            AppError::DatabaseError(db_err) => {
                error!("Database error in {}: {}", context, db_err);
            }
            AppError::InternalServerError(message) => {
                error!("Internal server error in {}: {}", context, message);
            }
            _ => {
                warn!("Application error in {}: {}", context, error);
            }
        }

        ApiResponse::from_error(error)
    }
}

/// 扩展 trait，为 anyhow::Result 提供便捷的转换方法
pub trait AnyhowResultExt<T> {
    /// 将 anyhow::Result 转换为 AppResult，并添加上下文
    fn into_app_error_with_context(self, context: &str) -> AppResult<T>;

    /// 将数据库相关的 anyhow::Result 转换为 AppResult
    fn into_db_error(self, operation: &str) -> AppResult<T>;

    /// 将验证相关的 anyhow::Result 转换为 AppResult
    fn into_validation_error(self, field: &str) -> AppResult<T>;
}

impl<T> AnyhowResultExt<T> for AnyhowResult<T> {
    fn into_app_error_with_context(self, context: &str) -> AppResult<T> {
        self.with_context(|| context.to_string()).map_err(|e| AppError::InternalServerError(e.to_string()).into())
    }

    fn into_db_error(self, operation: &str) -> AppResult<T> {
        self.with_context(|| format!("Database operation failed: {}", operation))
            .map_err(|e| AppError::InternalServerError(e.to_string()).into())
    }

    fn into_validation_error(self, field: &str) -> AppResult<T> {
        match self {
            Ok(value) => Ok(value),
            Err(err) => Err(AppError::InvalidInput(format!("Validation failed for {}: {}", field, err)).into()),
        }
    }
}

/// 扩展 trait，为 sqlx::Result 提供便捷的转换方法
pub trait SqlxResultExt<T> {
    /// 将 sqlx::Result 转换为 AppResult，并添加操作上下文
    fn with_db_context(self, operation: &str) -> AppResult<T>;
}

impl<T> SqlxResultExt<T> for Result<T, sqlx::Error> {
    fn with_db_context(self, operation: &str) -> AppResult<T> {
        self.map_err(|err| {
            error!("Database operation '{}' failed: {}", operation, err);
            AppError::DatabaseError(err).into()
        })
    }
}

impl<T> SqlxResultExt<T> for Result<Option<T>, sqlx::Error> {
    fn with_db_context(self, operation: &str) -> AppResult<T> {
        match self {
            Ok(Some(value)) => Ok(value),
            Ok(None) => Err(AppError::NotFound(format!("Resource not found during {}", operation)).into()),
            Err(err) => {
                error!("Database operation '{}' failed: {}", operation, err);
                Err(AppError::DatabaseError(err).into())
            }
        }
    }
}

/// 便捷的错误创建函数
pub mod errors {
    use super::AppError;

    pub fn validation_error(message: impl Into<String>) -> AppError {
        AppError::InvalidInput(message.into())
    }

    pub fn not_found(resource: impl Into<String>) -> AppError {
        AppError::NotFound(format!("{} not found", resource.into()))
    }

    pub fn forbidden(message: impl Into<String>) -> AppError {
        AppError::Forbidden(message.into())
    }

    pub fn conflict(message: impl Into<String>) -> AppError {
        AppError::Conflict(message.into())
    }

    pub fn bad_request(message: impl Into<String>) -> AppError {
        AppError::InvalidInput(message.into())
    }

    pub fn internal_error(message: impl Into<String>) -> AppError {
        AppError::InternalServerError(message.into())
    }
}
