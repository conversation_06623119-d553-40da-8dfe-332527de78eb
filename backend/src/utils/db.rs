use anyhow::Result;
use sqlx::{PgPool, Transaction, Postgres};
use uuid::Uuid;
use regex::Regex;
use once_cell::sync::Lazy;

// 安全的 schema 名称验证
static SCHEMA_NAME_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[a-z0-9_]+$").unwrap());

/// 动态 Schema 查询构建器
pub struct DynamicSchemaQuery {
    pool: PgPool,
    schema_name: String,
}

impl DynamicSchemaQuery {
    /// 创建新的动态 schema 查询构建器
    pub fn new(pool: PgPool, schema_name: String) -> Result<Self> {
        // 验证 schema 名称安全性
        if !SCHEMA_NAME_REGEX.is_match(&schema_name) {
            return Err(anyhow::anyhow!("Invalid schema name: {}", schema_name));
        }

        Ok(Self { pool, schema_name })
    }

    /// 在指定 schema 中执行查询
    pub async fn query_in_schema<T>(&self, sql: &str) -> Result<Vec<T>>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin,
    {
        let full_sql = format!("SET search_path TO \"{}\"; {}", self.schema_name, sql);
        let rows = sqlx::query_as::<_, T>(&full_sql)
            .fetch_all(&self.pool)
            .await?;
        Ok(rows)
    }

    /// 在事务中设置 search_path 并执行查询
    pub async fn with_schema_transaction<F, R, Fut>(&self, f: F) -> Result<R>
    where
        F: FnOnce(&mut Transaction<'_, Postgres>) -> Fut,
        Fut: std::future::Future<Output = Result<R>>,
    {
        let mut tx = self.pool.begin().await?;

        // 设置 search_path
        sqlx::query(&format!(r#"SET LOCAL search_path TO "{}""#, self.schema_name))
            .execute(&mut *tx)
            .await?;

        // 执行用户函数
        let result = f(&mut tx).await?;

        // 提交事务
        tx.commit().await?;

        Ok(result)
    }
}

/// 获取租户的 schema 名称
pub async fn get_tenant_schema(pool: &PgPool, tenant_id: Uuid) -> Result<String> {
    let record = sqlx::query!(
        r#"
        SELECT schema_name
        FROM public.tenants
        WHERE id = $1
        "#,
        tenant_id
    )
    .fetch_one(pool)
    .await
    .map_err(|e| {
        anyhow::anyhow!(
            "Failed to find tenant schema for id {}: {}. Make sure a tenant with this ID exists.",
            tenant_id,
            e
        )
    })?;

    Ok(record.schema_name)
}

/// 安全地构建带 schema 前缀的表名
pub fn build_table_name(schema_name: &str, table_name: &str) -> Result<String> {
    if !SCHEMA_NAME_REGEX.is_match(schema_name) {
        return Err(anyhow::anyhow!("Invalid schema name: {}", schema_name));
    }

    // 简单的表名验证
    if !table_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
        return Err(anyhow::anyhow!("Invalid table name: {}", table_name));
    }

    Ok(format!("\"{}\".\"{}\"", schema_name, table_name))
}
