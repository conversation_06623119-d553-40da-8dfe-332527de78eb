use serde::{Deserialize, Serialize};
use std::env;

/// 阅卷服务参数配置
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TaskConfig {
    pub grader_host: String,
    pub ocr_host: String,
    pub tracer_host: String,
}

impl Default for TaskConfig {
    fn default() -> Self {
        Self {
            grader_host: "***********:3000".to_string(),
            ocr_host: "***********:3000".to_string(),
            tracer_host: "***********:3000".to_string(),
        }
    }
}

impl TaskConfig {
    /// 从环境变量加载配置
    pub fn from_env()->Self {
        let mut config = Self::default();

        // 从环境变量覆盖默认配置
        if let Ok(host) = env::var("TASK_GRADER_HOST") {
            config.grader_host = host;
        }
        if let Ok(host) = env::var("TASK_OCR_HOST") {
            config.ocr_host = host;
        }
        if let Ok(host) = env::var("TASK_TRACER_HOST") {
            config.tracer_host = host;
        }
        config
    }
}