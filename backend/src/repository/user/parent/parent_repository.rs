use crate::model::user::auth::*;
use anyhow::Result;
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Row};
use tracing::info;
use uuid::Uuid;

pub struct ParentRepository {
    pool: PgPool,
}

impl ParentRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 根据手机号查找用户
    pub async fn find_user_by_phone(&self, phone: &str) -> Result<Option<User>> {
        let user = sqlx::query(
            "SELECT * FROM public.users WHERE phone_number = $1"
        )
        .bind(phone)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = user {
            let user = User {
                id: row.get("id"),
                username: row.get("username"),
                phone_number: row.get("phone_number"),
                phone_verified: row.get("phone_verified"),
                phone_verified_at: row.get("phone_verified_at"),
                password_hash: row.get("password_hash"),
                salt: row.get("salt"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                last_login_at: row.get("last_login_at"),
                is_active: row.get("is_active"),
                failed_login_attempts: row.get("failed_login_attempts"),
                locked_until: row.get("locked_until"),
            };
            Ok(Some(user))
        } else {
            Ok(None)
        }
    }

    /// 根据租户ID获取schema名称
    pub async fn get_tenant_schema(&self, tenant_id: Uuid) -> Result<Option<String>> {
        let tenant_schema: Option<String> = sqlx::query_scalar(
            "SELECT schema_name FROM public.tenants WHERE id = $1"
        )
        .bind(tenant_id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(tenant_schema)
    }

    /// 查找学生身份
    pub async fn find_student_identity(
        &self,
        user_id: Uuid,
        schema_name: &str,
    ) -> Result<Option<UserIdentity>> {
        let query = format!(
            r#"
            SELECT ui.id, ui.user_id, ui.role_id, ui.target_type, ui.target_id, ui.subject,
                   ui.created_at, ui.updated_at
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1 AND r.code = 'student'
            LIMIT 1
            "#
        );

        let identity = sqlx::query(&query)
            .bind(user_id)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = identity {
            let identity = UserIdentity {
                id: row.get("id"),
                user_id: row.get("user_id"),
                role_id: row.get("role_id"),
                target_type: row.get("target_type"),
                target_id: row.get("target_id"),
                subject: row.get("subject"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            };
            Ok(Some(identity))
        } else {
            Ok(None)
        }
    }

    /// 检查关系是否已存在
    pub async fn relationship_exists(
        &self,
        parent_user_id: Uuid,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> Result<bool> {
        let count: Option<i64> = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*) FROM public.parent_student_relations
            WHERE parent_user_id = $1 AND student_user_id = $2 AND student_tenant_id = $3 AND is_active = true
            "#,
            parent_user_id,
            student_user_id,
            tenant_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.unwrap_or(0) > 0)
    }

    /// 创建家长学生关系
    pub async fn create_parent_student_relation(
        &self,
        relation_id: Uuid,
        parent_user_id: Uuid,
        student_user_id: Uuid,
        tenant_id: Uuid,
        student_identity_id: Uuid,
        relationship_type: &str,
        verification_method: &str,
        additional_info: Option<serde_json::Value>,
        access_permissions: serde_json::Value,
    ) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            r#"
            INSERT INTO public.parent_student_relations 
            (id, parent_user_id, student_user_id, student_tenant_id, student_identity_id,
             relationship_type, verification_status, verification_method, verification_data,
             access_permissions, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#,
            relation_id,
            parent_user_id,
            student_user_id,
            tenant_id,
            student_identity_id,
            relationship_type,
            "pending",
            verification_method,
            additional_info,
            access_permissions,
            now,
            now
        )
        .execute(&self.pool)
        .await?;

        info!(
            "Parent-student relationship created: parent={}, student={}, relation_id={}",
            parent_user_id, student_user_id, relation_id
        );

        Ok(())
    }

    /// 获取家长关联的学生列表
    pub async fn get_linked_students(&self, parent_user_id: Uuid) -> Result<Vec<LinkedStudentData>> {
        let relations = sqlx::query!(
            r#"
            SELECT 
                psr.id as relation_id,
                psr.student_user_id,
                psr.student_tenant_id as tenant_id,
                psr.relationship_type,
                psr.verification_status,
                psr.access_permissions,
                psr.verified_at,
                u.phone_number as student_phone,
                u.username as student_name,
                t.name as tenant_name
            FROM public.parent_student_relations psr
            JOIN public.users u ON psr.student_user_id = u.id
            JOIN public.tenants t ON psr.student_tenant_id = t.id
            WHERE psr.parent_user_id = $1 AND psr.is_active = true
            ORDER BY psr.created_at DESC
            "#,
            parent_user_id
        )
        .fetch_all(&self.pool)
        .await?;

        let students: Vec<LinkedStudentData> = relations
            .into_iter()
            .map(|row| LinkedStudentData {
                relation_id: row.relation_id,
                student_user_id: row.student_user_id,
                student_name: row.student_name,
                tenant_id: row.tenant_id,
                tenant_name: row.tenant_name,
                relationship_type: row.relationship_type,
                verification_status: row.verification_status.unwrap_or_default(),
                access_permissions: row.access_permissions,
                verified_at: row.verified_at,
            })
            .collect();

        Ok(students)
    }

    /// 验证家长学生关系
    pub async fn verify_parent_student_relationship(
        &self,
        relation_id: Uuid,
        verified_by: Uuid,
        verification_method: &str,
    ) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            r#"
            UPDATE public.parent_student_relations 
            SET verification_status = 'verified', verified_by = $1, verified_at = $2, 
                verification_method = $3, updated_at = $4
            WHERE id = $5
            "#,
            verified_by,
            now,
            verification_method,
            now,
            relation_id
        )
        .execute(&self.pool)
        .await?;

        info!("Parent-student relationship verified: relation_id={}, verified_by={}", 
              relation_id, verified_by);

        Ok(())
    }

    /// 更新访问权限
    pub async fn update_access_permissions(
        &self,
        relation_id: Uuid,
        permissions: serde_json::Value,
    ) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE public.parent_student_relations 
            SET access_permissions = $1, updated_at = $2
            WHERE id = $3
            "#,
            permissions,
            Utc::now(),
            relation_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 获取家长权限
    pub async fn get_parent_permissions(
        &self,
        parent_user_id: Uuid,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> Result<Option<serde_json::Value>> {
        let relation = sqlx::query!(
            r#"
            SELECT access_permissions FROM public.parent_student_relations 
            WHERE parent_user_id = $1 AND student_user_id = $2 AND student_tenant_id = $3 
            AND verification_status = 'verified' AND is_active = true
            "#,
            parent_user_id,
            student_user_id,
            tenant_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(relation.and_then(|r| r.access_permissions))
    }

    /// 停用关系
    pub async fn deactivate_relationship(&self, relation_id: Uuid) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE public.parent_student_relations 
            SET is_active = false, updated_at = $1
            WHERE id = $2
            "#,
            Utc::now(),
            relation_id
        )
        .execute(&self.pool)
        .await?;

        info!("Parent-student relationship deactivated: relation_id={}", relation_id);
        Ok(())
    }

    /// 获取学生的家长信息
    pub async fn get_student_parents(
        &self,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> Result<Vec<ParentInfoData>> {
        let parents = sqlx::query!(
            r#"
            SELECT 
                psr.id as relation_id,
                psr.parent_user_id,
                psr.relationship_type,
                psr.verification_status,
                psr.verified_at,
                u.phone_number as parent_phone
            FROM public.parent_student_relations psr
            JOIN public.users u ON psr.parent_user_id = u.id
            WHERE psr.student_user_id = $1 AND psr.student_tenant_id = $2 
            AND psr.is_active = true AND psr.verification_status = 'verified'
            ORDER BY psr.created_at ASC
            "#,
            student_user_id,
            tenant_id
        )
        .fetch_all(&self.pool)
        .await?;

        let parent_info: Vec<ParentInfoData> = parents
            .into_iter()
            .map(|row| ParentInfoData {
                relation_id: row.relation_id,
                parent_user_id: row.parent_user_id,
                parent_phone: row.parent_phone,
                relationship_type: row.relationship_type,
                verification_status: row.verification_status.unwrap_or_default(),
                verified_at: row.verified_at,
            })
            .collect();

        Ok(parent_info)
    }
}

// Repository 数据结构
#[derive(Debug)]
pub struct LinkedStudentData {
    pub relation_id: Uuid,
    pub student_user_id: Uuid,
    pub student_name: String,
    pub tenant_id: Uuid,
    pub tenant_name: String,
    pub relationship_type: String,
    pub verification_status: String,
    pub access_permissions: Option<serde_json::Value>,
    pub verified_at: Option<DateTime<Utc>>,
}

#[derive(Debug)]
pub struct ParentInfoData {
    pub relation_id: Uuid,
    pub parent_user_id: Uuid,
    pub parent_phone: String,
    pub relationship_type: String,
    pub verification_status: String,
    pub verified_at: Option<DateTime<Utc>>,
}
