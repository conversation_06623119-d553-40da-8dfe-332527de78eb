use sqlx::{PgPool, QueryBuilder};
use uuid::Uuid;
use anyhow::Result;

use crate::model::teaching_classes::teaching_classes::{PageUserClassListParams, TeachingClasses};

/// 教学班数据访问层
#[derive(Clone)]
pub struct TeachingClassesRepository {
    db_pool: PgPool,
}

impl TeachingClassesRepository {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 根据教师ID分页查询教学班（任课老师查看自己的班级）
    pub async fn find_classes_by_teacher_id_paginated(
        &self,
        schema_name: &str,
        teacher_id: &Uuid,
        params: &PageUserClassListParams,
    ) -> Result<(Vec<TeachingClasses>, i64), sqlx::Error> {
        let PageUserClassListParams { 
            page_params, 
            name_like, 
            class_code, 
            is_active, 
            subject_group_id 
        } = params;
        
        let mut builder = QueryBuilder::new(format!(
            "SELECT * FROM {}.teaching_classes tc WHERE tc.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        
        let mut count_builder = QueryBuilder::new(format!(
            "SELECT count(*) FROM {}.teaching_classes tc WHERE tc.teacher_id = ",
            schema_name
        ));
        count_builder.push_bind(teacher_id);

        // 添加其他过滤条件
        if let Some(name_like) = name_like {
            builder.push(" AND tc.class_name LIKE ");
            builder.push_bind(format!("%{}%", name_like));
            
            count_builder.push(" AND tc.class_name LIKE ");
            count_builder.push_bind(format!("%{}%", name_like));
        }
        
        if let Some(class_code) = class_code {
            builder.push(" AND tc.code = ");
            builder.push_bind(class_code);
            
            count_builder.push(" AND tc.code = ");
            count_builder.push_bind(class_code);
        }
        
        if let Some(is_active) = is_active {
            builder.push(" AND tc.is_active = ");
            builder.push_bind(is_active);
            
            count_builder.push(" AND tc.is_active = ");
            count_builder.push_bind(is_active);
        }
        
        if let Some(subject_group_id) = subject_group_id {
            builder.push(" AND tc.subject_group_id = ");
            builder.push_bind(subject_group_id);
            
            count_builder.push(" AND tc.subject_group_id = ");
            count_builder.push_bind(subject_group_id);
        }

        builder.push(" ORDER BY tc.is_active DESC, tc.created_at DESC ");
        builder.push(" LIMIT ");
        builder.push_bind(page_params.get_limit());
        builder.push(" OFFSET ");
        builder.push_bind(page_params.get_offset());

        let classes = builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await?;

        let count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;

        Ok((classes, count))
    }

    /// 根据学科组ID列表分页查询教学班
    pub async fn find_classes_by_subject_groups_paginated(
        &self,
        schema_name: &str,
        subject_group_ids: &[Uuid],
        params: &PageUserClassListParams,
    ) -> Result<(Vec<TeachingClasses>, i64), sqlx::Error> {
        if subject_group_ids.is_empty() {
            return Ok((vec![], 0));
        }

        let PageUserClassListParams { 
            page_params, 
            name_like, 
            class_code, 
            is_active, 
            subject_group_id 
        } = params;
        
        let mut builder = QueryBuilder::new(format!(
            "SELECT * FROM {}.teaching_classes tc WHERE tc.subject_group_id IN (",
            schema_name
        ));
        
        let mut count_builder = QueryBuilder::new(format!(
            "SELECT count(*) FROM {}.teaching_classes tc WHERE tc.subject_group_id IN (",
            schema_name
        ));

        // 添加学科组ID条件
        for (index, sg_id) in subject_group_ids.iter().enumerate() {
            builder.push_bind(sg_id);
            count_builder.push_bind(sg_id);
            if index < subject_group_ids.len() - 1 {
                builder.push(", ");
                count_builder.push(", ");
            }
        }
        builder.push(") ");
        count_builder.push(") ");

        // 添加其他过滤条件
        if let Some(name_like) = name_like {
            builder.push(" AND tc.class_name LIKE ");
            builder.push_bind(format!("%{}%", name_like));
            
            count_builder.push(" AND tc.class_name LIKE ");
            count_builder.push_bind(format!("%{}%", name_like));
        }
        
        if let Some(class_code) = class_code {
            builder.push(" AND tc.code = ");
            builder.push_bind(class_code);
            
            count_builder.push(" AND tc.code = ");
            count_builder.push_bind(class_code);
        }
        
        if let Some(is_active) = is_active {
            builder.push(" AND tc.is_active = ");
            builder.push_bind(is_active);
            
            count_builder.push(" AND tc.is_active = ");
            count_builder.push_bind(is_active);
        }
        
        if let Some(subject_group_id) = subject_group_id {
            builder.push(" AND tc.subject_group_id = ");
            builder.push_bind(subject_group_id);
            
            count_builder.push(" AND tc.subject_group_id = ");
            count_builder.push_bind(subject_group_id);
        }

        builder.push(" ORDER BY tc.is_active DESC, tc.created_at DESC ");
        builder.push(" LIMIT ");
        builder.push_bind(page_params.get_limit());
        builder.push(" OFFSET ");
        builder.push_bind(page_params.get_offset());

        let classes = builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await?;

        let count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;

        Ok((classes, count))
    }

    /// 根据教师ID和学科组ID列表联合查询教学班（支持任课老师 + 学科组成员的复合权限）
    pub async fn find_classes_by_teacher_and_subject_groups_paginated(
        &self,
        schema_name: &str,
        teacher_id: &Uuid,
        subject_group_ids: &[Uuid],
        params: &PageUserClassListParams,
    ) -> Result<(Vec<TeachingClasses>, i64), sqlx::Error> {
        let PageUserClassListParams { 
            page_params, 
            name_like, 
            class_code, 
            is_active, 
            subject_group_id 
        } = params;
        
        let mut builder = QueryBuilder::new(format!(
            "SELECT DISTINCT * FROM {}.teaching_classes tc WHERE (tc.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        
        let mut count_builder = QueryBuilder::new(format!(
            "SELECT count(DISTINCT tc.id) FROM {}.teaching_classes tc WHERE (tc.teacher_id = ",
            schema_name
        ));
        count_builder.push_bind(teacher_id);

        // 添加学科组条件（如果有的话）
        if !subject_group_ids.is_empty() {
            builder.push(" OR tc.subject_group_id IN (");
            count_builder.push(" OR tc.subject_group_id IN (");
            
            for (index, sg_id) in subject_group_ids.iter().enumerate() {
                builder.push_bind(sg_id);
                count_builder.push_bind(sg_id);
                if index < subject_group_ids.len() - 1 {
                    builder.push(", ");
                    count_builder.push(", ");
                }
            }
            builder.push(")");
            count_builder.push(")");
        }
        
        builder.push(") ");
        count_builder.push(") ");

        // 添加其他过滤条件
        if let Some(name_like) = name_like {
            builder.push(" AND tc.class_name LIKE ");
            builder.push_bind(format!("%{}%", name_like));
            
            count_builder.push(" AND tc.class_name LIKE ");
            count_builder.push_bind(format!("%{}%", name_like));
        }
        
        if let Some(class_code) = class_code {
            builder.push(" AND tc.code = ");
            builder.push_bind(class_code);
            
            count_builder.push(" AND tc.code = ");
            count_builder.push_bind(class_code);
        }
        
        if let Some(is_active) = is_active {
            builder.push(" AND tc.is_active = ");
            builder.push_bind(is_active);
            
            count_builder.push(" AND tc.is_active = ");
            count_builder.push_bind(is_active);
        }
        
        if let Some(subject_group_id) = subject_group_id {
            builder.push(" AND tc.subject_group_id = ");
            builder.push_bind(subject_group_id);
            
            count_builder.push(" AND tc.subject_group_id = ");
            count_builder.push_bind(subject_group_id);
        }

        builder.push(" ORDER BY tc.is_active DESC, tc.created_at DESC ");
        builder.push(" LIMIT ");
        builder.push_bind(page_params.get_limit());
        builder.push(" OFFSET ");
        builder.push_bind(page_params.get_offset());

        let classes = builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await?;

        let count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;

        Ok((classes, count))
    }

    /// 获取用户教授的教学班ID列表
    pub async fn get_user_teaching_classes(
        pool: &PgPool,
        user_id: &Uuid,
        schema_name: &str,
    ) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT tc.id FROM {}.teaching_classes tc
            JOIN {}.user_identities ui ON tc.id = ui.target_id
            WHERE ui.user_id = $1 AND ui.target_type = 'teaching_class' AND tc.is_active = true
            "#,
            schema_name, schema_name
        );

        let class_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_all(pool)
            .await?;

        Ok(class_ids)
    }

    /// 从用户教授的教学班中获取学科组ID列表
    pub async fn get_subject_groups_from_teaching_classes(
        pool: &PgPool,
        user_id: &Uuid,
        schema_name: &str,
    ) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT DISTINCT tc.subject_group_id
            FROM {}.teaching_classes tc
            JOIN {}.teachers t ON tc.teacher_id = t.id
            WHERE t.user_id = $1 AND tc.is_active = true AND tc.subject_group_id IS NOT NULL
            "#,
            schema_name, schema_name
        );

        let group_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_all(pool)
            .await?;

        Ok(group_ids)
    }
}
