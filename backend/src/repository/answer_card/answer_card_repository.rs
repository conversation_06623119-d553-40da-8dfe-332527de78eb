use crate::model::answer_card::answer_card::{AnswerCard, UpdateAnswerCardParams};
use sqlx::{pool::PoolConnection, Postgres};
use uuid::Uuid;

pub struct AnswerCardRepository{}
impl AnswerCardRepository{
    ///通过id查询答题卡
    pub async fn get_answer_card_by_id(
        conn: &mut PoolConnection<Postgres>,
        answer_card_id: Uuid,
    ) -> Result<Option<AnswerCard>, String> {
        sqlx::query_as::<_, AnswerCard>("SELECT * FROM answer_cards WHERE id = $1")
            .bind(answer_card_id)
            .fetch_optional(conn.as_mut())
            .await
            .map_err(|e| e.to_string())
    }
    ///创建答题卡
    pub async fn create_answer_card(
        conn: &mut PoolConnection<Postgres>,
        new_answer_card: &AnswerCard,
    ) -> Result<AnswerCard, String> {
        sqlx::query_as::<_, AnswerCard>(
            "INSERT INTO answer_cards (
                paper_id, dpi, width, height, x, y, right, bottom,
                page_orientation_is_vertical, bucket_size, show_page_index, show_pos_point,
                pos_point_width, pos_point_height, page_total, admission_ticket_number_info_question_item_config
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9,
                $10, $11, $12, $13,
                $14, $15, $16, $17
            ) RETURNING *"
        )
        .bind(new_answer_card.paper_id)
        .bind(new_answer_card.dpi)
        .bind(new_answer_card.width)
        .bind(new_answer_card.height)
        .bind(new_answer_card.x)
        .bind(new_answer_card.y)
        .bind(new_answer_card.right)
        .bind(new_answer_card.bottom)
        .bind(new_answer_card.page_orientation_is_vertical)
        .bind(new_answer_card.bucket_size)
        .bind(new_answer_card.show_page_index)
        .bind(new_answer_card.show_pos_point)
        .bind(new_answer_card.pos_point_width)
        .bind(new_answer_card.pos_point_height)
        .bind(new_answer_card.page_total)
        .bind(&new_answer_card.admission_ticket_number_info_question_item_config)
        .fetch_one(conn.as_mut())
        .await
        .map_err(|e| e.to_string())
    }
    ///根据试卷id查询答题卡
    pub async fn get_answer_card_by_paper_id(
        conn: &mut PoolConnection<Postgres>,
        paper_id: Uuid,
    ) -> Result<Option<AnswerCard>, String> {
        sqlx::query_as::<_, AnswerCard>("SELECT * FROM answer_cards WHERE paper_id = $1")
            .bind(paper_id)
            .fetch_optional(conn.as_mut())
            .await
            .map_err(|e| e.to_string())
    }

    ///修改答题卡（不支持部修改paper_id）
    pub async fn update_answer_card(
        conn: &mut PoolConnection<Postgres>,
        answer_card_id: Uuid,
        UpdateAnswerCardParams{
            dpi,
            width,
            height,
            x,
            y,
            right,
            bottom,
            page_orientation_is_vertical,
            bucket_size,
            show_page_index,
            show_pos_point,
            pos_point_width,
            pos_point_height,
            page_total,
            admission_ticket_number_info_question_item_config,
        }: &UpdateAnswerCardParams,
    ) -> Result<Option<AnswerCard>, String> {
        let mut builder = sqlx::QueryBuilder::new("UPDATE answer_cards SET ");
        let mut first = true;
        if let Some(v) = dpi { if !first { builder.push(", "); } builder.push("dpi = ").push_bind(v); first = false; }
        if let Some(v) = width { if !first { builder.push(", "); } builder.push("width = ").push_bind(v); first = false; }
        if let Some(v) = height { if !first { builder.push(", "); } builder.push("height = ").push_bind(v); first = false; }
        if let Some(v) = x { if !first { builder.push(", "); } builder.push("x = ").push_bind(v); first = false; }
        if let Some(v) = y { if !first { builder.push(", "); } builder.push("y = ").push_bind(v); first = false; }
        if let Some(v) = right { if !first { builder.push(", "); } builder.push("right = ").push_bind(v); first = false; }
        if let Some(v) = bottom { if !first { builder.push(", "); } builder.push("bottom = ").push_bind(v); first = false; }
        if let Some(v) = page_orientation_is_vertical { if !first { builder.push(", "); } builder.push("page_orientation_is_vertical = ").push_bind(v); first = false; }
        if let Some(v) = bucket_size { if !first { builder.push(", "); } builder.push("bucket_size = ").push_bind(v); first = false; }
        if let Some(v) = show_page_index { if !first { builder.push(", "); } builder.push("show_page_index = ").push_bind(v); first = false; }
        if let Some(v) = show_pos_point { if !first { builder.push(", "); } builder.push("show_pos_point = ").push_bind(v); first = false; }
        if let Some(v) = pos_point_width { if !first { builder.push(", "); } builder.push("pos_point_width = ").push_bind(v); first = false; }
        if let Some(v) = pos_point_height { if !first { builder.push(", "); } builder.push("pos_point_height = ").push_bind(v); first = false; }
        if let Some(v) = page_total { if !first { builder.push(", "); } builder.push("page_total = ").push_bind(v); first = false; }
        if let Some(v) = admission_ticket_number_info_question_item_config { if !first { builder.push(", "); } builder.push("admission_ticket_number_info_question_item_config = ").push_bind(v); first = false; }
        // 更新时间戳
        if !first { builder.push(", "); }
        builder.push("updated_at = NOW()");
        builder.push(" WHERE id = ").push_bind(answer_card_id);
        builder.push(" RETURNING *");
        let query = builder.build_query_as::<AnswerCard>();
        query.fetch_optional(conn.as_mut()).await.map_err(|e| e.to_string())
    }

    ///删除答题卡
    pub async fn delete_answer_card(
        conn: &mut PoolConnection<Postgres>,
        answer_card_id: Uuid,
    ) -> Result<bool, String> {
        let result = sqlx::query("DELETE FROM answer_cards WHERE id = $1")
            .bind(answer_card_id)
            .execute(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        Ok(result.rows_affected() > 0)
    }
}
    
