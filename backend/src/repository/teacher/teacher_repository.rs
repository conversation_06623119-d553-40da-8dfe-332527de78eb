use sqlx::{PgPool, Row};
use uuid::Uuid;

/// 教师数据访问层
#[derive(Clone)]
pub struct TeacherRepository {
    db_pool: PgPool,
}

impl TeacherRepository {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 根据用户ID获取绑定的教师ID
    pub async fn find_teacher_id_by_user_id(
        &self,
        schema_name: &str,
        user_id: &Uuid,
    ) -> Result<Option<Uuid>, sqlx::Error> {
        let query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            schema_name
        );
        
        sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_optional(&self.db_pool)
            .await
    }
}
