use crate::model::paper::paper::Paper;
use serde_json::Value;
use sqlx::{pool::PoolConnection, Postgres};
use uuid::Uuid;

/**
 * 作者：张瀚
 * 说明：试卷表的接口
 */
pub struct PapersRepository {}

impl PapersRepository {
    /**
     * 作者：张瀚
     * 说明：通过试卷ID查询试卷信息
     */
    pub async fn find_by_paper_id(mut conn: PoolConnection<Postgres>, paper_id: &Uuid) -> Result<Paper, String> {
        sqlx::query_as::<_, Paper>("SELECT * FROM papers p WHERE p.id = $1")
            .bind(paper_id)
            .fetch_one(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：更新试卷的内容
     */
    pub async fn update_paper_content(mut conn: PoolConnection<Postgres>, paper_id: &Uuid, paper_content_json: &Value) -> Result<Paper, String> {
        sqlx::query_as::<_, Paper>("UPDATE papers SET paper_content = $1 WHERE id = $2 RETURNING *")
            .bind(paper_content_json)
            .bind(paper_id)
            .fetch_one(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }
}
