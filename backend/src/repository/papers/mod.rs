use crate::model::paper::paper::Paper;
use crate::model::paper::paper_cache::PaperContentData;
use sqlx::types::Json;
use sqlx::{PgPool, Postgres, Transaction};
use uuid::Uuid;
pub mod papers_repository;

pub async fn get_papers_by_textbook_id(textbook_id: Uuid, db: &PgPool, page_size: i64, paper_name: Option<String>, offset: i64) -> anyhow::Result<(Vec<Paper>, i64)> {
    // 查询总数
    let mut builder_count = sqlx::QueryBuilder::new(
        "SELECT COUNT(*) FROM public.papers p
         INNER JOIN public.textbook_papers tp ON p.id = tp.paper_id
         WHERE tp.textbook_id = ",
    );
    builder_count.push_bind(textbook_id);
    if let Some(name) = &paper_name {
        builder_count.push(" AND p.paper_name ILIKE ").push_bind(format!("%{}%", name));
    };
    let (total_count,) = builder_count.build_query_as().fetch_one(db).await?;
    let mut builder = sqlx::QueryBuilder::new(
        "SELECT p.* FROM public.papers p
         INNER JOIN public.textbook_papers tp ON p.id = tp.paper_id
         WHERE tp.textbook_id = ",
    );
    builder.push_bind(textbook_id);
    if let Some(name) = &paper_name {
        builder.push(" AND p.paper_name ILIKE ").push_bind(format!("%{}%", name));
    };
    builder.push(" ORDER BY tp.serial_number LIMIT ").push_bind(page_size).push(" OFFSET ").push_bind(offset);
    let papers = builder.build_query_as::<Paper>().fetch_all(db).await?;
    Ok((papers, total_count))
}
pub async fn get_papers(db: &PgPool) -> anyhow::Result<Vec<Paper>> {
    let mut builder = sqlx::QueryBuilder::new("SELECT * FROM public.papers");
    let papers = builder.build_query_as::<Paper>().fetch_all(db).await?;
    Ok(papers)
}

pub async fn get_paper_by_id(db: &PgPool, id: Uuid) -> anyhow::Result<Paper> {
    let mut builder = sqlx::QueryBuilder::new("SELECT * FROM public.papers  WHERE id = ");
    builder.push_bind(id);
    let paper = builder.build_query_as::<Paper>().fetch_one(db).await?;
    Ok(paper)
}

pub async fn create_paper(db: &PgPool, paper_name: String, paper_content: PaperContentData) -> anyhow::Result<Paper> {
    let mut builder = sqlx::QueryBuilder::new("INSERT INTO public.papers (paper_name, paper_content) VALUES (");
    builder.push_bind(paper_name).push(", ");
    builder.push_bind(Json(paper_content)).push(") RETURNING *");
    let paper = builder.build_query_as::<Paper>().fetch_one(db).await?;
    Ok(paper)
}
pub async fn create_paper_tx<'a>(tx: &mut Transaction<'a, Postgres>, id: Option<Uuid>, paper_name: String, paper_content: PaperContentData) -> anyhow::Result<Paper> {
    let mut builder = sqlx::QueryBuilder::new("INSERT INTO public.papers (id, paper_name, paper_content) VALUES (");
    builder.push_bind(id.unwrap_or(Uuid::new_v4())).push(", ");
    builder.push_bind(paper_name).push(", ");
    builder.push_bind(Json(paper_content)).push(") RETURNING *");
    let paper = builder.build_query_as::<Paper>().fetch_one(tx.as_mut()).await?;
    Ok(paper)
}

pub async fn update_paper(db: &PgPool, id: Uuid, paper_name: Option<String>, paper_content: Option<PaperContentData>) -> anyhow::Result<Paper> {
    let mut builder = sqlx::QueryBuilder::new("UPDATE public.papers SET ");
    if let Some(paper_name) = paper_name {
        builder.push("paper_name = ").push_bind(paper_name).push(", ");
    }
    if let Some(paper_content) = paper_content {
        builder.push("paper_content = ").push_bind(Json(paper_content)).push(", ");
    }
    builder.push("updated_at = now() WHERE id = ");
    builder.push_bind(Json(id)).push(" RETURNING *");
    let paper = builder.build_query_as::<Paper>().fetch_one(db).await?;
    Ok(paper)
}
pub async fn delete_paper(db: &PgPool, id: Uuid) -> anyhow::Result<()> {
    sqlx::query!("DELETE FROM public.papers WHERE id = $1", id).execute(db).await?;
    Ok(())
}
