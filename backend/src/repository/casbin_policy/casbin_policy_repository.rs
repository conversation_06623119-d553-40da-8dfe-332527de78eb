use sqlx::{Execute, FromRow, PgPool};
use crate::utils::error::AppError;
use crate::model::permission::casbin_policy::CasbinPolicyRecord;
use tracing::{info, error};
use anyhow::Result;

#[derive(Debu<PERSON>, <PERSON><PERSON>, FromRow, serde::Serialize)]
pub struct ScopeRole {
    pub code: String,
    pub name: String,
}
/// Casbin策略Repository
#[derive(<PERSON><PERSON>, Debug)]
pub struct CasbinPolicyRepository {
    pub db: PgPool,
}

impl CasbinPolicyRepository {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// 通用方法：根据数据范围前缀查询角色
    /// 
    /// # 参数
    /// - `scope_prefix`: 数据范围前缀，如 "subject_group", "class", "grade" 等
    /// 
    /// # 返回
    /// 返回匹配的角色列表，只包含 code 和 name
    pub async fn get_roles_by_scope_prefix(&self, scope_prefix: &str) -> Result<Vec<ScopeRole>, AppError> {
        let pattern = format!("{}:%", scope_prefix);
        
        // 直接与role表关联查询，返回完整的role信息
        let roles = sqlx::query_as!(
            ScopeRole,
            r#"
            SELECT DISTINCT r.code, r.name
            FROM public.casbin_policies cp
            INNER JOIN public.roles r ON r.code = cp.v0
            WHERE cp.ptype = 'p'
            AND cp.v2 LIKE $1
            AND cp.v2 IS NOT NULL
            AND r.is_active = true
            ORDER BY r.code
            "#,
            pattern
        )
        .fetch_all(&self.db)
        .await
        .map_err(|e| {
            error!("Failed to fetch roles by scope prefix '{}': {}", scope_prefix, e);
            AppError::DatabaseError(e)
        })?;

        info!("Found {} roles with scope prefix '{}'", roles.len(), scope_prefix);
        Ok(roles)
    }

    /// 获取所有数据范围类型
    /// 
    /// # 返回
    /// 返回所有不同的数据范围类型列表
    pub async fn get_all_scope_types(&self) -> Result<Vec<String>, AppError> {
        let scope_records = sqlx::query!(
            r#"
            SELECT DISTINCT 
                CASE 
                    WHEN v2 LIKE '%:%' THEN split_part(v2, ':', 1)
                    ELSE v2
                END as scope_type
            FROM public.casbin_policies 
            WHERE ptype = 'p'
            AND v2 IS NOT NULL
            AND v2 != ''
            ORDER BY scope_type
            "#
        )
        .fetch_all(&self.db)
        .await
        .map_err(|e| {
            error!("Failed to fetch scope types: {}", e);
            AppError::DatabaseError(e)
        })?;

        let scope_types: Vec<String> = scope_records
            .into_iter()
            .filter_map(|record| record.scope_type)
            .filter(|scope_type| !scope_type.is_empty())
            .collect();

        info!("Found {} scope types", scope_types.len());
        Ok(scope_types)
    }

    /// 查询Casbin策略
    pub async fn query_casbin_policies(&self, user_identities: &[String], domain: &str, resource: &str, action: &str) -> Result<Vec<CasbinPolicyRecord>> {
        if user_identities.is_empty() {
            return Ok(vec![]);
        }
        info!("Querying Casbin policies for user identities: {:?}, domain: {}, resource: {}, action: {}", user_identities, domain, resource, action);

        // 构建身份查询条件
        let identity_placeholders: Vec<String> = (1..=user_identities.len()).map(|i| format!("${}", i)).collect();
        let identity_condition = format!("v0 IN ({})", identity_placeholders.join(", "));

        let query = format!(
            r#"
            SELECT id, ptype, v0, v1, v2, v3, v4, v5, created_at, updated_at
            FROM public.casbin_policies
            WHERE ptype = 'p'
            AND ({})
            AND (v1 = ${} OR v1 = '*')
            AND (v2 like ${} OR v2 = '*')
            AND (v3 = ${} OR v3 = '*')
            ORDER BY created_at DESC
            "#,
            identity_condition,
            user_identities.len() + 1, // domain parameter
            user_identities.len() + 2, // resource parameter
            user_identities.len() + 3, // action parameter
        );

        let mut query_builder = sqlx::query_as(&query);

        // 绑定用户身份参数
        for identity in user_identities {
            query_builder = query_builder.bind(identity);
        }
        let resource_pattern = format!("{}:%", resource);
        // 绑定其他参数
        query_builder = query_builder.bind(domain).bind(resource_pattern).bind(action);
        info!("casbin policy query sql: {}", query_builder.sql());
        let policies = query_builder.fetch_all(&self.db).await?;
        info!("Casbin policies: {:?}", policies);
        Ok(policies)
    }

    /// 根据完整的数据范围查询角色
    /// 
    /// # 参数
    /// - `scope_pattern`: 完整的数据范围模式，如 "subject_group:math", "class:2024_1" 等
    /// 
    /// # 返回
    /// 返回匹配的角色列表
    pub async fn get_roles_by_exact_scope(&self, scope_pattern: &str) -> Result<Vec<ScopeRole>, AppError> {
        // 直接与role表关联查询，返回完整的role信息
        let roles = sqlx::query_as!(
            ScopeRole,
            r#"
            SELECT DISTINCT r.code, r.name
            FROM public.casbin_policies cp
            INNER JOIN public.roles r ON r.code = cp.v2
            WHERE cp.ptype = 'p'
            AND cp.v2 = $1
            AND cp.v2 IS NOT NULL
            AND r.is_active = true
            ORDER BY r.code
            "#,
            scope_pattern
        )
            .fetch_all(&self.db)
            .await
            .map_err(|e| {
                error!("Failed to fetch roles by scope '{}': {}", scope_pattern, e);
                AppError::DatabaseError(e)
            })?;

        info!("Found {} roles with scope '{}'", roles.len(), scope_pattern);
        Ok(roles)
    }
}
