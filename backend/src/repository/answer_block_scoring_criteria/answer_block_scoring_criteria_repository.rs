use sqlx::{pool::PoolConnection, Postgres};
use uuid::Uuid;
use crate::model::answer_block_scoring_criteria::answer_block_scoring_criteria::AnswerBlockScoringCriteria;

pub struct AnswerBlockScoringCriteriaRepository;
impl AnswerBlockScoringCriteriaRepository {
	/// 查询
	pub async fn get_by_id(
		conn: &mut PoolConnection<Postgres>,
		id: Uuid,
	) -> Result<Option<AnswerBlockScoringCriteria>, String> {
		sqlx::query_as::<_, AnswerBlockScoringCriteria>("SELECT * FROM answer_block_scoring_criteria WHERE id = $1")
			.bind(id)
			.fetch_optional(conn.as_mut())
			.await
			.map_err(|e| e.to_string())
	}

	/// 新增
	pub async fn create(
		conn: &mut PoolConnection<Postgres>,
		new_item: &AnswerBlockScoringCriteria,
	) -> Result<AnswerBlockScoringCriteria, String> {
		sqlx::query_as::<_, AnswerBlockScoringCriteria>(
			"INSERT INTO answer_block_scoring_criteria (id, scoring_criteria_id, block_group_id) VALUES ($1, $2, $3) RETURNING *"
		)
		.bind(new_item.id)
		.bind(new_item.scoring_criteria_id)
		.bind(new_item.block_group_id)
		.fetch_one(conn.as_mut())
		.await
		.map_err(|e| e.to_string())
	}

	/// 修改
	pub async fn update(
		conn: &mut PoolConnection<Postgres>,
		id: Uuid,
		scoring_criteria_id: Option<Uuid>,
		block_group_id: Option<Uuid>,
	) -> Result<Option<AnswerBlockScoringCriteria>, String> {
		let mut builder = sqlx::QueryBuilder::new("UPDATE answer_block_scoring_criteria SET ");
		let mut first = true;
		if let Some(v) = scoring_criteria_id { if !first { builder.push(", "); } builder.push("scoring_criteria_id = ").push_bind(v); first = false; }
		if let Some(v) = block_group_id { if !first { builder.push(", "); } builder.push("block_group_id = ").push_bind(v); first = false; }
		if !first { builder.push(", "); }
		builder.push("updated_at = NOW()");
		builder.push(" WHERE id = ").push_bind(id);
		builder.push(" RETURNING *");
		let query = builder.build_query_as::<AnswerBlockScoringCriteria>();
		query.fetch_optional(conn.as_mut()).await.map_err(|e| e.to_string())
	}

	/// 删除
	pub async fn delete(
		conn: &mut PoolConnection<Postgres>,
		id: Uuid,
	) -> Result<bool, String> {
		let result = sqlx::query("DELETE FROM answer_block_scoring_criteria WHERE id = $1")
			.bind(id)
			.execute(conn.as_mut())
			.await
			.map_err(|e| e.to_string())?;
		Ok(result.rows_affected() > 0)
	}
}