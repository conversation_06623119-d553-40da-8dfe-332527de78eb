use chrono::NaiveDateTime;
use sqlx::PgPool;
use uuid::Uuid;
use crate::controller::tenant::user::tenant_user_controller::SearchUsersQuery;
use crate::model::tenant::tenant_user::{TenantUser, TenantUserVO};

/**
 * 租户用户管理的数据库操作
 */
pub struct TenantUserRepository {
    pool: PgPool,
}

impl TenantUserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取租户用户列表（带分页）
    pub async fn get_tenant_users(
        &self,
        tenant_id: Uuid,
        page: Option<i64>,
        limit: Option<i64>,
    ) -> Result<(Vec<TenantUserVO>, i64), String> {
        let page = page.unwrap_or(1);
        let limit = limit.unwrap_or(20);
        let offset = (page - 1) * limit;
        // 查询用户列表
        let users: Vec<TenantUserVO> = sqlx::query_as(
            r#"
            SELECT 
                tu.id,
                tu.tenant_id,
                tu.user_id,
                u.username,
                u.phone_number,
                tu.access_type,
                tu.granted_by,
                tu.granted_at,
                tu.expires_at,
                tu.last_accessed_at,
                tu.created_at,
                tu.updated_at,
                NULL as granted_by_name
            FROM user_tenant_links tu
            LEFT JOIN users u ON tu.user_id = u.id
            WHERE tu.tenant_id = $1
            ORDER BY tu.granted_at DESC
            LIMIT $2 OFFSET $3
            "#
        )
        .bind(tenant_id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| e.to_string())?;

        // 查询总数
        let total = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM user_tenant_links WHERE tenant_id = $1",
            tenant_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or(0);
        Ok((users, total))
    }

    /// 添加用户到租户
    pub async fn add_user_to_tenant(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
        access_type: String,
        granted_by: Option<Uuid>,
        expires_at: Option<NaiveDateTime>,
    ) -> Result<TenantUser, String> {
        let id = Uuid::new_v4();

        let tenant_user = sqlx::query_as::<_, TenantUser>(
            r#"
            INSERT INTO user_tenant_links (id, tenant_id, user_id, access_type, granted_by, granted_at, expires_at, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, NOW(), $6, NOW(), NOW())
            RETURNING id, tenant_id, user_id, access_type, granted_by, granted_at, expires_at, last_accessed_at, created_at, updated_at
            "#
        )
        .bind(id)
        .bind(tenant_id)
        .bind(user_id)
        .bind(access_type)
        .bind(granted_by)
        .bind(expires_at)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| e.to_string())?;

        Ok(tenant_user)
    }

    /// 更新用户权限
    pub async fn update_user_permission(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
        access_type: Option<String>,
        expires_at: Option<NaiveDateTime>,
    ) -> Result<TenantUserVO, String> {
        // 构建动态更新查询
        let mut query_parts = Vec::new();
        let mut param_count = 3; // tenant_id, user_id 已占用前两个参数位置

        if access_type.is_some() {
            query_parts.push(format!("access_type = ${}", param_count));
            param_count += 1;
        }

        if expires_at.is_some() {
            query_parts.push(format!("expires_at = ${}", param_count));
        }

        query_parts.push("updated_at = NOW()".to_string());

        let set_clause = query_parts.join(", ");

        // 先执行更新操作
        let update_sql = format!(
            "UPDATE user_tenant_links SET {} WHERE tenant_id = $1 AND user_id = $2",
            set_clause
        );

        let mut update_query = sqlx::query(&update_sql)
            .bind(tenant_id)
            .bind(user_id);

        if let Some(access) = &access_type {
            update_query = update_query.bind(access);
        }

        if let Some(expires) = expires_at {
            update_query = update_query.bind(expires);
        }

        let result = update_query
            .execute(&self.pool)
            .await
            .map_err(|e| e.to_string())?;

        if result.rows_affected() == 0 {
            return Err("User not found in tenant".to_string());
        }

        // 然后查询更新后的完整用户信息（包含 username 和 phone_number）
        let user = sqlx::query_as::<_, TenantUserVO>(
            r#"
            SELECT
                tu.id,
                tu.tenant_id,
                tu.user_id,
                u.username,
                u.phone_number,
                tu.access_type,
                tu.granted_by,
                tu.granted_at,
                tu.expires_at,
                tu.last_accessed_at,
                tu.created_at,
                tu.updated_at,
                NULL as granted_by_name
            FROM user_tenant_links tu
            LEFT JOIN users u ON tu.user_id = u.id
            WHERE tu.tenant_id = $1 AND tu.user_id = $2
            "#
        )
        .bind(tenant_id)
        .bind(user_id)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| e.to_string())?;

        Ok(user)
    }

    /// 移除用户（软删除或硬删除）
    pub async fn remove_user_from_tenant(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
        hard_delete: bool,
    ) -> Result<(), String> {
        if hard_delete {
            // 硬删除
            let result = sqlx::query!(
                "DELETE FROM user_tenant_links WHERE tenant_id = $1 AND user_id = $2",
                tenant_id,
                user_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| e.to_string())?;

            if result.rows_affected() == 0 {
                return Err("User not found in tenant".to_string());
            }
        } else {
            // 软删除
            let result = sqlx::query!(
                "UPDATE user_tenant_links SET access_type = 'suspended', updated_at = NOW() WHERE tenant_id = $1 AND user_id = $2",
                tenant_id,
                user_id
            )
            .execute(&self.pool)
            .await
            .map_err(|e| e.to_string())?;

            if result.rows_affected() == 0 {
                return Err("User not found in tenant".to_string());
            }
        }

        Ok(())
    }

    /// 搜索租户用户
    pub async fn search_tenant_users(
        &self,
        tenant_id: Uuid,
        query: &SearchUsersQuery,
    ) -> Result<(Vec<TenantUserVO>, i64), String> {
        let page = query.page.unwrap_or(1);
        let limit = query.limit.unwrap_or(20);
        let offset = (page - 1) * limit;

        // 构建动态查询条件
        let mut where_conditions = vec!["tu.tenant_id = $1".to_string()];
        let mut param_count = 2; // tenant_id 已占用第一个参数

        // 关键词搜索（用户名或显示名）
        if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                where_conditions.push(format!(
                    "(u.username ILIKE ${})",
                    param_count,
                ));
                param_count += 1;
            }
        }

        // 访问类型筛选
        if let Some(access_type) = &query.access_type {
            if !access_type.trim().is_empty() {
                where_conditions.push(format!("tu.access_type = ${}", param_count));
                param_count += 1;
            }
        }

        let where_clause = where_conditions.join(" AND ");

        // 查询用户列表
        let query_sql = format!(
            r#"
            SELECT 
                tu.id,
                tu.tenant_id,
                tu.user_id,
                u.username,
                tu.access_type,
                tu.granted_by,
                tu.granted_at,
                tu.expires_at,
                tu.last_accessed_at,
                tu.created_at,
                tu.updated_at
            FROM user_tenant_links tu
            LEFT JOIN users u ON tu.user_id = u.id
            WHERE {}
            ORDER BY tu.granted_at DESC
            LIMIT ${} OFFSET ${}
            "#,
            where_clause,
            param_count,
            param_count + 1
        );

        let mut db_query = sqlx::query_as::<_, TenantUserVO>(&query_sql).bind(tenant_id);

        // 绑定参数
        let search_pattern = if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                Some(format!("%{}%", keyword))
            } else {
                None
            }
        } else {
            None
        };

        if let Some(ref pattern) = search_pattern {
            db_query = db_query.bind(pattern);
        }

        if let Some(access_type) = &query.access_type {
            if !access_type.trim().is_empty() {
                db_query = db_query.bind(access_type);
            }
        }

        db_query = db_query.bind(limit).bind(offset);

        let users = db_query
            .fetch_all(&self.pool)
            .await
            .map_err(|e| e.to_string())?;

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) FROM user_tenant_links tu LEFT JOIN users u ON tu.user_id = u.id WHERE {}",
            where_clause
        );

        let mut count_query = sqlx::query_scalar::<_, i64>(&count_sql).bind(tenant_id);

        // 绑定计数查询参数
        if let Some(ref pattern) = search_pattern {
            count_query = count_query.bind(pattern);
        }

        if let Some(access_type) = &query.access_type {
            if !access_type.trim().is_empty() {
                count_query = count_query.bind(access_type);
            }
        }

        let total = count_query
            .fetch_one(&self.pool)
            .await
            .map_err(|e| e.to_string())?;

        Ok((users, total))
    }

    /// 检查用户是否已在租户中
    pub async fn is_user_in_tenant(&self, tenant_id: Uuid, user_id: Uuid) -> Result<bool, String> {
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM user_tenant_links WHERE tenant_id = $1 AND user_id = $2)",
            tenant_id,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| e.to_string())?
        .unwrap_or(false);

        Ok(exists)
    }

    /// 获取用户在租户中的详细信息
    pub async fn get_tenant_user(
        &self,
        tenant_id: Uuid,
        user_id: Uuid,
    ) -> Result<Option<TenantUserVO>, String> {
        let user = sqlx::query_as::<_, TenantUserVO>(
            r#"
            SELECT 
                tu.id,
                tu.tenant_id,
                tu.user_id,
                u.phone_number,
                u.username,
                tu.access_type,
                tu.granted_by,
                tu.granted_at,
                tu.expires_at,
                tu.last_accessed_at,
                tu.created_at,
                tu.updated_at,
                NULL as granted_by_name
            FROM user_tenant_links tu
            LEFT JOIN users u ON tu.user_id = u.id
            WHERE tu.tenant_id = $1 AND tu.user_id = $2
            "#
        )
        .bind(tenant_id)
        .bind(user_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| e.to_string())?;

        Ok(user)
    }
}
