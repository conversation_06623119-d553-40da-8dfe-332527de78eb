use sqlx::PgPool;
use uuid::Uuid;

pub async fn fetch_tenant_name_by_id(db: &PgPool, tenant_id: Uuid) -> anyhow::Result<String> {
    let mut builder = sqlx::QueryBuilder::new("SELECT schema_name FROM public.tenants WHERE id = ");
    let v = builder.push_bind(tenant_id).build_query_scalar().fetch_one(db).await?;
    Ok(v)
}

pub async fn fetch_tenant_id_by_name(db: &PgPool, tenant_name: String) -> anyhow::Result<Uuid> {
    let mut builder = sqlx::QueryBuilder::new("SELECT id FROM public.tenants WHERE schema_name = ");
    let v = builder.push_bind(tenant_name).build_query_scalar().fetch_one(db).await?;
    Ok(v)
}

pub mod user;