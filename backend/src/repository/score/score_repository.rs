use crate::model::grading::paper_scan_block::PaperScanBlock;
use crate::model::score::{Score, ScoreStatus};
use anyhow::{anyhow, Context};
use bigdecimal::BigDecimal;
use serde::Serialize;
use sqlx::{Executor, FromRow, PgPool, Postgres, Row, Transaction};
use uuid::Uuid;

/// 纸张扫描打分点相关数据访问层
/// 作者: 萧达光
pub struct ScoreRepository {}

impl ScoreRepository {
    /// 批量插入评分块信息
    pub async fn batch_insert_scores_with_unnest(tx: &mut Transaction<'_, Postgres>, scores: &Vec<Score>) -> anyhow::Result<usize> {
        // 构建批量插入SQL
        let mut builder = sqlx::QueryBuilder::new("INSERT INTO scores (id, criteria_id, student_id, score_status, score) VALUES ");

        for (i, score) in scores.iter().enumerate() {
            builder
                .push(" ( ")
                .push_bind(score.id)
                .push(" , ")
                .push_bind(score.criteria_id)
                .push(" , ")
                .push_bind(&score.student_id)
                .push(" , ")
                .push_bind(&score.score_status)
                .push(" , ")
                .push_bind(&score.score)
                .push(" ) ");

            if i < scores.len() - 1 {
                builder.push(" , ");
            }
        }

        builder.build().execute(tx.as_mut()).await.context("执行批量插入 score 表失败")?;

        Ok(scores.len())
    }

    ///
    pub async fn batch_insert_score_blocks(tx: &mut Transaction<'_, Postgres>, scores_blocks: &Vec<(Uuid, Uuid)>) -> anyhow::Result<usize> {
        // 构建批量插入SQL
        let mut builder = sqlx::QueryBuilder::new("INSERT INTO score_blocks (score_id, block_id ) VALUES ");

        for (i, (score_id, block_id)) in scores_blocks.iter().enumerate() {
            builder.push(" ( ").push_bind(score_id).push(" , ").push_bind(block_id).push(" ) ");

            if i < scores_blocks.len() - 1 {
                builder.push(" , ");
            }
        }

        builder.build().execute(tx.as_mut()).await.context("执行批量插入 score_blocks 表失败")?;

        Ok(scores_blocks.len())
    }

    /// 批量删除相关的打分表(scores) 中的记录
    pub async fn batch_delete_score(tx: &mut Transaction<'_, Postgres>, score_ids: &Vec<Uuid>) -> anyhow::Result<u64> {
        if score_ids.len() == 0 {
            // return Err(anyhow!("打分 score_id 不能为空"));
            return Ok(0);
        }

        let mut builder = sqlx::QueryBuilder::new("DELETE FROM scores WHERE ");

        builder.push(" id = ANY( ").push_bind(score_ids).push(")");
        let count = builder.build().execute(tx.as_mut()).await?.rows_affected();

        Ok(count)
    }
}

pub async fn update<'e, E>(executor: E, schema_name: &str, id: Uuid, score: Option<BigDecimal>, score_status: Option<ScoreStatus>, student_id: Option<Uuid>) -> anyhow::Result<u64>
where
    E: 'e + Executor<'e, Database = Postgres>,
{
    let mut query = sqlx::QueryBuilder::new(format!("UPDATE {}.scores SET ", schema_name));
    let mut pre = " ";
    if let Some(score) = score {
        query.push(pre).push("score = ").push_bind(score);
        pre = ", ";
    }
    if let Some(score_status) = score_status {
        query.push(pre).push("score_status = ").push_bind(score_status);
        pre = ", ";
    }
    if let Some(student_id) = student_id {
        query.push(pre).push("student_id = ").push_bind(student_id);
    }
    query.push(" WHERE id = ").push_bind(id);
    let query = query.build();
    let result = query.execute(executor).await?;
    Ok(result.rows_affected())
}
pub async fn check_correct_by_ids(db: &PgPool, schema_name: &str, ids: Vec<Uuid>) -> anyhow::Result<u64> {
    let mut query = sqlx::QueryBuilder::new(format!("UPDATE {}.scores SET ", schema_name));
    query.push("score_status = ").push_bind(ScoreStatus::CheckedCorrect);
    query.push(" WHERE id = ANY(").push_bind(ids).push(")");
    query
        .push(" AND score_status NOT IN (")
        .push_bind(ScoreStatus::CheckedCorrect)
        .push(", ")
        .push_bind(ScoreStatus::CheckedError)
        .push(")");
    let result = query.build().execute(db).await?;
    Ok(result.rows_affected())
}
#[derive(Debug, FromRow, Clone, Serialize)]
pub struct ScoreBlock {
    pub block_id: Uuid,
    pub answer_block_url: Option<String>,
    pub answer_block_group_id: Uuid,
    pub answer_content: Option<String>,
}

impl ScoreBlock {
    pub fn from(paper_scan_block: PaperScanBlock) -> Self {
        Self {
            block_id: paper_scan_block.id,
            answer_block_url: paper_scan_block.answer_block_url,
            answer_block_group_id: paper_scan_block.answer_block_group_id,
            answer_content: paper_scan_block.answer_content,
        }
    }
}
pub async fn fetch_paged_scores_with_criteria_id(db: &PgPool, schema_name: &str, criteria_id: Uuid, page_size: i32, offset: i32) -> anyhow::Result<(Vec<Score>, i64)> {
    let mut query = sqlx::QueryBuilder::new(format!("SELECT COUNT(*) FROM {}.scores s WHERE s.criteria_id = ", schema_name));
    let total = query.push_bind(criteria_id).build().fetch_one(db).await?.try_get(0)?;
    let scores = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.scores s WHERE s.criteria_id = ", schema_name))
        .push_bind(criteria_id)
        .push(" LIMIT ")
        .push_bind(page_size)
        .push(" OFFSET ")
        .push_bind(offset)
        .build_query_as::<Score>()
        .fetch_all(db)
        .await?;
    Ok((scores, total))
}

pub async fn fetch_score_criteria_ids(db: &PgPool, schema_name: &str, criteria_ids: Vec<Uuid>, student_ids: Vec<Uuid>, mut status_list: Vec<ScoreStatus>) -> anyhow::Result<Vec<(Uuid, Uuid)>> {
    let mut builder = sqlx::QueryBuilder::new(format!("SELECT id, criteria_id FROM {}.scores WHERE ", schema_name));
    builder.push(" criteria_id = ANY(").push_bind(criteria_ids).push(")");
    if !student_ids.is_empty() {
        builder.push(" AND student_id = ANY(").push_bind(student_ids).push(")");
    }
    if !status_list.is_empty() {
        let status = status_list.pop().unwrap_or(ScoreStatus::Distributed);
        builder.push(" AND score_status IN (");
        status_list.into_iter().for_each(|status| {
            builder.push_bind(status).push(", ");
        });
        builder.push_bind(status).push(")");
    }
    let ids = builder.build_query_as().fetch_all(db).await?;
    Ok(ids)
}

pub async fn fetch_all_scores(db: &PgPool, schema_name: &str, criteria_ids: Option<Vec<Uuid>>, student_ids: Option<Vec<Uuid>>) -> anyhow::Result<Vec<Score>> {
    if criteria_ids.is_none() && student_ids.is_none() {
        return Err(anyhow!("no criteria and student ids given to fetch"));
    }
    let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.scores s WHERE 1=1", schema_name));
    if let Some(criteria_ids) = criteria_ids {
        builder.push(" AND s.criteria_id = ANY(").push_bind(criteria_ids).push(")");
    }
    if let Some(student_ids) = student_ids {
        builder.push(" AND s.student_id = ANY(").push_bind(student_ids).push(")");
    }
    let scores = builder.build_query_as::<Score>().fetch_all(db).await?;
    Ok(scores)
}

pub async fn get_blocks_with_score_id(db: &PgPool, schema_name: &str, score_id: Uuid) -> anyhow::Result<Vec<ScoreBlock>> {
    let mut builder = sqlx::QueryBuilder::new(format!(
        r#"
    SELECT b.answer_block_url, b.answer_block_group_id, b.answer_content, b.id as block_id
    FROM {}.score_blocks sb
    INNER JOIN {}.paper_scan_block b ON b.id = sb.block_id
    WHERE sb.score_id =
    "#,
        schema_name, schema_name
    ));
    builder.push_bind(score_id);
    builder.push(" ORDER BY b.serial_number ASC");
    let data = builder.build_query_as().fetch_all(db).await?;
    Ok(data)
}
#[derive(FromRow)]
pub struct CriteriaSummary {
    pub total_score: BigDecimal,
    pub criteria_id: Uuid,
    pub status: ScoreStatus,
    pub count: i64,
}
pub async fn fetch_criteria_summary(db: &PgPool, schema_name: &str, criteria_ids: Option<Vec<Uuid>>, student_ids: Option<Vec<Uuid>>) -> anyhow::Result<Vec<CriteriaSummary>> {
    if criteria_ids.is_none() && student_ids.is_none() {
        return Err(anyhow::anyhow!("no criteria and student ids given"));
    }
    let mut builder = sqlx::QueryBuilder::new(format!(
        r#"SELECT
            criteria_id,
            score_status as status,
            COUNT(*) as "count",
            SUM(score) as "total_score"
        FROM {}.scores
        WHERE 1=1 "#,
        schema_name
    ));
    if let Some(criteria_ids) = criteria_ids {
        builder.push(" AND criteria_id = ANY(").push_bind(criteria_ids).push(")");
    }
    if let Some(student_ids) = student_ids {
        builder.push(" AND student_id = ANY(").push_bind(student_ids).push(")");
    }
    builder.push(" GROUP BY criteria_id, score_status");
    let data = builder.build_query_as().fetch_all(db).await?;
    Ok(data)
}
#[derive(FromRow, Debug)]
pub struct ScoreWithBlockId {
    pub id: Uuid,
    pub student_id: Option<Uuid>,
    pub criteria_id: Uuid,
    pub score: BigDecimal,
    pub score_status: ScoreStatus,
    pub block_id: Uuid,
}
pub async fn get_scores_with_block_ids(db: &PgPool, schema_name: &str, block_ids: Vec<Uuid>) -> anyhow::Result<Vec<ScoreWithBlockId>> {
    let mut builder = sqlx::QueryBuilder::new(format!(
        r#"
        SELECT s.id, s.student_id, sb.block_id, s.criteria_id, s.score_status, s.score
        FROM {}.score_blocks sb
        INNER JOIN {}.scores s ON s.id = sb.score_id
        "#,
        schema_name, schema_name
    ));
    builder.push(" WHERE  sb.block_id = ANY(").push_bind(block_ids).push(")");
    //let a = builder.sql();

    let data = builder.build_query_as().fetch_all(db).await?;
    Ok(data)
}

/// 打分点重新绑定学生对应打分点
pub async fn rebind_student_to_score(db: &PgPool, schema_name: &str, student_id: Uuid, score_ids: Vec<Uuid>) -> anyhow::Result<u64> {
    let update_query = format!("UPDATE {}.scores SET student_id = ", schema_name);

    let mut builder = sqlx::QueryBuilder::new(update_query);
    builder.push_bind(student_id);
    builder.push(" WHERE id = ANY (").push_bind(score_ids).push(")");
    let count = builder.build().execute(db).await?.rows_affected();
    Ok(count)
}

/// 批量删除相关的打分与题块中间关系表 (score_blocks) 中的记录
pub async fn batch_delete_score_blocks(tx: &mut Transaction<'_, Postgres>, score_ids: &Vec<Uuid>) -> anyhow::Result<u64> {
    if score_ids.len() == 0 {
        // return Err(anyhow!("score_ids 值不能为空"));
        return Ok(0);
    }

    let mut builder = sqlx::QueryBuilder::new("DELETE FROM score_blocks WHERE ");

    builder.push("score_id = ANY( ").push_bind(score_ids).push(")");

    let count = builder.build().execute(tx.as_mut()).await?.rows_affected();

    Ok(count)
}
