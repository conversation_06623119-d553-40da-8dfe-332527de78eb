use crate::model::score::score_detail::ScoreDetail;
use anyhow::Context;
use sqlx::{Executor, Postgres, Transaction};
use uuid::Uuid;

pub async fn insert<'e, E>(executor: E, schema_name: &str, sd: ScoreDetail) -> anyhow::Result<()>
where
    E: 'e + Executor<'e, Database = Postgres>,
{
    let mut builder = sqlx::QueryBuilder::new(format!("INSERT INTO {}.score_details (id, score_id, status, score, scoring_type, ocr, reason) VALUES ", schema_name));
    builder
        .push("(")
        .push_bind(sd.id)
        .push(", ")
        .push_bind(sd.score_id)
        .push(", ")
        .push_bind(sd.status)
        .push(", ")
        .push_bind(sd.score)
        .push(", ")
        .push_bind(sd.scoring_type)
        .push(", ")
        .push_bind(sd.ocr)
        .push(", ")
        .push_bind(sd.reason)
        .push(")");
    builder.build().execute(executor).await.context("执行批量插入 score 表失败")?;
    Ok(())
}

pub async fn fetch_details_by_score_id<'e, E>(executor: E, schema_name: &str, score_id: Uuid) -> anyhow::Result<Vec<ScoreDetail>>
where
    E: 'e + Executor<'e, Database = Postgres>,
{
    let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.score_details  ", schema_name));
    builder.push("WHERE score_id = ").push_bind(score_id);
    builder.push(" ORDER BY created_at DESC");
    let score_details = builder.build_query_as::<ScoreDetail>().fetch_all(executor).await?;
    Ok(score_details)
}

///批量删除相关的打分明细表(score_details) 中的记录
pub async fn batch_delete_score_details(tx: &mut Transaction<'_, Postgres>, score_ids: &Vec<Uuid>) -> anyhow::Result<u64> {
    if score_ids.len() == 0 {
        // return Err(anyhow!("score_ids 不能为空"));
        return Ok(0);
    }

    let mut builder = sqlx::QueryBuilder::new("DELETE FROM score_details WHERE ");

    builder.push("score_id = ANY( ").push_bind(score_ids).push(")");

    let count = builder.build().execute(tx.as_mut()).await?.rows_affected();

    Ok(count)
}
