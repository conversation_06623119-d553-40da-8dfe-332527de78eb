use sqlx::{PgPool, Row};
use uuid::Uuid;
use anyhow::Result;

/// 学科组数据访问层
#[derive(Clone)]
pub struct SubjectGroupRepository {
    db_pool: PgPool,
}

impl SubjectGroupRepository {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 根据教师ID获取其所属的学科组ID列表
    pub async fn find_subject_group_ids_by_teacher_id(
        &self,
        schema_name: &str,
        teacher_id: &Uuid,
    ) -> Result<Vec<Uuid>, sqlx::Error> {
        let query = format!(
            "SELECT DISTINCT subject_group_id FROM {}.subject_group_members WHERE teacher_id = $1 AND is_active = true",
            schema_name
        );
        
        sqlx::query_scalar(&query)
            .bind(teacher_id)
            .fetch_all(&self.db_pool)
            .await
    }

    /// 获取用户管理的学科组ID列表
    pub async fn get_user_managed_subject_groups(
        pool: &PgPool,
        user_id: &Uuid,
        schema_name: &str,
    ) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT DISTINCT sgm.subject_group_id
            FROM {}.subject_group_members sgm
            JOIN {}.teachers t ON sgm.teacher_id = t.id
            WHERE t.user_id = $1 AND sgm.is_active = true
            "#,
            schema_name, schema_name
        );

        let group_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_all(pool)
            .await?;

        Ok(group_ids)
    }
}
