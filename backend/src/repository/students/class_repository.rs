use sqlx::{Pg<PERSON>ool, QueryBuilder};
use uuid::Uuid;
use anyhow::Result;
use crate::model::classes::classes::ClassesSummary;

pub async fn fetch_administrative_classes_summaries(db: &PgPool, tenant_name: &str, is_active: Option<bool>) -> Result<Vec<ClassesSummary>, sqlx::Error> {
    let mut builder = QueryBuilder::new(format!("SELECT id, class_name as name, code, is_active FROM {}.administrative_classes", tenant_name));
    builder.push(" WHERE code IS NOT NULL AND code != ''");
    if let Some(active) = is_active {
        builder.push(" AND is_active = ").push_bind(active);
    }
    builder.push(" ORDER BY class_name ASC");
    let ret = builder.build_query_as().fetch_all(db).await?;
    Ok(ret)
}
pub async fn fetch_teaching_classes_summary(db: &PgPool, tenant_name: &str, class_id: Uuid) -> Result<ClassesSummary, sqlx::Error> {
    let mut builder = QueryBuilder::new(format!("SELECT id, class_name as name, code, is_active FROM {}.teaching_classes", tenant_name));
    builder.push(" WHERE id = ").push_bind(class_id);
    let ret = builder.build_query_as().fetch_one(db).await?;
    Ok(ret)
}
pub async fn fetch_teaching_class_with_name_list_by_ids(db: &PgPool, tenant_name: &str, class_ids: Vec<Uuid>) -> Result<Vec<(Uuid, String)>, sqlx::Error> {
    let mut builder = QueryBuilder::new(format!("SELECT id, class_name as name, code, is_active FROM {}.teaching_classes WHERE ", tenant_name));
    builder.push(" id = ANY(").push_bind(class_ids).push(")");
    let ret = builder.build_query_as().fetch_all(db).await?;
    Ok(ret)
}
pub async fn fetch_teaching_class_with_name_list_by_group_ids(db: &PgPool, tenant_name: &str, subject_group_ids: &Vec<Uuid>) -> Result<Vec<(Uuid, String)>, sqlx::Error> {
    let mut builder = QueryBuilder::new(format!("SELECT id, class_name as name, code, is_active FROM {}.teaching_classes WHERE ", tenant_name));
    builder.push(" subject_group_id = ANY(").push_bind(subject_group_ids).push(") AND ");
    builder.push(" is_active = true ORDER BY class_name ASC");
    let ret = builder.build_query_as().fetch_all(db).await?;
    Ok(ret)
}

/// 获取用户管理的行政班ID列表（班主任）
pub async fn get_user_managed_classes(
    pool: &PgPool,
    user_id: &Uuid,
    schema_name: &str,
) -> Result<Vec<Uuid>> {
    let query = format!(
        r#"
        SELECT ac.id FROM {}.administrative_classes ac
        JOIN {}.user_identities ui ON ac.id = ui.target_id
        WHERE ui.user_id = $1 AND ui.target_type = 'administrative_class' AND ac.is_active = true
        "#,
        schema_name, schema_name
    );

    let class_ids: Vec<Uuid> = sqlx::query_scalar(&query)
        .bind(user_id)
        .fetch_all(pool)
        .await?;

    Ok(class_ids)
}

/// 获取用户管理的年级列表
pub async fn get_user_managed_grades(
    pool: &PgPool,
    user_id: &Uuid,
    schema_name: &str,
) -> Result<Vec<String>> {
    let query = format!(
        r#"
        SELECT DISTINCT ac.grade_level_code FROM {}.administrative_classes ac
        JOIN {}.user_identities ui ON ac.grade_leader_id = ui.user_id
        WHERE ui.user_id = $1 AND ac.is_active = true
        "#,
        schema_name, schema_name
    );

    let grades: Vec<String> = sqlx::query_scalar(&query)
        .bind(user_id)
        .fetch_all(pool)
        .await?;

    Ok(grades)
}