use sqlx::{PgPool, QueryBuilder};
use uuid::Uuid;

pub async fn fetch_subject_groups(db: &PgPool, tenant_name: &str, subject_codes: Vec<String>) -> Result<Vec<(Uuid, String)>, sqlx::Error> {
    let mut builder = QueryBuilder::new(format!("SELECT id, group_name FROM {}.subject_groups", tenant_name));
    if !subject_codes.is_empty() {
        builder.push(" WHERE id = ANY(").push_bind(subject_codes).push(")");
    }
    let ret = builder.build_query_as().fetch_all(db).await?;
    Ok(ret)
}