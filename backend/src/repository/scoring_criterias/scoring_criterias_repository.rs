use sqlx::{pool::PoolConnection, Postgres};
use uuid::Uuid;
use crate::model::scoring_criterias::scoring_criterias::{ScoringCriteria, UpdateScoringCriteriaParams};

pub struct ScoringCriteriasRepository{}
impl ScoringCriteriasRepository {
	/// 查询
	pub async fn get_by_id(
		conn: &mut PoolConnection<Postgres>,
		id: &Uuid,
	) -> Result<Option<ScoringCriteria>, String> {
		sqlx::query_as::<_, ScoringCriteria>("SELECT * FROM scoring_criterias WHERE id = $1")
			.bind(id)
			.fetch_optional(conn.as_mut())
			.await
			.map_err(|e| e.to_string())
	}

	/// 新增
	pub async fn create(
		conn: &mut PoolConnection<Postgres>,
		new_item: &ScoringCriteria,
	) -> Result<ScoringCriteria, String> {
		sqlx::query_as::<_, ScoringCriteria>(
			"INSERT INTO scoring_criterias (
				id, scoring_type, mode, criteria_name, answer, score, ocr_work_id, check_work_id, question_tips, created_at, updated_at
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()
			) RETURNING *"
		)
		.bind(new_item.id)
		.bind(&new_item.scoring_type)
		.bind(&new_item.mode)
		.bind(&new_item.criteria_name)
		.bind(&new_item.answer)
		.bind(new_item.score)
		.bind(&new_item.ocr_work_id)
		.bind(&new_item.check_work_id)
		.bind(&new_item.question_tips)
		.fetch_one(conn.as_mut())
		.await
		.map_err(|e| e.to_string())
	}

	/// 修改
	pub async fn update(
		conn: &mut PoolConnection<Postgres>,
		id: &Uuid,
		UpdateScoringCriteriaParams {
            scoring_type,
            mode,
            criteria_name,
            answer,
            score,
            ocr_work_id,
            check_work_id,
            question_tips,
        }: &UpdateScoringCriteriaParams,
	) -> Result<Option<ScoringCriteria>, String> {
		let mut builder = sqlx::QueryBuilder::new("UPDATE scoring_criterias SET ");
		let mut first = true;
		if let Some(v) = scoring_type { if !first { builder.push(", "); } builder.push("scoring_type = ").push_bind(v); first = false; }
		if let Some(v) = mode { if !first { builder.push(", "); } builder.push("mode = ").push_bind(v); first = false; }
		if let Some(v) = criteria_name { if !first { builder.push(", "); } builder.push("criteria_name = ").push_bind(v); first = false; }
		if let Some(v) = answer { if !first { builder.push(", "); } builder.push("answer = ").push_bind(v); first = false; }
		if let Some(v) = score { if !first { builder.push(", "); } builder.push("score = ").push_bind(v); first = false; }
		if let Some(v) = ocr_work_id { if !first { builder.push(", "); } builder.push("ocr_work_id = ").push_bind(v); first = false; }
		if let Some(v) = check_work_id { if !first { builder.push(", "); } builder.push("check_work_id = ").push_bind(v); first = false; }
		if let Some(v) = question_tips { if !first { builder.push(", "); } builder.push("question_tips = ").push_bind(v); first = false; }
		if !first { builder.push(", "); }
		builder.push("updated_at = NOW()");
		builder.push(" WHERE id = ").push_bind(id);
		builder.push(" RETURNING *");
		let query = builder.build_query_as::<ScoringCriteria>();
		query.fetch_optional(conn.as_mut()).await.map_err(|e| e.to_string())
	}

	/// 删除
	pub async fn delete(
		conn: &mut PoolConnection<Postgres>,
		id: Uuid,
	) -> Result<bool, String> {
		let result = sqlx::query("DELETE FROM scoring_criterias WHERE id = $1")
			.bind(id)
			.execute(conn.as_mut())
			.await
			.map_err(|e| e.to_string())?;
		Ok(result.rows_affected() > 0)
	}
}
