use crate::model::grading::paper_scan_block::PaperScanBlock;
use sqlx::{PgPool, Postgres, Transaction};
use uuid::Uuid;

/// 纸张扫描拆分后题块存放相关数据访问层
/// 作者: 萧达光
pub struct PaperScansBlockRepository {}
impl PaperScansBlockRepository {
    /// 保存题目分块及对应分数信息
    pub async fn batch_insert_blocks_with_unnest(tx: &mut Transaction<'_, Postgres>, mut blocks: Vec<PaperScanBlock>) -> anyhow::Result<usize> {
        // 构建批量插入SQL
        let mut builder = sqlx::QueryBuilder::new("INSERT INTO paper_scan_block (id, paper_scan_page_id, answer_block_group_id, answer_block_url,answer_content,serial_number ) VALUES ");

        let mut pre = " ";
        let size = blocks.len();
        let block = blocks.pop();
        for block in blocks {
            builder
                .push(pre).push(" ( ")
                .push_bind(block.id)
                .push(" , ")
                .push_bind(block.paper_scan_page_id)
                .push(" , ")
                .push_bind(block.answer_block_group_id)
                .push(" , ")
                .push_bind(block.answer_block_url)
                .push(" , ")
                .push_bind(block.answer_content)
                .push(" , ")
                .push_bind(block.serial_number)
                .push(" ) ");
            pre = ","
        }
        if let Some(block) = block {
            builder
                .push(pre).push(" ( ")
                .push_bind(block.id)
                .push(" , ")
                .push_bind(block.paper_scan_page_id)
                .push(" , ")
                .push_bind(block.answer_block_group_id)
                .push(" , ")
                .push_bind(block.answer_block_url)
                .push(" , ")
                .push_bind(block.answer_content)
                .push(" , ")
                .push_bind(block.serial_number)
                .push(" ) ");
        }

        builder.build().execute(tx.as_mut()).await?;

        Ok(size)
    }

    /// 根据页码ID批量删除相关记录
    pub async fn batch_delete_paper_scan_block(tx: &mut Transaction<'_, Postgres>, page_ids: &Vec<Uuid>) -> anyhow::Result<u64> {
        if page_ids.len() == 0 {
            // return Err(anyhow!("page_ids 不能为空"));
            return Ok(0);
        }

        let mut builder = sqlx::QueryBuilder::new("DELETE FROM paper_scan_block WHERE ");

        builder.push("paper_scan_page_id = ANY( ").push_bind(page_ids).push(")");

        let count = builder.build().execute(tx.as_mut()).await?.rows_affected();

        Ok(count)
    }

    pub async fn get_blocks_by_page_ids(db: &PgPool, schema_name: &str, page_ids: Vec<Uuid>) -> anyhow::Result<Vec<PaperScanBlock>> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            r#"SELECT * FROM {}.paper_scan_block
        WHERE paper_scan_page_id = ANY("#,
            schema_name
        ));
        builder.push_bind(page_ids).push(")");
        let data = builder.build_query_as().fetch_all(db).await?;
        Ok(data)
    }
    pub async fn update(db: &PgPool, schema_name: &str, id: Uuid, content: String) -> anyhow::Result<u64> {
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.paper_scan_block SET ", schema_name));
        builder.push(" answer_content = ").push_bind(content);
        builder.push(" WHERE id = ").push_bind(id);
        let result = builder.build().execute(db).await?;
        Ok(result.rows_affected())
    }
}
