use anyhow::{Context, Result};
use sqlx::{PgPool, Row};
use uuid::Uuid;

use crate::middleware::auth_middleware::UserTenantLinks;
use crate::model::user::auth::{IdentityInfo, Roles};
use crate::model::RoleCategory;
use crate::utils::schema::connect_with_schema;

/// 认证相关数据访问层
pub struct AuthRepository;

impl AuthRepository {
    /// 获取用户租户链接信息
    pub async fn get_user_tenant_links(pool: &PgPool, user_id: Uuid) -> Result<Vec<UserTenantLinks>> {
        let utls = sqlx::query_as::<_, UserTenantLinks>(
            r#"
                SELECT
                    utl.tenant_id,
                    utl.access_type,
                    t.name,
                    t.schema_name
                FROM public.user_tenant_links utl
                LEFT JOIN public.tenants t
                    ON utl.tenant_id = t.id
                WHERE utl.user_id = $1
                "#,
        )
        .bind(user_id)
        .fetch_all(pool)
        .await
        .context("Failed to get user tenant links")?;

        Ok(utls)
    }

    /// 获取用户在指定租户中的角色
    pub async fn get_user_roles_in_tenant(
        pool: &PgPool,
        user_id: Uuid,
        schema_name: &str,
    ) -> Result<Vec<Roles>> {
        let mut conn = connect_with_schema(pool, schema_name).await?;
        let roles = sqlx::query_as::<_, Roles>(
            r#"
                SELECT
                    r.name,
                    r.code,
                    r.description,
                    r.category,
                    r.level
                FROM user_identities ui
                LEFT JOIN public.roles r
                    ON ui.role_id = r.id
                WHERE ui.user_id = $1
                "#,
        )
        .bind(user_id)
        .fetch_all(&mut *conn)
        .await
        .context("Failed to get user roles in tenant")?;

        Ok(roles)
    }

    /// 检查用户是否为家长
    pub async fn check_is_parent(
        pool: &PgPool,
        user_id: Uuid,
        tenant_id: Uuid,
        schema_name: &str,
    ) -> Result<bool> {
        let mut conn = connect_with_schema(pool, schema_name).await?;
        let is_parent = sqlx::query_scalar::<_, bool>(
            r#"
                SELECT EXISTS (
                    SELECT 1
                    FROM public.parent_student_relations
                    WHERE parent_user_id = $1
                    AND student_tenant_id = $2
                )
                "#,
        )
        .bind(user_id)
        .bind(tenant_id)
        .fetch_one(&mut *conn)
        .await
        .context("Failed to check if user is parent")?;

        Ok(is_parent)
    }

    /// 获取用户的所有身份信息
    pub async fn get_user_identities(pool: &PgPool, user_id: Uuid) -> Result<Vec<IdentityInfo>, sqlx::Error> {
        // 获取所有活跃租户的schema
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT id as tenant_id, schema_name, name as tenant_name
            FROM public.tenants
            WHERE status = 'active'
            ORDER BY created_at
            "#
        )
        .fetch_all(pool)
        .await?;
        let mut identities = Vec::new();

        // 遍历所有租户schema，查询用户在每个租户中的身份
        for tenant_record in tenant_schemas {
            let query = format!(
                r#"
                SELECT
                    ui.id as identity_id,
                    ui.role_id,
                    ui.target_type,
                    ui.target_id,
                    ui.subject,
                    r.code as role_code,
                    r.name as role_name
                FROM "{schema_name}".user_identities ui
                JOIN public.roles r ON ui.role_id = r.id
                WHERE ui.user_id = $1
                ORDER BY ui.created_at DESC
                "#,
                schema_name = tenant_record.schema_name
            );

            let identity_records = sqlx::query(&query)
                .bind(user_id)
                .fetch_all(pool)
                .await
                .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist or query fails

            for record in identity_records {
                let identity_id: Uuid = record.get("identity_id");
                let role_code: String = record.get("role_code");
                let role_name: String = record.get("role_name");

                identities.push(IdentityInfo {
                    identity_id,
                    tenant_id: tenant_record.tenant_id,
                    tenant_name: Some(tenant_record.tenant_name.clone()),
                    identity_type: role_code,
                    display_name: Some(role_name),
                    is_primary: false, // Since we don't have is_primary in the new schema
                });
            }
        }

        Ok(identities)
    }

    /// 检查角色是否属于特定类别
    pub fn has_role_category(roles: &[Roles], category: RoleCategory) -> bool {
        roles.iter().any(|r| r.category == category)
    }

    /// 获取活跃租户列表
    pub async fn get_active_tenants(pool: &PgPool) -> Result<Vec<(Uuid, String, String)>> {
        let tenants = sqlx::query!(
            r#"
            SELECT id, schema_name, name
            FROM public.tenants
            WHERE status = 'active'
            ORDER BY created_at
            "#
        )
        .fetch_all(pool)
        .await
        .context("Failed to get active tenants")?;

        Ok(tenants
            .into_iter()
            .map(|t| (t.id, t.schema_name, t.name))
            .collect())
    }

    /// 检查用户在租户中是否有特定访问类型
    pub async fn check_user_tenant_access(
        pool: &PgPool,
        user_id: Uuid,
        tenant_id: Uuid,
        access_type: &str,
    ) -> Result<bool> {
        let exists = sqlx::query_scalar::<_, bool>(
            r#"
            SELECT EXISTS (
                SELECT 1
                FROM public.user_tenant_links
                WHERE user_id = $1 AND tenant_id = $2 AND access_type = $3
            )
            "#,
        )
        .bind(user_id)
        .bind(tenant_id)
        .bind(access_type)
        .fetch_one(pool)
        .await
        .context("Failed to check user tenant access")?;

        Ok(exists)
    }

    /// 为用户添加租户访问权限
    pub async fn add_user_tenant_access(
        pool: &PgPool,
        user_id: Uuid,
        tenant_id: Uuid,
        access_type: &str,
    ) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO public.user_tenant_links (user_id, tenant_id, access_type)
            VALUES ($1, $2, $3)
            ON CONFLICT (user_id, tenant_id) DO UPDATE SET access_type = $3
            "#,
            user_id,
            tenant_id,
            access_type
        )
        .execute(pool)
        .await
        .context("Failed to add user tenant access")?;

        Ok(())
    }

    /// 移除用户的租户访问权限
    pub async fn remove_user_tenant_access(
        pool: &PgPool,
        user_id: Uuid,
        tenant_id: Uuid,
    ) -> Result<()> {
        sqlx::query!(
            "DELETE FROM public.user_tenant_links WHERE user_id = $1 AND tenant_id = $2",
            user_id,
            tenant_id
        )
        .execute(pool)
        .await
        .context("Failed to remove user tenant access")?;

        Ok(())
    }
}
