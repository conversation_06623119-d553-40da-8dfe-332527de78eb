// 测试数学文本清理功能
function testMathCleanup() {
  const parseDelimitersWithStack = (text) => {
    let result = '';
    let i = 0;
    
    while (i < text.length) {
      if (text[i] === '$' && text[i + 1] !== '$') {
        // 找到单个 $ 的开始，寻找对应的结束 $
        let j = i + 1;
        let mathContent = '';
        let foundEnd = false;

        // 寻找匹配的结束 $（必须是单个 $，不是 $$）
        while (j < text.length) {
          if (text[j] === '$' && text[j + 1] !== '$' && text[j - 1] !== '$') {
            foundEnd = true;
            break;
          }
          mathContent += text[j];
          j++;
        }

        if (foundEnd) {
          // 找到了完整的 $...$ 对，处理其中的内容
          // 将数学内容中的 $$ 替换成空
          const cleanedMathContent = mathContent.replace(/\$\$/g, '');
          result += '$' + cleanedMathContent + '$';
          i = j + 1; // 跳过结束的 $
        } else {
          // 没有找到匹配的 $，直接添加当前字符
          result += text[i];
          i++;
        }
      } else {
        // 不是单个 $，直接添加
        result += text[i];
        i++;
      }
    }
    
    return result;
  };

  // 测试用例
  const testCases = [
    {
      input: '$a$$b$',
      expected: '$ab$',
      description: '简单的 $$ 替换'
    },
    {
      input: '$x + $$y$$ + z$',
      expected: '$x + y + z$',
      description: '数学表达式中的 $$ 替换'
    },
    {
      input: '$3$$($$a$$+b)$$-3 $',
      expected: '$3(a+b)-3 $',
      description: '复杂嵌套情况'
    },
    {
      input: 'Normal text $math$$content$ more text',
      expected: 'Normal text $mathcontent$ more text',
      description: '混合文本'
    },
    {
      input: '$$ outside math $$ and $inside$$math$',
      expected: '$$ outside math $$ and $insidemath$',
      description: '内外 $$ 区别处理'
    }
  ];

  console.log('=== 数学文本清理测试 ===\n');
  
  testCases.forEach((testCase, index) => {
    const result = parseDelimitersWithStack(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`输入: ${testCase.input}`);
    console.log(`输出: ${result}`);
    console.log(`期望: ${testCase.expected}`);
    console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
    console.log('---');
  });
}

// 运行测试
testMathCleanup();
