use proc_macro::TokenStream;
use quote::quote;
use syn::{parse_macro_input, Data, DeriveInput};

#[proc_macro_derive(QcSqlxEnum)]
pub fn derive_qc_enum_sqlx_type(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;
    let expanded = match input.data {
        Data::Enum(data_enum) => {
            let variants = data_enum.variants.iter().map(|v| &v.ident).collect::<Vec<_>>();
            let decode_arms = data_enum.variants.iter().map(|variant| {
                let variant_str = variant.ident.to_string();
                quote! {
                    #variant_str => Ok(#name::#variant),
                }
            }).collect::<Vec<_>>();
            let encode_arms = variants.iter().map(|variant| {
                let variant_str = variant.to_string();
                quote! {
                    #name::#variant => buf.extend_from_slice(#variant_str.as_bytes()),
                }
            }).collect::<Vec<_>>();
            quote! {
                impl sqlx::Type<sqlx::Postgres> for #name {
                    fn type_info() -> sqlx::postgres::PgTypeInfo {
                       sqlx::postgres::PgTypeInfo::with_name("VARCHAR")
                    }
                }
                impl<'r> sqlx::Decode<'r, sqlx::Postgres> for #name {
                    fn decode(value: sqlx::postgres::PgValueRef<'r>) -> Result<Self, sqlx::error::BoxDynError> {
                        let s = value.as_str()?;
                        match s {
                            #(#decode_arms)*
                            _ => Err("invalid score status".into()),
                        }
                    }
                }
                impl sqlx::Encode<'_, sqlx::Postgres> for #name {
                    fn encode_by_ref(&self, buf: &mut <sqlx::Postgres as sqlx::Database>::ArgumentBuffer<'_>) -> Result<sqlx::encode::IsNull, sqlx::error::BoxDynError> {
                        match self {
                            #(#encode_arms)*
                        }
                        Ok(sqlx::encode::IsNull::No)
                    }
                }
            }
        },
        _ => {
            quote! {
                compile_error!("MySqlxType can only be derived for enums");
            }
        },
    };
    TokenStream::from(expanded)
}