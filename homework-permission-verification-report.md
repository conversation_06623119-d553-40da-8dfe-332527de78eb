# 作业权限过滤验证报告

## 🚨 验证结果：作业系统缺乏权限过滤

### 当前实现状态

#### ❌ 权限过滤缺失
1. **作业列表查询**：`page_homework` 方法中有明确的 TODO 注释
   ```rust
   //TODO:根据用户查询可以看到的列表，现在都是管理员先
   let (list, total) = HomeworkRepository::page_all_homework(&mut conn, page_params, None, name, status, subject_group_id)
   ```

2. **Repository 层**：`homework_id_in_list` 参数存在但未使用
   ```rust
   // homework_id_in_list:不传代表是管理员，可以看所有的，如果传了就只能查到这个范围内的作业
   pub async fn page_all_homework(
       conn: &mut PoolConnection<Postgres>,
       page_params: &PageParams,
       homework_id_in_list: Option<Vec<Uuid>>, // 存在但未在服务层调用时传入
   ```

3. **权限中间件缺失**：作业相关的 API 端点没有应用权限过滤中间件

#### ✅ 现有基础设施
1. **数据模型完整**：
   - `homework` 表有 `subject_group_id` 字段，可关联学科组
   - `homework_students` 表有 `class_id` 字段，可关联班级
   - 支持多对多的学生-作业关系

2. **数据关联清晰**：
   ```
   homework → subject_group_id → subject_groups
   homework_students → class_id → teaching_classes/administrative_classes
   homework_students → student_id → students
   ```

### 权限风险评估

#### 🔴 高风险问题
1. **数据泄露**：任何登录用户都能查看所有作业
2. **越权访问**：教师可以查看非自己负责的学科组作业
3. **学生隐私**：学生作业信息可能被未授权人员访问

#### 🟡 中风险问题
1. **业务逻辑混乱**：缺乏明确的权限边界
2. **审计困难**：无法追踪谁访问了哪些作业数据

## 🎯 需要实现的权限控制

### 教育场景权限需求

#### 1. 任课老师权限
- **可访问**：自己教授的教学班相关作业
- **操作权限**：创建、查看、修改自己负责的作业
- **数据范围**：仅限自己的教学班学生作业

#### 2. 学科组长权限  
- **可访问**：本学科组所有教学班的作业
- **操作权限**：查看、统计本学科组所有作业
- **数据范围**：学科组下所有教学班和学生

#### 3. 班主任权限
- **可访问**：本班学生的所有学科作业
- **操作权限**：查看本班学生作业完成情况
- **数据范围**：行政班级内所有学生的作业

#### 4. 年级长权限
- **可访问**：本年级所有班级的作业
- **操作权限**：查看、统计年级作业情况
- **数据范围**：年级内所有班级和学生

#### 5. 学生权限
- **可访问**：仅自己的作业
- **操作权限**：查看自己的作业和成绩
- **数据范围**：个人作业记录

#### 6. 家长权限
- **可访问**：子女的作业信息
- **操作权限**：查看子女作业完成情况
- **数据范围**：特定学生的作业记录

### 数据过滤逻辑

#### 基于学科组的过滤
```sql
-- 任课老师/学科组长：通过学科组过滤
SELECT h.* FROM homework h 
WHERE h.subject_group_id IN (
    SELECT sgm.subject_group_id 
    FROM subject_group_members sgm 
    WHERE sgm.teacher_id = ? AND sgm.is_active = true
)
```

#### 基于教学班的过滤
```sql
-- 任课老师：通过教学班过滤
SELECT h.* FROM homework h
JOIN homework_students hs ON h.id = hs.homework_id
WHERE hs.class_id IN (
    SELECT tc.id FROM teaching_classes tc 
    WHERE tc.teacher_id = ? AND tc.is_active = true
)
```

#### 基于行政班的过滤
```sql
-- 班主任：通过行政班过滤
SELECT h.* FROM homework h
JOIN homework_students hs ON h.id = hs.homework_id
JOIN students s ON hs.student_id = s.id
WHERE s.administrative_class_id IN (
    SELECT ac.id FROM administrative_classes ac 
    WHERE ac.teacher_id = ? AND ac.is_active = true
)
```

#### 基于年级的过滤
```sql
-- 年级长：通过年级过滤
SELECT h.* FROM homework h
JOIN homework_students hs ON h.id = hs.homework_id
JOIN students s ON hs.student_id = s.id
JOIN administrative_classes ac ON s.administrative_class_id = ac.id
WHERE ac.grade_level_code = ?
```

#### 基于学生的过滤
```sql
-- 学生：只能看自己的作业
SELECT h.* FROM homework h
JOIN homework_students hs ON h.id = hs.homework_id
WHERE hs.student_id = ?
```

## 🚀 实现方案

### 第一步：创建作业数据过滤器

需要创建 `HomeworkDataFilter` 实现 `DataFilter` trait，支持：
1. 基于用户角色的权限判断
2. 多种数据范围的过滤逻辑
3. 高性能的查询条件构建

### 第二步：修改作业服务

1. **集成权限过滤**：在 `page_homework` 方法中应用数据过滤器
2. **传递权限上下文**：将用户身份和租户信息传递给过滤器
3. **优化查询性能**：使用索引优化的查询条件

### 第三步：更新作业控制器

1. **应用权限中间件**：在作业相关路由上应用权限检查
2. **权限验证**：验证用户是否有访问作业的基础权限
3. **审计日志**：记录作业数据的访问日志

### 第四步：测试验证

1. **单元测试**：测试各种角色的权限过滤逻辑
2. **集成测试**：验证端到端的权限控制
3. **性能测试**：确保权限过滤不影响查询性能

## 📋 实施优先级

### 🔴 紧急（立即实施）
1. 创建作业数据过滤器
2. 修改作业列表查询接口
3. 应用基础权限中间件

### 🟡 重要（近期实施）
4. 完善各种教育角色的权限逻辑
5. 添加作业操作权限控制
6. 实现权限审计日志

### 🟢 一般（后期优化）
7. 性能优化和缓存
8. 权限配置界面
9. 高级权限分析功能

## 🎯 预期效果

实施后将实现：
- ✅ **数据安全**：确保用户只能访问授权的作业数据
- ✅ **角色隔离**：不同教育角色看到不同范围的作业
- ✅ **审计合规**：完整的作业访问审计日志
- ✅ **性能保证**：权限过滤不影响系统性能
- ✅ **用户体验**：用户界面只显示相关的作业信息

## ⚠️ 注意事项

1. **向后兼容**：确保现有功能不受影响
2. **数据迁移**：可能需要补充历史数据的权限关联
3. **性能监控**：密切监控权限过滤对查询性能的影响
4. **用户培训**：需要向用户说明新的权限控制逻辑
