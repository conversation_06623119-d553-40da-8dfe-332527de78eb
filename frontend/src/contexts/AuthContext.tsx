import {createContext, useState, useContext, ReactNode, useEffect} from 'react';
import {LoginForm} from "@/pages/LoginPage.tsx";
import apiClient from "@/services/apiClient.ts";
import {isAxiosError} from "axios";
import {CurrentUserData, IdentityInfo, Role, TenantInfo, UserTenantInfo} from "@/types/identity.ts";
import {getCurrentUser} from "@/services/identityApi.ts";
import {TenantCache} from "@/utils/tenantCache.ts";

// 更新 AuthContextType 接口以包含 request 方法
interface AuthContextType {
    isAuthenticated: boolean;
    token: string | null;

    /** @deprecated 身份相关采用currentUserData userRole tenant*/
    identity: IdentityInfo | null;
    /** @deprecated 身份相关采用currentUserData userRole tenant*/
    selectIdentity: (identity: IdentityInfo) => void;
    /** @deprecated 身份相关采用currentUserData userRole tenant*/
    clearIdentity: () => void;

    login: (data: LoginForm) => Promise<any>;
    logout: () => void;

    currentUserData: CurrentUserData | null;
    switchableInfo: Role[];
    tenant: TenantInfo | null;
    selectTenant: (tenant: UserTenantInfo) => void;
    userRole: Role | null;
    selectUserRole: (role: Role) => void;
}


const AuthContext = createContext<AuthContextType | undefined>(undefined);
export {AuthContext};

export const AuthProvider = ({children}: { children: ReactNode }) => {
    const [token, setToken] = useState<string | null>(() => localStorage.getItem('token'));

    /** @deprecated 身份相关采用currentUserData userRole*/
    const [identity, setIdentity] = useState<IdentityInfo | null>(() => {
        const stored = localStorage.getItem('identity');
        if (!stored) return null;
        try {
            return JSON.parse(stored);
        } catch {
            return null;
        }
    });

    const [currentUserData, setCurrentUserData] = useState<CurrentUserData | null>(() => {
        const stored = localStorage.getItem('currentUserData');
        if (!stored) return null;
        try {
            return JSON.parse(stored);
        } catch {
            return null;
        }
    });
    const [isAuthenticated, setIsAuthenticated] = useState(!!token);
    const [tenant, setTenant] = useState<TenantInfo | null>(() => {
        const stored = localStorage.getItem('tenant');
        if (!stored) return null;
        try {
            return JSON.parse(stored);
        } catch {
            return null;
        }
    });

    const [switchableInfo, setSwitchableInfo] = useState<Role[]>([])

    const [userRole, setUserRole] = useState<Role | null>(null);

    const loadUserInfo = async () => {
        let userData = await getCurrentUser()
        setCurrentUserData(userData)

        if (userData.tenants?.length) {
            // 使用工具类选择租户
            const selectedTenant = TenantCache.selectTenantFromList(userData.tenants);
            if (selectedTenant) {
                selectTenant(selectedTenant);

                // 确保 user_switchable_infos 存在且是数组
                const switchableInfos = selectedTenant.user_switchable_infos || [];
                setSwitchableInfo(switchableInfos)

                if (switchableInfos.length > 0) {
                    if (selectedTenant.user_hit_info) {
                        setUserRole(selectedTenant.user_hit_info)
                    } else selectUserRole(switchableInfos[0])
                }
            }
        }
    }

    useEffect(() => {
        if (token)
            loadUserInfo()
    }, [token]);

    const login = async (data: LoginForm) => {
        try {
            console.log("🔐 开始登录...");
            const res = await apiClient.post('/api/v1/auth/login', data);
            console.log("✅ 登录响应数据:", res.data);
            const {access_token, refresh_token, available_identities, current_identity} = res.data;

            // 保存 token 到 state 和 localStorage
            setToken(access_token);
            localStorage.setItem('token', access_token);
            if (refresh_token) {
                localStorage.setItem('refresh_token', refresh_token);
            }
            console.log("✅ 登录成功，token已保存");

            // 调用 /auth/me 接口获取用户信息
            console.log("📋 获取用户信息...");
            const userData = await getCurrentUser();
            console.log("✅ 用户信息获取成功:", userData);

            // 保存用户数据
            setCurrentUserData(userData);
            localStorage.setItem('currentUserData', JSON.stringify(userData));

            // 优先使用缓存中的租户，如果缓存中的租户在当前用户的租户列表中存在
            if (userData.tenants && userData.tenants.length > 0) {
                const selectedTenant = TenantCache.selectTenantFromList(userData.tenants);
                if (selectedTenant) {
                    selectTenant(selectedTenant);
                }
            }

            setIsAuthenticated(true);
            console.log("✅ 登录流程完成，isAuthenticated:", true);

            // 返回登录响应数据，包括身份信息
            return {
                available_identities,
                current_identity
            };
        } catch (err: unknown) {
            console.error("❌ 登录失败:", err);
            if (isAxiosError(err)) {
                const msg = err.response?.data?.message || '请求失败';
                throw new Error(msg);
            } else if (err instanceof Error) {
                throw new Error(err.message);
            } else {
                throw new Error('未知错误');
            }
        }
    };

    /** @deprecated 身份相关采用currentUserData userRole*/
    const selectIdentity = (identity: IdentityInfo) => {
        console.log("🎯 选择身份:", identity);
    // TODO 发送请求记录，并且将当前设置为is_primarylocalStorage.setItem('identity', JSON.stringify(identity));
        setIdentity(identity);
        console.log("✅ 身份设置完成，identity:", identity);

        // 根据身份信息自动设置对应的租户
        if (identity.tenant_id && currentUserData?.tenants) {
            const matchingTenant = currentUserData.tenants.find(
                tenant => tenant.tenant_id === identity.tenant_id
            );

            if (matchingTenant) {
                console.log("🎯 根据身份自动选择租户:", matchingTenant);
                selectTenant(matchingTenant);
            } else {
                console.warn("⚠️ 未找到与身份匹配的租户信息");
            }
        }
    }

    /** @deprecated 身份相关采用currentUserData userRole*/
    const clearIdentity = () => {
        localStorage.removeItem('identity');
        setIdentity(null);
    }

    const logout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('identity');
        localStorage.removeItem('currentUserData');
        // 使用工具类清除租户缓存
        TenantCache.clearCachedTenant();
        setToken(null);
        setIdentity(null);
        setCurrentUserData(null);
        setTenant(null);
        setIsAuthenticated(false);
        setCurrentUserData(null)
        setUserRole(null)
        setSwitchableInfo([])
    };

    // todo 方法可能需要和后端做交互？
    const selectTenant = (tenant: TenantInfo | UserTenantInfo) => {
        let realTenant: TenantInfo = {
            tenant_id: tenant.tenant_id,
            tenant_name: tenant.tenant_name,
            schema_name: tenant.schema_name,
        }
        // 使用工具类缓存租户
        TenantCache.setCachedTenant(realTenant);
        setTenant(realTenant);
    }

    // todo 方法可能需要和后端做交互？
    const selectUserRole = (role: Role) => {
        setUserRole(role);
    }

    const contextValue: AuthContextType = {
        isAuthenticated,
        token,
        identity,
        currentUserData,
        login,
        selectIdentity,
        selectTenant,
        clearIdentity,
        logout,
        tenant,
        switchableInfo,
        userRole,
        selectUserRole
    };

    return (
        <AuthContext.Provider value={contextValue}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
