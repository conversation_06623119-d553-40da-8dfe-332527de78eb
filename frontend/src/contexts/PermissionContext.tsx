import React, { createContext, useContext, ReactNode } from 'react';

// 权限类型定义
export interface Permission {
  resource: string;
  action: string;
  scope?: string;
}

// 数据范围权限
export interface DataScope {
  resource: string;
  scope_type: string;
  scope_value: string;
  actions: string[];
}

// 用户角色信息
export interface UserRole {
  role_id: string;
  role_code: string;
  role_name: string;
  level: number;
  category: string;
}

// 菜单结构（重新导出以保持兼容性）
export interface MenuPermission {
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  sort_order?: number;
  children?: MenuPermission[];
}

// 简化的权限上下文类型（主要用于兼容性）
interface PermissionContextType {
  // 基础权限检查（始终返回 true，因为后端已验证）
  hasPermission: (resource: string, action: string, scope?: string) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;

  // 角色检查（始终返回 true，因为后端已验证）
  hasRole: (roleCode: string) => boolean;
  hasAnyRole: (roleCodes: string[]) => boolean;
  isSystemAdmin: () => boolean;

  // 数据权限检查（始终返回 true，因为后端已验证）
  canAccessData: (resource: string, scopeType: string, scopeValue: string) => boolean;

  // 菜单相关（委托给 MenuContext）
  canAccessMenu: (menuId: string) => boolean;
  getAccessibleMenus: () => MenuPermission[];

  // 状态（为兼容性保留）
  isLoading: boolean;
  error: string | null;
}

import { useMenus } from './MenuContext';

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export const PermissionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { getAccessibleMenus, findMenu, isLoading, error } = useMenus();

  // 简化的权限检查函数 - 始终返回 true，因为后端已经验证过权限
  const hasPermission = (resource: string, action: string, scope?: string): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  const hasRole = (roleCode: string): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  const hasAnyRole = (roleCodes: string[]): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  const isSystemAdmin = (): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  const canAccessData = (resource: string, scopeType: string, scopeValue: string): boolean => {
    return true; // 后端已验证，前端不需要再检查
  };

  // 菜单相关函数 - 委托给 MenuContext
  const canAccessMenu = (menuId: string): boolean => {
    return findMenu(menuId) !== null;
  };

  const contextValue: PermissionContextType = {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    isSystemAdmin,
    canAccessData,
    canAccessMenu,
    getAccessibleMenus,
    isLoading,
    error,
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
};

// Hook for using permission context
export const usePermissions = (): PermissionContextType => {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
};

// 权限检查 Hook
export const usePermissionCheck = (resource: string, action: string, scope?: string): boolean => {
  const { hasPermission } = usePermissions();
  return hasPermission(resource, action, scope);
};

// 菜单访问检查 Hook
export const useMenuAccess = (menuId: string): boolean => {
  const { canAccessMenu } = usePermissions();
  return canAccessMenu(menuId);
};

// 角色检查 Hook
export const useRoleCheck = (roleCode: string): boolean => {
  const { hasRole } = usePermissions();
  return hasRole(roleCode);
};