import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { permissionApi } from '@/services/permissionApi';

// 菜单结构
export interface MenuPermission {
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  sort_order?: number;
  children?: MenuPermission[];
}

// 菜单上下文类型
interface MenuContextType {
  // 基础状态
  menus: MenuPermission[];
  isLoading: boolean;
  error: string | null;

  // 菜单操作
  getAccessibleMenus: () => MenuPermission[];
  findMenu: (menuId: string) => MenuPermission | null;

  // 刷新菜单数据
  refreshMenus: () => Promise<void>;
}

const MenuContext = createContext<MenuContextType | undefined>(undefined);

export const MenuProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated, tenant } = useAuth();
  const [menus, setMenus] = useState<MenuPermission[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载菜单数据
  const loadMenus = async () => {
    if (!isAuthenticated ) {
      console.log("🔒 用户未认证或缺少租户信息，跳过菜单加载");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("📋 开始加载菜单数据...");
      
      // 获取用户菜单
      const menusResponse = await permissionApi.getUserMenus(tenant?.tenant_id??null);
      console.log("📋 菜单数据:", menusResponse);
      
      setMenus(menusResponse.menus || []);
      console.log("🎉 菜单数据加载完成");
    } catch (err) {
      console.error('❌ 菜单数据加载失败:', err);
      setError(err instanceof Error ? err.message : 'Failed to load menus');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取可访问的菜单列表（已经过后端过滤）
  const getAccessibleMenus = (): MenuPermission[] => {
    return menus;
  };

  // 查找指定菜单
  const findMenu = (menuId: string): MenuPermission | null => {
    const searchMenu = (menuList: MenuPermission[], id: string): MenuPermission | null => {
      for (const menu of menuList) {
        if (menu.menu_id === id) {
          return menu;
        }
        if (menu.children) {
          const found = searchMenu(menu.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    return searchMenu(menus, menuId);
  };

  // 刷新菜单数据
  const refreshMenus = async () => {
    await loadMenus();
  };

  // 当认证状态或身份信息变化时重新加载菜单
  useEffect(() => {
    loadMenus();
  }, [isAuthenticated, tenant?.tenant_id]);

  const contextValue: MenuContextType = {
    menus,
    isLoading,
    error,
    getAccessibleMenus,
    findMenu,
    refreshMenus,
  };

  return (
    <MenuContext.Provider value={contextValue}>
      {children}
    </MenuContext.Provider>
  );
};

// Hook for using menu context
export const useMenus = (): MenuContextType => {
  const context = useContext(MenuContext);
  if (context === undefined) {
    throw new Error('useMenus must be used within a MenuProvider');
  }
  return context;
};

// 菜单访问检查 Hook
export const useMenuAccess = (menuId: string): boolean => {
  const { findMenu } = useMenus();
  return findMenu(menuId) !== null;
};

// 获取菜单信息 Hook
export const useMenuInfo = (menuId: string): MenuPermission | null => {
  const { findMenu } = useMenus();
  return findMenu(menuId);
};
