import React, {createContext, useContext, useState} from 'react';
import { Node } from "@/pages/TeachingAids/Preview/tiptap-types.ts";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipProvider, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Plus, Minus, ShoppingBasket } from "lucide-react";
import {renderNode} from "@/pages/TeachingAids/Preview/TiptapContentRenderer.tsx";
import {toast} from "sonner";

/** context 类型 */
interface QuestionBasketContextType {
    basket: Node[];
    AddRemoveButton: React.FC<{ question: Node }>;
}

/** context 实例 */
const QuestionBasketContext = createContext<QuestionBasketContextType | undefined>(undefined);
export { QuestionBasketContext }

/** provider */
export const QuestionBasketContextProvider: React.FC<{ children: React.ReactNode }> = ({children}) => {
    const [basket, setBasket] = useState<Node[]>([])
    const [previewOpen, setPreviewOpen] = useState(false)

    const addToBasket = (question: Node) => {
        setBasket(prev => [...prev, question])
        console.log("添加到试题篮:", question)
    }

    const removeFromBasket = (question: Node) => {
        setBasket(prev => prev.filter(item => item.attrs?.id !== question.attrs?.id))
        console.log("从试题篮移除:", question)
    }

    /** 判断是否在篮子里 */
    const isInBasket = (question: Node) => {
        return basket.some(item => item.attrs?.id === question.attrs?.id )
    }

    /** UI：添加/移除按钮 */
    interface AddRemoveButtonProps {
        question: Node;
        className?: string; // 可传入额外 className
    }

    const AddRemoveButton: React.FC<AddRemoveButtonProps> = ({ question, className }) => {
        // 先判断 question_content
        if (!question.question_content) return null;

        const inBasket = basket.some(item => item.attrs?.id === question.attrs?.id);

        return (
            <TooltipProvider delayDuration={50}>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className={`absolute top-0 right-0 -mt-1.5 -mr-2 ${className || ""}`} // 支持外部传入 className
                            onClick={() => (inBasket ? removeFromBasket(question) : addToBasket(question))}
                        >
                            {inBasket ? <Minus className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>{inBasket ? "移出试题篮" : "添加到试题篮"}</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        );
    };

    /** UI：悬浮按钮 */
    const BasketFloatingButton: React.FC = () => {
        return (
            <div className="fixed bottom-6 right-6">
                <Button
                    size="icon"
                    className="relative rounded-full w-12 h-12"
                    onClick={() => {
                        if (basket.length === 0) {
                            toast.info("暂无选中试题，请先添加试题");
                            return;
                        }
                        setPreviewOpen(true);
                    }}
                >
                    <ShoppingBasket className="h-5 w-5" />
                    {basket.length > 0 && (
                        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {basket.length}
          </span>
                    )}
                </Button>
            </div>
        );
    };



    /** UI：预览对话框 */
    const BasketPreviewDialog: React.FC = () => {
        // todo 增加答题卡预览，切换按钮，保存试卷按钮
        //  答题卡预览参照demo.tsx
        const [previewMode, setPreviewMode] = useState<"question" | "answerSheet">("question");

        return (
            <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
                <DialogContent className="max-w-[90vw] max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>试题篮预览</DialogTitle>
                    </DialogHeader>

                    {/* 切换按钮 */}
                    <div className="flex justify-end mb-2 space-x-2">
                        <Button
                            variant={previewMode === "question" ? "default" : "outline"}
                            size="sm"
                            onClick={() => setPreviewMode("question")}
                        >
                            试题预览
                        </Button>
                        <Button
                            variant={previewMode === "answerSheet" ? "default" : "outline"}
                            size="sm"
                            onClick={() => setPreviewMode("answerSheet")}
                        >
                            答题卡预览
                        </Button>
                    </div>

                    {/* 内容 */}
                    {previewMode === "question" ? (
                        basket.map(renderNode)
                    ) : (
                        <div className="text-center py-10 text-gray-500">答题卡预览区域（暂未实现）</div>
                    )}
                </DialogContent>
            </Dialog>

        )
    }

    const contextValue: QuestionBasketContextType = {basket, AddRemoveButton}

    return (
        <QuestionBasketContext.Provider value={contextValue}>
            {children}
            {/* 悬浮按钮和预览对话框作为全局 UI */}
            <BasketFloatingButton/>
            <BasketPreviewDialog/>
        </QuestionBasketContext.Provider>
    )
}

/** hook */
export const useBasket = () => {
    const context = useContext(QuestionBasketContext)
    if (context === undefined) {
        throw new Error('useBasket must be used within a QuestionBasketContextProvider');
    }
    return context;
}
