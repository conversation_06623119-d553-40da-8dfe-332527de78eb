import React from 'react';
import { MenuProvider } from '@/contexts/MenuContext';
import { PermissionProvider, usePermissions } from '@/contexts/PermissionContext';
import { AuthProvider } from '@/contexts/AuthContext';

// 测试组件
const TestComponent: React.FC = () => {
  const {
    hasPermission,
    hasRole,
    canAccessMenu,
    getAccessibleMenus,
    isLoading,
    error
  } = usePermissions();

  return (
    <div>
      <h2>权限上下文测试</h2>
      <div>
        <p>加载状态: {isLoading ? '加载中...' : '已加载'}</p>
        <p>错误信息: {error || '无错误'}</p>
        <p>权限检查 (student:read): {hasPermission('student', 'read') ? '有权限' : '无权限'}</p>
        <p>角色检查 (teacher): {hasRole('teacher') ? '有角色' : '无角色'}</p>
        <p>菜单访问 (student_management): {canAccessMenu('student_management') ? '可访问' : '不可访问'}</p>
        <p>可访问菜单数量: {getAccessibleMenus().length}</p>
      </div>
    </div>
  );
};

// 完整的测试应用
export const PermissionContextTestApp: React.FC = () => {
  return (
    <AuthProvider>
      <MenuProvider>
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      </MenuProvider>
    </AuthProvider>
  );
};

export default PermissionContextTestApp;
