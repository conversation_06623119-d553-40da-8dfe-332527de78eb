import React from 'react';

// 测试所有重要的导入是否正常工作
import { MenuProvider, useMenus, MenuPermission } from '@/contexts/MenuContext';
import { PermissionProvider, usePermissions, Permission, UserRole, DataScope } from '@/contexts/PermissionContext';
import { useMenuAccess } from '@/contexts/PermissionContext';
import { PermissionCheck, MenuPermissionCheck } from '@/components/permissions/PermissionComponents';
import { DynamicNavigation } from '@/components/navigation/DynamicNavigation';

const ImportTestComponent: React.FC = () => {
  // 测试 hooks
  const { menus, isLoading: menuLoading } = useMenus();
  const { hasPermission, canAccessMenu, isLoading: permissionLoading } = usePermissions();
  const canAccessStudents = useMenuAccess('student_management');

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">导入测试</h2>
      
      <div className="space-y-2">
        <p>菜单加载状态: {menuLoading ? '加载中' : '已加载'}</p>
        <p>权限加载状态: {permissionLoading ? '加载中' : '已加载'}</p>
        <p>菜单数量: {menus.length}</p>
        <p>权限检查 (student:read): {hasPermission('student', 'read') ? '有权限' : '无权限'}</p>
        <p>菜单访问 (student_management): {canAccessMenu('student_management') ? '可访问' : '不可访问'}</p>
        <p>Hook 菜单访问: {canAccessStudents ? '可访问' : '不可访问'}</p>
      </div>

      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">权限组件测试</h3>
        
        <PermissionCheck resource="student" action="read">
          <div className="p-2 bg-green-100 text-green-800 rounded">
            ✅ 权限检查组件正常工作
          </div>
        </PermissionCheck>

        <MenuPermissionCheck menuId="student_management">
          <div className="p-2 bg-blue-100 text-blue-800 rounded mt-2">
            ✅ 菜单权限检查组件正常工作
          </div>
        </MenuPermissionCheck>
      </div>

      <div className="mt-4">
        <h3 className="text-lg font-semibold mb-2">导航组件测试</h3>
        <div className="border p-2 rounded">
          <DynamicNavigation />
        </div>
      </div>
    </div>
  );
};

// 完整的测试应用
export const ImportTestApp: React.FC = () => {
  return (
    <MenuProvider>
      <PermissionProvider>
        <ImportTestComponent />
      </PermissionProvider>
    </MenuProvider>
  );
};

export default ImportTestApp;
