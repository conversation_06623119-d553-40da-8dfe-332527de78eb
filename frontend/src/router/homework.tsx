import { PermissionProtectedRoute } from './ProtectedRoutes';
import HomeworkDetail from '@/pages/Homework/HomeworkDetail.tsx';
import HomeworkScan from '@/pages/Homework/HomeworkScan';
import { HomeworkManagementPage } from '@/pages/Homework';
import HomeworkReview from '@/pages/Homework/HomeworkReview';
import HomeworkReport from '@/pages/Homework/HomeworkReport';
import BatchDetail from '@/pages/Homework/BatchDetail';
import ScanImagePreview from '@/pages/Homework/ScanImagePreview';
import QuestionReviewDetail from '@/pages/Homework/QuestionReviewDetail';
import ClassReport from '@/pages/Homework/ClassReport';
import StudentList from '@/pages/Homework/StudentList';

// 作业管理路由
export const homeworkSettingRoute = [
  {
    path: 'homework-management',
    element: (
      <PermissionProtectedRoute>
        <HomeworkManagementPage />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-setting/:id',
    element: (
      <PermissionProtectedRoute>
        <HomeworkDetail />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-scan/:id',
    element: (
      <PermissionProtectedRoute>
        <HomeworkScan />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-grading/:id',
    element: (
      <PermissionProtectedRoute>
        <HomeworkReview />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-report/:id',
    element: (
      <PermissionProtectedRoute>
        <HomeworkReport />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'class-report/:classId',
    element: (
      <PermissionProtectedRoute>
        <ClassReport />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-scan/batch-detail/:batchNo',
    element: (
      <PermissionProtectedRoute>
        <BatchDetail />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-scan/scan-image-preview/:pageId',
    element: (
      <PermissionProtectedRoute>
        <ScanImagePreview />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-review/:scoreId',
    element: (
      <PermissionProtectedRoute>
        <QuestionReviewDetail />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'homework-student/:homeworkId',
    element: (
      <PermissionProtectedRoute>
        <StudentList />
      </PermissionProtectedRoute>
    ),
  },
  {
    path: 'class-report/:classId',
    element: (
      <PermissionProtectedRoute>
        <ClassReport />
      </PermissionProtectedRoute>
    ),
  },
];
