import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionContext';
import type { Permission } from '@/contexts/PermissionContext';
import { AlertCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

// 基础权限保护路由属性
interface BaseProtectedRouteProps {
  children?: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  showError?: boolean;
}

// 权限保护路由属性
interface PermissionProtectedRouteProps extends BaseProtectedRouteProps {
  permissions?: Permission | Permission[];
  requireAll?: boolean; // true: 需要所有权限, false: 需要任意一个权限
}

// 角色保护路由属性
interface RoleProtectedRouteProps extends BaseProtectedRouteProps {
  roles?: string | string[];
  requireAll?: boolean; // true: 需要所有角色, false: 需要任意一个角色
}

// 菜单保护路由属性
interface MenuProtectedRouteProps extends BaseProtectedRouteProps {
  menuId: string;
}

// 管理员保护路由属性
interface AdminProtectedRouteProps extends BaseProtectedRouteProps {
  requireSystemAdmin?: boolean;
}

// 组合权限保护路由属性
interface CombinedProtectedRouteProps extends BaseProtectedRouteProps {
  permissions?: Permission | Permission[];
  roles?: string | string[];
  menuId?: string;
  requireSystemAdmin?: boolean;
  requireAll?: boolean;
}

// 默认的加载组件
const DefaultLoadingComponent: React.FC = () => (
  <div className="flex flex-col space-y-4 p-6">
    <Skeleton className="h-8 w-64" />
    <div className="space-y-2">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
    </div>
  </div>
);

// 默认的权限拒绝组件
const DefaultPermissionDeniedComponent: React.FC<{ message?: string }> = ({ message }) => (
  <Card className="m-6">
    <CardContent className="pt-6">
      <div className="relative w-full rounded-lg border border-destructive/50 bg-destructive/5 p-4 text-destructive">
        <AlertCircle className="h-4 w-4 absolute left-4 top-4" />
        <div className="pl-7 text-sm">
          {message || '您没有访问此页面的权限。如果您认为这是一个错误，请联系管理员。'}
        </div>
      </div>
    </CardContent>
  </Card>
);

// 基础权限检查路由
export const BaseProtectedRoute: React.FC<BaseProtectedRouteProps> = ({
  children,
  redirectTo = "/login",
}) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  if (!isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return children ? <>{children}</> : <Outlet />;
};

// 权限保护路由
export const PermissionProtectedRoute: React.FC<PermissionProtectedRouteProps> = ({
  permissions,
  requireAll = false,
  children,
  fallback,
  redirectTo,
  showError = true
}) => {
  const { isAuthenticated } = useAuth();
  const { hasAnyPermission, hasAllPermissions, isLoading } = usePermissions();
  const location = useLocation();

  // 未认证检查
  if (!isAuthenticated) {
    return <Navigate to={redirectTo || "/login"} state={{ from: location }} replace />;
  }

  // 权限加载中
  if (isLoading) {
    return fallback || <DefaultLoadingComponent />;
  }

  // 权限检查
  if (permissions) {
    const permsArray = Array.isArray(permissions) ? permissions : [permissions];
    const hasAccess = requireAll 
      ? hasAllPermissions(permsArray)
      : hasAnyPermission(permsArray);

    if (!hasAccess) {
      if (redirectTo) {
        return <Navigate to={redirectTo} state={{ from: location }} replace />;
      }
      
      if (showError) {
        const permissionStrings = permsArray.map(p => `${p.resource}:${p.action}${p.scope ? `:${p.scope}` : ''}`);
        const message = `需要权限: ${permissionStrings.join(requireAll ? ' 和 ' : ' 或 ')}`;
        return fallback || <DefaultPermissionDeniedComponent message={message} />;
      }
      
      return null;
    }
  }

  return children ? <>{children}</> : <Outlet />;
};

// 角色保护路由
export const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  roles,
  requireAll = false,
  children,
  fallback,
  redirectTo,
  showError = true
}) => {
  const { isAuthenticated } = useAuth();
  const { hasRole, hasAnyRole, isLoading } = usePermissions();
  const location = useLocation();

  // 未认证检查
  if (!isAuthenticated) {
    return <Navigate to={redirectTo || "/login"} state={{ from: location }} replace />;
  }

  // 权限加载中
  if (isLoading) {
    return fallback || <DefaultLoadingComponent />;
  }

  // 角色检查
  if (roles) {
    const rolesArray = Array.isArray(roles) ? roles : [roles];
    const hasAccess = requireAll
      ? rolesArray.every(role => hasRole(role))
      : hasAnyRole(rolesArray);

    if (!hasAccess) {
      if (redirectTo) {
        return <Navigate to={redirectTo} state={{ from: location }} replace />;
      }
      
      if (showError) {
        const message = `需要角色: ${rolesArray.join(requireAll ? ' 和 ' : ' 或 ')}`;
        return fallback || <DefaultPermissionDeniedComponent message={message} />;
      }
      
      return null;
    }
  }

  return children ? <>{children}</> : <Outlet />;
};

// 菜单保护路由
export const MenuProtectedRoute: React.FC<MenuProtectedRouteProps> = ({
  menuId,
  children,
  fallback,
  redirectTo,
  showError = true
}) => {
  const { isAuthenticated } = useAuth();
  const { canAccessMenu, isLoading } = usePermissions();
  const location = useLocation();

  // 未认证检查
  if (!isAuthenticated) {
    return <Navigate to={redirectTo || "/login"} state={{ from: location }} replace />;
  }

  // 权限加载中
  if (isLoading) {
    return fallback || <DefaultLoadingComponent />;
  }

  // 菜单访问检查
  if (!canAccessMenu(menuId)) {
    if (redirectTo) {
      return <Navigate to={redirectTo} state={{ from: location }} replace />;
    }
    
    if (showError) {
      const message = `您没有访问菜单 "${menuId}" 的权限`;
      return fallback || <DefaultPermissionDeniedComponent message={message} />;
    }
    
    return null;
  }

  return children ? <>{children}</> : <Outlet />;
};

// 管理员保护路由
export const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({
  requireSystemAdmin = true,
  children,
  fallback,
  redirectTo,
  showError = true
}) => {
  const { isAuthenticated } = useAuth();
  const { isSystemAdmin, isLoading } = usePermissions();
  const location = useLocation();

  // 未认证检查
  if (!isAuthenticated) {
    return <Navigate to={redirectTo || "/login"} state={{ from: location }} replace />;
  }

  // 权限加载中
  if (isLoading) {
    return fallback || <DefaultLoadingComponent />;
  }

  // 管理员权限检查
  if (requireSystemAdmin && !isSystemAdmin()) {
    if (redirectTo) {
      return <Navigate to={redirectTo} state={{ from: location }} replace />;
    }
    
    if (showError) {
      const message = "此功能仅限系统管理员访问";
      return fallback || <DefaultPermissionDeniedComponent message={message} />;
    }
    
    return null;
  }

  return children ? <>{children}</> : <Outlet />;
};

// 组合权限保护路由（支持多种权限检查）
export const CombinedProtectedRoute: React.FC<CombinedProtectedRouteProps> = ({
  permissions,
  roles,
  menuId,
  requireSystemAdmin = false,
  requireAll = false,
  children,
  fallback,
  redirectTo,
  showError = true
}) => {
  const { isAuthenticated } = useAuth();
  const { 
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    canAccessMenu,
    isSystemAdmin,
    isLoading 
  } = usePermissions();
  const location = useLocation();

  // 未认证检查
  if (!isAuthenticated) {
    return <Navigate to={redirectTo || "/login"} state={{ from: location }} replace />;
  }

  // 权限加载中
  if (isLoading) {
    return fallback || <DefaultLoadingComponent />;
  }

  // 系统管理员检查（如果需要，直接通过）
  if (requireSystemAdmin && !isSystemAdmin()) {
    if (redirectTo) {
      return <Navigate to={redirectTo} state={{ from: location }} replace />;
    }
    
    if (showError) {
      return fallback || <DefaultPermissionDeniedComponent message="此功能仅限系统管理员访问" />;
    }
    
    return null;
  }

  // 如果是系统管理员且不强制其他检查，直接通过
  if (isSystemAdmin() && !requireAll) {
    return children ? <>{children}</> : <Outlet />;
  }

  const checks: Array<{ passed: boolean; message: string }> = [];

  // 权限检查
  if (permissions) {
    const permsArray = Array.isArray(permissions) ? permissions : [permissions];
    const hasAccess = requireAll 
      ? hasAllPermissions(permsArray)
      : hasAnyPermission(permsArray);
    
    const permissionStrings = permsArray.map(p => `${p.resource}:${p.action}${p.scope ? `:${p.scope}` : ''}`);
    checks.push({
      passed: hasAccess,
      message: `权限: ${permissionStrings.join(requireAll ? ' 和 ' : ' 或 ')}`
    });
  }

  // 角色检查
  if (roles) {
    const rolesArray = Array.isArray(roles) ? roles : [roles];
    const hasAccess = requireAll
      ? rolesArray.every(role => hasRole(role))
      : hasAnyRole(rolesArray);
    
    checks.push({
      passed: hasAccess,
      message: `角色: ${rolesArray.join(requireAll ? ' 和 ' : ' 或 ')}`
    });
  }

  // 菜单检查
  if (menuId) {
    checks.push({
      passed: canAccessMenu(menuId),
      message: `菜单访问: ${menuId}`
    });
  }

  // 检查结果
  if (redirectTo) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return children ? <>{children}</> : <Outlet />;
};

// 默认导出（向后兼容）
export default BaseProtectedRoute;