import {UserTenantInfo} from "@/types/identity.ts";
import {FC, ReactNode} from "react";
import {Divide} from "lucide-react";


interface TenantListProps {
    tenants: UserTenantInfo[];
    onClick?: (tenant: UserTenantInfo) => void
    onBind?: () => void
}

const IconWrapper: FC<{ children: ReactNode }> = ({ children }) => (
    <div className="p-1 rounded-md bg-gray-200 flex items-center justify-center w-6 h-6">
        {children}
    </div>
);

export function TenantList({ tenants,onClick,onBind }: TenantListProps) {
    return (
        <>
            <p className="text-xs text-gray-400 px-2 mb-1 select-none">我所在的学校</p>

            <ul className="flex flex-col space-y-2 p-2 min-w-[180px]">
                {tenants.map((tenant) => (
                    <li key={tenant.tenant_id} className="flex items-center space-x-2" onClick={()=>onClick?.(tenant)}>
                        <IconWrapper>
                            <Divide className="w-4 h-4 text-gray-600" />
                        </IconWrapper>

                        <span className="text-sm text-gray-700">{tenant.name}</span>
                    </li>
                ))}
            </ul>

            <hr className="border-t border-gray-300 mx-2" />
            <button
                onClick={onBind}
                type="button"
                className="flex items-center space-x-2 p-2 w-full text-gray-500 text-sm cursor-pointer hover:bg-gray-100 rounded"
            >
                <IconWrapper>
                    <Divide className="w-4 h-4 text-gray-400" />
                </IconWrapper>
                <span>绑定更多</span>
            </button>
        </>
    );
}