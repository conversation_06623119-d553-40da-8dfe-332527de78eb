import {
    User,
    Bell,
    LogOut,
    Users, CheckCircle2, ChevronDown, Plus, ChevronsUpDown, Lock
} from "lucide-react"

import {
    Avatar,
    AvatarFallback,
    AvatarImage,
} from "@/components/ui/avatar"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from "@/components/ui/sidebar"
import {useAuth} from "@/contexts/AuthContext.tsx";
import {NavLink, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import {IdentityInfo, translateRole} from "@/types/identity.ts";
import {getIdentities} from "@/services/identityApi.ts";
import {toast} from "sonner";
import {ChangePasswordCard} from "@/pages/Profile/components/ChangePasswordCard";

export function NavUser({
                            user,
                        }: {
    user: {
        avatar: string
    }
}) {
    const {switchableInfo, userRole, selectUserRole} = useAuth()

    const {isMobile} = useSidebar()
    const {logout, selectIdentity, identity, currentUserData} = useAuth();
    const navigate = useNavigate();
    const [availableIdentities, setAvailableIdentities] = useState<IdentityInfo[]>([]);
    const [changePasswordOpen, setChangePasswordOpen] = useState(false);
    const [isAccordionOpen, setIsAccordionOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // 获取可用的身份列表
    useEffect(() => {
        const fetchIdentities = async () => {
            setIsLoading(true);
            try {
                const identities = await getIdentities();
                setAvailableIdentities(identities);
            } catch (error) {
                console.error("获取身份列表失败:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchIdentities();
    }, []);

    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    const handleMe = () => {
        toast.info("通知功能开发中...")
    }

    const handleChangePassword = () => {
        setChangePasswordOpen(true);
    }

    const handleSwitchIdentity = async (identity: IdentityInfo) => {
        try {
            selectIdentity(identity);
            // 刷新页面以确保所有数据更新
            window.location.reload();
        } catch (error) {
            toast.error("身份切换失败");
            console.error("切换身份错误:", error);
        }
    };

    const toggleAccordion = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <>
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                        >
                            <Avatar className="h-8 w-8 rounded-lg">
                                <AvatarImage src={user.avatar}/>
                                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                            </Avatar>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-medium">{currentUserData?.username}</span>
                                <span className="truncate text-xs">{currentUserData?.phone_number}</span>
                            </div>
                            <ChevronsUpDown className="ml-auto size-4"/>
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                        side={isMobile ? "bottom" : "right"}
                        align="end"
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="p-0 font-normal">
                            <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                <Avatar className="h-8 w-8 rounded-lg">
                                    <AvatarImage src={user.avatar}/>
                                    <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                                </Avatar>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    {identity && (
                                        <span className="truncate text-xs text-muted-foreground">
                                            {identity.display_name} | {identity.tenant_name}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </DropdownMenuLabel>


                        <DropdownMenuLabel className="flex space-x-2 mb-2">
                            {switchableInfo.map((info, idx) => (
                                <span
                                    onClick={() => selectUserRole(info)}
                                    key={idx}
                                    className={`inline-block text-xs rounded-full px-2 py-0.5 truncate max-w-[100px] cursor-default
                                                    ${info === userRole ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-700"}`}
                                    title={info}
                                >
                                        {translateRole(info)}
                                    </span>
                            ))}
                        </DropdownMenuLabel>


                        {/* 身份切换手风琴区域 */}
                        {/*<DropdownMenuSeparator/>*/}
                        {/*<DropdownMenuLabel className="p-0 font-normal">*/}
                        {/*    <div className="overflow-hidden">*/}
                        {/*        <DropdownMenuItem*/}
                        {/*            className="justify-between focus:bg-transparent"*/}
                        {/*            onMouseEnter={toggleAccordion}*/}
                        {/*        >*/}
                        {/*            <div className="flex items-center gap-2">*/}
                        {/*                <Users className="h-4 w-4"/>*/}
                        {/*                <span>切换身份</span>*/}
                        {/*            </div>*/}
                        {/*            <ChevronDown*/}
                        {/*                className={`h-4 w-4 transition-transform duration-200 ${*/}
                        {/*                    isAccordionOpen ? "rotate-180" : ""*/}
                        {/*                }`}*/}
                        {/*            />*/}
                        {/*        </DropdownMenuItem>*/}

                                {/*{isAccordionOpen && (*/}
                                {/*    <div className="ml-7 pl-1 border-l border-gray-200 dark:border-gray-700">*/}
                                {/*        <div className="max-h-60 overflow-y-auto py-1 pr-1">*/}
                                {/*            {isLoading ? (*/}
                                {/*                <div className="flex justify-center py-2">*/}
                                {/*                    <div*/}
                                {/*                        className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>*/}
                                {/*                </div>*/}
                                {/*            ) : availableIdentities.length > 0 ? (*/}
                                {/*                availableIdentities.map((identity) => (*/}
                                {/*                    <DropdownMenuItem*/}
                                {/*                        key={identity.identity_id}*/}
                                {/*                        className={`flex items-center gap-2 my-1 rounded ${*/}
                                {/*                            identity.identity_id === identity?.identity_id*/}
                                {/*                                ? 'bg-accent'*/}
                                {/*                                : 'hover:bg-gray-100 dark:hover:bg-gray-800'*/}
                                {/*                        }`}*/}
                                {/*                        onClick={() => handleSwitchIdentity(identity)}*/}
                                {/*                    >*/}
                                {/*                        <div className="grid flex-1">*/}
                                {/*                            <span className="font-medium">{identity.display_name}</span>*/}
                                {/*                            <span*/}
                                {/*                                className="text-xs text-muted-foreground truncate max-w-[140px]">*/}
                                {/*                            {identity.tenant_name}*/}
                                {/*                        </span>*/}
                                {/*                        </div>*/}
                                {/*                        {identity.identity_id === identity?.identity_id && (*/}
                                {/*                            <CheckCircle2 className="ml-auto h-4 w-4 text-primary"/>*/}
                                {/*                        )}*/}
                                {/*                    </DropdownMenuItem>*/}
                                {/*                ))*/}
                                {/*            ) : (*/}
                                {/*                <DropdownMenuItem disabled className="text-muted-foreground text-xs">*/}
                                {/*                    无可用身份*/}
                                {/*                </DropdownMenuItem>*/}
                                {/*            )}*/}

                                {/*            /!* 添加绑定新身份选项 *!/*/}
                                {/*            */}
                                {/*        </div>*/}
                                {/*    </div>*/}
                                {/*)}*/}
                        {/*    </div>*/}
                        {/*</DropdownMenuLabel>*/}
                        {/*<DropdownMenuSeparator/>*/}


                        <DropdownMenuItem
                            className="flex items-center gap-2 my-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
                            onClick={() => navigate("/identityBinding")}
                        >
                            <div className="bg-gray-200 dark:bg-gray-700 rounded-full p-1">
                                <Plus className="h-4 w-4"/>
                            </div>
                            <span className="font-medium">绑定新身份</span>
                        </DropdownMenuItem>

                        <DropdownMenuGroup>
                            <DropdownMenuItem>
                                <User className="mr-2 h-4 w-4" />
                                <NavLink to="/profile">
                                    <span>个人中心</span>
                                </NavLink>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={handleChangePassword}
                            >
                                <Lock className="mr-2 h-4 w-4"/>
                                修改密码
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={handleMe}
                            >
                                <Bell className="mr-2 h-4 w-4"/>
                                通知
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator/>
                        <DropdownMenuItem
                            onClick={handleLogout}
                        >
                            <LogOut className="mr-2 h-4 w-4"/>
                            退出登录
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
        
        {/* 修改密码对话框 */}
        <ChangePasswordCard 
            open={changePasswordOpen}
            trigger ={true }
            onOpenChange={setChangePasswordOpen}
        />
        </>
    )
}