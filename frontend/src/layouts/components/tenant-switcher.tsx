"use client"

import {Building2, Check, ChevronsUpDown, Plus} from "lucide-react"

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from "@/components/ui/sidebar"
import logoImage from "@/assets/logo.png";
import {useAuth} from "@/contexts/AuthContext";
import {UserTenantInfo} from "@/types/identity.ts";
import React from "react";
import {useNavigate} from "react-router-dom";
import {TenantCache} from "@/utils/tenantCache.ts";

export function TenantSwitcher({tenants,}: { tenants: UserTenantInfo[] | undefined }) {
    const navigate = useNavigate();

    const {currentUserData} = useAuth()
    const {isMobile} = useSidebar()
    const {tenant, selectTenant} = useAuth()
    // 使用 AuthContext 中的 selectedTenant，如果没有则使用第一个 tenant
    const activeTenant = tenant || tenants?.[0]
    // 当 tenants 变化且没有选中的 tenant 时，优先使用缓存中的租户
    React.useEffect(() => {
        if (tenants && tenants.length > 0 && !tenant) {
            const selectedTenant = TenantCache.selectTenantFromList(tenants);
            if (selectedTenant) {
                selectTenant(selectedTenant);
            }
        }
    }, [tenants, tenant, selectTenant])

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                        >
                            <div className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
                                <div
                                    className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-white border border-gray-200 shadow-sm">
                                    <img
                                        src={logoImage}
                                        alt="语文出版社logo"
                                        className="w-8 h-8 object-contain"
                                    />
                                </div>
                                <div className="flex flex-col group-data-[collapsible=icon]:hidden">
                                    <h1 className="text-sm font-bold text-sidebar-foreground truncate max-w-[150px]">{activeTenant?.tenant_name ?? '数智教服引擎'}</h1>
                                    <p className="text-xs text-sidebar-foreground/70">{activeTenant?.user_hit_info ?? '超级管理员'}</p>
                                </div>
                            </div>
                            <ChevronsUpDown className="ml-auto"/>
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                        align="start"
                        side={isMobile ? "bottom" : "right"}
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="text-muted-foreground text-xs">
                            教育机构
                        </DropdownMenuLabel>
                        {currentUserData?.tenants?.map((tenant) => (
                            <DropdownMenuItem
                                key={tenant.tenant_name}
                                onClick={() => selectTenant(tenant)}
                                className={`gap-2 p-2 ${activeTenant?.tenant_name === tenant.tenant_name ? 'bg-accent text-accent-foreground' : ''}`}
                            >
                                <Building2 className="size-4"/>
                                <span className="flex-1">{tenant.tenant_name}</span>
                                {activeTenant?.tenant_id === tenant.tenant_id && (
                                    <Check className="size-4 text-primary"/>
                                )}
                            </DropdownMenuItem>
                        ))}
                        <DropdownMenuSeparator/>
                        <DropdownMenuItem className="gap-2 p-2">
                            <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                                <Plus className="size-4"/>
                            </div>
                            <div onClick={() => navigate("/identityBinding")}
                                 className="text-muted-foreground font-medium">加入教育机构
                            </div>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
