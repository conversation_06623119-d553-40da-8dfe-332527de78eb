import { ThemeToggle } from '@/components/ThemeToggle';
import { Separator } from '@/components/ui/separator';
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarInset,
    SidebarProvider,
    SidebarRail,
    SidebarTrigger
} from '@/components/ui/sidebar';
import { Outlet } from 'react-router-dom';

import { PageContainer } from '@/components/layout/PageContainer';
import { DynamicNavigation } from '@/components/navigation/DynamicNavigation';
import { Toaster } from "@/components/ui/sonner";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from '@/contexts/PermissionContext';
import { LayoutWidthProvider, useLayoutWidth } from '@/hooks/useLayoutWidth';
import { NavUser } from "@/layouts/components/nav-user.tsx";
import { TenantSwitcher } from "@/layouts/components/tenant-switcher.tsx";
import { APP_VERSION } from '@/version';
import { AlertCircle } from 'lucide-react';

function MainContent() {
    const { width } = useLayoutWidth();

    return (
        <div className="flex flex-1 flex-col p-6 ">
            <PageContainer maxWidth={width} padding={false}>
                <Outlet />
            </PageContainer>
        </div>
    );
}

export default function RootLayout() {
    const { error, isLoading } = usePermissions();
    const { currentUserData } = useAuth();

    return (
        <LayoutWidthProvider>
            <SidebarProvider>
                <div className="flex h-screen w-full">
                    <Sidebar collapsible="icon">
                        <SidebarHeader>
                            <TenantSwitcher tenants={currentUserData?.tenants} />
                        </SidebarHeader>

                        <SidebarContent>
                            {/* 权限错误提示 */}
                            {error && (
                                <div className="p-4">
                                    <div
                                        className="relative w-full rounded-lg border border-destructive/50 bg-destructive/5 p-4 text-destructive">
                                        <AlertCircle className="h-4 w-4 absolute left-4 top-4" />
                                        <div className="pl-7 text-sm">
                                            权限加载失败，请刷新页面重试
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* 动态导航菜单 */}
                            <DynamicNavigation
                                showLoading={true}
                                showError={true}
                                groupByCategory={true}
                                defaultExpanded={true}
                            />
                        </SidebarContent>

                        <SidebarFooter>
                            <NavUser
                                user={{ avatar: 'https://avatars.githubusercontent.com/u/10656201?v=4' }}
                            />
                        </SidebarFooter>

                        <SidebarRail />
                    </Sidebar>

                    <SidebarInset>
                        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-6">
                            <SidebarTrigger className="-ml-1" />
                            <Separator orientation="vertical" className="mr-2 h-4" />
                            <div className="flex items-center justify-between flex-1">
                                <div className="flex items-center space-x-2">
                                    <h2 className="text-lg font-semibold">工作台</h2>
                                    {isLoading && (
                                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                            <div
                                                className="animate-spin h-3 w-3 border border-primary border-t-transparent rounded-full"></div>
                                            加载权限中...
                                        </div>
                                    )}
                                </div>
                                <div className="flex items-center gap-3">
                                    <span className="text-xs text-muted-foreground">v{APP_VERSION}</span>
                                    <ThemeToggle />
                                </div>
                            </div>
                        </header>

                        <MainContent />
                        <Toaster position='top-center' />
                    </SidebarInset>
                </div>
            </SidebarProvider>
        </LayoutWidthProvider>
    );
}