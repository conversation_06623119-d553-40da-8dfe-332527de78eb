import {MathJaxContext} from 'better-react-mathjax';
import {RouterProvider} from 'react-router-dom';
import {AuthProvider} from './contexts/AuthContext';
import {MenuProvider} from './contexts/MenuContext';
import {PermissionProvider} from './contexts/PermissionContext';
import {ThemeProvider} from './contexts/ThemeContext';
import {router} from './router';
import {Toaster} from "@/components/ui/sonner.tsx";

function App() {
    return (
        <MathJaxContext src='/mathjax/tex-mml-chtml.js'>
            <Toaster position='top-center'/>
            <ThemeProvider>
                <AuthProvider>
                    <MenuProvider>
                        <PermissionProvider>
                                <RouterProvider router={router}/>
                        </PermissionProvider>
                    </MenuProvider>
                </AuthProvider>
            </ThemeProvider>
        </MathJaxContext>
    );
}

export default App;
