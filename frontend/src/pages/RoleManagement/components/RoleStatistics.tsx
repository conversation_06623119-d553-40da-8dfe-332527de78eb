import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  UserCheck, 
  Settings, 
  Users, 
  TrendingUp, 
  TrendingDown,
  Activity
} from 'lucide-react';

interface RoleStatisticsProps {
  statistics: {
    totalRoles: number;
    activeRoles: number;
    systemRoles: number;
    userCount: number;
  };
  previousStats?: {
    totalRoles: number;
    activeRoles: number;
    systemRoles: number;
    userCount: number;
  };
}

type ColorType = 'blue' | 'green' | 'purple' | 'orange';

const RoleStatistics: React.FC<RoleStatisticsProps> = ({ statistics, previousStats }) => {
  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const renderChangeIndicator = (change: number) => {
    if (change > 0) {
      return (
        <div className="flex items-center text-green-600 text-xs">
          <TrendingUp className="h-3 w-3 mr-1" />
          +{change.toFixed(1)}%
        </div>
      );
    } else if (change < 0) {
      return (
        <div className="flex items-center text-red-600 text-xs">
          <TrendingDown className="h-3 w-3 mr-1" />
          {change.toFixed(1)}%
        </div>
      );
    }
    return (
      <div className="flex items-center text-slate-600 text-xs">
        <Activity className="h-3 w-3 mr-1" />
        0%
      </div>
    );
  };

  const bgColorMap: Record<ColorType, string> = {
    blue: 'bg-blue-100',
    green: 'bg-green-100',
    purple: 'bg-purple-100',
    orange: 'bg-orange-100'
  };

  const iconColorMap: Record<ColorType, string> = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600'
  };

  const stats = [
    {
      title: '总角色数',
      value: statistics.totalRoles,
      icon: Shield,
      color: 'blue' as ColorType,
      change: previousStats ? calculateChange(statistics.totalRoles, previousStats.totalRoles) : 0
    },
    {
      title: '启用角色',
      value: statistics.activeRoles,
      icon: UserCheck,
      color: 'green' as ColorType,
      change: previousStats ? calculateChange(statistics.activeRoles, previousStats.activeRoles) : 0
    },
    {
      title: '系统角色',
      value: statistics.systemRoles,
      icon: Settings,
      color: 'purple' as ColorType,
      change: previousStats ? calculateChange(statistics.systemRoles, previousStats.systemRoles) : 0
    },
    {
      title: '用户总数',
      value: statistics.userCount,
      icon: Users,
      color: 'orange' as ColorType,
      change: previousStats ? calculateChange(statistics.userCount, previousStats.userCount) : 0
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const IconComponent = stat.icon;
        const bgColorClass = bgColorMap[stat.color];
        const iconColorClass = iconColorMap[stat.color];

        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value.toLocaleString()}</p>
                  {previousStats && renderChangeIndicator(stat.change)}
                </div>
                <div className={`p-3 ${bgColorClass} rounded-lg`}>
                  <IconComponent className={`h-6 w-6 ${iconColorClass}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default RoleStatistics; 