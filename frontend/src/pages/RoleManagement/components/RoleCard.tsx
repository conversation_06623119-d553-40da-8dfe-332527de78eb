import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  Edit, 
  Trash2, 
  Eye, 
  Lock, 
  Unlock 
} from 'lucide-react';
import { Role, RoleCategory, ROLE_CATEGORY_LABELS, ROLE_LEVEL_LABELS } from '@/types/role';

interface RoleCardProps {
  role: Role;
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
  onViewPermissions: (role: Role) => void;
  onToggleStatus: (role: Role) => void;
}

const RoleCard: React.FC<RoleCardProps> = ({
  role,
  onEdit,
  onDelete,
  onViewPermissions,
  onToggleStatus
}) => {
  const renderRoleStatus = (role: Role) => (
    <Badge variant={role.is_active ? 'default' : 'secondary'} className="flex items-center gap-1">
      {role.is_active ? <Unlock className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
      {role.is_active ? '启用' : '禁用'}
    </Badge>
  );

  const renderRoleCategory = (category: RoleCategory) => {
    const variants = {
      [RoleCategory.SYSTEM]: 'destructive',
      [RoleCategory.TENANT]: 'default',
      [RoleCategory.SCHOOL]: 'secondary',
      [RoleCategory.BUSINESS]: 'outline',
      [RoleCategory.CLASS_GRADE]: 'outline',
      [RoleCategory.END_USER]: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[category] || 'outline'}>
        {ROLE_CATEGORY_LABELS[category]}
      </Badge>
    );
  };

  const renderRoleLevel = (level: number) => (
    <span className="text-sm text-slate-600 font-medium">
      {ROLE_LEVEL_LABELS[level as keyof typeof ROLE_LEVEL_LABELS]}
    </span>
  );

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-slate-100 rounded-lg">
              <Shield className="h-5 w-5 text-slate-600" />
            </div>
            <div>
              <CardTitle className="text-lg">{role.name}</CardTitle>
              <code className="text-xs bg-slate-100 px-2 py-1 rounded text-slate-600">
                {role.code}
              </code>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {role.is_system && (
              <Badge variant="secondary" className="text-xs">
                系统
              </Badge>
            )}
            {renderRoleStatus(role)}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">分类</span>
          {renderRoleCategory(role.category)}
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">级别</span>
          {renderRoleLevel(role.level)}
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">权限数量</span>
          <span className="font-medium">{role.permissions.length}</span>
        </div>
        {role.description && (
          <p className="text-sm text-slate-600 line-clamp-2">
            {role.description}
          </p>
        )}
        <div className="flex items-center justify-between pt-2 border-t">
          <span className="text-xs text-slate-500">
            {new Date(role.created_at).toLocaleDateString()}
          </span>
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onViewPermissions(role)}
              title="查看权限"
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onEdit(role)}
              disabled={role.is_system}
              title="编辑角色"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onToggleStatus(role)}
              disabled={role.is_system}
              title={role.is_active ? '禁用角色' : '启用角色'}
            >
              {role.is_active ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onDelete(role)}
              disabled={role.is_system}
              className="text-red-500 hover:text-red-700"
              title="删除角色"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoleCard; 