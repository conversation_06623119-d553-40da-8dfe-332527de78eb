/**
 * 角色管理页面 - 现代化设计
 * 提供角色的增删改查功能，支持权限分配和用户角色管理
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  RefreshCw,
  Shield,
  Users,
  Settings,
  Eye,
  UserCheck,
  Lock,
  Unlock,
  Download,
  Upload,
  Grid3X3,
  List,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { roleApi, permissionApi } from '@/services/roleApi';
import { 
  Role, 
  Permission,
  CreateRoleRequest, 
  UpdateRoleRequest,
  RoleCategory, 
  RoleLevel,
  RoleQueryParams,
  ROLE_CATEGORY_LABELS,
  ROLE_LEVEL_LABELS,
} from '@/types/role';

// 表单验证 Schema
const roleFormSchema = z.object({
  name: z.string().min(1, '角色名称不能为空').max(50, '角色名称不能超过50字符'),
  code: z.string().min(1, '角色编码不能为空').max(50, '角色编码不能超过50字符'),
  description: z.string().max(200, '描述不能超过200字符').optional(),
  category: z.nativeEnum(RoleCategory, { errorMap: () => ({ message: '请选择角色分类' }) }),
  level: z.nativeEnum(RoleLevel, { errorMap: () => ({ message: '请选择角色级别' }) }),
  is_active: z.boolean()
});

type RoleFormValues = z.infer<typeof roleFormSchema>;

const RoleManagementPage: React.FC = () => {
  // 状态管理
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  
  // 分页和筛选
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<RoleCategory | 'all'>('all');
  const [selectedLevel, setSelectedLevel] = useState<RoleLevel | 'all'>('all');
  const [showActiveOnly, setShowActiveOnly] = useState<boolean>(true);
  
  // 对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  
  // 统计信息
  const [statistics, setStatistics] = useState({
    totalRoles: 0,
    activeRoles: 0,
    systemRoles: 0,
    userCount: 0
  });
  
  // 表单
  const form = useForm<RoleFormValues>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      category: RoleCategory.BUSINESS,
      level: RoleLevel.TEACHER,
      is_active: true
    }
  });

  // 加载数据
  const loadRoles = async () => {
    try {
      setLoading(true);
      const params: RoleQueryParams = {
        page: currentPage,
        page_size: 12,
        search: searchTerm || undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        level: selectedLevel !== 'all' ? selectedLevel : undefined,
        is_active: showActiveOnly ? true : undefined
      };
      
      const response = await roleApi.getRoles(params);
      if (response) {
        setRoles(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.total_pages);
          setTotalCount(response.pagination.total);
        }
      }
      setError(null);
    } catch (err: any) {
      setError(err.message || '加载角色列表失败');
      toast.error('加载角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadPermissions = async () => {
    try {
      const response = await permissionApi.getPermissions({ page_size: 1000 });
      if (response.success && response.data) {
        setPermissions(response.data);
      }
    } catch (err: any) {
      console.error('加载权限列表失败:', err);
    }
  };

  const loadStatistics = async () => {
    try {
      // 这里应该调用统计API，暂时使用模拟数据
      setStatistics({
        totalRoles: totalCount,
        activeRoles: roles?.filter(r => r.is_active).length,
        systemRoles: roles?.filter(r => r.is_system).length,
        userCount: 1250
      });
    } catch (err: any) {
      console.error('加载统计信息失败:', err);
    }
  };

  // 生命周期
  useEffect(() => {
    loadRoles();
  }, [currentPage, searchTerm, selectedCategory, selectedLevel, showActiveOnly]);

  useEffect(() => {
    loadPermissions();
  }, []);

  useEffect(() => {
    loadStatistics();
  }, [roles, totalCount]);

  // 表单处理
  const handleCreate = async (data: RoleFormValues) => {
    try {
      const createData: CreateRoleRequest = {
        ...data,
        permissions: selectedPermissions
      };
      
      const response = await roleApi.createRole(createData);
      if (response.success) {
        toast.success('角色创建成功');
        setIsCreateDialogOpen(false);
        form.reset();
        setSelectedPermissions([]);
        loadRoles();
      }
    } catch (err: any) {
      toast.error(err.message || '创建角色失败');
    }
  };

  const handleEdit = async (data: RoleFormValues) => {
    if (!selectedRole) return;
    
    try {
      const updateData: UpdateRoleRequest = {
        name: data.name,
        description: data.description,
        permissions: selectedPermissions
      };
      
      const response = await roleApi.updateRole(selectedRole.id, updateData);
      if (response.success) {
        toast.success('角色更新成功');
        setIsEditDialogOpen(false);
        setSelectedRole(null);
        form.reset();
        setSelectedPermissions([]);
        loadRoles();
      }
    } catch (err: any) {
      toast.error(err.message || '更新角色失败');
    }
  };

  const handleDelete = async () => {
    if (!selectedRole) return;
    
    try {
      const response = await roleApi.deleteRole(selectedRole.id);
      if (response.success) {
        toast.success('角色删除成功');
        setIsDeleteDialogOpen(false);
        setSelectedRole(null);
        loadRoles();
      }
    } catch (err: any) {
      toast.error(err.message || '删除角色失败');
    }
  };

  const handleToggleStatus = async (role: Role) => {
    try {
      const response = await roleApi.toggleRoleStatus(role.id, !role.is_active);
      if (response.success) {
        toast.success(`角色已${role.is_active ? '禁用' : '启用'}`);
        loadRoles();
      }
    } catch (err: any) {
      toast.error(err.message || '操作失败');
    }
  };

  // 对话框处理
  const openCreateDialog = () => {
    form.reset();
    setSelectedPermissions([]);
    setIsCreateDialogOpen(true);
  };

  const openEditDialog = (role: Role) => {
    setSelectedRole(role);
    form.reset({
      name: role.name,
      code: role.code,
      description: role.description,
      category: role.category,
      level: role.level,
      is_active: role.is_active
    });
    setSelectedPermissions(role.permissions.map(p => p.id));
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (role: Role) => {
    setSelectedRole(role);
    setIsDeleteDialogOpen(true);
  };

  const openPermissionDialog = (role: Role) => {
    setSelectedRole(role);
    setIsPermissionDialogOpen(true);
  };

  // 渲染函数
  const renderRoleStatus = (role: Role) => (
    <Badge variant={role.is_active ? 'default' : 'secondary'} className="flex items-center gap-1">
      {role.is_active ? <Unlock className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
      {role.is_active ? '启用' : '禁用'}
    </Badge>
  );

  const renderRoleCategory = (category: RoleCategory) => {
    const variants = {
      [RoleCategory.SYSTEM]: 'destructive',
      [RoleCategory.TENANT]: 'default',
      [RoleCategory.SCHOOL]: 'secondary',
      [RoleCategory.BUSINESS]: 'outline',
      [RoleCategory.CLASS_GRADE]: 'outline',
      [RoleCategory.END_USER]: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[category] || 'outline'}>
        {ROLE_CATEGORY_LABELS[category]}
      </Badge>
    );
  };

  const renderRoleLevel = (level: RoleLevel) => (
    <span className="text-sm text-slate-600 font-medium">
      {ROLE_LEVEL_LABELS[level]}
    </span>
  );

  const renderRoleCard = (role: Role) => (
    <Card key={role.id} className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-slate-100 rounded-lg">
              <Shield className="h-5 w-5 text-slate-600" />
            </div>
            <div>
              <CardTitle className="text-lg">{role.name}</CardTitle>
              <code className="text-xs bg-slate-100 px-2 py-1 rounded text-slate-600">
                {role.code}
              </code>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {role.is_system && (
              <Badge variant="secondary" className="text-xs">
                系统
              </Badge>
            )}
            {renderRoleStatus(role)}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">分类</span>
          {renderRoleCategory(role.category)}
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">级别</span>
          {renderRoleLevel(role.level)}
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">权限数量</span>
          <span className="font-medium">{role.permissions.length}</span>
        </div>
        {role.description && (
          <p className="text-sm text-slate-600 line-clamp-2">
            {role.description}
          </p>
        )}
        <div className="flex items-center justify-between pt-2 border-t">
          <span className="text-xs text-slate-500">
            {new Date(role.created_at).toLocaleDateString()}
          </span>
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => openPermissionDialog(role)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => openEditDialog(role)}
              disabled={role.is_system}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => handleToggleStatus(role)}
              disabled={role.is_system}
            >
              {role.is_active ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => openDeleteDialog(role)}
              disabled={role.is_system}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderTableLoading = () => (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[150px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[80px]" />
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* 页头 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">角色管理</h1>
          <p className="text-slate-600 mt-2">
            管理系统角色和权限分配，支持多层级权限控制
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            导入
          </Button>
          <Button onClick={openCreateDialog} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            新建角色
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">总角色数</p>
                <p className="text-2xl font-bold">{statistics.totalRoles}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">启用角色</p>
                <p className="text-2xl font-bold">{statistics.activeRoles}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">系统角色</p>
                <p className="text-2xl font-bold">{statistics.systemRoles}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Settings className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">用户总数</p>
                <p className="text-2xl font-bold">{statistics.userCount}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-lg">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 筛选和工具栏 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">角色列表</CardTitle>
            <div className="flex items-center gap-3">
              <div className="text-sm text-slate-600">
                共 {totalCount} 个角色
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            {/* 搜索框 */}
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="搜索角色名称或编码..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* 分类筛选 */}
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                {Object.entries(ROLE_CATEGORY_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* 级别筛选 */}
            <Select 
              value={selectedLevel === 'all' ? 'all' : selectedLevel.toString()} 
              onValueChange={(value) => setSelectedLevel(value === 'all' ? 'all' : parseInt(value) as RoleLevel)}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="选择级别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部级别</SelectItem>
                {Object.entries(ROLE_LEVEL_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* 状态筛选 */}
            <Button 
              variant={showActiveOnly ? "default" : "outline"} 
              size="sm"
              onClick={() => setShowActiveOnly(!showActiveOnly)}
            >
              {showActiveOnly ? "仅显示启用" : "显示全部"}
            </Button>
            
            {/* 刷新按钮 */}
            <Button variant="outline" size="sm" onClick={loadRoles}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>

          {/* 角色内容 */}
          {loading ? (
            viewMode === 'table' ? renderTableLoading() : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }).map((_, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                      <Skeleton className="h-4 w-1/2" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            )
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              {error}
            </div>
          ) : roles.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-slate-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">暂无角色数据</h3>
              <p className="text-slate-600 mb-4">开始创建您的第一个角色</p>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                创建角色
              </Button>
            </div>
          ) : viewMode === 'table' ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>角色名称</TableHead>
                  <TableHead>角色编码</TableHead>
                  <TableHead>分类</TableHead>
                  <TableHead>级别</TableHead>
                  <TableHead>权限数量</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles?.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-slate-400" />
                        <span className="font-medium">{role.name}</span>
                        {role.is_system && (
                          <Badge variant="secondary" className="text-xs">
                            系统
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-slate-100 px-2 py-1 rounded">
                        {role.code}
                      </code>
                    </TableCell>
                    <TableCell>{renderRoleCategory(role.category)}</TableCell>
                    <TableCell>{renderRoleLevel(role.level)}</TableCell>
                    <TableCell>
                      <span className="text-sm">{role.permissions.length}</span>
                    </TableCell>
                    <TableCell>{renderRoleStatus(role)}</TableCell>
                    <TableCell>
                      <span className="text-sm text-slate-600">
                        {new Date(role.created_at).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center gap-1 justify-end">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => openPermissionDialog(role)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => openEditDialog(role)}
                          disabled={role.is_system}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleToggleStatus(role)}
                          disabled={role.is_system}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => openDeleteDialog(role)}
                          disabled={role.is_system}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {roles?.map(renderRoleCard)}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationPrevious 
                    onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                    className={currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = i + 1;
                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink 
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}
                  <PaginationNext 
                    onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
                    className={currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建角色对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>新建角色</DialogTitle>
            <DialogDescription>
              创建新的系统角色并配置权限
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="permissions">权限配置</TabsTrigger>
            </TabsList>
            <TabsContent value="basic">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleCreate)} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色名称</FormLabel>
                          <FormControl>
                            <Input placeholder="输入角色名称" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色编码</FormLabel>
                          <FormControl>
                            <Input placeholder="输入角色编码" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色分类</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择角色分类" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(ROLE_CATEGORY_LABELS).map(([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="level"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色级别</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value.toString()}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择角色级别" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(ROLE_LEVEL_LABELS).map(([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>描述</FormLabel>
                        <FormControl>
                          <Textarea placeholder="输入角色描述（可选）" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      取消
                    </Button>
                    <Button type="submit">
                      创建
                    </Button>
                  </div>
                </form>
              </Form>
            </TabsContent>
            <TabsContent value="permissions">
              <div className="space-y-4">
                <div className="text-sm text-slate-600">
                  选择该角色拥有的权限
                </div>
                <div className="grid grid-cols-2 gap-4 max-h-60 overflow-y-auto">
                  {permissions.map((permission) => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={permission.id}
                        checked={selectedPermissions.includes(permission.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPermissions([...selectedPermissions, permission.id]);
                          } else {
                            setSelectedPermissions(selectedPermissions.filter(id => id !== permission.id));
                          }
                        }}
                      />
                      <Label htmlFor={permission.id} className="text-sm">
                        {permission.name}
                      </Label>
                    </div>
                  ))}
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={form.handleSubmit(handleCreate)}>
                    创建
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* 编辑角色对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑角色</DialogTitle>
            <DialogDescription>
              修改角色信息和权限配置
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="permissions">权限配置</TabsTrigger>
            </TabsList>
            <TabsContent value="basic">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleEdit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>角色名称</FormLabel>
                        <FormControl>
                          <Input placeholder="输入角色名称" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>描述</FormLabel>
                        <FormControl>
                          <Textarea placeholder="输入角色描述（可选）" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                      取消
                    </Button>
                    <Button type="submit">
                      保存
                    </Button>
                  </div>
                </form>
              </Form>
            </TabsContent>
            <TabsContent value="permissions">
              <div className="space-y-4">
                <div className="text-sm text-slate-600">
                  选择该角色拥有的权限
                </div>
                <div className="grid grid-cols-2 gap-4 max-h-60 overflow-y-auto">
                  {permissions.map((permission) => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={permission.id}
                        checked={selectedPermissions.includes(permission.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPermissions([...selectedPermissions, permission.id]);
                          } else {
                            setSelectedPermissions(selectedPermissions.filter(id => id !== permission.id));
                          }
                        }}
                      />
                      <Label htmlFor={permission.id} className="text-sm">
                        {permission.name}
                      </Label>
                    </div>
                  ))}
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={form.handleSubmit(handleEdit)}>
                    保存
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* 删除角色确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除角色 {selectedRole?.name}吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              删除
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 权限详情对话框 */}
      <Dialog open={isPermissionDialogOpen} onOpenChange={setIsPermissionDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>角色权限详情</DialogTitle>
            <DialogDescription>
              查看角色 {selectedRole?.name} 的权限配置
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {selectedRole?.permissions.length ? (
                selectedRole.permissions.map((permission) => (
                  <Card key={permission.id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">{permission.name}</h4>
                        <p className="text-xs text-slate-600">{permission.description}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {permission.resource}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {permission.action}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-8 text-slate-500">
                  <Shield className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <p>该角色暂无权限</p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RoleManagementPage;