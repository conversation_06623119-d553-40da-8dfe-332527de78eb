import QuestionCard from "@/components/question-card"
import { createComponentDataListStore } from "@/components/question-card/store/componentDataListStore"
import { createDataCallbackStore, QuestionType, Workflow } from "@/components/question-card/store/dataCallbackStore"
import { AdmissionTicketQRCodeMsgBean, createPaperDataStore } from "@/components/question-card/store/paperDataStore"
import { createToolbarDataStore } from "@/components/question-card/store/toolbarDataStore"
import { Button } from "@/components/ui/button"
import { usePageFullWidth } from "@/hooks/useLayoutWidth"
import { getTenantInfoFromLocalStorage } from "@/lib/apiUtils"
import { publicPaperApi } from "@/services/publicPapersApi"
import { questionTypeApi } from "@/services/questionApi"
import { tenantPaperApi } from "@/services/tenantPapersApi"
import { workflowSettingApi } from "@/services/workflowApi"
import { ArrowLeft, Save } from "lucide-react"
import React, { useEffect, useMemo, useRef, useState } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { toast } from "sonner"
import './index.scss'

/**
 * 作者：张瀚
 * 说明：答题卡编辑主页面
 */
const AnswerSheetEditing: React.FC = () => {
    usePageFullWidth();
    const navigate = useNavigate();
    const rootRef = useRef<HTMLDivElement>(null)
    const [rootHeight, setRootHeight] = useState(60)
    const { paperId: id, schema_name = '' } = useParams<{ schema_name: string, paperId: string }>();
    const [nowId, setNowId] = useState(id)
    if (nowId !== id) {
        setNowId(id)
    }
    const [nowSchemaName, setNowSchemaName] = useState(schema_name)
    if (nowSchemaName !== schema_name) {
        setNowSchemaName(schema_name)
    }
    const [nowPaperId, setNowPaperId] = useState('')
    //---------------------租户信息
    const tenantInfo = getTenantInfoFromLocalStorage()
    const [nowTenantId, setNowTenantId] = useState(tenantInfo?.tenant_id)
    if (nowTenantId !== tenantInfo?.tenant_id) {
        setNowTenantId(tenantInfo?.tenant_id)
    }
    const isWrong = tenantInfo === undefined || (schema_name !== tenantInfo.schema_name && schema_name !== 'public')
    //---------------------试卷数据
    const componentDataListStore = useMemo(() => createComponentDataListStore(), []);
    const paperDataStore = useMemo(() => createPaperDataStore(), []);
    const toolbarDataStore = useMemo(() => createToolbarDataStore(), []);
    const dataCallbackStore = useMemo(() => createDataCallbackStore(), []);
    const setPaperData = paperDataStore(state => state.setPaperData)
    const getPaperData = paperDataStore(state => state.getPaperData)
    const isReadyToPrint = paperDataStore(state => state.isReadyToPrint)

    const setAllCallback = dataCallbackStore(state => state.setAllCallback)

    useEffect(() => {
        if (nowTenantId === undefined || nowId === undefined) {
            return
        }
        if (nowSchemaName === 'public') {
            publicPaperApi.findByAnswerId(nowTenantId, nowId).then((res) => {
                const { success, data, message } = res
                if (!success) {
                    console.error('报错:' + message);
                    return
                }
                if (data) {
                    setNowPaperId(data.id)
                    setPaperData({
                        ...data
                    })
                }
            })
        } else {
            tenantPaperApi.findById(nowTenantId, nowSchemaName, nowId).then((res) => {
                const { success, data, message } = res
                if (!success) {
                    console.error('报错:' + message);
                    return
                }
                if (data) {
                    setNowPaperId(data.id)
                    setPaperData({
                        ...data
                    })
                }
            })
        }
    }, [nowId, nowSchemaName, setPaperData, nowTenantId])
    useEffect(() => {
        //设置回调
        setAllCallback({
            getOcrWorkflowList: function (): Promise<Workflow[]> {
                questionTypeApi.getQuestionTypeSummaries(true)
                return new Promise<Workflow[]>((resolve) => {
                    workflowSettingApi.getWorkflowSummaryInSetting({ workflow_type: 'ocr' }).then((res) => {
                        const { success, data, message } = res
                        if (!success) {
                            console.error('报错:' + message);
                            return
                        }
                        resolve(data?.map(item => ({ ...item })) ?? [])
                    })
                })
            },
            getCheckWorkflowList: function (): Promise<Workflow[]> {
                return new Promise<Workflow[]>((resolve) => {
                    workflowSettingApi.getWorkflowSummaryInSetting({ workflow_type: 'correction' }).then((res) => {
                        const { success, data, message } = res
                        if (!success) {
                            console.error('报错:' + message);
                            return
                        }
                        resolve(data?.map(item => ({ ...item })) ?? [])
                    })
                })
            },
            getQuestionTypeList: function (): Promise<QuestionType[]> {
                return new Promise<QuestionType[]>((resolve) => {
                    questionTypeApi.getQuestionTypeSummaries(true).then((res) => {
                        const { success, data, message } = res
                        if (!success) {
                            console.error('报错:' + message);
                            return
                        }
                        resolve(data?.map(item => {
                            const { code, type_name } = item
                            return {
                                code,
                                type_name
                            } satisfies QuestionType
                        }) ?? [])
                    })
                })
            }
        })
    }, [setAllCallback])

    /**
     * 作者：张瀚
     * 说明：导航栏
     */
    const NavigationBar: React.FC = () => {
        return <div className="flex items-center justify-between" >
            <Button variant="outline" onClick={() => navigate(-1)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
            </Button>
            <div className="flex items-center justify-end">
                {
                    nowSchemaName !== 'public' && <Button disabled={!isReadyToPrint} variant="outline" className="mr-3" onClick={() => {
                        if (!nowTenantId || !nowId) {
                            return
                        }
                        const newPaperData = getPaperData()
                        const qrcodeBean = new AdmissionTicketQRCodeMsgBean(newPaperData.paper_content.answer_card.admissionTicketNumberInfoQuestionItemConfig.qrcodeMsg)
                        tenantPaperApi.updatePaperId(nowTenantId, schema_name, {
                            old_paper_id: nowPaperId,
                            new_paper_id: qrcodeBean.paperId ?? ''
                        }).then((res) => {
                            const { success, message } = res
                            if (!success) {
                                toast("保存失败", {
                                    description: message,
                                })
                                return
                            }
                            toast("保存成功", {
                                description: "更新成功，页面跳转到新地址，请稍候。。。",
                            })
                            setTimeout(() => {
                                navigate(`/answerSheetEditing/${schema_name}/${qrcodeBean.paperId ?? ''}`, { replace: true })
                            }, 1000);
                        }, () => {
                            toast("保存失败！请检查试卷ID是否符合UUID格式！")
                        })
                    }}>
                        <Save className="h-4 w-4 mr-2" />
                        替换试卷ID为二维码内试卷ID
                    </Button>
                }
                <Button disabled={!isReadyToPrint} onClick={() => {
                    if (!nowTenantId || !nowId) {
                        return
                    }
                    const newPaperData = getPaperData()
                    if (nowSchemaName === 'public') {
                        publicPaperApi.updatePaperContent(nowTenantId, {
                            paper_id: nowPaperId,
                            paper_content: newPaperData.paper_content
                        }).then((res) => {
                            const { success, message } = res
                            if (!success) {
                                toast("保存失败", {
                                    description: message,
                                })
                                return
                            }
                            toast("保存成功", {
                                description: "数据已经更新到后台",
                            })
                        })
                    } else {
                        tenantPaperApi.updatePaperContent(nowTenantId, schema_name, {
                            paper_id: nowPaperId,
                            paper_content: newPaperData.paper_content
                        }).then((res) => {
                            const { success, message } = res
                            if (!success) {
                                toast("保存失败", {
                                    description: message,
                                })
                                return
                            }
                            toast("保存成功", {
                                description: "数据已经更新到后台",
                            })
                        })
                    }
                }}>
                    <Save className="h-4 w-4 mr-2" />
                    保存
                </Button>
            </div>
        </div>
    }

    useEffect(() => {
        if (rootRef.current) {
            const rootDom = rootRef.current.parentElement?.parentElement
            if (rootDom) {
                const { height } = rootDom.getBoundingClientRect()
                const computedStyle = window.getComputedStyle(rootDom);
                const paddingTop = parseFloat(computedStyle.paddingTop);
                const paddingBottom = parseFloat(computedStyle.paddingBottom);
                const targetHeight = Math.floor(height - paddingTop - paddingBottom)
                setRootHeight(targetHeight ?? 0)
            }
        }
    }, [])

    return <div key={nowId} ref={rootRef}>
        <NavigationBar />
        {
            isWrong ? <div>无效的请求，请返回重试或联系管理员！</div> :
                <QuestionCard
                    componentDataListStore={componentDataListStore}
                    toolbarDataStore={toolbarDataStore}
                    paperDataStore={paperDataStore}
                    dataCallbackStore={dataCallbackStore}
                    className="w-full mt-2"
                    style={{ height: `${rootHeight - 50}px` }}
                />
        }
    </div>
}
export default AnswerSheetEditing