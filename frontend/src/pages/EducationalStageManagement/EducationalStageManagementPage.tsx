import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Search, Download, BarChart3, Grid3X3, List, Filter } from 'lucide-react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import EducationalStageGrid from './components/EducationalStageGrid';
import EducationalStageTable from './components/EducationalStageTable';
import EducationalStageForm from './components/EducationalStageForm';
import EducationalStageStats from './components/EducationalStageStats';
import { educationalStageApi } from '@/services/educationalStageApi';
import { 
  EducationalStage, 
  EducationalStageQueryParams, 
  EducationalStageFormData,
  DEFAULT_EDUCATIONAL_STAGE_QUERY,
  EDUCATIONAL_STAGE_SORT_OPTIONS,
  EDUCATIONAL_STAGE_STATUS_OPTIONS
} from '@/types/educationalStage';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

type ViewMode = 'grid' | 'table';
type ActiveTab = 'overview' | 'management';

const EducationalStageManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const [stages, setStages] = useState<EducationalStage[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<EducationalStageQueryParams>(DEFAULT_EDUCATIONAL_STAGE_QUERY);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0, totalPages: 0 });
  const [formOpen, setFormOpen] = useState(false);
  const [editingStage, setEditingStage] = useState<EducationalStage | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [stageToDelete, setStageToDelete] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [activeTab, setActiveTab] = useState<ActiveTab>('overview');

  useEffect(() => {
    loadStages();
    setSearchInput(queryParams.search || '');
  }, []);

  const loadStages = async (params?: Partial<EducationalStageQueryParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...DEFAULT_EDUCATIONAL_STAGE_QUERY, ...queryParams, ...params };
      const response = await educationalStageApi.getEducationalStages(finalParams);
      
      if (response.success && response.data) {
        setStages(response.data);
        if (response.pagination) {
          setPagination({
            current: response.pagination.page || 1,
            pageSize: response.pagination.page_size || 10,
            total: response.pagination.total || 0,
            totalPages: response.pagination.total_pages || 1,
          });
        }
        setQueryParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load educational stages:', error);
      toast.error('加载学段列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (data: EducationalStageFormData) => {
    try {
      setFormLoading(true);
      const validatedData = { ...data, order_level: Number(data.order_level) };

      if (editingStage) {
        const response = await educationalStageApi.updateEducationalStage(editingStage.id, validatedData);
        if (response.success) {
          toast.success('学段更新成功');
          setFormOpen(false);
          await loadStages();
        }
      } else {
        const response = await educationalStageApi.createEducationalStage(validatedData);
        if (response.success) {
          toast.success('学段创建成功');
          setFormOpen(false);
          await loadStages();
        }
      }
    } catch (error: any) {
      console.error('Failed to save educational stage:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await educationalStageApi.toggleEducationalStageStatus(id, isActive);
      if (response.success) {
        toast.success(`学段已${isActive ? '启用' : '禁用'}`);
        await loadStages();
      }
    } catch (error: any) {
      console.error('Failed to toggle educational stage status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  const handleDelete = (id: string) => {
    setDeleteDialogOpen(true);
    setStageToDelete(id);
  };

  const confirmDeleteStage = async () => {
    if (!stageToDelete) return;
    try {
      const response = await educationalStageApi.deleteEducationalStage(stageToDelete);
      if (response.success) {
        toast.success('学段删除成功');
        await loadStages();
      }
    } catch (error: any) {
      console.error('Failed to delete educational stage:', error);
      toast.error(error.response?.data?.message || '删除失败');
    } finally {
      setDeleteDialogOpen(false);
      setStageToDelete(null);
    }
  };

  const handleCreateStage = () => {
    setEditingStage(undefined);
    setFormOpen(true);
  };

  const handleEditStage = (stage: EducationalStage) => {
    setEditingStage(stage);
    setFormOpen(true);
  };

  const handleViewGrades = (stage: EducationalStage) => {
    navigate(`/grades?stage_code=${stage.code}`);
  };

  const handleSearch = () => {
    loadStages({ ...queryParams, search: searchInput, page: 1 });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') handleSearch();
  };

  const handleFilterChange = (key: keyof EducationalStageQueryParams, value: any) => {
    loadStages({ ...queryParams, [key]: value, page: 1 });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    loadStages({ ...queryParams, page, page_size: pageSize || queryParams.page_size });
  };

  const handleExport = async () => {
    try {
      const blob = await educationalStageApi.exportEducationalStages(queryParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `educational-stages-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export educational stages:', error);
      toast.error('导出接口开发中');
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">学段管理</h1>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button onClick={handleCreateStage}>
            <Plus className="mr-2 h-4 w-4" />
            添加学段
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ActiveTab)}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="management">管理</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <EducationalStageStats stages={stages} />
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <CardTitle className="text-lg">学段列表</CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid3X3 className="mr-2 h-4 w-4" />
                    卡片
                  </Button>
                  <Button
                    variant={viewMode === 'table' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('table')}
                  >
                    <List className="mr-2 h-4 w-4" />
                    表格
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {viewMode === 'grid' ? (
                <EducationalStageGrid
                  stages={stages}
                  loading={loading}
                  onEdit={handleEditStage}
                  onDelete={handleDelete}
                  onToggleStatus={handleToggleStatus}
                  onViewGrades={handleViewGrades}
                />
              ) : (
                <EducationalStageTable
                  stages={stages}
                  loading={loading}
                  onEdit={handleEditStage}
                  onDelete={handleDelete}
                  onToggleStatus={handleToggleStatus}
                  onViewGrades={handleViewGrades}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="management" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                筛选与搜索
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索学段名称或代码..."
                    className="pl-10"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                  />
                </div>
                <Select
                  value={queryParams.is_active === undefined ? 'all' : String(queryParams.is_active)}
                  onValueChange={(value) => handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="全部状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {EDUCATIONAL_STAGE_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={String(option.value)} value={String(option.value)}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={queryParams.order_by ? `${queryParams.order_by}-${queryParams.order_direction || 'asc'}` : ''}
                  onValueChange={(value) => {
                    const [orderBy, orderDirection] = value.split('-');
                    handleFilterChange('order_by', orderBy);
                    handleFilterChange('order_direction', orderDirection);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="排序方式" />
                  </SelectTrigger>
                  <SelectContent>
                    {EDUCATIONAL_STAGE_SORT_OPTIONS.map((option) => (
                      <React.Fragment key={option.value}>
                        <SelectItem value={`${option.value}-asc`}>{option.label} ↑</SelectItem>
                        <SelectItem value={`${option.value}-desc`}>{option.label} ↓</SelectItem>
                      </React.Fragment>
                    ))}
                  </SelectContent>
                </Select>
                <Button onClick={handleSearch}>搜索</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <EducationalStageForm
        stage={editingStage}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除学段？</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除该学段，且无法恢复。是否继续？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteStage}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EducationalStageManagementPage;
