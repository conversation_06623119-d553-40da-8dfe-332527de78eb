import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power, 
  PowerOff,
  BookOpen,
  Calendar,
  Users,
  Hash
} from 'lucide-react';
import { EducationalStage } from '@/types/educationalStage';
import { cn } from '@/lib/utils';

interface EducationalStageGridProps {
  stages: EducationalStage[];
  loading: boolean;
  onEdit: (stage: EducationalStage) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  onViewGrades?: (stage: EducationalStage) => void;
}

const EducationalStageGrid: React.FC<EducationalStageGridProps> = ({
  stages,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  onViewGrades,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const handleToggleStatus = (stage: EducationalStage) => {
    onToggleStatus(stage.id, !stage.is_active);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (stages.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <BookOpen className="h-12 w-12 text-gray-400" />
        </div>
        <p className="text-muted-foreground">暂无学段数据</p>
        <p className="text-sm text-muted-foreground mt-2">点击"添加学段"开始创建</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {stages.map((stage) => (
        <Card 
          key={stage.id} 
          className={cn(
            "hover:shadow-lg transition-all duration-200 cursor-pointer group",
            !stage.is_active && "opacity-75 bg-gray-50"
          )}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-primary transition-colors">
                  {stage.name}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs font-mono">
                    {stage.code}
                  </Badge>
                  {stage.short_name && (
                    <Badge variant="secondary" className="text-xs">
                      {stage.short_name}
                    </Badge>
                  )}
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onViewGrades?.(stage)}>
                    <BookOpen className="mr-2 h-4 w-4" />
                    查看年级
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onEdit(stage)}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleToggleStatus(stage)}>
                    {stage.is_active ? (
                      <>
                        <PowerOff className="mr-2 h-4 w-4" />
                        禁用
                      </>
                    ) : (
                      <>
                        <Power className="mr-2 h-4 w-4" />
                        启用
                      </>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onDelete(stage.id)}
                    className="text-red-600 hover:!text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">排序:</span>
                <span className="font-medium">{stage.order_level}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">学制:</span>
                <span className="font-medium">{stage.duration_years}年</span>
              </div>
            </div>
            
            {stage.age_range && (
              <div className="flex items-center gap-2 text-sm">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">适龄:</span>
                <span className="font-medium">{stage.age_range}岁</span>
              </div>
            )}
            
            {stage.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {stage.description}
              </p>
            )}
            
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="flex items-center gap-2">
                <Badge
                  variant={stage.is_active ? "default" : "secondary"}
                  className={cn(
                    "text-xs",
                    stage.is_active
                      ? "bg-green-100 text-green-800 hover:bg-green-200"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  )}
                >
                  {stage.is_active ? '启用' : '禁用'}
                </Badge>
                {stage.is_standard && (
                  <Badge variant="outline" className="text-xs">
                    标准学段
                  </Badge>
                )}
              </div>
              <span className="text-xs text-muted-foreground">
                {formatDate(stage.created_at)}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default EducationalStageGrid; 