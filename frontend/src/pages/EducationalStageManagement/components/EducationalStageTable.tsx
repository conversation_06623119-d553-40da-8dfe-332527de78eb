import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power, 
  PowerOff,
  BookOpen,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { EducationalStage } from '@/types/educationalStage';
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface EducationalStageTableProps {
  stages: EducationalStage[];
  loading: boolean;
  onEdit: (stage: EducationalStage) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  onViewGrades?: (stage: EducationalStage) => void;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number, pageSize?: number) => void;
}

const EducationalStageTable: React.FC<EducationalStageTableProps> = ({
  stages,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  onViewGrades,
  pagination,
  onPageChange,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleToggleStatus = (stage: EducationalStage) => {
    onToggleStatus(stage.id, !stage.is_active);
  };

  const generatePageNumbers = () => {
    const pages = [];
    const totalPages = pagination.totalPages;
    const current = pagination.current;
    
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i);
        pages.push('...');
        pages.push(totalPages);
      } else if (current >= totalPages - 3) {
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push('...');
        for (let i = current - 1; i <= current + 1; i++) pages.push(i);
        pages.push('...');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>学段代码</TableHead>
                <TableHead>学段名称</TableHead>
                <TableHead>简称</TableHead>
                <TableHead>排序</TableHead>
                <TableHead>学制(年)</TableHead>
                <TableHead>年龄范围</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>标准学段</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1, 2, 3, 4].map((i) => (
                <TableRow key={i}>
                  <TableCell colSpan={11}>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (stages.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <BookOpen className="h-12 w-12 text-gray-400" />
        </div>
        <p className="text-muted-foreground">暂无学段数据</p>
        <p className="text-sm text-muted-foreground mt-2">点击"添加学段"开始创建</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">学段代码</TableHead>
              <TableHead className="w-[100px]">学段名称</TableHead>
              <TableHead className="w-[60px]">简称</TableHead>
              <TableHead className="w-[60px]">排序</TableHead>
              <TableHead className="w-[80px]">学制(年)</TableHead>
              <TableHead className="w-[100px]">年龄范围</TableHead>
              <TableHead className="w-[200px]">描述</TableHead>
              <TableHead className="w-[80px]">标准学段</TableHead>
              <TableHead className="w-[70px]">状态</TableHead>
              <TableHead className="w-[150px]">创建时间</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {stages.map((stage) => (
              <TableRow key={stage.id} className="hover:bg-gray-50">
                <TableCell className="font-medium">{stage.code}</TableCell>
                <TableCell>{stage.name}</TableCell>
                <TableCell>{stage.short_name}</TableCell>
                <TableCell>{stage.order_level}</TableCell>
                <TableCell>{stage.duration_years}年</TableCell>
                <TableCell>{stage.age_range}</TableCell>
                <TableCell className="line-clamp-2" title={stage.description}>
                  {stage.description || '-'}
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {stage.is_standard ? '是' : '否'}
                  </Badge>
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant={stage.is_active ? "default" : "secondary"}
                    className={cn(
                      "text-xs",
                      stage.is_active
                        ? "bg-green-100 text-green-800 hover:bg-green-200"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                  >
                    {stage.is_active ? '启用' : '禁用'}
                  </Badge>
                </TableCell>
                <TableCell>{formatDate(stage.created_at)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">打开菜单</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewGrades?.(stage)}>
                        <BookOpen className="mr-2 h-4 w-4" />
                        查看年级
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEdit(stage)}>
                        <Edit className="mr-2 h-4 w-4" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleStatus(stage)}>
                        {stage.is_active ? (
                          <>
                            <PowerOff className="mr-2 h-4 w-4" />
                            禁用
                          </>
                        ) : (
                          <>
                            <Power className="mr-2 h-4 w-4" />
                            启用
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => onDelete(stage.id)}
                        className="text-red-600 hover:!text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination.total > 0 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-muted-foreground">
            显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
            {pagination.total} 条记录
          </div>
          
          <div className="flex items-center gap-2">
            <Select
              value={String(pagination.pageSize)}
              onValueChange={(value) => onPageChange(1, Number(value))}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[10, 20, 50, 100].map(size => (
                  <SelectItem key={size} value={String(size)}>{size} 条/页</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(pagination.current - 1)}
                    disabled={pagination.current === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </PaginationItem>
                
                {generatePageNumbers().map((page, index) => (
                  <PaginationItem key={index}>
                    {page === '...' ? (
                      <span className="px-3 py-2 text-muted-foreground">...</span>
                    ) : (
                      <Button
                        variant={page === pagination.current ? "default" : "outline"}
                        size="sm"
                        onClick={() => onPageChange(page as number)}
                      >
                        {page}
                      </Button>
                    )}
                  </PaginationItem>
                ))}
                
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(pagination.current + 1)}
                    disabled={pagination.current === pagination.totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default EducationalStageTable;
