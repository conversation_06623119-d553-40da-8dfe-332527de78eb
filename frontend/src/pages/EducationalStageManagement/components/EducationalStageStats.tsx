import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  GraduationCap, 
  Users, 
  CheckCircle, 
  XCircle,
  TrendingUp,
  BookOpen
} from 'lucide-react';
import { EducationalStage } from '@/types/educationalStage';

interface EducationalStageStatsProps {
  stages: EducationalStage[];
}

const EducationalStageStats: React.FC<EducationalStageStatsProps> = ({ stages }) => {
  const totalStages = stages.length;
  const activeStages = stages.filter(stage => stage.is_active).length;
  const inactiveStages = totalStages - activeStages;
  const standardStages = stages.filter(stage => stage.is_standard).length;
  const totalDuration = stages.reduce((sum, stage) => sum + (stage.duration_years || 0), 0);

  const stats = [
    {
      title: '总学段数',
      value: totalStages,
      icon: GraduationCap,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: '系统中所有学段'
    },
    {
      title: '启用学段',
      value: activeStages,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: '当前可用的学段'
    },
    {
      title: '禁用学段',
      value: inactiveStages,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      description: '已停用的学段'
    },
    {
      title: '标准学段',
      value: standardStages,
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: '系统默认学段'
    },
    {
      title: '总学制年数',
      value: totalDuration,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: '所有学段学制总和'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default EducationalStageStats; 