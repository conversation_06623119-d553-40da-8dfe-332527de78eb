import React, {useState, useEffect} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs';
import {Plus, Search, Download, Upload, Users, BarChart3} from 'lucide-react';
import {toast} from 'sonner';

import TeacherTable from './components/TeacherTable';
import TeacherForm from './components/TeacherForm';
import TeacherDetail from './components/TeacherDetail';
import TeacherImportExport from '@/components/TeacherImportExport';
import {teachersApi} from '@/services/teacherApi';
import {
    TeacherFormData,
    TeacherListVO,
    TeacherSearchParams,
    TeacherDetailVO,
    DEFAULT_TEACHER_SEARCH,
    EMPLOYMENT_STATUS_OPTIONS,
} from '@/types/teacher';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';

const TeacherManagementPage: React.FC = () => {
    const identityInfo = getTenantInfoFromLocalStorage();
    const tenant_id = identityInfo?.tenant_id || "";
    const schema_name = identityInfo?.schema_name || "";

    // State management
    const [teachers, setTeachers] = useState<TeacherListVO[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchParams, setSearchParams] = useState<TeacherSearchParams>(DEFAULT_TEACHER_SEARCH);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });

    // Form state
    const [formOpen, setFormOpen] = useState(false);
    const [editingTeacher, setEditingTeacher] = useState<TeacherListVO | undefined>();
    const [formLoading, setFormLoading] = useState(false);

    // Detail state
    const [detailOpen, setDetailOpen] = useState(false);
    const [teacherDetail, setTeacherDetail] = useState<TeacherDetailVO | undefined>();
    const [detailLoading, setDetailLoading] = useState(false);

    // Import/Export state
    const [showImportExport, setShowImportExport] = useState(false);

    // Statistics state
    const [stats, setStats] = useState({
        total: 0,
        active: 0,
        inactive: 0,
        byStatus: {} as Record<string, number>,
    });

    // 处理搜索
    const handleSearch = (value: string) => {
        // 检查是否是工号（纯数字）
        const isEmployeeId = /^\d+$/.test(value);

        if (isEmployeeId) {
            // 如果是工号搜索，清空名称搜索
            loadTeachers({
                ...searchParams,
                page: 1,
                employee_id: value,
                name: undefined
            }).then();
        } else {
            // 否则按名称搜索
            loadTeachers({
                ...searchParams,
                page: 1,
                name: value || undefined,
                employee_id: undefined
            }).then();
        }
    };

    // 处理筛选条件变化
    const handleFilterChange = (key: keyof TeacherSearchParams, value: any) => {
        let processedValue = value;
        if (key === 'is_active') {
            if (value === 'true') processedValue = true;
            else if (value === 'false') processedValue = false;
            else if (value === 'all') processedValue = undefined;
        }

        const newParams = {...searchParams, [key]: processedValue, page: 1};
        console.log('Filter changed:', key, value, '->', processedValue, newParams);
        loadTeachers(newParams).then();
    };

    // 处理分页变化
    const handlePageChange = (page: number, pageSize: number) => {
        loadTeachers({...searchParams, page, page_size: pageSize}).then();
    };

    // Load teachers data
    const loadTeachers = async (params?: Partial<TeacherSearchParams>) => {
        try {
            setLoading(true);
            const finalParams = {...searchParams, ...params};

            const response = await teachersApi.pageAllTeacher(
                tenant_id,
                schema_name,
                {
                    page_params: {
                        page: finalParams.page || 1,
                        page_size: finalParams.page_size || 10,
                    },
                    name_like: finalParams.name,
                    ...(finalParams.employee_id && {employee_id: finalParams.employee_id}),
                    ...(finalParams.employment_status && {employment_status: finalParams.employment_status}),
                    ...(finalParams.is_active !== undefined && {is_active: finalParams.is_active}),
                }
            );
            if (response.success && response.data) {
                setTeachers(response.data);
                setPagination({
                    current: response.pagination.page,
                    pageSize: response.pagination.page_size,
                    total: response.pagination.total,
                    totalPages: response.pagination.total_pages,
                });
                setSearchParams(finalParams);
            }
        } catch (error) {
            console.error('Failed to load teachers:', error);
            toast.error('加载教师列表失败');
        } finally {
            setLoading(false);
        }
    };

    // Load statistics
    const loadStats = async () => {
        try {
            const statusResponse = await teachersApi.getTeachersCountByStatus();
            if (statusResponse.success) {
                const statusCounts = statusResponse.data;
                const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
                const active = statusCounts['在职'] || 0;
                const inactive = total - active;

                setStats({
                    total,
                    active,
                    inactive,
                    byStatus: statusCounts,
                });
            }
        } catch (error) {
            console.error('Failed to load statistics:', error);
        }
    };

    // Initialize data
    useEffect(() => {
        loadTeachers().then();
        loadStats().then();
    }, []);

    // Handle create teacher
    const handleCreateTeacher = () => {
        setEditingTeacher(undefined);
        setFormOpen(true);
    };

    // Handle edit teacher
    const handleEditTeacher = (teacher: TeacherListVO) => {
        // Convert TeacherListVO to Teacher for editing
        const teacherForEdit: TeacherListVO = {
            id: teacher.id,
            // tenant_id: '',
            // user_id: '',
            employee_id: teacher.employee_id,
            teacher_name: teacher.teacher_name,
            phone: teacher.phone,
            email: teacher.email,
            gender: teacher.gender,
            date_of_birth: teacher.date_of_birth,
            id_card_number: teacher.id_card_number,
            highest_education: teacher.highest_education,
            graduation_school: teacher.graduation_school,
            // major: teacher.major,
            hire_date: teacher.hire_date,
            employment_status: teacher.employment_status,
            title: teacher.title,
            teaching_subjects: teacher.teaching_subjects,
            // homeroom_class_id: teacher.homeroom_class_id,
            // grade_level_id: teacher.grade_level_id,
            // subject_group_id: teacher.subject_group_id,
            office_location: teacher.office_location,
            // bio: teacher.bio,
            is_active: teacher.is_active,
            // created_at:teacher.created_at,
            // updated_at: teacher.updated_at,
        };
        setEditingTeacher(teacherForEdit);
        setFormOpen(true);
    };

    // Handle view teacher detail
    const handleViewDetail = async (teacher: TeacherListVO) => {
        try {
            setDetailLoading(true);
            setDetailOpen(true);
            const response = await teachersApi.getTeacherDetail(teacher.id);
            if (response.success && response.data) {
                setTeacherDetail(response.data);
            }
        } catch (error) {
            console.error('Failed to load teacher detail:', error);
            toast.error('加载教师详情失败');
        } finally {
            setDetailLoading(false);
        }
    };

    // 切换教师状态
    const handleToggleStatus = async (id: string, isActive: boolean) => {
        try {
            const response = await teachersApi.toggleTeacherStatus(schema_name, id, isActive);
            if (response.success) {
                toast.success(`教师已${isActive ? '启用' : '禁用'}`);
                loadTeachers();
                loadStats(); // 刷新统计信息
            }
        } catch (error: any) {
            console.error('切换教师状态失败:', error);
            toast.error(error.response?.data?.message || '状态切换失败，请重试');
        }
    };

    // Handle form submit
    const handleFormSubmit = async (data: TeacherFormData) => {
        try {
            setFormLoading(true);
            if (editingTeacher) {
                console.log('handleFormSubmitupdate', data);
                const response = await teachersApi.updateTeacher(
                    tenant_id,
                    schema_name,
                    {
                        ...data
                    }
                );
                if (response.success) {
                    toast.success('教师信息更新成功');
                    setFormOpen(false);
                    await loadTeachers();
                    await loadStats();
                }
            } else {
                const response = await teachersApi.createTeacher(
                    tenant_id,
                    schema_name,
                    data
                );

                if (response.success) {
                    toast.success('教师创建成功');
                    setFormOpen(false);
                    await loadTeachers();
                    await loadStats();
                }
            }
        } catch (error: any) {
            console.error('Failed to save teacher:', error);
            toast.error(error.response?.data?.message || '保存失败');
        } finally {
            setFormLoading(false);
        }
    };

    // Handle delete teacher
    const handleDeleteTeacher = async (id: string) => {
        if (!confirm('确定要删除这个教师吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await teachersApi.deleteTeacher(id);
            if (response.success) {
                toast.success('教师删除成功');
                await loadTeachers();
                await loadStats();
            }
        } catch (error: any) {
            console.error('Failed to delete teacher:', error);
            toast.error(error.response?.data?.message || '删除失败');
        }
    };

    // Handle export
    const handleExport = async () => {
        try {
            const blob = await teachersApi.exportTeachers(searchParams);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `teachers-${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            toast.success('导出成功');
        } catch (error) {
            console.error('Failed to export teachers:', error);
            toast.error('导出失败');
        }
    };

    // Handle import complete
    const handleImportComplete = () => {
        loadTeachers();
        loadStats();
    };


    return (
        <div className="space-y-6 p-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">教师管理</h1>
                    <p className="text-gray-600 mt-1">管理系统中的教师信息，包括教师档案、任职分配和教学能力</p>
                </div>
                <div className="flex space-x-2">
                    <Button variant="outline" onClick={handleExport}>
                        <Download className="w-4 h-4 mr-2"/>
                        导出
                    </Button>
                    <Button variant="outline" onClick={() => setShowImportExport(true)}>
                        <Upload className="w-4 h-4 mr-2"/>
                        导入导出
                    </Button>
                    <Button onClick={handleCreateTeacher}>
                        <Plus className="w-4 h-4 mr-2"/>
                        新增教师
                    </Button>
                </div>
            </div>

            <Tabs defaultValue="list" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="list">教师列表</TabsTrigger>
                    <TabsTrigger value="statistics">统计分析</TabsTrigger>
                </TabsList>

                <TabsContent value="list">
                    <Card>
                        <CardHeader className="pb-2">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                <CardTitle className="text-lg">教师列表</CardTitle>
                                <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                                    <div className="flex flex-col sm:flex-row gap-3 w-full">
                                        <div className="relative w-full md:w-64">
                                            <button
                                                type="button"
                                                onClick={() => handleSearch(searchParams.name || '')}
                                                className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                                            >
                                                <Search className="h-4 w-4"/>
                                            </button>
                                            <Input
                                                placeholder="搜索教师姓名或工号..."
                                                className="w-full pl-8"
                                                value={searchParams.name || searchParams.employee_id || ''}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    const isEmployeeId = /^[a-zA-Z0-9-]+$/.test(value.trim());
                                                    setSearchParams(prev => ({
                                                        ...prev,
                                                        name: isEmployeeId ? undefined : value,
                                                        employee_id: isEmployeeId ? value : undefined,
                                                    }));
                                                }}
                                                onKeyDown={(e) => e.key === 'Enter' && handleSearch(e.currentTarget.value)}
                                            />
                                        </div>

                                        <Select
                                            value={searchParams.employment_status || 'all'}
                                            onValueChange={(value) =>
                                                handleFilterChange('employment_status', value === 'all' ? undefined : value)
                                            }
                                        >
                                            <SelectTrigger className="w-[180px]">
                                                <SelectValue placeholder="就职状态"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">全部状态</SelectItem>
                                                {EMPLOYMENT_STATUS_OPTIONS.map((option) => (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        <Select
                                            value={searchParams.is_active === undefined ? 'all' : searchParams.is_active ? 'active' : 'inactive'}
                                            onValueChange={(value) =>
                                                handleFilterChange('is_active', value === 'all' ? undefined : value === 'active')
                                            }
                                        >
                                            <SelectTrigger className="w-[120px]">
                                                <SelectValue placeholder="启用状态"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">全部</SelectItem>
                                                <SelectItem value="active">已启用</SelectItem>
                                                <SelectItem value="inactive">已禁用</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <TeacherTable
                                teachers={teachers}
                                loading={loading}
                                onEdit={handleEditTeacher}
                                onDelete={handleDeleteTeacher}
                                onViewDetail={handleViewDetail}
                                onToggleStatus={handleToggleStatus}
                                pagination={{
                                    current: pagination.current,
                                    pageSize: pagination.pageSize,
                                    total: pagination.total,
                                    onChange: handlePageChange,
                                }}
                            />
                        </CardContent>
                    </Card>
                </TabsContent>


                <TabsContent value="statistics">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">教师总数</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total}</div>
                                <p className="text-xs text-muted-foreground">全校教师总人数</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">在职教师</CardTitle>
                                <Users className="h-4 w-4 text-green-600"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">{stats.active}</div>
                                <p className="text-xs text-muted-foreground">
                                    占比 {stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}%
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">其他状态</CardTitle>
                                <BarChart3 className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.inactive}</div>
                                <p className="text-xs text-muted-foreground">离职、退休等状态</p>
                            </CardContent>
                        </Card>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>在职状态分布</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {Object.entries(stats.byStatus).map(([status, count]) => (
                                    <div key={status} className="flex items-center justify-between">
                                        <span className="text-sm font-medium">{status}</span>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-32 bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-blue-600 h-2 rounded-full"
                                                    style={{
                                                        width: stats.total > 0 ? `${(count / stats.total) * 100}%` : '0%'
                                                    }}
                                                />
                                            </div>
                                            <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            {/* Teacher Form Modal */}
            <TeacherForm
                teacher={editingTeacher}
                open={formOpen}
                onClose={() => setFormOpen(false)}
                onSubmit={handleFormSubmit}
                loading={formLoading}
            />

            {/* Teacher Detail Modal */}
            <TeacherDetail
                teacher={teacherDetail}
                open={detailOpen}
                onClose={() => setDetailOpen(false)}
                loading={detailLoading}
            />

            {/* Teacher Import Export Modal */}
            <TeacherImportExport
                open={showImportExport}
                onOpenChange={setShowImportExport}
                teachers={teachers}
                onImportComplete={handleImportComplete}
                tenantId={tenant_id}
                tenantName={schema_name}
            />
        </div>
    );
};

export default TeacherManagementPage;