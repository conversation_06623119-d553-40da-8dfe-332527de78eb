import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import SmartPagination from '@/components/ui/smart-pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {GUARDIAN_RELATIONS, StudentTableProps} from '@/types/student';
import {
  Calendar,
  Edit,
  MoreHorizontal,
} from 'lucide-react';
import React, {useRef } from 'react';
import { cn } from "@/lib/utils.ts";

const StudentTable: React.FC<StudentTableProps> = ({
                                                     students,
                                                     loading,
                                                     onEdit,
                                                     // onDelete,
                                                     // onViewDetail,
                                                     pagination,
                                                   }) => {
  const tableRef = useRef<HTMLDivElement>(null);

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'graduated':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'transferred':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    const statusMap = {
      'active': '在校',
      'inactive': '休学',
      'graduated': '毕业',
      'transferred': '转学',
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getProfileLevelColor = (level?: string) => {
    if (!level) return 'bg-gray-100 text-gray-600';

    const levelColors = {
      'A+': 'bg-red-100 text-red-800',
      'A': 'bg-red-100 text-red-700',
      'B+': 'bg-orange-100 text-orange-800',
      'B': 'bg-orange-100 text-orange-700',
      'C+': 'bg-yellow-100 text-yellow-800',
      'C': 'bg-yellow-100 text-yellow-700',
      'D+': 'bg-green-100 text-green-800',
      'D': 'bg-green-100 text-green-700',
    };
    return levelColors[level as keyof typeof levelColors] || 'bg-gray-100 text-gray-600';
  };

  if (loading) {
    return (
        <div className="space-y-4">
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>姓名</TableHead>
                  <TableHead>学号</TableHead>
                  <TableHead>性别</TableHead>
                  <TableHead>联系方式</TableHead>
                  <TableHead>班级</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>能力等级</TableHead>
                  <TableHead>入学时间</TableHead>
                  <TableHead className="w-[100px]">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      {Array.from({ length: 9 }).map((_, cellIndex) => (
                          <TableCell key={cellIndex}>
                            <div className="h-4 bg-gray-200 rounded animate-pulse" />
                          </TableCell>
                      ))}
                    </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
    );
  }

  if (students.length === 0) {
    return (
        <div className="text-center py-8">
          <div className="text-gray-500 text-lg mb-2">暂无学生数据</div>
          <div className="text-gray-400 text-sm">点击&#34;新增学生&#34;创建第一个学生档案</div>
        </div>
    );
  }

  return (
      <div className="space-y-4" ref={tableRef}>
        {/* 固定表头和首列的容器 */}
        <div className="rounded-lg border overflow-auto max-h-[70vh] relative">
          <Table className="min-w-full">
            {/* 固定表头实现 */}
            <TableHeader className="sticky top-0 bg-white z-10 shadow-sm">
              <TableRow>
                <TableHead className="sticky left-0 bg-white z-20 min-w-[120px] text-center">姓名</TableHead>
                <TableHead className="text-center min-w-[60px]">
                  学号
                </TableHead>
                <TableHead className="text-center min-w-[60px]">性别</TableHead>
                <TableHead className="text-center min-w-[100px]">出生日期</TableHead>
                <TableHead className="text-center min-w-[180px]">身份证号</TableHead>
                <TableHead className="text-center min-w-[120px]">联系电话</TableHead>
                <TableHead className="text-center min-w-[150px]">电子邮箱</TableHead>
                <TableHead className="text-center min-w-[200px]">家庭地址</TableHead>
                <TableHead className="text-center min-w-[120px]">监护人姓名</TableHead>
                <TableHead className="text-center min-w-[120px]">监护人电话</TableHead>
                <TableHead className="text-center min-w-[120px]">监护人关系</TableHead>
                <TableHead className="text-center min-w-[150px]">行政班班级</TableHead>
                <TableHead className="text-center min-w-[150px]">年级</TableHead>
                <TableHead className="text-center min-w-[120px]">入学日期</TableHead>
                <TableHead className="text-center min-w-[100px]">状态</TableHead>
                <TableHead className="text-center min-w-[100px]">能力等级</TableHead>
                <TableHead className="text-center min-w-[200px]">备注</TableHead>
                <TableHead className="sticky right-0 bg-white z-20 min-w-[100px] text-center">
                  操作
                </TableHead>
              </TableRow>
            </TableHeader>

            {/* 表格内容 */}
            <TableBody>
              {students.map((student) => (
                  <TableRow key={student.id} className="hover:bg-gray-50">
                    {/* 固定首列 - 姓名 */}
                    <TableCell className=" sticky left-0 bg-white z-10 text-center">
                      <div className="flex items-center justify-center">
                        {student.student_name}
                      </div>
                    </TableCell>

                    <TableCell className="font-medium text-center">
                      <div className="flex items-center justify-center">
                        {student.student_number}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <Badge variant="secondary" className="text-xs">
                        {student.gender || '-'}
                      </Badge>
                    </TableCell>

                    <TableCell className="text-sm text-gray-500 text-center">
                      <div className="flex items-center justify-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(student.birth_date)}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {student.id_number || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {student.phone || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {student.email || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {student.address || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        { student.guardian_name || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {student.guardian_phone || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {GUARDIAN_RELATIONS.find(relation => relation.value === student.guardian_relation)?.label || '-'}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      {student.class_name ? (
                          <div className="flex items-center justify-center">
                            {student.class_name}
                          </div>
                      ) : (
                          <span className="text-gray-400">未分配</span>
                      )}
                    </TableCell>

                    <TableCell className="text-center">
                      {student.grade_level_name ? (
                          <div className="flex items-center justify-center">
                            {student.grade_level_name}
                          </div>
                      ) : (
                          <span className="text-gray-400">未分配</span>
                      )}
                    </TableCell>

                    <TableCell className="text-sm text-gray-500 text-center">
                      <div className="flex items-center justify-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(student.enrollment_date)}
                      </div>
                    </TableCell>

                    <TableCell className="text-center">
                      <Badge
                          variant="secondary"
                          className={cn("text-xs", getStatusColor(student.status))}
                      >
                        {getStatusLabel(student.status)}
                      </Badge>
                    </TableCell>

                    <TableCell className="text-center">
                      {student.profile_level ? (
                        <Badge
                          variant="secondary"
                          className={cn("text-xs", getProfileLevelColor(student.profile_level))}
                        >
                          {student.profile_level}
                        </Badge>
                      ) : (
                        <span className="text-gray-400 text-xs">未评级</span>
                      )}
                    </TableCell>

                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        {student.notes || '-'}
                      </div>
                    </TableCell>

                    {/* 固定操作列 */}
                    <TableCell className="sticky right-0 bg-white z-10 text-center">
                      <div className="flex items-center justify-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            {/*<DropdownMenuItem*/}
                            {/*    onClick={() => onViewDetail(student)}*/}
                            {/*    className="cursor-pointer"*/}
                            {/*>*/}
                            {/*  <Eye className="mr-2 h-4 w-4" />*/}
                            {/*  查看详情*/}
                            {/*</DropdownMenuItem>*/}

                            <DropdownMenuItem
                                onClick={() => onEdit(student)}
                                className="cursor-pointer"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>

                            {/*<DropdownMenuItem*/}
                            {/*    onClick={() => onDelete(student.id)}*/}
                            {/*    className="cursor-pointer text-red-600 focus:text-red-600"*/}
                            {/*>*/}
                            {/*  <Trash2 className="mr-2 h-4 w-4" />*/}
                            {/*  删除*/}
                            {/*</DropdownMenuItem>*/}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* 分页组件 */}
        {pagination && pagination.total > 0 && (
          <SmartPagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={pagination.onChange}
          />
        )}
      </div>
  );
};

export default StudentTable;