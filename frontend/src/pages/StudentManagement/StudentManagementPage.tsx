import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Search, User, Upload } from 'lucide-react';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import StudentTable from './components/StudentTable';
import StudentForm from './components/StudentForm';
import StudentDetail from './components/StudentDetail';
import StudentImportExport from '@/components/StudentImportExport';
import { studentsApi } from '@/services/studentApi';
import { gradeApi } from '@/services/gradeApi';
import { ClassesApi } from '@/services/classesApi';
import {
  Student,
  StudentSearchParams,
  StudentFormData,
  StudentDetail as StudentDetailType,
  DEFAULT_STUDENT_SEARCH,
  FindAllStudentParams,
  STUDENT_STATUS_OPTIONS,
} from '@/types/student';
import {Input} from "@/components/ui/input.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@radix-ui/react-select';
import {useAuth} from "@/contexts/AuthContext.tsx";

const StudentManagementPage: React.FC = () => {
  const { tenant} = useAuth();
  const tenant_id = tenant?.tenant_id;
  const tenant_name = tenant?.schema_name;
  // State management
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<StudentSearchParams>(DEFAULT_STUDENT_SEARCH);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  });

  const [grades, setGrades] = useState<Array<{id: string, name: string, code: string}>>([]);
  const [classes, setClasses] = useState<Array<{id: string, name: string, code: string}>>([]);
  const [loadingAdditionalData, setLoadingAdditionalData] = useState(false);

  const [formOpen, setFormOpen] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // Detail state
  const [detailOpen, setDetailOpen] = useState(false);
  const [studentDetail, setStudentDetail] = useState<StudentDetailType | undefined>();
  const [detailLoading, setDetailLoading] = useState(false);

  // Import/Export state
  const [importExportOpen, setImportExportOpen] = useState(false);

  // Load students data
  const loadStudents = async (params?: Partial<StudentSearchParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...searchParams, ...params };

      const apiParams: FindAllStudentParams = {
        page_params: {
          page: finalParams.page || 1,
          page_size: finalParams.pageSize || 10,
        },
        student_number: finalParams.student_number,
        name_like: finalParams.name,
        phone: finalParams.phone,
      };

      const response = await studentsApi.pageAllStudent(tenant_id, tenant_name, apiParams);
      
      if (response.success && response.data) {
        setStudents(response.data);
        
        // Update pagination from API response
        setPagination({
          current: response.pagination?.page || 1,
          pageSize: response.pagination?.page_size || 10,
          total: response.pagination?.total || 0,
          totalPages: response.pagination?.total_pages || 1,
        });
        
        setSearchParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load students:', error);
      toast.error('加载学生列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAdditionalData = async () => {
    try {
      setLoadingAdditionalData(true);
      
      // Load grades and classes in parallel
      const [gradesResponse, classesResponse] = await Promise.all([
        gradeApi.getGradeSummaries(),
        ClassesApi.getClassesSummaries(tenant_name)
      ]);

      if (gradesResponse.success && gradesResponse.data) {
        setGrades(gradesResponse.data.map(grade => ({
          id: grade.id,
          name: grade.name,
          code: grade.code
        })));
      }

      if (classesResponse.success && classesResponse.data) {
        setClasses(classesResponse.data);
      }
    } catch (error) {
      console.error('Failed to load additional data:', error);
      toast.error('加载年级和班级数据失败');
    } finally {
      setLoadingAdditionalData(false);
    }
  };

  // Initialize data
  useEffect(() => {
    loadStudents();
    loadAdditionalData();
  }, []);

  // Handle search
  const handleSearch = (search: string) => {
    const trimmedSearch = search.trim();
    
    const searchParamsUpdate: Partial<StudentSearchParams> = {
      page: 1,
      name: undefined,
      student_number: undefined
    };

    if (trimmedSearch) {
      const isStudentNumber = /^[a-zA-Z0-9-]+$/.test(trimmedSearch);
      if (isStudentNumber) {
        searchParamsUpdate.student_number = trimmedSearch;
      } else {
        searchParamsUpdate.name = trimmedSearch;
      }
    }

    loadStudents({ ...searchParams, ...searchParamsUpdate });
  };

  // Handle filter changes (currently not used but kept for future use)
  const handleFilterChange = React.useCallback((key: keyof StudentSearchParams, value: any) => {
    const newParams = { ...searchParams, [key]: value, page: 1 };
    loadStudents(newParams);
  }, [searchParams]);

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    loadStudents({ 
      ...searchParams, 
      page, 
      pageSize,
      name: searchParams.page === page ? searchParams.name : undefined,
      student_number: searchParams.page === page ? searchParams.student_number : undefined
    });
  };

  const handleFormClose = () => {
    setFormOpen(false);
    setEditingStudent(undefined);
  };

  // Handle create student
  const handleCreateStudent = () => {
    setEditingStudent(undefined);
    setFormOpen(true);
  };

  // Handle edit student
  const handleEditStudent = (student: Student) => {
    setEditingStudent(student);
    setFormOpen(true);
  };

  // Handle view student detail
  const handleViewDetail = async (student: Student) => {
    try {
      setDetailLoading(true);
      setDetailOpen(true);
      const response = await studentsApi.getStudentDetail(tenant_id, tenant_name, student.id);
      if (response.success && response.data) {
        // Map the API response to match the StudentDetail type
        const detailData: StudentDetailType = {
          student: response.data,
          teaching_classes: [], // These would come from the API in a real implementation
          profile_levels: [],
          profile_tags: []
        };
        setStudentDetail(detailData);
      }
    } catch (error) {
      console.error('Failed to load student detail:', error);
      toast.error('加载学生详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  // 处理表单提交
  const handleFormSubmit = async (data: StudentFormData) => {
    try {
      setFormLoading(true);
      
      // 辅助函数：将日期格式化为 YYYY-MM-DD 格式
      const formatDateToYMD = (dateString: string | undefined) => {
        if (!dateString) return undefined;
        const date = new Date(dateString);
        // 检查日期是否有效
        if (isNaN(date.getTime())) return undefined;
        // 格式化为 YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // 准备学生数据，确保日期格式正确
      const studentData = {
        ...data,
        // 将日期格式化为 YYYY-MM-DD 格式
        birth_date: formatDateToYMD(data.birth_date),
        enrollment_date: formatDateToYMD(data.enrollment_date),
      };

      if (editingStudent) {
        // Update student
        const response = await studentsApi.updateStudent(tenant_id, tenant_name, {
          ...studentData,
          id: editingStudent.id,
        });
        if (response.success) {
          toast.success('学生信息更新成功');
          setFormOpen(false);
          loadStudents();
        }
      } else {
        // Create student
        const response = await studentsApi.createStudent(tenant_id, tenant_name, studentData);
        if (response.success) {
          toast.success('学生创建成功');
          setFormOpen(false);
          loadStudents();
        }
      }
    } catch (error: any) {
      console.error('Failed to save student:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete student
  const handleDeleteStudent = async (id: string) => {
    if (!confirm('确定要删除这个学生吗？此操作不可恢复。')) {
      return;
    }

    try {
      const response = await studentsApi.deleteStudent(tenant_id, tenant_name, id);
      if (response.success) {
        toast.success('学生删除成功');
        loadStudents();
      }
    } catch (error: any) {
      console.error('Failed to delete student:', error);
      toast.error(error.response?.data?.message || '删除失败');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">学生管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的学生信息，包括学生档案、班级关系和学业表现</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setImportExportOpen(true)}>
            <Upload className="w-4 h-4 mr-2" />
            导入导出
          </Button>
          <Button onClick={handleCreateStudent}>
            <Plus className="w-4 h-4 mr-2" />
            新增学生
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">学生列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <CardTitle className="text-lg">学生列表</CardTitle>
                
                <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                  <div className="relative w-full md:w-64">
                    <button 
                      type="button" 
                      onClick={() => handleSearch(searchParams.name || '')}
                      className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      <Search className="h-4 w-4" />
                    </button>
                    <Input
                      placeholder="搜索学生姓名或完整学号..."
                      className="w-full pl-8"
                      value={searchParams.name || searchParams.student_number || ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        const isStudentNumber = /^[a-zA-Z0-9-]+$/.test(value.trim());
                        setSearchParams(prev => ({
                          ...prev,
                          name: isStudentNumber ? undefined : value,
                          student_number: isStudentNumber ? value : undefined,
                        }));
                      }}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e.currentTarget.value)}
                    />

                  <div className="flex gap-2">
                    <Select
                      value={searchParams.status || 'all'}
                      onValueChange={(value) =>
                        handleFilterChange('status', value === 'all' ? undefined : value)
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="全部状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        {STUDENT_STATUS_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select
                      value={searchParams.profile_level || 'all'}
                      onValueChange={(value) =>
                        handleFilterChange('profile_level', value === 'all' ? undefined : value)
                      }
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="全部等级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部等级</SelectItem>
                        <SelectItem value="A+">A+</SelectItem>
                        <SelectItem value="A">A</SelectItem>
                        <SelectItem value="B+">B+</SelectItem>
                        <SelectItem value="B">B</SelectItem>
                        <SelectItem value="C+">C+</SelectItem>
                        <SelectItem value="C">C</SelectItem>
                        <SelectItem value="D+">D+</SelectItem>
                        <SelectItem value="D">D</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <StudentTable
                students={students}
                loading={loading}
                onEdit={handleEditStudent}
                onDelete={handleDeleteStudent}
                onViewDetail={handleViewDetail}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: handlePageChange,
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                学生统计分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                统计分析功能开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Student Form Modal */}
      <StudentForm
        student={editingStudent}
        open={formOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        loading={formLoading || loadingAdditionalData}
        grades={grades}
        classes={classes}
      />

      {/* Student Detail Modal */}
      <StudentDetail
        student={studentDetail}
        open={detailOpen}
        onClose={() => setDetailOpen(false)}
        loading={detailLoading}
      />

      {/* Student Import/Export Modal */}
      <StudentImportExport
        open={importExportOpen}
        onOpenChange={setImportExportOpen}
        students={students}
        onImportComplete={() => loadStudents()}
        tenantId={tenant_id}
        tenantName={tenant_name}
      />
    </div>
  );
};

export default StudentManagementPage;