// 策略相关类型定义
export interface CasbinPolicy {
  id?: string;
  policy_type: string;  // "permission" or "role"
  subject: string;
  domain: string;
  object?: string;
  action?: string;
  effect?: string;
  created_at?: string;
}

export interface PolicyStats {
  total_policies: number;
  permission_policies: number;
  role_policies: number;
  tenants: string[];
}

export interface PolicyTestRequest {
  tenant_id: string;
  subject: string;
  object: string;
  action: string;
}

export interface PolicyTestResponse {
  allowed: boolean;
  subject: string;
  object: string;
  action: string;
  tenant_id: string;
  matched_policies: CasbinPolicy[];
  test_timestamp: string;
}

export interface CreatePolicyRequest {
  tenant_id: string;
  policy_type: string;
  subject: string;
  object?: string;
  action?: string;
  effect?: string;
  role?: string;
}

export interface DeletePolicyRequest {
  tenant_id: string;
  policy_type: string;
  subject: string;
  object?: string;
  action?: string;
  effect?: string;
  role?: string;
}

export interface PolicyFilters {
  subject: string;
  object: string;
  action: string;
  policyType: string;
  tenantId: string;
}

export interface PolicyTableProps {
  policies: CasbinPolicy[];
  loading: boolean;
  onDelete: (policy: CasbinPolicy) => void;
  onEdit?: (policy: CasbinPolicy) => void;
}

export interface PolicyStatsProps {
  stats: PolicyStats | null;
  loading: boolean;
}

export interface PolicyFiltersProps {
  filters: PolicyFilters;
  onFiltersChange: (filters: PolicyFilters) => void;
  onRefresh: () => void;
} 