import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious, PaginationEllipsis } from '@/components/ui/pagination';
import {
  Plus, 
  TestTube,
  Download,
  Upload,
  RefreshCw,
  Trash2,
  Shield,
  Settings
} from 'lucide-react';

import { usePolicyManagement } from './hooks/usePolicyManagement';
import PolicyStats from './components/PolicyStats';
import PolicyFilters from './components/PolicyFilters';
import PolicyTable from './components/PolicyTable';
import CreatePolicyDialog from './components/CreatePolicyDialog';
import TestPermissionDialog from './components/TestPermissionDialog';
import { CasbinPolicy } from './types/policy';

// 分页辅助函数
const generatePagination = (currentPage: number, totalPages: number): (number | string)[] => {
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 4) {
    return [1, 2, 3, 4, 5, '...', totalPages];
  }

  if (currentPage >= totalPages - 3) {
    return [1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  return [
    1,
    '...',
    currentPage - 1,
    currentPage,
    currentPage + 1,
    '...',
    totalPages,
  ];
};

const CasbinPolicyManagementPage: React.FC = () => {
  const {
    policies,
    stats,
    loading,
    error,
    currentPage,
    totalPages,
    totalCount,
    filters,
    createPolicy,
    deletePolicy,
    testPermission,
    exportPolicies,
    importPolicies,
    clearPolicies,
    syncPolicies,
    updateFilters,
    setCurrentPage,
  } = usePolicyManagement();

  // 对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<CasbinPolicy | null>(null);
  const [importFile, setImportFile] = useState<File | null>(null);

  // 处理删除策略
  const handleDeletePolicy = async () => {
    if (!selectedPolicy) return;
    
    const success = await deletePolicy(selectedPolicy);
    if (success) {
      setIsDeleteDialogOpen(false);
      setSelectedPolicy(null);
    }
  };

  // 处理导入策略
  const handleImportPolicies = async () => {
    if (!importFile) return;
    
    const success = await importPolicies(importFile);
    if (success) {
      setIsImportDialogOpen(false);
      setImportFile(null);
    }
  };

  // 处理清除策略
  const handleClearPolicies = async () => {
    if (!confirm('确定要清除所有策略吗？此操作不可撤销！')) {
      return;
    }
    await clearPolicies();
  };

  return (
    <div className="space-y-6">
      {/* 页头 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Casbin策略管理</h1>
          <p className="text-slate-600 mt-2">
            管理Casbin权限策略和角色关系，支持策略测试和批量操作
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setIsTestDialogOpen(true)}>
            <TestTube className="h-4 w-4 mr-2" />
            权限测试
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            新建策略
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <PolicyStats stats={stats} loading={loading} />

      {/* 主要内容 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">策略列表</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={exportPolicies}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
              <Button variant="outline" size="sm" onClick={() => setIsImportDialogOpen(true)}>
                <Upload className="h-4 w-4 mr-2" />
                导入
              </Button>
              <Button variant="outline" size="sm" onClick={syncPolicies}>
                <RefreshCw className="h-4 w-4 mr-2" />
                同步
              </Button>
              <Button variant="destructive" size="sm" onClick={handleClearPolicies}>
                <Trash2 className="h-4 w-4 mr-2" />
                清空
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 筛选栏 */}
          <PolicyFilters 
            filters={filters}
            onFiltersChange={updateFilters}
            onRefresh={() => window.location.reload()}
          />

          {/* 错误提示 */}
          {error && (
            <div className="text-center py-4 text-red-500 bg-red-50 rounded-md">
              {error}
            </div>
          )}

          {/* 策略表格 */}
          <PolicyTable 
            policies={policies}
            loading={loading}
            onDelete={(policy) => {
              setSelectedPolicy(policy);
              setIsDeleteDialogOpen(true);
            }}
          />

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationPrevious
                    onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                    className={currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                  {generatePagination(currentPage, totalPages).map((page, index) => (
                    <PaginationItem key={index}>
                      {typeof page === 'string' ? (
                        <PaginationEllipsis />
                      ) : (
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ))}
                  <PaginationNext
                    onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
                    className={currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建策略对话框 */}
      <CreatePolicyDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={createPolicy}
        tenantId={filters.tenantId}
      />

      {/* 权限测试对话框 */}
      <TestPermissionDialog
        open={isTestDialogOpen}
        onOpenChange={setIsTestDialogOpen}
        onTest={testPermission}
        tenantId={filters.tenantId}
      />

      {/* 删除策略确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条策略吗？此操作无法撤销。
              <br />
              策略详情: {selectedPolicy?.subject} → {selectedPolicy?.object} | {selectedPolicy?.action}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeletePolicy}>
              删除
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 导入策略对话框 */}
      <AlertDialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>导入策略</AlertDialogTitle>
            <AlertDialogDescription>
              从JSON文件导入Casbin策略配置
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div>
              <label htmlFor="import-file" className="block text-sm font-medium mb-2">
                选择文件
              </label>
              <input
                id="import-file"
                type="file"
                accept=".json"
                onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-slate-50 file:text-slate-700 hover:file:bg-slate-100"
              />
            </div>
            
            {importFile && (
              <div className="p-3 bg-slate-50 rounded">
                <p className="text-sm">
                  <strong>文件名:</strong> {importFile.name}
                </p>
                <p className="text-sm">
                  <strong>大小:</strong> {(importFile.size / 1024).toFixed(2)} KB
                </p>
              </div>
            )}
          </div>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleImportPolicies} disabled={!importFile}>
              <Upload className="h-4 w-4 mr-2" />
              导入
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default CasbinPolicyManagementPage; 