import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, RefreshCw, Filter } from 'lucide-react';
import { PolicyFiltersProps } from '../types/policy';

const PolicyFilters: React.FC<PolicyFiltersProps> = ({ 
  filters, 
  onFiltersChange, 
  onRefresh 
}) => {
  const handleInputChange = (field: keyof typeof filters, value: string) => {
    onFiltersChange({ ...filters, [field]: value });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      subject: '',
      object: '',
      action: '',
      policyType: 'all',
      tenantId: filters.tenantId,
    });
  };

  return (
    <div className="bg-white rounded-lg border p-4 space-y-4">
      <div className="flex items-center gap-2">
        <Filter className="h-4 w-4 text-muted-foreground" />
        <h3 className="text-sm font-medium">筛选条件</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* 主体筛选 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="搜索主体..."
            value={filters.subject}
            onChange={(e) => handleInputChange('subject', e.target.value)}
            className="pl-10"
          />
        </div>
        
        {/* 对象筛选 */}
        <div>
          <Input
            placeholder="搜索对象..."
            value={filters.object}
            onChange={(e) => handleInputChange('object', e.target.value)}
          />
        </div>
        
        {/* 动作筛选 */}
        <div>
          <Input
            placeholder="搜索动作..."
            value={filters.action}
            onChange={(e) => handleInputChange('action', e.target.value)}
          />
        </div>
        
        {/* 策略类型筛选 */}
        <div>
          <Select 
            value={filters.policyType} 
            onValueChange={(value) => handleInputChange('policyType', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="策略类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="permission">权限策略</SelectItem>
              <SelectItem value="role">角色策略</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleClearFilters}
          >
            清除筛选
          </Button>
          <span className="text-sm text-muted-foreground">
            共找到 {filters.subject || filters.object || filters.action ? '?' : '全部'} 条策略
          </span>
        </div>
        
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onRefresh}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          刷新
        </Button>
      </div>
    </div>
  );
};

export default PolicyFilters; 