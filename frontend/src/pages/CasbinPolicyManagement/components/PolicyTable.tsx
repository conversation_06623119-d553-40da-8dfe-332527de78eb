import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Trash2, Edit, Eye, Shield } from 'lucide-react';
import { PolicyTableProps } from '../types/policy';

const PolicyTable: React.FC<PolicyTableProps> = ({ 
  policies, 
  loading, 
  onDelete, 
  onEdit 
}) => {
  const renderPolicyType = (type: string) => (
    <Badge variant={type === 'permission' ? 'default' : 'secondary'}>
      {type === 'permission' ? '权限策略' : '角色策略'}
    </Badge>
  );

  const renderEffect = (effect?: string) => {
    if (!effect) return '-';
    return (
      <Badge variant={effect === 'allow' ? 'default' : 'destructive'}>
        {effect === 'allow' ? '允许' : '拒绝'}
      </Badge>
    );
  };

  const renderTableLoading = () => (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[150px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[80px]" />
        </div>
      ))}
    </div>
  );

  if (loading) {
    return renderTableLoading();
  }

  if (policies.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground mb-4">
          <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium">暂无策略数据</h3>
          <p className="text-sm">当前没有找到匹配的策略</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[120px]">策略类型</TableHead>
            <TableHead>主体</TableHead>
            <TableHead>对象</TableHead>
            <TableHead>动作</TableHead>
            <TableHead className="w-[100px]">效果</TableHead>
            <TableHead className="w-[120px]">租户</TableHead>
            <TableHead className="w-[100px] text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {policies.map((policy) => (
            <TableRow key={policy.id || `${policy.subject}-${policy.object}-${policy.action}`}>
              <TableCell>{renderPolicyType(policy.policy_type)}</TableCell>
              <TableCell>
                <code className="text-sm bg-slate-100 px-2 py-1 rounded">
                  {policy.subject}
                </code>
              </TableCell>
              <TableCell>
                <code className="text-sm bg-slate-100 px-2 py-1 rounded">
                  {policy.object || '-'}
                </code>
              </TableCell>
              <TableCell>
                <span className="text-sm">{policy.action || '-'}</span>
              </TableCell>
              <TableCell>{renderEffect(policy.effect)}</TableCell>
              <TableCell>
                <span className="text-sm text-slate-600">{policy.domain}</span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  {onEdit && (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => onEdit(policy)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => onDelete(policy)}
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default PolicyTable; 