import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CheckCircle, XCircle, TestTube } from 'lucide-react';
import { PolicyTestRequest, PolicyTestResponse } from '../types/policy';

// 表单验证 Schema
const testFormSchema = z.object({
  subject: z.string().min(1, '主体不能为空'),
  object: z.string().min(1, '对象不能为空'),
  action: z.string().min(1, '动作不能为空'),
});

type TestFormValues = z.infer<typeof testFormSchema>;

interface TestPermissionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTest: (data: PolicyTestRequest) => Promise<PolicyTestResponse | null>;
  tenantId: string;
}

const TestPermissionDialog: React.FC<TestPermissionDialogProps> = ({
  open,
  onOpenChange,
  onTest,
  tenantId,
}) => {
  const [testResult, setTestResult] = useState<PolicyTestResponse | null>(null);
  const [testing, setTesting] = useState(false);

  const form = useForm<TestFormValues>({
    resolver: zodResolver(testFormSchema),
    defaultValues: {
      subject: '',
      object: '',
      action: '',
    }
  });

  const handleSubmit = async (data: TestFormValues) => {
    setTesting(true);
    try {
      const testData: PolicyTestRequest = {
        tenant_id: tenantId,
        subject: data.subject,
        object: data.object,
        action: data.action,
      };
      
      const result = await onTest(testData);
      setTestResult(result);
    } finally {
      setTesting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setTestResult(null);
    onOpenChange(false);
  };

  const renderTestResult = () => {
    if (!testResult) return null;

    return (
      <div className="mt-4 p-4 border rounded-md">
        <div className="flex items-center gap-2 mb-3">
          {testResult.allowed ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-500" />
          )}
          <span className="font-medium">
            测试结果: {testResult.allowed ? '允许访问' : '拒绝访问'}
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <Label>主体</Label>
            <p className="text-slate-600">{testResult.subject}</p>
          </div>
          <div>
            <Label>对象</Label>
            <p className="text-slate-600">{testResult.object}</p>
          </div>
          <div>
            <Label>动作</Label>
            <p className="text-slate-600">{testResult.action}</p>
          </div>
          <div>
            <Label>测试时间</Label>
            <p className="text-slate-600">
              {new Date(testResult.test_timestamp).toLocaleString()}
            </p>
          </div>
        </div>

        {testResult.matched_policies.length > 0 && (
          <div className="mt-4">
            <Label>匹配的策略</Label>
            <div className="mt-2 space-y-2">
              {testResult.matched_policies.map((policy, index) => (
                <div key={index} className="p-2 bg-slate-50 rounded text-sm">
                  {policy.subject} → {policy.object} | {policy.action} ({policy.effect})
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>权限测试</DialogTitle>
          <DialogDescription>
            测试指定主体对资源的访问权限
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>主体</FormLabel>
                  <FormControl>
                    <Input placeholder="用户ID或角色" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="object"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>对象</FormLabel>
                  <FormControl>
                    <Input placeholder="资源对象" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="action"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>动作</FormLabel>
                  <FormControl>
                    <Input placeholder="read, write, create, delete" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {renderTestResult()}

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                关闭
              </Button>
              <Button type="submit" disabled={testing}>
                <TestTube className="h-4 w-4 mr-2" />
                {testing ? '测试中...' : '开始测试'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default TestPermissionDialog; 