import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CreatePolicyRequest } from '../types/policy';

// 表单验证 Schema
const policyFormSchema = z.object({
  policy_type: z.enum(['permission', 'role'] as const),
  subject: z.string().min(1, '主体不能为空'),
  object: z.string().optional(),
  action: z.string().optional(),
  effect: z.string().optional(),
  role: z.string().optional(),
});

type PolicyFormValues = z.infer<typeof policyFormSchema>;

interface CreatePolicyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreatePolicyRequest) => Promise<boolean>;
  tenantId: string;
}

const CreatePolicyDialog: React.FC<CreatePolicyDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  tenantId,
}) => {
  const form = useForm<PolicyFormValues>({
    resolver: zodResolver(policyFormSchema),
    defaultValues: {
      policy_type: 'permission',
      subject: '',
      object: '',
      action: '',
      effect: 'allow',
      role: '',
    }
  });

  const handleSubmit = async (data: PolicyFormValues) => {
    const createData: CreatePolicyRequest = {
      tenant_id: tenantId,
      policy_type: data.policy_type,
      subject: data.subject,
      object: data.object,
      action: data.action,
      effect: data.effect || 'allow',
      role: data.role,
    };
    
    const success = await onSubmit(createData);
    if (success) {
      form.reset();
      onOpenChange(false);
    }
  };

  const policyType = form.watch('policy_type');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>新建策略</DialogTitle>
          <DialogDescription>
            创建新的Casbin权限策略或角色关系
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="policy_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>策略类型</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择策略类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="permission">权限策略</SelectItem>
                      <SelectItem value="role">角色策略</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>主体</FormLabel>
                  <FormControl>
                    <Input placeholder="用户ID或角色" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {policyType === 'permission' ? (
              <>
                <FormField
                  control={form.control}
                  name="object"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>对象</FormLabel>
                      <FormControl>
                        <Input placeholder="资源对象" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="action"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>动作</FormLabel>
                      <FormControl>
                        <Input placeholder="read, write, create, delete" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="effect"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>效果</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择效果" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="allow">允许</SelectItem>
                          <SelectItem value="deny">拒绝</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            ) : (
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色</FormLabel>
                    <FormControl>
                      <Input placeholder="角色名称" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit">
                创建
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePolicyDialog; 