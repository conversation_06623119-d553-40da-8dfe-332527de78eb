import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { policyApi } from '@/services/policyApi';
import { 
  CasbinPolicy, 
  PolicyStats, 
  CreatePolicyRequest, 
  DeletePolicyRequest,
  PolicyTestRequest,
  PolicyTestResponse,
  PolicyFilters 
} from '../types/policy';

export const usePolicyManagement = () => {
  const { tenant } = useAuth();
  const currentTenantId = tenant?.tenant_id || '';

  // 状态管理
  const [policies, setPolicies] = useState<CasbinPolicy[]>([]);
  const [stats, setStats] = useState<PolicyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  // 筛选状态
  const [filters, setFilters] = useState<PolicyFilters>({
    subject: '',
    object: '',
    action: '',
    policyType: 'all',
    tenantId: currentTenantId,
  });

  // 加载策略列表
  const loadPolicies = useCallback(async () => {
    if (!currentTenantId) return;
    
    try {
      setLoading(true);
      const params: any = {
        tenant_id: currentTenantId,
        page: currentPage,
        page_size: 20,
        subject: filters.subject || undefined,
        object: filters.object || undefined,
        action: filters.action || undefined,
      };
      
      if (filters.policyType !== 'all') {
        params.policy_type = filters.policyType;
      }
      
      const response = await policyApi.getPolicies(params);
      if (response.success && response.data) {
        setPolicies(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.total_pages || 1);
          setTotalCount(response.pagination.total || 0);
        }
      }
      setError(null);
    } catch (err: any) {
      setError(err.message || '加载策略列表失败');
      toast.error('加载策略列表失败');
    } finally {
      setLoading(false);
    }
  }, [currentTenantId, currentPage, filters]);

  // 加载统计信息
  const loadStats = useCallback(async () => {
    if (!currentTenantId) return;
    
    try {
      const response = await policyApi.getPolicyStats(currentTenantId);
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err: any) {
      console.error('加载统计信息失败:', err);
    }
  }, [currentTenantId]);

  // 创建策略
  const createPolicy = useCallback(async (data: CreatePolicyRequest) => {
    try {
      const response = await policyApi.createPolicy(data);
      if (response.success) {
        toast.success('策略创建成功');
        await loadPolicies();
        await loadStats();
        return true;
      }
      return false;
    } catch (err: any) {
      toast.error(err.message || '创建策略失败');
      return false;
    }
  }, [loadPolicies, loadStats]);

  // 删除策略
  const deletePolicy = useCallback(async (policy: CasbinPolicy) => {
    try {
      const deleteData: DeletePolicyRequest = {
        tenant_id: policy.domain,
        policy_type: policy.policy_type,
        subject: policy.subject,
        object: policy.object,
        action: policy.action,
        effect: policy.effect,
        role: policy.policy_type === 'role' ? policy.object : undefined,
      };
      
      const response = await policyApi.deletePolicy(deleteData);
      if (response.success) {
        toast.success('策略删除成功');
        await loadPolicies();
        await loadStats();
        return true;
      }
      return false;
    } catch (err: any) {
      toast.error(err.message || '删除策略失败');
      return false;
    }
  }, [loadPolicies, loadStats]);

  // 测试权限
  const testPermission = useCallback(async (data: PolicyTestRequest): Promise<PolicyTestResponse | null> => {
    try {
      const response = await policyApi.testPermission(data);
      if (response.success && response.data) {
        toast.success('权限测试完成');
        return response.data;
      }
      return null;
    } catch (err: any) {
      toast.error(err.message || '权限测试失败');
      return null;
    }
  }, []);

  // 导出策略
  const exportPolicies = useCallback(async () => {
    try {
      const response = await policyApi.exportPolicies(currentTenantId);
      if (response.success && response.data) {
        const dataStr = JSON.stringify(response.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `casbin-policies-${currentTenantId}-${Date.now()}.json`;
        link.click();
        URL.revokeObjectURL(url);
        toast.success('策略导出成功');
        return true;
      }
      return false;
    } catch (err: any) {
      toast.error(err.message || '导出策略失败');
      return false;
    }
  }, [currentTenantId]);

  // 导入策略
  const importPolicies = useCallback(async (file: File) => {
    try {
      const response = await policyApi.importPolicies(file, currentTenantId);
      if (response.success) {
        toast.success(`导入成功：${response.data.imported_policies} 条策略`);
        await loadPolicies();
        await loadStats();
        return true;
      }
      return false;
    } catch (err: any) {
      toast.error(err.message || '导入策略失败');
      return false;
    }
  }, [currentTenantId, loadPolicies, loadStats]);

  // 清除策略
  const clearPolicies = useCallback(async () => {
    try {
      const response = await policyApi.clearTenantPolicies(currentTenantId);
      if (response.success) {
        toast.success('策略清除成功');
        await loadPolicies();
        await loadStats();
        return true;
      }
      return false;
    } catch (err: any) {
      toast.error(err.message || '清除策略失败');
      return false;
    }
  }, [currentTenantId, loadPolicies, loadStats]);

  // 同步策略
  const syncPolicies = useCallback(async () => {
    try {
      const response = await policyApi.syncTenantPolicies(currentTenantId);
      if (response.success) {
        toast.success('策略同步成功');
        await loadPolicies();
        await loadStats();
        return true;
      }
      return false;
    } catch (err: any) {
      toast.error(err.message || '同步策略失败');
      return false;
    }
  }, [currentTenantId, loadPolicies, loadStats]);

  // 更新筛选条件
  const updateFilters = useCallback((newFilters: Partial<PolicyFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // 重置到第一页
  }, []);

  // 生命周期
  useEffect(() => {
    if (currentTenantId) {
      loadPolicies();
      loadStats();
    }
  }, [currentTenantId, currentPage, filters, loadPolicies, loadStats]);

  return {
    // 状态
    policies,
    stats,
    loading,
    error,
    currentPage,
    totalPages,
    totalCount,
    filters,
    
    // 操作方法
    loadPolicies,
    loadStats,
    createPolicy,
    deletePolicy,
    testPermission,
    exportPolicies,
    importPolicies,
    clearPolicies,
    syncPolicies,
    updateFilters,
    setCurrentPage,
  };
}; 