import React from 'react';
import { usePageFullWidth } from "@/hooks/useLayoutWidth.tsx";

// 导入shadcn-ui风格的组件
import { WorkspaceHeader } from './components/WorkspaceHeader';
import { MetricsCards } from './components/MetricsCards';
import { ActivityChart } from './components/ActivityChart';
import { RecentTasksCards } from './components/RecentTasksCards';
import QuickActionsGrid from './components/QuickActionsGrid';
import { LatestTeachingAids } from './components/LatestTeachingAids';
import { TeachingClasses } from './components/TeachingClasses';
import { AdministrativeClasses } from './components/AdministrativeClasses';

/**
 * 基于shadcn-ui dashboard-01设计的工作台页面
 * 采用现代化布局和组件设计
 */
const WorkspaceDashboard: React.FC = () => {
  usePageFullWidth();

  return (
    <div className="flex flex-1 flex-col gap-6 py-6">
      {/* 页面头部 */}
      <WorkspaceHeader />
      
      {/* 指标卡片 */}
      <MetricsCards />
      
      {/* 主要内容区 - 活动图表和快捷操作 */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-6 items-stretch">
        <div className="lg:col-span-4 h-full">
          <ActivityChart />
        </div>
        <div className="lg:col-span-3 h-full">
          <QuickActionsGrid />
        </div>
      </div>
        
      {/* 任务卡片 */}
      <RecentTasksCards />
      
      {/* 最新教辅 */}
      <LatestTeachingAids />

      {/* 班级管理区 - 双列布局 */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <TeachingClasses />
        <AdministrativeClasses />
      </div>
    </div>
  );
};

export default WorkspaceDashboard;
