import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  FileText, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Scan, 
  GraduationCap, 
  BookOpen,
  CheckCircle2
} from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
  status?: 'normal' | 'warning' | 'success';
  href?: string;
  onClick?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  description, 
  icon, 
  trend,
  status = 'normal',
  onClick
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'warning': return 'border-orange-200 bg-orange-50/50';
      case 'success': return 'border-green-200 bg-green-50/50';
      default: return '';
    }
  };

  const cardClassName = `${getStatusColor()} ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`;

  return (
    <Card className={cardClassName} onClick={onClick}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
        {trend && (
          <div className={`flex items-center text-xs mt-2 ${
            trend.isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            {trend.isPositive ? (
              <TrendingUp className="h-3 w-3 mr-1" />
            ) : (
              <TrendingDown className="h-3 w-3 mr-1" />
            )}
            {trend.isPositive ? '+' : ''}{trend.value}% {trend.label}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * 基于shadcn-ui设计的指标卡片组件
 * 展示关键业务指标和趋势
 */
export const MetricsCards: React.FC = () => {
  const navigate = useNavigate();
  
  const metrics = [
    {
      title: '在校学生',
      value: 1248,
      description: '当前在读学生总数',
      icon: <Users className="h-4 w-4" />,
      status: 'success' as const,
      trend: {
        value: 2.3,
        isPositive: true,
        label: '较上学期'
      },
      onClick: () => navigate('/students')
    },
    {
      title: '教学班级',
      value: 36,
      description: '活跃教学班级数量',
      icon: <GraduationCap className="h-4 w-4" />,
      trend: {
        value: 5.2,
        isPositive: true,
        label: '较上月'
      },
      onClick: () => navigate('/teaching-classes')
    },
    {
      title: '进行中考试',
      value: 5,
      description: '当前活跃考试数量',
      icon: <FileText className="h-4 w-4" />,
      trend: {
        value: 12,
        isPositive: true,
        label: '较上月'
      },
      onClick: () => navigate('/exam-management')
    },
    {
      title: '待评阅作业',
      value: 89,
      description: '等待评阅的作业',
      icon: <Scan className="h-4 w-4" />,
      status: 'warning' as const,
      trend: {
        value: 15,
        isPositive: false,
        label: '较昨日'
      },
      onClick: () => navigate('/homework-management')
    },
    {
      title: '教学资源',
      value: 456,
      description: '可用教辅资料数量',
      icon: <BookOpen className="h-4 w-4" />,
      trend: {
        value: 8.7,
        isPositive: true,
        label: '较上月'
      },
      onClick: () => navigate('/teaching-aids')
    },
    {
      title: '作业提交率',
      value: '92%',
      description: '最近一周按时提交占比',
      icon: <CheckCircle2 className="h-4 w-4" />,
      status: 'success' as const,
      trend: {
        value: 1.8,
        isPositive: true,
        label: '较上周'
      },
      onClick: () => navigate('/homework-management')
    }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};
