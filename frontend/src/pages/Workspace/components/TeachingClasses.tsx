import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, MoreHorizontal, GraduationCap, Loader2 } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { workspaceTeachingClassApi, WorkspaceTeachingClass, WorkspaceTeachingClassMetrics } from '@/services/teachingClassesApi';
import {useAuth} from "@/contexts/AuthContext.tsx";

// 使用API中的类型定义，移除本地重复定义

const getStatusBadge = (status: string) => {
  const variants = {
    active: { variant: 'default' as const, label: '进行中', color: 'bg-blue-50 text-blue-700 border-blue-200' },
    inactive: { variant: 'secondary' as const, label: '暂停', color: 'bg-gray-50 text-gray-700 border-gray-200' },
    completed: { variant: 'outline' as const, label: '已完成', color: 'bg-green-50 text-green-700 border-green-200' }
  };
  
  const config = variants[status as keyof typeof variants] || variants.active;
  return <Badge variant={config.variant} className={config.color}>{config.label}</Badge>;
};

const getSubjectColor = (subject: string) => {
  const colors = {
    '数学': 'bg-blue-100 text-blue-800',
    '英语': 'bg-green-100 text-green-800',
    '物理': 'bg-purple-100 text-purple-800',
    '化学': 'bg-orange-100 text-orange-800',
    '语文': 'bg-red-100 text-red-800',
    '生物': 'bg-emerald-100 text-emerald-800'
  };
  return colors[subject as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};

const TeachingClassCard: React.FC<{ teachingClass: WorkspaceTeachingClass; onClassClick: (teachingClass: WorkspaceTeachingClass) => void }> = ({ teachingClass, onClassClick }) => {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => onClassClick(teachingClass)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
              <GraduationCap className="h-4 w-4" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <CardTitle className="text-sm font-semibold line-clamp-1">
                  {teachingClass.name}
                </CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onClassClick(teachingClass); }}>查看详情</DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>编辑班级</DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>学生管理</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className={`text-xs ${getSubjectColor(teachingClass.subject)}`}>
                  {teachingClass.subject}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {teachingClass.grade}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-3">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-1 text-muted-foreground">
            <Users className="h-3 w-3" />
            <span>{teachingClass.studentCount}人</span>
          </div>
          <span className="text-xs text-muted-foreground">
            进度: {teachingClass.progress}%
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          {getStatusBadge(teachingClass.status)}
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <span>{teachingClass.teacher}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * 教学班管理组件
 * 展示和管理教学班级信息
 */
export const TeachingClasses: React.FC = () => {
  const navigate = useNavigate();
  const [teachingClasses, setTeachingClasses] = useState<WorkspaceTeachingClass[]>([]);
  const [metrics, setMetrics] = useState<WorkspaceTeachingClassMetrics>({ activeClasses: 0, totalStudents: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { tenant } = useAuth();
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 检查tenant是否存在
        if (!tenant?.tenant_id || !tenant?.schema_name) {
          setError('租户信息缺失');
          return;
        }
        
        // 并行获取教学班数据和统计信息
        const [classes, metricsData] = await Promise.all([
          workspaceTeachingClassApi.getTeachingClasses(tenant.tenant_id, tenant.schema_name, 6),
          workspaceTeachingClassApi.getTeachingClassMetrics(tenant.tenant_id, tenant.schema_name)
        ] as const);
        
        setTeachingClasses(classes);
        setMetrics(metricsData);
      } catch (err) {
        console.error('Failed to fetch teaching classes:', err);
        setError('加载教学班数据失败');
        // 设置默认数据作为降级方案
        setTeachingClasses([]);
        setMetrics({ activeClasses: 0, totalStudents: 0 });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tenant?.tenant_id, tenant?.schema_name]);
  
  const handleClassClick = (teachingClass: WorkspaceTeachingClass) => {
    navigate(`/teaching-classes/${teachingClass.id}`);
  };

  const handleCreateClass = () => {
    navigate('/teaching-classes');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <GraduationCap className="h-5 w-5 text-blue-600" />
              <span>教学班</span>
            </CardTitle>
            <CardDescription>
              管理各学科教学班级和课程安排
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={handleCreateClass}>
            全部教学班
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">加载中...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 text-sm text-muted-foreground">
            <span>{error}</span>
          </div>
        ) : teachingClasses.length === 0 ? (
          <div className="flex items-center justify-center py-8 text-sm text-muted-foreground">
            <span>暂无教学班数据</span>
          </div>
        ) : (
          <>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {teachingClasses.map((teachingClass) => (
                <TeachingClassCard key={teachingClass.id} teachingClass={teachingClass} onClassClick={handleClassClick} />
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <span>活跃班级: {metrics.activeClasses}</span>
                <span>总学生数: {metrics.totalStudents}</span>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
