import React, { useState } from 'react';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, ResponsiveContainer, Tooltip } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// 模拟数据
const chartData = [
  { date: '08-19', scans: 45, gradings: 32 },
  { date: '08-20', scans: 52, gradings: 28 },
  { date: '08-21', scans: 38, gradings: 45 },
  { date: '08-22', scans: 67, gradings: 38 },
  { date: '08-23', scans: 43, gradings: 52 },
  { date: '08-24', scans: 58, gradings: 41 },
  { date: '08-25', scans: 71, gradings: 35 },
  { date: '08-26', scans: 49, gradings: 48 }
];

/**
 * 基于shadcn-ui设计的活动趋势图
 * 展示扫描、评阅活动趋势
 */
export const formatXAxisLabel = (tickItem: string) => {
  return tickItem.slice(5); // 移除 "2024-" 前缀
};

export const ActivityChart: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1 text-center sm:text-left">
          <CardTitle>活动趋势</CardTitle>
          <CardDescription>
            显示最近的扫描、评阅活动趋势
          </CardDescription>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger
            className="w-[160px] rounded-lg sm:ml-auto"
            aria-label="选择时间范围"
          >
            <SelectValue placeholder="选择时间范围" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="7d" className="rounded-lg">
              最近7天
            </SelectItem>
            <SelectItem value="30d" className="rounded-lg">
              最近30天
            </SelectItem>
            <SelectItem value="90d" className="rounded-lg">
              最近90天
            </SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6 flex-1">
        <div className="aspect-auto h-[250px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="fillScans" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient id="fillGradings" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickCount={3}
              />
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="rounded-lg border bg-background p-2 shadow-sm">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex flex-col">
                            <span className="text-[0.70rem] uppercase text-muted-foreground">
                              扫描
                            </span>
                            <span className="font-bold text-muted-foreground">
                              {payload[0].value}
                            </span>
                          </div>
                          <div className="flex flex-col">
                            <span className="text-[0.70rem] uppercase text-muted-foreground">
                              评阅
                            </span>
                            <span className="font-bold text-muted-foreground">
                              {payload[1]?.value}
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Area
                dataKey="scans"
                type="natural"
                fill="url(#fillScans)"
                stroke="#3b82f6"
                stackId="a"
              />
              <Area
                dataKey="gradings"
                type="natural"
                fill="url(#fillGradings)"
                stroke="#10b981"
                stackId="a"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
