import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, MoreHorizontal, School, Loader2, AlertCircle } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { WorkspaceAdministrativeClassesApi, WorkspaceAdministrativeClass, WorkspaceAdministrativeClassMetrics } from '@/services/administrativeClassesApi';

// 使用API中定义的类型，添加显示所需的额外属性
type AdministrativeClass = WorkspaceAdministrativeClass & {
  name: string;
  grade: string;
  classTeacher: string;
  status: 'active' | 'graduated' | 'inactive';
  averageScore?: number;
  totalCapacity?: number;
};

// 数据转换函数
const transformToDisplayClass = (apiClass: WorkspaceAdministrativeClass): AdministrativeClass => ({
  ...apiClass,
  name: apiClass.className,
  grade: apiClass.gradeLevel || '未知',
  classTeacher: apiClass.teacherName || '未分配',
  status: apiClass.isActive ? 'active' as const : 'inactive' as const,
  totalCapacity: 50, // 默认容量
  averageScore: 0, // 暂无平均分数据
});

const getStatusBadge = (status: string) => {
  const variants = {
    active: { variant: 'default' as const, label: '在读', color: 'bg-blue-50 text-blue-700 border-blue-200' },
    graduated: { variant: 'outline' as const, label: '已毕业', color: 'bg-green-50 text-green-700 border-green-200' },
    inactive: { variant: 'secondary' as const, label: '停课', color: 'bg-gray-50 text-gray-700 border-gray-200' }
  };
  
  const config = variants[status as keyof typeof variants] || variants.active;
  return <Badge variant={config.variant} className={config.color}>{config.label}</Badge>;
};

const getGradeColor = (grade: string) => {
  const colors = {
    '高一': 'bg-green-100 text-green-800',
    '高二': 'bg-blue-100 text-blue-800', 
    '高三': 'bg-purple-100 text-purple-800',
    '一年级': 'bg-green-100 text-green-800',
    '二年级': 'bg-blue-100 text-blue-800',
    '三年级': 'bg-purple-100 text-purple-800'
  };
  return colors[grade as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};

const AdministrativeClassCard: React.FC<{ administrativeClass: AdministrativeClass; onClassClick: (administrativeClass: AdministrativeClass) => void }> = ({ administrativeClass, onClassClick }) => {
  
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => onClassClick(administrativeClass)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <div className="p-2 rounded-lg bg-purple-50 text-purple-600">
              <School className="h-4 w-4" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <CardTitle className="text-sm font-semibold line-clamp-1">
                  {administrativeClass.name}
                </CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onClassClick(administrativeClass); }}>查看详情</DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>学生名单</DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>成绩统计</DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>班级设置</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className={`text-xs ${getGradeColor(administrativeClass.grade)}`}>
                  {administrativeClass.grade}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {administrativeClass.academicYear}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-3">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-1 text-muted-foreground">
            <Users className="h-3 w-3" />
            <span>{administrativeClass.studentCount}/{administrativeClass.totalCapacity}人</span>
          </div>
          <span className="text-xs text-muted-foreground">
            {administrativeClass.averageScore ? (
              <span>平均分: {administrativeClass.averageScore}</span>
            ) : (
              <span>暂无成绩</span>
            )}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          {getStatusBadge(administrativeClass.status)}
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <span>{administrativeClass.classTeacher}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * 行政班管理组件
 * 展示和管理行政班级信息
 */
export const AdministrativeClasses: React.FC = () => {
  const navigate = useNavigate();
  const { tenant } = useAuth();
  
  const [classes, setClasses] = useState<AdministrativeClass[]>([]);
  const [metrics, setMetrics] = useState<WorkspaceAdministrativeClassMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!tenant?.tenant_id || !tenant?.schema_name) {
        setError('租户信息不可用');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const data = await WorkspaceAdministrativeClassesApi.getClassListWithMetrics(
          tenant.tenant_id,
          tenant.schema_name
        );
        
        const transformedClasses = data.classes.map(transformToDisplayClass);
        // 只显示前6个班级
        setClasses(transformedClasses.slice(0, 6));
        setMetrics(data.metrics);
      } catch (err) {
        console.error('Failed to fetch administrative classes:', err);
        setError('加载行政班数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tenant]);
  
  const handleClassClick = (administrativeClass: AdministrativeClass) => {
    navigate(`/administrative-classes/${administrativeClass.id}`);
  };

  const handleCreateClass = () => {
    navigate('/administrative-classes');
  };

  // 加载状态
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <School className="h-5 w-5 text-purple-600" />
            <span>行政班</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <School className="h-5 w-5 text-purple-600" />
            <span>行政班</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // 空状态
  if (classes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <School className="h-5 w-5 text-purple-600" />
                <span>行政班</span>
              </CardTitle>
              <CardDescription>
                管理学校行政班级和学生信息
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={handleCreateClass}>
              全部班级
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <School className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>暂无行政班数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <School className="h-5 w-5 text-purple-600" />
              <span>行政班</span>
            </CardTitle>
            <CardDescription>
              管理学校行政班级和学生信息
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={handleCreateClass}>
            全部班级
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {classes.map((administrativeClass) => (
            <AdministrativeClassCard key={administrativeClass.id} administrativeClass={administrativeClass} onClassClick={handleClassClick} />
          ))}
        </div>
        
        {metrics && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>总班级: {metrics.totalClasses}</span>
              <span>总学生: {metrics.totalStudents}人</span>
              <span>总教师: {metrics.totalTeachers}人</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
