import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Search, MoreHorizontal, FileText, Scan, GraduationCap, BookOpen, Calendar, Clock, Loader2, AlertCircle } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { WorkspaceHomeworkApi, WorkspaceHomeworkTask } from '@/services/homeworkApi';

interface Task {
  id: string;
  title: string;
  type: 'exam' | 'scan' | 'grading' | 'homework';
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  priority: 'high' | 'medium' | 'low';
  assignee: string;
  createdAt: string;
  progress?: number;
  dueDate?: string;
  subject?: string;
  grade?: string;
}

// 保留非作业类型的模拟数据
const mockNonHomeworkTasks: Task[] = [
  {
    id: '1',
    title: '高三数学月考试卷扫描',
    type: 'scan',
    status: 'in_progress',
    priority: 'high',
    assignee: '张老师',
    createdAt: '2024-08-26',
    progress: 75
  },
  {
    id: '2', 
    title: '高二英语作业评阅',
    type: 'grading',
    status: 'pending',
    priority: 'medium',
    assignee: '李老师',
    createdAt: '2024-08-25'
  },
  {
    id: '3',
    title: '期中考试安排',
    type: 'exam',
    status: 'completed',
    priority: 'high',
    assignee: '王老师',
    createdAt: '2024-08-24'
  }
];

const getStatusBadge = (status: string) => {
  const variants = {
    pending: { variant: 'secondary' as const, label: '待处理', color: 'bg-yellow-50 text-yellow-700 border-yellow-200' },
    in_progress: { variant: 'default' as const, label: '进行中', color: 'bg-blue-50 text-blue-700 border-blue-200' },
    completed: { variant: 'outline' as const, label: '已完成', color: 'bg-green-50 text-green-700 border-green-200' },
    error: { variant: 'destructive' as const, label: '异常', color: 'bg-red-50 text-red-700 border-red-200' }
  };
  
  const config = variants[status as keyof typeof variants] || variants.pending;
  return <Badge variant={config.variant} className={config.color}>{config.label}</Badge>;
};

const getTabLabel = (tab: string) => {
  const labels = {
    homework: '作业',
    exam: '考试',
    scan: '扫描',
    grading: '评阅'
  };
  return labels[tab as keyof typeof labels] || '';
};

const getPriorityBadge = (priority: string) => {
  const variants = {
    high: { variant: 'destructive' as const, label: '高', color: 'bg-red-50 text-red-700' },
    medium: { variant: 'secondary' as const, label: '中', color: 'bg-orange-50 text-orange-700' },
    low: { variant: 'outline' as const, label: '低', color: 'bg-gray-50 text-gray-700' }
  };
  
  const config = variants[priority as keyof typeof variants] || variants.medium;
  return <Badge variant={config.variant} className={config.color}>{config.label}</Badge>;
};

const getTypeIcon = (type: string) => {
  const icons = {
    exam: { icon: <FileText className="h-4 w-4" />, color: 'text-blue-600', bg: 'bg-blue-50' },
    scan: { icon: <Scan className="h-4 w-4" />, color: 'text-green-600', bg: 'bg-green-50' },
    grading: { icon: <GraduationCap className="h-4 w-4" />, color: 'text-purple-600', bg: 'bg-purple-50' },
    homework: { icon: <BookOpen className="h-4 w-4" />, color: 'text-orange-600', bg: 'bg-orange-50' }
  };
  
  return icons[type as keyof typeof icons] || icons.exam;
};

const TaskCard: React.FC<{ task: Task; onTaskClick: (task: Task) => void }> = ({ task, onTaskClick }) => {
  const typeConfig = getTypeIcon(task.type);
  
  return (
    <Card className="hover:shadow-md transition-shadow h-full flex flex-col cursor-pointer" onClick={() => onTaskClick(task)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-lg ${typeConfig.bg} ${typeConfig.color}`}>
              {typeConfig.icon}
            </div>
            <div>
              <CardTitle className="text-sm font-medium">{task.title}</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                {task.subject && task.grade && (
                  <span className="text-xs text-muted-foreground">
                    {task.grade} · {task.subject}
                  </span>
                )}
              </div>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onTaskClick(task); }}>查看详情</DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => e.stopPropagation()}>编辑</DropdownMenuItem>
              <DropdownMenuItem className="text-destructive" onClick={(e) => e.stopPropagation()}>删除</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0 flex-1">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            {getStatusBadge(task.status)}
            {getPriorityBadge(task.priority)}
          </div>
          
          {task.progress && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>进度</span>
                <span>{task.progress}%</span>
              </div>
              <Progress value={task.progress} className="h-1" />
            </div>
          )}
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{task.createdAt}</span>
            </div>
            {task.dueDate && (
              <div className="flex items-center space-x-1">
                <Calendar className="h-3 w-3" />
                <span>截止 {task.dueDate}</span>
              </div>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            负责人: {task.assignee}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * 基于shadcn-ui设计的任务卡片组件
 * 以卡片形式展示最近的任务和活动
 */
export const RecentTasksCards: React.FC = () => {
  const navigate = useNavigate();
  const { tenant } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | 'homework' | 'exam' | 'scan' | 'grading'>('all');
  const [homeworkTasks, setHomeworkTasks] = useState<WorkspaceHomeworkTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHomeworkTasks = async () => {
      if (!tenant?.tenant_id || !tenant?.schema_name) {
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const tasks = await WorkspaceHomeworkApi.getRecentHomeworkTasks(
          tenant.tenant_id,
          tenant.schema_name,
          6
        );
        
        setHomeworkTasks(tasks);
      } catch (err) {
        console.error('Failed to fetch homework tasks:', err);
        setError('加载作业任务失败');
      } finally {
        setLoading(false);
      }
    };

    fetchHomeworkTasks();
  }, [tenant]);

  const handleTaskClick = (task: Task) => {
    // 根据任务类型导航到不同页面
    switch (task.type) {
      case 'homework':
        navigate(`/homework-setting/${task.id}`);
        break;
      case 'exam':
        navigate(`/homework-setting/${task.id}`);
        break;
      case 'scan':
        navigate(`/homework-setting/${task.id}`);
        break;
      case 'grading':
        navigate(`/homework-setting/${task.id}`);
        break;
      default:
        navigate('/homework-management');
    }
  };

  // 合并真实作业数据和模拟的其他类型任务
  const allTasks: Task[] = [
    ...homeworkTasks,
    ...mockNonHomeworkTasks
  ];

  const filteredTasks = allTasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTab = activeTab === 'all' || task.type === activeTab;
    return matchesSearch && matchesTab;
  });

  const tasksToShow = filteredTasks.slice(0, 6);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>最近任务</CardTitle>
            <CardDescription>
              查看和管理最近的扫描、评阅、考试和作业任务
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索任务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 w-[200px]"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'all' | 'homework' | 'exam' | 'scan' | 'grading')} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="homework">作业</TabsTrigger>
            <TabsTrigger value="exam">考试</TabsTrigger>
            <TabsTrigger value="scan">扫描</TabsTrigger>
            <TabsTrigger value="grading">评阅</TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab} className="mt-4">
            {loading && activeTab === 'homework' ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>加载作业任务中...</span>
              </div>
            ) : error && activeTab === 'homework' ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : tasksToShow.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无{activeTab === 'all' ? '' : getTabLabel(activeTab)}任务</p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 items-stretch">
                {tasksToShow.map((task) => (
                  <TaskCard key={task.id} task={task} onTaskClick={handleTaskClick} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
