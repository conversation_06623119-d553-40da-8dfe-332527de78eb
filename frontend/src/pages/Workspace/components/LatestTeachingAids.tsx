import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BookOpen, Download, Eye, Star, Users, ArrowRight, Loader2 } from 'lucide-react';
import { workspaceTeachingAidApi, WorkspaceTeachingAid } from '@/services/teachingAidsApi';

// 使用API中的类型定义，移除本地重复定义

const getTypeConfig = (type: string) => {
  const configs = {
    exercise: { label: '练习', color: 'bg-blue-50 text-blue-700 border-blue-200' },
    test: { label: '测试', color: 'bg-purple-50 text-purple-700 border-purple-200' },
    material: { label: '资料', color: 'bg-green-50 text-green-700 border-green-200' },
    video: { label: '视频', color: 'bg-orange-50 text-orange-700 border-orange-200' }
  };
  return configs[type as keyof typeof configs] || configs.material;
};

const TeachingAidCard: React.FC<{ aid: WorkspaceTeachingAid; onAidClick: (aid: WorkspaceTeachingAid) => void }> = ({ aid, onAidClick }) => {
  const typeConfig = getTypeConfig(aid.type);
  
  return (
    <Card className="hover:shadow-md transition-shadow group cursor-pointer" onClick={() => onAidClick(aid)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
              <BookOpen className="h-4 w-4" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <CardTitle className="text-sm font-semibold line-clamp-1">
                  {aid.title}
                </CardTitle>
                {aid.isNew && (
                  <Badge variant="destructive" className="text-xs px-1.5 py-0.5 ml-2 flex-shrink-0">
                    新
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-muted-foreground">
                  {aid.grade} · {aid.subject}
                </span>
                <Badge variant="outline" className={`text-xs ${typeConfig.color}`}>
                  {typeConfig.label}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-3">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-1 text-muted-foreground">
            <Users className="h-3 w-3" />
            <span>{aid.downloads}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span className="text-muted-foreground">{aid.rating}</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">
            {aid.author}
          </span>
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={(e) => { e.stopPropagation(); onAidClick(aid); }}>
              <Eye className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={(e) => e.stopPropagation()}>
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * 最新教辅组件
 * 展示最新上传和热门的教学辅助资料
 */
export const LatestTeachingAids: React.FC = () => {
  const navigate = useNavigate();
  const [teachingAids, setTeachingAids] = useState<WorkspaceTeachingAid[]>([]);
  const [metrics, setMetrics] = useState({ todayNew: 0, weeklyPopular: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 并行获取教辅数据和统计信息
        const [aids, metricsData] = await Promise.all([
          workspaceTeachingAidApi.getLatestTeachingAids(8),
          workspaceTeachingAidApi.getTeachingAidMetrics()
        ]);
        
        setTeachingAids(aids);
        setMetrics({
          todayNew: metricsData.todayNew,
          weeklyPopular: metricsData.weeklyPopular
        });
      } catch (err) {
        console.error('Failed to fetch teaching aids:', err);
        setError('加载教辅资料失败');
        // 设置默认数据作为降级方案
        setTeachingAids([]);
        setMetrics({ todayNew: 3, weeklyPopular: 12 });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  
  const handleAidClick = (aid: WorkspaceTeachingAid) => {
    navigate(`/teaching-aids/textbook/${aid.id}`);
  };

  const handleViewAll = () => {
    navigate('/teaching-aids');
  };
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <span>最新教辅</span>
            </CardTitle>
            <CardDescription>
              最新上传的教学资料和练习材料
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" className="text-sm" onClick={handleViewAll}>
            查看全部
            <ArrowRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">加载中...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 text-sm text-muted-foreground">
            <span>{error}</span>
          </div>
        ) : teachingAids.length === 0 ? (
          <div className="flex items-center justify-center py-8 text-sm text-muted-foreground">
            <span>暂无教辅资料</span>
          </div>
        ) : (
          <>
            <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {teachingAids.map((aid) => (
                <TeachingAidCard key={aid.id} aid={aid} onAidClick={handleAidClick} />
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <span>今日新增: {metrics.todayNew}</span>
                <span>本周热门: {metrics.weeklyPopular}</span>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
