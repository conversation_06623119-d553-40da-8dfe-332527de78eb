import React, { use<PERSON>allback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  BookOpen, 
  Scan, 
  GraduationCap, 
  Users, 
  BarChart3,
  Target
} from 'lucide-react';

interface QuickAction {
  icon: React.ReactNode;
  label: string;
  href: string;
  description?: string;
}

/**
 * 快捷操作网格组件
 * 提供常用功能的快速访问入口
 */
const QuickActionsGrid: React.FC = () => {
  const navigate = useNavigate();
  
  const quickActions = useMemo<QuickAction[]>(() => [
    { 
      icon: <FileText className="h-5 w-5" />, 
      label: '创建考试', 
      href: '/exam-management',
      description: '创建新的考试任务'
    },
    { 
      icon: <BookOpen className="h-5 w-5" />, 
      label: '布置作业', 
      href: '/homework-management',
      description: '为班级布置新作业'
    },
    { 
      icon: <Scan className="h-5 w-5" />, 
      label: '扫描管理', 
      href: '/homework-management',
      description: '管理试卷扫描任务'
    },
    { 
      icon: <GraduationCap className="h-5 w-5" />, 
      label: '评阅中心', 
      href: '/homework-management',
      description: '进入评阅工作台'
    },
    { 
      icon: <Users className="h-5 w-5" />, 
      label: '班级管理', 
      href: '/teaching-classes',
      description: '管理班级和学生'
    },
    { 
      icon: <BarChart3 className="h-5 w-5" />, 
      label: '数据统计', 
      href: '/statistics',
      description: '查看详细统计报告'
    }
  ], []);

  const handleActionClick = useCallback((href: string) => {
    navigate(href);
  }, [navigate]);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Target className="h-5 w-5" />
          <span>快捷操作</span>
        </CardTitle>
        <CardDescription>常用功能的快速入口</CardDescription>
      </CardHeader>
      <CardContent className="flex-1">
        <div className="grid h-full grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3 place-content-center">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              aria-label={action.label}
              className="group w-full h-[100px] sm:h-[108px] rounded-xl border bg-card p-3 sm:p-4 flex flex-col items-center justify-center space-y-1.5 sm:space-y-2 text-foreground shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200 focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              onClick={() => handleActionClick(action.href)}
            >
              <div className="rounded-full p-1.5 sm:p-2 bg-primary/10 text-primary transition-colors group-hover:bg-primary/15">
                {action.icon}
              </div>
              <div className="text-center">
                <div className="text-sm font-medium">{action.label}</div>
                {action.description && (
                  <div className="text-xs text-muted-foreground mt-0.5 sm:mt-1 leading-tight line-clamp-2">
                    {action.description}
                  </div>
                )}
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActionsGrid;
