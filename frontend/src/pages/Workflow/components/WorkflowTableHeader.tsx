import {
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary,
    Tenant, WorkflowCategory,
    workflowCategoryOptions,
    WorkflowSettingQueryParams
} from "@/types";
import {FC, useEffect, useRef, useState} from "react"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";

interface WorkflowTableHeaderProps {
    questionTypeSummaries: QuestionTypeSummary[]
    subjectSummaries: SubjectSummary[]
    gradeLevelSummaries: GradeLevelSummary[]
    tenants: Tenant[]
    onFilterChange: (filter: WorkflowSettingQueryParams) => void;
}

const WorkflowTableHeader: FC<WorkflowTableHeaderProps> = ({
                                                               questionTypeSummaries,
                                                               subjectSummaries,
                                                               gradeLevelSummaries,
                                                               tenants,
                                                               onFilterChange,
                                                           }) => {

    const [workflowCategoryValue, setWorkflowCategoryValue] = useState<WorkflowCategory | '' | undefined>(undefined); // 默认值可自行设置
    const [questionTypeSelect, setQuestionTypeSelect] = useState<string | undefined>(undefined);
    const [subjectSelect, setSubjectSelect] = useState<string | undefined>(undefined);
    const [gradeLevelSelect, setGradeLevelSelect] = useState<string | undefined>(undefined);
    const [tenantSelect, setTenantSelect] = useState<string | undefined>(undefined);


    const firstRender = useRef(true);
    useEffect(() => {
        if (firstRender.current) {
            firstRender.current = false;
            return;
        }

        onFilterChange({
            workflow_type: workflowCategoryValue === '' ? undefined : workflowCategoryValue,
            question_type_code: questionTypeSelect === '' ? undefined : questionTypeSelect,
            subject_code: subjectSelect === '' ? undefined : subjectSelect,
            grade_level_code: gradeLevelSelect === '' ? undefined : gradeLevelSelect,
            schema_name: tenantSelect === '' ? undefined : tenantSelect,
        });
    }, [questionTypeSelect, subjectSelect, gradeLevelSelect, tenantSelect, workflowCategoryValue]);

    return (
        <div className="w-full flex justify-end mt-2">
            <div className="grid grid-cols-3 gap-4">
                <Select
                    value={workflowCategoryValue}
                    onValueChange={(value) =>
                        setWorkflowCategoryValue(value === 'none' ? '' : value as WorkflowCategory)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="请选择流程类型"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {workflowCategoryOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                                {option.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>


                <Select
                    value={tenantSelect}
                    onValueChange={(value) =>
                        setTenantSelect(value === 'none' ? '' : value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择租户"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {tenants.map((tenant) => (
                            <SelectItem key={String(tenant.schemaName)} value={String(tenant.schemaName)}>
                                {tenant.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                <Select
                    value={questionTypeSelect}
                    onValueChange={(value) =>
                        setQuestionTypeSelect(value === 'none' ? '' : value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择题型"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {questionTypeSummaries.map((questionType) => (
                            <SelectItem key={String(questionType.code)} value={String(questionType.code)}>
                                {questionType.type_name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                <Select
                    value={subjectSelect}
                    onValueChange={(value) =>
                        setSubjectSelect(value === 'none' ? '' : value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择学科"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {subjectSummaries.map((subject) => (
                            <SelectItem key={String(subject.code)} value={String(subject.code)}>
                                {subject.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                <Select
                    value={gradeLevelSelect}
                    onValueChange={(value) =>
                        setGradeLevelSelect(value === 'none' ? '' : value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择年级"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {gradeLevelSummaries.map((gradeLevel) => (
                            <SelectItem key={String(gradeLevel.code)} value={String(gradeLevel.code)}>
                                {gradeLevel.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
        </div>
    )
}

export default WorkflowTableHeader
