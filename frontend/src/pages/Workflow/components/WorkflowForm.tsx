import {<PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/components/ui/dialog.tsx";
import {MultiSelect, Option} from "@/components/ui/multi-select.tsx";
import React, {FC, useEffect, useState} from "react";
import {
    CreateWorkflowSettingRequest,
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary,
    Tenant, WorkflowCategory, workflowCategoryOptions,
    WorkflowSetting, WorkflowSummary
} from "@/types";
import {Button} from "@/components/ui/button.tsx";
import {HelpCircle, Loader2} from "lucide-react";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {workflowSettingApi} from "@/services/workflowApi.ts";
import {toast} from "sonner";
import {Label} from "@/components/ui/label.tsx";
import {Tooltip, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip.tsx";
import {Input} from "@/components/ui/input.tsx";
import {cn} from "@/lib/utils.ts";

interface WorkflowFormProps {
    workflowSetting?: WorkflowSetting;
    open: boolean;
    onClose: () => void;
    onSubmit: (data: CreateWorkflowSettingRequest) => Promise<void>;
    loading?: boolean;
    questionTypeSummaries: QuestionTypeSummary[]
    subjectSummaries: SubjectSummary[]
    gradeLevelSummaries: GradeLevelSummary[]
    tenants: Tenant[]
}

const WorkflowForm: FC<WorkflowFormProps> = ({
                                                 workflowSetting,
                                                 open,
                                                 onClose,
                                                 loading,
                                                 onSubmit,
                                                 questionTypeSummaries,
                                                 subjectSummaries,
                                                 gradeLevelSummaries,
                                                 tenants,
                                             }) => {
    const [workflowCategoryValue, setWorkflowCategoryValue] = useState<WorkflowCategory | undefined>(undefined); // 默认值可自行设置
    const [queryWorkflowLoading, setQueryWorkflowLoading] = useState(false)
    const [workflowSummary, setWorkflowSummary] = useState<WorkflowSummary[]>([]);

    // 转换成 Option[]
    const questionTypeOptions: Option[] = questionTypeSummaries.map((q) => ({
        value: q.code,
        label: q.type_name,
    }))
    const subjectOptions: Option[] = subjectSummaries.map((s) => ({
        value: s.code,
        label: s.name,
    }))
    const gradeOptions: Option[] = gradeLevelSummaries.map((g) => ({
        value: g.code,
        label: g.name,
    }))
    const tenantOptions: Option[] = tenants.map((t) => ({
        value: t.schemaName,
        label: t.name,
    }))

    // 状态
    const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<Option[]>([])
    const [selectedSubjects, setSelectedSubjects] = useState<Option[]>([])
    const [selectedGrades, setSelectedGrades] = useState<Option[]>([])
    const [selectedTenants, setSelectedTenants] = useState<Option[]>([])
    const [selectedWorkflow, setSelectedWorkflow] = useState<string | undefined>(undefined)
    const [inputPriority, setInputPriority] = useState<string | undefined>(undefined)
    const [error, setError] = useState<string | null>(null);

    const validate = (value: string) => {
        const num = Number(value);
        if (!/^\d+$/.test(value)) {
            return "请输入数字";
        }
        if (isNaN(num) || num < 1 || num > 100) {
            return "优先级需在 1 到 100 之间";
        }
        return null;
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setInputPriority(value);
        const err = validate(value);
        setError(err);
    };

    const initialData = async () => {
        if (workflowSetting) {
            setWorkflowCategoryValue(workflowSetting.workflow_type)
            await loadWorkflow(workflowSetting.workflow_type)
            setSelectedWorkflow(workflowSetting.workflow_id)
            setSelectedQuestionTypes(questionTypeOptions.filter(q => workflowSetting.question_type_codes.includes(q.value)))
            setSelectedSubjects(subjectOptions.filter(s => workflowSetting.subject_codes.includes(s.value)))
            setSelectedGrades(gradeOptions.filter(g => workflowSetting.grade_level_codes.includes(g.value)))
            setSelectedTenants(tenantOptions.filter(t => workflowSetting.schema_names.includes(t.value)))
            setInputPriority(workflowSetting.priority + '')
        } else {
            setWorkflowCategoryValue(undefined)
            setSelectedWorkflow(undefined)
            setSelectedQuestionTypes([])
            setSelectedSubjects([])
            setSelectedGrades([])
            setSelectedTenants([])
            setInputPriority(undefined)
        }
    }

    useEffect(() => {
        if (open) {
            initialData()
        }
    }, [open, workflowSetting]);

    const loadWorkflow = async (value: WorkflowCategory) => {
        setWorkflowCategoryValue(value)
        setWorkflowSummary([])
        try {
            setQueryWorkflowLoading(true)
            let response = await workflowSettingApi.getWorkflowSummary(value)
            if (response.success && response.data) {
                setWorkflowSummary(response.data)
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            toast.error('加载数据失败');
        } finally {
            setQueryWorkflowLoading(false)
        }
    }

    const handleCategorySelect = async (value: WorkflowCategory) => {
        setSelectedWorkflow('')
        await loadWorkflow(value)
    }

    const handleFormSubmit = async () => {
        await onSubmit({
            workflow_setting_id: workflowSetting?.id,
            workflow_id: selectedWorkflow ?? '',
            subject_codes: selectedSubjects.map(s => s.value),
            grade_level_codes: selectedGrades.map(g => g.value),
            question_type_codes: selectedQuestionTypes.map(q => q.value),
            schema_names: selectedTenants.map(t => t.value),
            priority:Number(inputPriority)
        })
    };

    const handleFormClose = () => {
        setSelectedWorkflow('')
        setSelectedQuestionTypes([])
        setSelectedSubjects([])
        setSelectedGrades([])
        setSelectedTenants([])
        setInputPriority(undefined)
        setError(null)
        onClose()
    }

    const isEditMode = !!workflowSetting;

    return (
        <Dialog open={open} onOpenChange={handleFormClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        {isEditMode ? '编辑工作流配置' : '新增工作流配置'}
                    </DialogTitle>
                </DialogHeader>
                <div className="space-y-6">
                    {/* 分类选择 */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="workflow-category" className="col-span-1">
                            流程类型
                        </Label>
                        <Select
                            value={workflowCategoryValue}
                            onValueChange={handleCategorySelect}
                        >
                            <SelectTrigger id="workflow-category" className="w-full col-span-3">
                                <SelectValue placeholder="请选择流程类型"/>
                            </SelectTrigger>
                            <SelectContent>
                                {workflowCategoryOptions.map(option => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    {/* 工作流选择 */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="workflow" className="col-span-1">选择工作流</Label>
                        <Select
                            value={selectedWorkflow}
                            onValueChange={setSelectedWorkflow}
                            disabled={queryWorkflowLoading}
                        >
                            <SelectTrigger id="workflow" className="w-full col-span-3">
                                <SelectValue placeholder={queryWorkflowLoading ? "加载中..." : "请选择工作流"}/>
                            </SelectTrigger>
                            <SelectContent>
                                {queryWorkflowLoading ? (
                                    <div className="p-2 text-muted-foreground text-sm">加载中...</div>
                                ) : !workflowCategoryValue ? (
                                    <div className="p-2 text-muted-foreground text-sm">请先选择流程类型</div>
                                ) : workflowSummary.length === 0 ? (
                                    <div className="p-2 text-muted-foreground text-sm">暂无数据</div>
                                ) : (
                                    workflowSummary.map(option => (
                                        <SelectItem key={option.workflow_id} value={option.workflow_id}>
                                            {option.workflow_name}
                                        </SelectItem>
                                    ))
                                )}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* 条件筛选（多选） */}
                    {selectedWorkflow && (
                        <>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <div className="flex items-center gap-1 col-span-1">
                                    <Label>筛选维度</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <HelpCircle className="w-4 h-4 text-muted-foreground cursor-pointer"/>
                                            </TooltipTrigger>
                                            <TooltipContent side="top">
                                                <p>选填，若不提供则视为通用工作流</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <div className="col-span-4 grid grid-cols-2 md:grid-cols-2 gap-2">
                                    <MultiSelect
                                        value={selectedQuestionTypes}
                                        onChange={setSelectedQuestionTypes}
                                        options={questionTypeOptions}
                                        placeholder="所有题型"
                                        hidePlaceholderWhenSelected={true}
                                    />
                                    <MultiSelect
                                        value={selectedSubjects}
                                        onChange={setSelectedSubjects}
                                        options={subjectOptions}
                                        placeholder="所有学科"
                                        hidePlaceholderWhenSelected={true}
                                    />
                                    <MultiSelect
                                        value={selectedGrades}
                                        onChange={setSelectedGrades}
                                        options={gradeOptions}
                                        placeholder="所有年级"
                                        hidePlaceholderWhenSelected={true}
                                    />
                                    <MultiSelect
                                        value={selectedTenants}
                                        onChange={setSelectedTenants}
                                        options={tenantOptions}
                                        placeholder="所有租户"
                                        hidePlaceholderWhenSelected={true}
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="priority" className="col-span-1">
                                    优先级
                                </Label>
                                <div className="col-span-3">
                                    <Input
                                        id="priority"
                                        type="text"
                                        inputMode="numeric"
                                        pattern="[0-9]*"
                                        value={inputPriority}
                                        onChange={handleChange}
                                        className={cn(error && "border-red-500")}
                                        placeholder={"默认使用的优先级，较小值优先，默认100"}
                                    />
                                    {error && (
                                        <p className="text-sm text-red-500 mt-1">{error}</p>
                                    )}
                                </div>
                            </div>
                        </>
                    )}

                    {/* 底部按钮 */}
                    <DialogFooter className="pt-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleFormClose}
                            disabled={loading}
                        >
                            取消
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading || !!error}
                            onClick={handleFormSubmit}
                        >
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>}
                            {isEditMode ? '更新' : '创建'}
                        </Button>
                    </DialogFooter>
                </div>
            </DialogContent>
        </Dialog>
    )
};

export default WorkflowForm;
