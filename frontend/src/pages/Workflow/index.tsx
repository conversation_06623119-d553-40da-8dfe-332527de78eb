import {<PERSON>, CardContent, <PERSON>Header, CardTitle} from "@/components/ui/card.tsx";
import WorkflowForm from "@/pages/Workflow/components/WorkflowForm.tsx";
import WorkflowTable from "@/pages/Workflow/components/WorkflowTable.tsx";
import WorkflowTableHeader from "@/pages/Workflow/components/WorkflowTableHeader.tsx";
import {useEffect, useState} from "react";
import {questionTypeApi} from "@/services/questionApi.ts";
import subjectApi from "@/services/subjectApi.ts";
import gradeApi from "@/services/gradeApi.ts";
import {toast} from "sonner";
import {
    DEFAULT_WORKFLOW_SETTING_QUERY,
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary,
    WorkflowSettingQueryParams,
    WorkflowSetting,
    Tenant,
    CreateWorkflowSettingRequest, WorkflowSummary,
} from "@/types";
import {getTenantSummaries} from "@/services/tenantApi.ts";
import {workflowSetting<PERSON><PERSON>} from "@/services/workflowApi.ts";
import {Tabs<PERSON>ontent, Ta<PERSON>List, TabsTrigger} from "@/components/ui/tabs.tsx";
import {Tabs} from "@radix-ui/react-tabs";
import {Button} from "@/components/ui/button.tsx";
import {Plus} from "lucide-react";
import WorkflowSettingFilterPreview from "@/pages/Workflow/components/WorkflowSettingFilterPreview.tsx";

const WorkflowPage = () => {

    const [loading, setLoading] = useState(false);
    const [workflowSettings, setWorkflowSettings] = useState<WorkflowSetting[]>([])
    const [workflowSummaryInSetting, setWorkflowSummaryInSetting] = useState<WorkflowSummary[]>([])
    const [workflowSettingQueryParams, setWorkflowSettingQueryParams] = useState<WorkflowSettingQueryParams>(DEFAULT_WORKFLOW_SETTING_QUERY);
    const [workflowSettingsPagination, setWorkflowSettingsPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });

    const [questionTypeSummaries, setQuestionTypeSummaries] = useState<QuestionTypeSummary[]>([])
    const [subjectSummaries, setSubjectSummaries] = useState<SubjectSummary[]>([])
    const [gradeLevelSummaries, setGradeLevelSummaries] = useState<GradeLevelSummary[]>([])
    const [tenants, setTenants] = useState<Tenant[]>([]);
    const [formOpen, setFormOpen] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [editingWorkflowSetting, setEditingWorkflowSetting] = useState<WorkflowSetting>();

    const loadWorkflowSetting = async (params?: Partial<WorkflowSettingQueryParams>) => {
        try {
            setLoading(true)
            const finalParams = {...workflowSettingQueryParams, ...params};
            let response = await workflowSettingApi.getWorkflowSetting(finalParams)
            setWorkflowSettings(response.data)
            setWorkflowSettingsPagination({
                current: response.pagination.page,
                pageSize: response.pagination.page_size,
                total: response.pagination.total,
                totalPages: response.pagination.total_pages,
            });
            setWorkflowSettingQueryParams(finalParams);
        } catch (error) {
            console.error('Failed to load settings:', error);
            toast.error('加载数据失败');
        } finally {
            setLoading(false)
        }
    }


    const loadWorkflowSummaryInSetting = async (params?: Partial<WorkflowSettingQueryParams>) => {
        try {
            setLoading(true)
            let response = await workflowSettingApi.getWorkflowSummaryInSetting(params)
            console.log(response)
            setWorkflowSummaryInSetting(response.data??[])
        } catch (error) {
            console.error('Failed to load settings:', error);
            toast.error('加载数据失败');
        } finally {
            setLoading(false)
        }
    }

    const loadComposeSelection = async () => {
        try {
            setLoading(true)
            const [questionTypesRes, subjectsRes, gradesRes, tenantsRes] = await Promise.all([
                questionTypeApi.getQuestionTypeSummaries(),
                subjectApi.getSubjectSummaries(),
                gradeApi.getGradeSummaries(),
                getTenantSummaries()
            ]);

            setQuestionTypeSummaries(questionTypesRes.data ?? [])
            setSubjectSummaries(subjectsRes.data ?? [])
            setGradeLevelSummaries(gradesRes.data ?? [])
            setTenants(tenantsRes.data ?? [])
        } catch (error) {
            console.error('Failed to load summaries:', error);
            toast.error('加载数据失败');
        } finally {
            setLoading(false);
        }
    }

    const handleCreateWorkflowSetting = () => {
        setEditingWorkflowSetting(undefined);
        setFormOpen(true);
    };

    const handleEditWorkflowSetting = (workflowSetting: WorkflowSetting) => {
        setEditingWorkflowSetting(workflowSetting);
        setFormOpen(true);
    };

    const handleWorkflowPageChange = (page: number, pageSize: number) => {
        loadWorkflowSetting({...workflowSettingQueryParams, page, page_size: pageSize});
    };

    const handleFormSubmit = async (data: CreateWorkflowSettingRequest) => {
        try {
            setFormLoading(true);

            const validatedData: CreateWorkflowSettingRequest = {
                ...data,
            };
            if (editingWorkflowSetting) {
                console.log(validatedData)
                let res = await workflowSettingApi.updateWorkflowSetting(validatedData)
                if (res.success) {
                    toast.success('工作流更新成功');
                    setFormOpen(false);
                    await loadWorkflowSetting();
                }
            } else {
                let res = await workflowSettingApi.createWorkflowSetting(validatedData)
                if (res.success) {
                    toast.success('工作流创建成功');
                    setFormOpen(false);
                    await loadWorkflowSetting();
                }
            }
        } catch
            (error: any) {
            console.error('Failed to save setting:', error);
            toast.error(error.response?.data?.message || '保存失败');
        } finally {
            setFormLoading(false);
        }
    }

    const handleDeleteWorkflowSetting = async (id: string) => {
        try {
            let res = await workflowSettingApi.deleteWorkflowSetting(id)
            if (res.success) {
                toast.success('工作流删除成功');
                setFormOpen(false);
                await loadWorkflowSetting();
            }
        } catch (error: any) {
            console.error('Failed to delete question type:', error);
            toast.error(error.response?.data?.message || '删除失败');
        }

    };

    const [tabsValue, setTabsValue] = useState('list')

    const tabsChange = async (value: string) => {
        setTabsValue(value)
        if (value === 'filterPreview') {
            console.log('筛选器预览');
        } else {
            await loadWorkflowSetting()
            await loadComposeSelection();
        }
    }

    useEffect(() => {
        loadWorkflowSetting()
        loadComposeSelection();
    }, []);

    return (
        <div className="space-y-6 p-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">工作流管理</h1>
                    <p className="text-gray-600 mt-1">管理系统中的用以评分的工作流信息</p>
                </div>
                {tabsValue === 'list' && <div className="flex space-x-2">
                    <Button onClick={handleCreateWorkflowSetting}>
                        <Plus className="w-4 h-4 mr-2"/>
                        新增工作流设置
                    </Button>
                </div>}
            </div>

            <Tabs defaultValue="list" className="w-full" onValueChange={tabsChange}>
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="list">题型列表</TabsTrigger>
                    <TabsTrigger value="filterPreview">筛选器预览</TabsTrigger>
                </TabsList>

                <TabsContent value="list">
                    <Card>
                        <CardHeader className="pb-2">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                <CardTitle className="text-lg w-[20%]">工作流配置列表</CardTitle>

                                <WorkflowTableHeader
                                    questionTypeSummaries={questionTypeSummaries}
                                    subjectSummaries={subjectSummaries}
                                    gradeLevelSummaries={gradeLevelSummaries}
                                    tenants={tenants}
                                    onFilterChange={(v) => {
                                        loadWorkflowSetting(v)
                                    }}
                                />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <WorkflowTable
                                workflowSettings={workflowSettings}
                                questionTypeSummaries={questionTypeSummaries}
                                subjectSummaries={subjectSummaries}
                                gradeLevelSummaries={gradeLevelSummaries}
                                tenants={tenants}
                                loading={loading}
                                pagination={{
                                    current: workflowSettingsPagination.current,
                                    pageSize: workflowSettingsPagination.pageSize,
                                    total: workflowSettingsPagination.total,
                                    onChange: handleWorkflowPageChange,
                                }}
                                onDelete={handleDeleteWorkflowSetting}
                                onEdit={handleEditWorkflowSetting}
                            />
                        </CardContent>
                        <WorkflowForm
                            workflowSetting={editingWorkflowSetting}
                            questionTypeSummaries={questionTypeSummaries}
                            subjectSummaries={subjectSummaries}
                            gradeLevelSummaries={gradeLevelSummaries}
                            tenants={tenants}
                            open={formOpen}
                            onClose={() => setFormOpen(false)}
                            onSubmit={handleFormSubmit}
                            loading={formLoading}
                        />
                    </Card>
                </TabsContent>

                <TabsContent value="filterPreview">
                    <Card>
                        <WorkflowSettingFilterPreview
                            questionTypeSummaries={questionTypeSummaries}
                            subjectSummaries={subjectSummaries}
                            gradeLevelSummaries={gradeLevelSummaries}
                            tenants={tenants}
                            preview={workflowSummaryInSetting}
                            onFilterChange={(v) => {
                                loadWorkflowSummaryInSetting(v)
                            }}
                        />
                    </Card>

                    </TabsContent>
            </Tabs>

        </div>
    );
};

export default WorkflowPage;
