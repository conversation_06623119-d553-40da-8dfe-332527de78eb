import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus } from 'lucide-react';
import { MenuItem, MENU_TYPE_OPTIONS, MenuTreePanelProps } from '@/types/menu';
import MenuTreeNode from './MenuTreeNode';

const MenuTreePanel: React.FC<MenuTreePanelProps> = ({
  menus,
  loading,
  searchTerm,
  filterType,
  selectedMenu,
  expandedNodes,
  onSearchChange,
  onFilterChange,
  onMenuSelect,
  onMenuEdit,
  onMenuDelete,
  onCreateMenu,
  onToggleNode,
}) => {
  // 递归过滤菜单，保持树形结构
  const filterMenuTree = (items: MenuItem[]): MenuItem[] => {
    return items.filter(menu => {
      const matchesSearch = menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           menu.menu_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           menu.path.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = filterType === 'all' || menu.menu_type === filterType;

      // 检查子菜单是否匹配
      const hasMatchingChildren = menu.children && menu.children.length > 0 &&
                                 filterMenuTree(menu.children).length > 0;

      // 如果当前菜单匹配或有匹配的子菜单，则保留
      if ((matchesSearch && matchesType) || hasMatchingChildren) {
        // 如果有子菜单，递归过滤子菜单
        if (menu.children && menu.children.length > 0) {
          return {
            ...menu,
            children: filterMenuTree(menu.children)
          };
        }
        return menu;
      }

      return null;
    }).filter(Boolean) as MenuItem[];
  };

  const filteredMenus = filterMenuTree(menus);

  // 菜单树渲染
  const renderMenuTree = (items: MenuItem[], level = 0) => {
    return (
      <div className="space-y-1">
        {items.map((menu) => (
          <MenuTreeNode
            key={menu.menu_id}
            menu={menu}
            level={level}
            isExpanded={expandedNodes.has(menu.menu_id)}
            isSelected={selectedMenu?.menu_id === menu.menu_id}
            expandedNodes={expandedNodes}
            selectedMenu={selectedMenu}
            onToggle={onToggleNode}
            onSelect={onMenuSelect}
            onEdit={onMenuEdit}
            onDelete={onMenuDelete}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">菜单结构</h2>
          <Button size="sm" onClick={onCreateMenu}>
            <Plus className="w-4 h-4 mr-2" />
            新建菜单
          </Button>
        </div>
        
        {/* 搜索和过滤 */}
        <div className="space-y-2">
          <Input
            placeholder="搜索菜单..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full"
          />
          <Select value={filterType} onValueChange={onFilterChange}>
            <SelectTrigger>
              <SelectValue placeholder="选择菜单类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              {MENU_TYPE_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 菜单树 */}
      <div className="flex-1 overflow-auto p-4">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : (
          renderMenuTree(filteredMenus)
        )}
      </div>
    </div>
  );
};

export default MenuTreePanel;