import React, { useState, useEffect } from 'react';
import {MenuItem} from '@/types/menu';
import MenuRoleMultiSelect from '@/components/role/MenuRoleMultiSelect';

interface PermissionFormProps {
  data: Partial<MenuItem>;
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
  onSave?: () => void;
}

const PermissionConfigForm: React.FC<PermissionFormProps> = ({
  data,
  onChange,
  disabled
}) => {
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [loading] = useState(false);

// 处理角色选择变化
  const handleRoleSelectionChange = async (roleCodes: string[]) => {
    console.log("Selected role codes:", roleCodes);
    setSelectedRoles(roleCodes);

    // 更新菜单数据的required_roles字段（用于前端显示）
    onChange({
      ...data,
      required_roles: roleCodes
    });

    // 如果菜单已存在，直接保存到Casbin策略
    if (!disabled && data.menu_id) {
      // 这里需要将role codes转换为role IDs来调用现有的API
      // 暂时跳过自动保存，让用户通过菜单保存来更新Casbin策略
      console.log("Menu role codes updated, will be saved when menu is saved");
    }
  };

  // 组件挂载时初始化角色配置
  useEffect(() => {
    if (data.required_roles) {
      setSelectedRoles(data.required_roles);
    } else {
      setSelectedRoles([]);
    }
  }, [data.required_roles]);
  

  return (
    <div className="space-y-6">
      {/* 角色权限配置 */}
      <div>
        <MenuRoleMultiSelect
          label="所需角色"
          description="选择可以访问此菜单的角色（将保存到Casbin策略中）"
          value={selectedRoles}
          onChange={handleRoleSelectionChange}
          disabled={disabled || loading}
          placeholder={loading ? "加载中..." : "选择角色..."}
          excludeSystemRoles={false}
          className="w-full"
        />
        {loading && (
          <div className="mt-2 text-sm text-muted-foreground">
            正在保存角色权限配置...
          </div>
        )}
        {data.required_roles && data.required_roles.length > 0 && (
          <div className="mt-2 text-sm text-muted-foreground">
            当前配置的角色代码: {data.required_roles.join(', ')}
          </div>
        )}
      </div>
    </div>
  );
};
export default PermissionConfigForm;