import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FormComponentProps } from '@/types/menu';

const AdvancedSettingsForm: React.FC<FormComponentProps> = ({ data, onChange, disabled }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="access_level">访问级别</Label>
          <Input
            id="access_level"
            type="number"
            min="0"
            max="100"
            value={data.access_level || 0}
            onChange={(e) => onChange({ ...data, access_level: parseInt(e.target.value) || 0 })}
            disabled={disabled}
          />
          <p className="text-xs text-gray-500 mt-1">0-100，用于排序和过滤</p>
        </div>
        <div>
          <Label htmlFor="sort_order">排序顺序</Label>
          <Input
            id="sort_order"
            type="number"
            value={data.sort_order || 0}
            onChange={(e) => onChange({ ...data, sort_order: parseInt(e.target.value) || 0 })}
            disabled={disabled}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="component_path">前端组件路径</Label>
        <Input
          id="component_path"
          value={data.component_path || ''}
          onChange={(e) => onChange({ ...data, component_path: e.target.value })}
          disabled={disabled}
          placeholder="/path/to/Component"
        />
      </div>

      <div>
        <Label htmlFor="external_link">外部链接</Label>
        <Input
          id="external_link"
          value={data.external_link || ''}
          onChange={(e) => onChange({ ...data, external_link: e.target.value })}
          disabled={disabled}
          placeholder="https://example.com"
        />
      </div>

      <div>
        <Label>扩展元数据 (JSON)</Label>
        <Textarea
          value={data.metadata ? JSON.stringify(data.metadata, null, 2) : '{}'}
          onChange={(e) => {
            try {
              const metadata = JSON.parse(e.target.value);
              onChange({ ...data, metadata });
            } catch (err) {
                console.log(err);
            }
          }}
          disabled={disabled}
          rows={6}
          placeholder='{"key": "value"}'
        />
      </div>
    </div>
  );
};

export default AdvancedSettingsForm;