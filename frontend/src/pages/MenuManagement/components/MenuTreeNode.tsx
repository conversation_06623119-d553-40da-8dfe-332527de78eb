import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Menu, Edit, Trash2, ChevronRight, ChevronDown } from 'lucide-react';
import { MenuTreeNodeProps } from '@/types/menu';

const MenuTreeNode: React.FC<MenuTreeNodeProps> = ({
  menu,
  level,
  isExpanded,
  isSelected,
  expandedNodes,
  selectedMenu,
  onToggle,
  onSelect,
  onEdit,
  onDelete
}) => {
  const hasChildren = menu.children_count > 0;
  const paddingLeft = level * 20 + 8;

  return (
    <div>
      <div
        className={`flex items-center p-2 rounded cursor-pointer hover:bg-gray-50 group ${
          isSelected ? 'bg-blue-50 border border-blue-200' : ''
        }`}
        style={{ paddingLeft }}
        onClick={() => onSelect(menu)}
      >
        {/* 展开/收起图标 */}
        <div className="w-4 h-4 mr-2">
          {hasChildren && (
            <button onClick={(e) => { e.stopPropagation(); onToggle(menu.menu_id); }}>
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* 菜单图标 */}
        <div className="w-4 h-4 mr-2 text-gray-400">
          <Menu className="w-4 h-4" />
        </div>

        {/* 菜单信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900 truncate">
              {menu.name}
            </span>
            <div className="ml-2 flex items-center space-x-1">
              {!menu.is_active && (
                <Badge variant="secondary" className="text-xs">
                  禁用
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                {menu.menu_type}
              </Badge>
            </div>
          </div>
          <div className="text-xs text-gray-500 truncate">
            {menu.path}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => { e.stopPropagation(); onEdit(menu); }}
            className="h-6 w-6 p-0"
          >
            <Edit className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => { e.stopPropagation(); onDelete(menu.menu_id); }}
            className="h-6 w-6 p-0"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* 子菜单 */}
      {hasChildren && isExpanded && menu.children && (
        <div>
          {menu.children.map((child) => (
            <MenuTreeNode
              key={child.menu_id}
              menu={child}
              level={level + 1}
              isExpanded={expandedNodes.has(child.menu_id)}
              isSelected={selectedMenu?.menu_id === child.menu_id}
              expandedNodes={expandedNodes}
              selectedMenu={selectedMenu}
              onToggle={onToggle}
              onSelect={onSelect}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default MenuTreeNode;