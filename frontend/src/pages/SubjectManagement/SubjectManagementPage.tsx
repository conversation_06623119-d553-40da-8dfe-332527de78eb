import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

import SubjectHeader from './components/SubjectHeader';
import SubjectListTab from './components/SubjectListTab';
import SubjectStatisticsTab from './components/SubjectStatisticsTab';
import SubjectForm from './components/SubjectForm';
import DeleteConfirmDialog from './components/DeleteConfirmDialog';
import { subjectApi } from '@/services/subjectApi';
import { 
  Subject, 
  SubjectQueryParams, 
  SubjectFormData,
  DEFAULT_SUBJECT_QUERY
} from '@/types/subject';

const SubjectManagementPage: React.FC = () => {
  // 数据状态
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<SubjectQueryParams>(DEFAULT_SUBJECT_QUERY);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  
  // 表单状态
  const [formOpen, setFormOpen] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // 删除确认状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState<string | null>(null);

  // 计算统计数据
  const activeSubjectsCount = subjects.filter(s => s.is_active).length;

  // 加载学科数据
  const loadSubjects = async (params?: Partial<SubjectQueryParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...queryParams, ...params };
      const response = await subjectApi.getSubjects(finalParams);
      
      if (response.success && response.data) {
        setSubjects(response.data);
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.page_size,
          total: response.pagination.total,
          totalPages: response.pagination.total_pages,
        });
        setQueryParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load subjects:', error);
      toast.error('加载学科列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadSubjects();
  }, []);

  // 处理搜索
  const handleSearch = (search: string) => {
    loadSubjects({ ...queryParams, search, page: 1 });
  };

  // 处理筛选变化
  const handleFilterChange = (key: keyof SubjectQueryParams, value: any) => {
    const newParams = { ...queryParams, [key]: value, page: 1 };
    loadSubjects(newParams);
  };

  // 处理分页
  const handlePageChange = (page: number, pageSize: number) => {
    loadSubjects({ ...queryParams, page, page_size: pageSize });
  };

  // 处理创建学科
  const handleCreateSubject = () => {
    setEditingSubject(undefined);
    setFormOpen(true);
  };

  // 处理编辑学科
  const handleEditSubject = (subject: Subject) => {
    setEditingSubject(subject);
    setFormOpen(true);
  };

  // 处理表单提交
  const handleFormSubmit = async (data: SubjectFormData) => {
    try {
      setFormLoading(true);
      const validatedData: SubjectFormData = {
        ...data,
        order_level: Number(data.order_level),
      };

      if (editingSubject) {
        const response = await subjectApi.updateSubject(editingSubject.id, validatedData);
        if (response.success) {
          toast.success('学科更新成功');
          setFormOpen(false);
          await loadSubjects();
        }
      } else {
        const response = await subjectApi.createSubject(validatedData);
        if (response.success) {
          toast.success('学科创建成功');
          setFormOpen(false);
          await loadSubjects();
        }
      }
    } catch (error: any) {
      console.error('Failed to save subject:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  // 处理删除学科
  const handleDeleteSubject = (id: string) => {
    setSubjectToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteSubject = async () => {
    if (!subjectToDelete) return;

    try {
      const response = await subjectApi.deleteSubject(subjectToDelete);
      if (response.success) {
        toast.success('学科删除成功');
        await loadSubjects();
      }
    } catch (error: any) {
      console.error('Failed to delete subject:', error);
      toast.error(error.response?.data?.message || '删除失败');
    } finally {
      setDeleteDialogOpen(false);
      setSubjectToDelete(null);
    }
  };

  // 处理状态切换
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await subjectApi.toggleSubjectStatus(id, isActive);
      if (response.success) {
        toast.success(`学科已${isActive ? '启用' : '禁用'}`);
        await loadSubjects();
      }
    } catch (error: any) {
      console.error('Failed to toggle subject status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  // 处理导出
  const handleExport = async () => {
    try {
      const blob = await subjectApi.exportSubjects(queryParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `subjects-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export subjects:', error);
      toast.error('导出功能开发中');
    }
  };

  return (
    <div className="space-y-8 p-6">
      {/* 页面头部 */}
      <SubjectHeader 
        onCreateSubject={handleCreateSubject}
        onExport={handleExport}
        subjectsCount={subjects.length}
        activeSubjectsCount={activeSubjectsCount}
      />

      {/* 标签页内容 */}
      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">学科列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <SubjectListTab
            subjects={subjects}
            loading={loading}
            queryParams={queryParams}
            pagination={pagination}
            onSearch={handleSearch}
            onFilterChange={handleFilterChange}
            onPageChange={handlePageChange}
            onEdit={handleEditSubject}
            onDelete={handleDeleteSubject}
            onToggleStatus={handleToggleStatus}
          />
        </TabsContent>

        <TabsContent value="statistics">
          <SubjectStatisticsTab subjects={subjects} />
        </TabsContent>
      </Tabs>

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDeleteSubject}
      />

      {/* 学科表单 */}
      <SubjectForm
        subject={editingSubject}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />
    </div>
  );
};

export default SubjectManagementPage;