import React from 'react';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { AlertCircle } from 'lucide-react';
import { Subject } from '@/types/subject';

interface SubjectFormInfoProps {
  subject: Subject;
}

const SubjectFormInfo: React.FC<SubjectFormInfoProps> = ({ subject }) => {
  return (
    <AlertDialog>
      <AlertCircle className="h-4 w-4" />
      <AlertDialogDescription>
        <div className="flex items-center justify-between">
          <span>学科状态：</span>
          <Badge variant={subject.is_active ? "default" : "secondary"}>
            {subject.is_active ? '启用' : '禁用'}
          </Badge>
        </div>
        <div className="flex items-center justify-between mt-1">
          <span>关联数据：</span>
          <span className="text-sm">
            {subject.question_count || 0} 题目，{subject.paper_count || 0} 试卷
          </span>
        </div>
      </AlertDialogDescription>
    </AlertDialog>
  );
};

export default SubjectFormInfo; 