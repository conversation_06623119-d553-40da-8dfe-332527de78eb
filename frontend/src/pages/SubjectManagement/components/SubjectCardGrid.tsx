import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Grid3X3, List, BarChart3 } from 'lucide-react';
import { Subject } from '@/types/subject';
import SubjectCard from './SubjectCard';

interface SubjectCardGridProps {
  subjects: Subject[];
  loading: boolean;
  onEdit: (subject: Subject) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
}

type ViewMode = 'grid' | 'list' | 'compact';

const SubjectCardGrid: React.FC<SubjectCardGridProps> = ({
  subjects,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg border p-6 animate-pulse">
              <div className="space-y-3">
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-200 rounded w-20"></div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-6 bg-gray-200 rounded w-32"></div>
                <div className="h-4 bg-gray-200 rounded w-48"></div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!Array.isArray(subjects) || subjects.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <BarChart3 className="h-16 w-16 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无学科数据</h3>
        <p className="text-gray-500 mb-4">点击"新增学科"创建第一个学科</p>
        <div className="text-sm text-gray-400">
          <p>• 学科是教学管理的基础单位</p>
          <p>• 每个学科可以包含多个题目和试卷</p>
          <p>• 支持启用/禁用状态管理</p>
        </div>
      </div>
    );
  }

  const renderGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {subjects.map((subject) => (
        <SubjectCard
          key={subject.id}
          subject={subject}
          onEdit={onEdit}
          onDelete={onDelete}
          onToggleStatus={onToggleStatus}
        />
      ))}
    </div>
  );

  const renderList = () => (
    <div className="space-y-3">
      {subjects.map((subject) => (
        <div
          key={subject.id}
          className="bg-white rounded-lg border p-4 hover:shadow-md transition-shadow"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 flex-1 min-w-0">
              <div className="flex-shrink-0">
                <Badge variant="outline" className="font-mono">
                  {subject.code}
                </Badge>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-gray-900 truncate">{subject.name}</h3>
                {subject.description && (
                  <p className="text-sm text-gray-500 truncate">{subject.description}</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>排序: {subject.order_level}</span>
              <span>{subject.question_count || 0} 题目</span>
              <span>{subject.paper_count || 0} 试卷</span>
              <Badge 
                variant={subject.is_active ? "default" : "secondary"}
                className={subject.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
              >
                {subject.is_active ? '启用' : '禁用'}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(subject)}
              >
                编辑
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleStatus(subject.id, !subject.is_active)}
              >
                {subject.is_active ? '禁用' : '启用'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(subject.id)}
                className="text-red-600 hover:text-red-700"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderCompact = () => (
    <div className="bg-white rounded-lg border overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科信息
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                统计
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {subjects.map((subject) => (
              <tr key={subject.id} className="hover:bg-gray-50">
                <td className="px-4 py-3">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="font-mono text-xs">
                        {subject.code}
                      </Badge>
                      <span className="font-medium text-gray-900">{subject.name}</span>
                    </div>
                    {subject.description && (
                      <p className="text-sm text-gray-500 truncate max-w-xs">
                        {subject.description}
                      </p>
                    )}
                  </div>
                </td>
                <td className="px-4 py-3 text-sm text-gray-600">
                  <div className="space-y-1">
                    <div>排序: {subject.order_level}</div>
                    <div>{subject.question_count || 0} 题目</div>
                    <div>{subject.paper_count || 0} 试卷</div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <Badge 
                    variant={subject.is_active ? "default" : "secondary"}
                    className={subject.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
                  >
                    {subject.is_active ? '启用' : '禁用'}
                  </Badge>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(subject)}
                    >
                      编辑
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onToggleStatus(subject.id, !subject.is_active)}
                    >
                      {subject.is_active ? '禁用' : '启用'}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(subject.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      删除
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* 视图模式切换 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">视图模式:</span>
          <div className="flex border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-none"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'compact' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('compact')}
              className="rounded-l-none"
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          共 {subjects.length} 个学科
        </div>
      </div>

      {/* 内容渲染 */}
      {viewMode === 'grid' && renderGrid()}
      {viewMode === 'list' && renderList()}
      {viewMode === 'compact' && renderCompact()}
    </div>
  );
};

export default SubjectCardGrid; 