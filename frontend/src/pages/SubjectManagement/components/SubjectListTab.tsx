import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SubjectQueryParams } from '@/types/subject';
import EnhancedSearchFilter from './EnhancedSearchFilter';
import SubjectCardGrid from './SubjectCardGrid';

interface SubjectListTabProps {
  subjects: any[];
  loading: boolean;
  queryParams: SubjectQueryParams;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  onSearch: (search: string) => void;
  onFilterChange: (key: keyof SubjectQueryParams, value: any) => void;
  onPageChange: (page: number, pageSize: number) => void;
  onEdit: (subject: any) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
}

const SubjectListTab: React.FC<SubjectListTabProps> = ({
  subjects,
  loading,
  queryParams,
  pagination,
  onSearch,
  onFilterChange,
  onPageChange,
  onEdit,
  onDelete,
  onToggleStatus,
}) => {
  const handleClearFilters = () => {
    onFilterChange('is_active', undefined);
    onFilterChange('order_by', undefined);
    onFilterChange('order_direction', undefined);
    onSearch('');
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">学科列表</CardTitle>
        <p className="text-sm text-gray-600">
          管理系统中的所有学科，支持多种视图模式和高级筛选
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 增强的搜索和筛选 */}
        <EnhancedSearchFilter
          queryParams={queryParams}
          onSearch={onSearch}
          onFilterChange={onFilterChange}
          onClearFilters={handleClearFilters}
        />

        {/* 学科卡片网格 */}
        <SubjectCardGrid
          subjects={subjects}
          loading={loading}
          onEdit={onEdit}
          onDelete={onDelete}
          onToggleStatus={onToggleStatus}
        />

        {/* 分页信息 */}
        {pagination.total > 0 && (
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-500">
              显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
              {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
              {pagination.total} 条记录
            </div>
            
            <div className="flex items-center gap-2">
              <select
                className="border rounded px-2 py-1 text-sm"
                value={pagination.pageSize}
                onChange={e => onPageChange(1, Number(e.target.value))}
              >
                {[10, 20, 50, 100].map(size => (
                  <option key={size} value={size}>{size} 条/页</option>
                ))}
              </select>
              
              <div className="flex items-center gap-1">
                <button
                  onClick={() => onPageChange(pagination.current - 1, pagination.pageSize)}
                  disabled={pagination.current === 1}
                  className="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                <span className="px-3 py-1 text-sm">
                  {pagination.current} / {pagination.totalPages}
                </span>
                <button
                  onClick={() => onPageChange(pagination.current + 1, pagination.pageSize)}
                  disabled={pagination.current === pagination.totalPages}
                  className="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SubjectListTab; 