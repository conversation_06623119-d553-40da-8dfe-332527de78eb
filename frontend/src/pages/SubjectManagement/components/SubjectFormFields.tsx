import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SubjectFormData, SubjectFormErrors } from '@/types/subject';

interface SubjectFormFieldsProps {
  register: any;
  errors: any;
  formErrors: SubjectFormErrors;
  codeChecking: boolean;
  codeAvailable: boolean | null;
  isEditMode: boolean;
  watchedCode: string;
}

const SubjectFormFields: React.FC<SubjectFormFieldsProps> = ({
  register,
  errors,
  formErrors,
  codeChecking,
  codeAvailable,
  isEditMode,
  watchedCode,
}) => {
  return (
    <div className="space-y-4">
      {/* 学科代码 */}
      <div className="space-y-2">
        <Label htmlFor="code" className="text-sm font-medium">
          学科代码 <span className="text-red-500">*</span>
        </Label>
        <div className="relative">
          <Input
            id="code"
            {...register('code', {
              required: '请输入学科代码',
              pattern: {
                value: /^[A-Z0-9_]+$/,
                message: '学科代码只能包含大写字母、数字和下划线'
              },
              minLength: {
                value: 2,
                message: '学科代码至少需要2个字符'
              },
              maxLength: {
                value: 20,
                message: '学科代码不能超过20个字符'
              }
            })}
            placeholder="例如：MATH, CHINESE"
            disabled={isEditMode}
            className={cn(
              errors.code || formErrors.code ? "border-red-500" : "",
              !isEditMode && codeAvailable === false ? "border-red-500" : "",
              !isEditMode && codeAvailable === true ? "border-green-500" : ""
            )}
          />
          
          {/* 代码检查指示器 */}
          {!isEditMode && watchedCode && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {codeChecking ? (
                <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
              ) : codeAvailable === true ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : codeAvailable === false ? (
                <AlertCircle className="w-4 h-4 text-red-500" />
              ) : null}
            </div>
          )}
        </div>
        
        {/* 代码验证消息 */}
        {errors.code && (
          <p className="text-sm text-red-500">{errors.code.message}</p>
        )}
        {formErrors.code && (
          <p className="text-sm text-red-500">{formErrors.code}</p>
        )}
        {!isEditMode && codeAvailable === false && (
          <p className="text-sm text-red-500">该学科代码已存在</p>
        )}
        {!isEditMode && codeAvailable === true && (
          <p className="text-sm text-green-600">学科代码可用</p>
        )}
        
        {!isEditMode && (
          <p className="text-xs text-gray-500">
            学科代码用于系统识别，创建后不可修改，建议使用英文大写字母
          </p>
        )}
      </div>

      {/* 学科名称 */}
      <div className="space-y-2">
        <Label htmlFor="name" className="text-sm font-medium">
          学科名称 <span className="text-red-500">*</span>
        </Label>
        <Input
          id="name"
          {...register('name', {
            required: '请输入学科名称',
            minLength: {
              value: 1,
              message: '学科名称不能为空'
            },
            maxLength: {
              value: 100,
              message: '学科名称不能超过100个字符'
            }
          })}
          placeholder="例如：数学、语文、英语"
          className={errors.name || formErrors.name ? "border-red-500" : ""}
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
        {formErrors.name && (
          <p className="text-sm text-red-500">{formErrors.name}</p>
        )}
      </div>

      {/* 描述 */}
      <div className="space-y-2">
        <Label htmlFor="description" className="text-sm font-medium">
          学科描述
        </Label>
        <Textarea
          id="description"
          {...register('description', {
            maxLength: {
              value: 500,
              message: '学科描述不能超过500个字符'
            }
          })}
          placeholder="可选，输入学科的详细描述..."
          rows={3}
          className={formErrors.description ? "border-red-500" : ""}
        />
        {errors.description && (
          <p className="text-sm text-red-500">{errors.description.message}</p>
        )}
        {formErrors.description && (
          <p className="text-sm text-red-500">{formErrors.description}</p>
        )}
      </div>

      {/* 排序级别 */}
      <div className="space-y-2">
        <Label htmlFor="order_level" className="text-sm font-medium">
          排序级别 <span className="text-red-500">*</span>
        </Label>
        <Input
          id="order_level"
          min="1"
          type="number"
          {...register('order_level', {
            required: '请输入排序级别',
            min: {
              value: 1,
              message: '排序级别不能小于1'
            },
            max: {
              value: 999,
              message: '排序级别不能大于999'
            }
          })}
          placeholder="数字越小排序越靠前"
          className={errors.order_level || formErrors.order_level ? "border-red-500" : ""}
        />
        {errors.order_level && (
          <p className="text-sm text-red-500">{errors.order_level.message}</p>
        )}
        {formErrors.order_level && (
          <p className="text-sm text-red-500">{formErrors.order_level}</p>
        )}
        <p className="text-xs text-gray-500">
          排序级别决定学科在列表中的显示顺序，数字越小越靠前
        </p>
      </div>
    </div>
  );
};

export default SubjectFormFields; 