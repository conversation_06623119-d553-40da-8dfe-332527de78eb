import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power, 
  PowerOff,
  BookOpen,
  FileText,
  Calendar,
  Hash
} from 'lucide-react';
import { Subject } from '@/types/subject';
import { cn } from '@/lib/utils';

interface SubjectCardProps {
  subject: Subject;
  onEdit: (subject: Subject) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
}

const SubjectCard: React.FC<SubjectCardProps> = ({
  subject,
  onEdit,
  onDelete,
  onToggleStatus,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const handleToggleStatus = () => {
    onToggleStatus(subject.id, !subject.is_active);
  };

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-lg",
      subject.is_active 
        ? "border-l-4 border-l-green-500" 
        : "border-l-4 border-l-gray-300"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="font-mono text-xs">
                {subject.code}
              </Badge>
              <Badge 
                variant={subject.is_active ? "default" : "secondary"}
                className={cn(
                  "text-xs",
                  subject.is_active 
                    ? "bg-green-100 text-green-800 hover:bg-green-200" 
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                )}
              >
                {subject.is_active ? '启用' : '禁用'}
              </Badge>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {subject.name}
            </h3>
            {subject.description && (
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {subject.description}
              </p>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(subject)}
                className="cursor-pointer"
              >
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              
              <DropdownMenuItem 
                onClick={handleToggleStatus}
                className="cursor-pointer"
              >
                {subject.is_active ? (
                  <>
                    <PowerOff className="mr-2 h-4 w-4" />
                    禁用
                  </>
                ) : (
                  <>
                    <Power className="mr-2 h-4 w-4" />
                    启用
                  </>
                )}
              </DropdownMenuItem>

              <DropdownMenuItem 
                onClick={() => onDelete(subject.id)}
                className="cursor-pointer text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2 text-gray-600">
            <Hash className="w-4 h-4" />
            <span>排序：{subject.order_level}</span>
          </div>
          
          <div className="flex items-center gap-2 text-gray-600">
            <BookOpen className="w-4 h-4" />
            <span>{subject.question_count || 0} 题目</span>
          </div>
          
          <div className="flex items-center gap-2 text-gray-600">
            <FileText className="w-4 h-4" />
            <span>{subject.paper_count || 0} 试卷</span>
          </div>
          
          <div className="flex items-center gap-2 text-gray-600">
            <Calendar className="w-4 h-4" />
            <span>{formatDate(subject.created_at)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubjectCard; 