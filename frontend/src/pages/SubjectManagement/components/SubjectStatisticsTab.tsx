import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  BookOpen, 
  FileText, 
  Users, 
  TrendingUp, 
  Calendar,
  Activity,
  Target,
  PieChart,
  <PERSON><PERSON><PERSON>,
  Award,
  Clock
} from 'lucide-react';
import { Subject } from '@/types/subject';

interface SubjectStatisticsTabProps {
  subjects: Subject[];
}

const SubjectStatisticsTab: React.FC<SubjectStatisticsTabProps> = ({ subjects }) => {
  // 基础统计数据
  const totalSubjects = subjects.length;
  const activeSubjects = subjects.filter(s => s.is_active).length;
  const inactiveSubjects = totalSubjects - activeSubjects;
  const totalQuestions = subjects.reduce((sum, s) => sum + (Number(s.question_count) || 0), 0);
  const totalPapers = subjects.reduce((sum, s) => sum + (Number(s.paper_count) || 0), 0);
  
  // 计算使用率
  const usageRate = totalSubjects > 0 ? Math.round((activeSubjects / totalSubjects) * 100) : 0;
  
  // 按创建时间统计（最近30天）
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentlyCreated = subjects.filter(s => new Date(s.created_at) > thirtyDaysAgo).length;
  
  // 按题目数量排序的前5个学科
  const topSubjectsByQuestions = [...subjects]
    .sort((a, b) => (Number(b.question_count) || 0) - (Number(a.question_count) || 0))
    .slice(0, 5);
  
  // 按试卷数量排序的前5个学科
  const topSubjectsByPapers = [...subjects]
    .sort((a, b) => (Number(b.paper_count) || 0) - (Number(a.paper_count) || 0))
    .slice(0, 5);

  // 计算平均数据
  const avgQuestionsPerSubject = totalSubjects > 0 ? Math.round(totalQuestions / totalSubjects) : 0;
  const avgPapersPerSubject = totalSubjects > 0 ? Math.round(totalPapers / totalSubjects) : 0;

  // 最近创建的学科（最近7天）
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  const recentSubjects = subjects
    .filter(s => new Date(s.created_at) > sevenDaysAgo)
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总学科数</p>
                <p className="text-3xl font-bold text-gray-900">{totalSubjects}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <BookOpen className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">启用学科</p>
                <p className="text-3xl font-bold text-gray-900">{activeSubjects}</p>
                <p className="text-sm text-green-600">使用率 {usageRate}%</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总题目数</p>
                <p className="text-3xl font-bold text-gray-900">{totalQuestions}</p>
                <p className="text-sm text-purple-600">平均 {avgQuestionsPerSubject} 题/科</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总试卷数</p>
                <p className="text-3xl font-bold text-gray-900">{totalPapers}</p>
                <p className="text-sm text-orange-600">平均 {avgPapersPerSubject} 卷/科</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <FileText className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细分析区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 学科状态分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5 text-blue-600" />
              学科状态分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">启用学科</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${totalSubjects > 0 ? (activeSubjects / totalSubjects) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium w-12 text-right">{activeSubjects}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                  <span className="text-sm font-medium">禁用学科</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gray-400 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${totalSubjects > 0 ? (inactiveSubjects / totalSubjects) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium w-12 text-right">{inactiveSubjects}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 最近活动 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-green-600" />
              最近活动
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Calendar className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">本月新增学科</span>
                </div>
                <Badge variant="secondary">{recentlyCreated} 个</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">活跃学科比例</span>
                </div>
                <Badge variant="secondary">{usageRate}%</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <BarChart className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium">平均题目数</span>
                </div>
                <Badge variant="secondary">{avgQuestionsPerSubject} 题</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 排行榜区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 题目数量排行榜 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5 text-purple-600" />
              题目数量排行榜
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topSubjectsByQuestions.map((subject, index) => (
                <div key={subject.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      index === 0 ? 'bg-yellow-500 text-white' :
                      index === 1 ? 'bg-gray-400 text-white' :
                      index === 2 ? 'bg-orange-500 text-white' :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{subject.name}</p>
                      <p className="text-xs text-gray-500">{subject.code}</p>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {subject.question_count || 0} 题
                  </Badge>
                </div>
              ))}
              {topSubjectsByQuestions.length === 0 && (
                <p className="text-center text-gray-500 py-4">暂无数据</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 试卷数量排行榜 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5 text-orange-600" />
              试卷数量排行榜
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topSubjectsByPapers.map((subject, index) => (
                <div key={subject.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      index === 0 ? 'bg-yellow-500 text-white' :
                      index === 1 ? 'bg-gray-400 text-white' :
                      index === 2 ? 'bg-orange-500 text-white' :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{subject.name}</p>
                      <p className="text-xs text-gray-500">{subject.code}</p>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {subject.paper_count || 0} 卷
                  </Badge>
                </div>
              ))}
              {topSubjectsByPapers.length === 0 && (
                <p className="text-center text-gray-500 py-4">暂无数据</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 最近创建的学科 */}
      {recentSubjects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-indigo-600" />
              最近创建的学科
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentSubjects.map((subject) => (
                <div key={subject.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className={`w-3 h-3 rounded-full ${subject.is_active ? 'bg-green-500' : 'bg-gray-300'}`} />
                    <div>
                      <p className="font-medium">{subject.name}</p>
                      <p className="text-sm text-gray-500">{subject.code}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{subject.question_count || 0} 题目</span>
                    <span>{subject.paper_count || 0} 试卷</span>
                    <Badge variant={subject.is_active ? "default" : "secondary"}>
                      {subject.is_active ? '启用' : '禁用'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SubjectStatisticsTab;
