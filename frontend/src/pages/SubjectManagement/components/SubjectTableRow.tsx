import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power, 
  PowerOff,
  BarChart3
} from 'lucide-react';
import { Subject } from '@/types/subject';
import { cn } from '@/lib/utils';

interface SubjectTableRowProps {
  subject: Subject;
  onEdit: (subject: Subject) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
}

const SubjectTableRow: React.FC<SubjectTableRowProps> = ({
  subject,
  onEdit,
  onDelete,
  onToggleStatus,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleToggleStatus = () => {
    onToggleStatus(subject.id, !subject.is_active);
  };

  return (
    <TableRow className="hover:bg-gray-50">
      <TableCell className="font-mono">
        <Badge variant="outline" className="text-xs">
          {subject.code}
        </Badge>
      </TableCell>
      
      <TableCell className="font-medium">
        {subject.name}
      </TableCell>
      
      <TableCell className="text-gray-600">
        {subject.description || '-'}
      </TableCell>
      
      <TableCell className="text-center">
        <Badge variant="secondary" className="text-xs">
          {subject.order_level}
        </Badge>
      </TableCell>
      
      <TableCell className="text-center">
        <Badge 
          variant={subject.is_active ? "default" : "secondary"}
          className={cn(
            "text-xs",
            subject.is_active 
              ? "bg-green-100 text-green-800 hover:bg-green-200" 
              : "bg-gray-100 text-gray-600 hover:bg-gray-200"
          )}
        >
          {subject.is_active ? '启用' : '禁用'}
        </Badge>
      </TableCell>
      
      <TableCell className="text-center">
        <div className="flex items-center justify-center">
          <BarChart3 className="w-3 h-3 mr-1 text-gray-400" />
          <span className="text-sm">{subject.question_count || 0}</span>
        </div>
      </TableCell>
      
      <TableCell className="text-center">
        <div className="flex items-center justify-center">
          <BarChart3 className="w-3 h-3 mr-1 text-gray-400" />
          <span className="text-sm">{subject.paper_count || 0}</span>
        </div>
      </TableCell>
      
      <TableCell className="text-sm text-gray-500">
        {formatDate(subject.created_at)}
      </TableCell>
      
      <TableCell>
        <div className="flex items-center justify-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(subject)}
                className="cursor-pointer"
              >
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              
              <DropdownMenuItem 
                onClick={handleToggleStatus}
                className="cursor-pointer"
              >
                {subject.is_active ? (
                  <>
                    <PowerOff className="mr-2 h-4 w-4" />
                    禁用
                  </>
                ) : (
                  <>
                    <Power className="mr-2 h-4 w-4" />
                    启用
                  </>
                )}
              </DropdownMenuItem>

              <DropdownMenuItem 
                onClick={() => onDelete(subject.id)}
                className="cursor-pointer text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </TableCell>
    </TableRow>
  );
};

export default SubjectTableRow; 