import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

import SubjectFormFields from './SubjectFormFields';
import SubjectFormInfo from './SubjectFormInfo';
import { subjectApi } from '@/services/subjectApi';
import { SubjectFormProps, SubjectFormData, SubjectFormErrors } from '@/types/subject';

const SubjectForm: React.FC<SubjectFormProps> = ({
  subject,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formErrors, setFormErrors] = useState<SubjectFormErrors>({});
  const [codeChecking, setCodeChecking] = useState(false);
  const [codeAvailable, setCodeAvailable] = useState<boolean | null>(null);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<SubjectFormData>({
    defaultValues: {
      code: '',
      name: '',
      description: '',
      order_level: 1,
    },
  });

  const watchedCode = watch('code');

  // 重置表单
  useEffect(() => {
    if (open) {
      if (subject) {
        reset({
          code: subject.code,
          name: subject.name,
          description: subject.description || '',
          order_level: subject.order_level,
        });
      } else {
        reset({
          code: '',
          name: '',
          description: '',
          order_level: 1,
        });
      }
      setFormErrors({});
      setCodeAvailable(null);
    }
  }, [open, subject, reset]);

  // 检查代码可用性
  useEffect(() => {
    if (!subject && watchedCode && watchedCode.length >= 2) {
      const checkCodeDebounced = setTimeout(async () => {
        try {
          setCodeChecking(true);
          const response = await subjectApi.checkCodeAvailability(watchedCode);
          if (response.success && response.data) {
            setCodeAvailable(response.data.is_available);
          } else {
            setCodeAvailable(false);
          }
        } catch (error) {
          console.error('Failed to check code availability:', error);
        } finally {
          setCodeChecking(false);
        }
      }, 500);

      return () => clearTimeout(checkCodeDebounced);
    } else {
      setCodeAvailable(null);
      setCodeChecking(false);
    }
  }, [watchedCode, subject]);

  const handleFormSubmit = async (data: SubjectFormData) => {
    // 验证代码可用性
    if (!subject && codeAvailable === false) {
      return;
    }

    try {
      await onSubmit(data);
    } catch (error: any) {
      // 处理验证错误
      if (error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      }
    }
  };

  const isEditMode = !!subject;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? '编辑学科' : '新增学科'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <SubjectFormFields
            register={register}
            errors={errors}
            formErrors={formErrors}
            codeChecking={codeChecking}
            codeAvailable={codeAvailable}
            isEditMode={isEditMode}
            watchedCode={watchedCode}
              />

          {/* 系统信息 */}
          {isEditMode && subject && (
            <SubjectFormInfo subject={subject} />
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || (codeChecking || (!isEditMode && codeAvailable === false))}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditMode ? '更新' : '创建'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectForm;