import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Download, BarChart3, BookOpen, Users } from 'lucide-react';

interface SubjectHeaderProps {
  onCreateSubject: () => void;
  onExport: () => void;
  subjectsCount?: number;
  activeSubjectsCount?: number;
}

const SubjectHeader: React.FC<SubjectHeaderProps> = ({
  onCreateSubject,
  onExport,
  subjectsCount = 0,
  activeSubjectsCount = 0,
}) => {
  return (
    <div className="space-y-6">
      {/* 主标题区域 */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            学科管理
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl">
            管理系统中的学科信息，包括学科的创建、编辑和状态管理。
            支持多种视图模式，提供直观的数据展示和高效的操作体验。
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={onExport} className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            导出数据
          </Button>
          <Button onClick={onCreateSubject} className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
            <Plus className="w-4 h-4" />
            新增学科
          </Button>
        </div>
      </div>

      {/* 快速统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">总学科数</p>
              <p className="text-3xl font-bold text-blue-900">{subjectsCount}</p>
            </div>
            <div className="p-3 bg-blue-500 rounded-full">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">启用学科</p>
              <p className="text-3xl font-bold text-green-900">{activeSubjectsCount}</p>
            </div>
            <div className="p-3 bg-green-500 rounded-full">
              <Users className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">使用率</p>
              <p className="text-3xl font-bold text-purple-900">
                {subjectsCount > 0 ? Math.round((activeSubjectsCount / subjectsCount) * 100) : 0}%
              </p>
            </div>
            <div className="p-3 bg-purple-500 rounded-full">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default SubjectHeader; 