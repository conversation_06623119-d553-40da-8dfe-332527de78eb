import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X, SortAsc, SortDesc } from 'lucide-react';
import { SubjectQueryParams, SUBJECT_SORT_OPTIONS, SUBJECT_STATUS_OPTIONS } from '@/types/subject';

interface EnhancedSearchFilterProps {
  queryParams: SubjectQueryParams;
  onSearch: (search: string) => void;
  onFilterChange: (key: keyof SubjectQueryParams, value: any) => void;
  onClearFilters: () => void;
}

const EnhancedSearchFilter: React.FC<EnhancedSearchFilterProps> = ({
  queryParams,
  onSearch,
  onFilterChange,
  onClearFilters,
}) => {
  const [searchInput, setSearchInput] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const handleSearch = () => {
    onSearch(searchInput);
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const hasActiveFilters = () => {
    return queryParams.is_active !== undefined || 
           queryParams.order_by !== undefined ||
           searchInput.trim() !== '';
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (queryParams.is_active !== undefined) count++;
    if (queryParams.order_by !== undefined) count++;
    if (searchInput.trim() !== '') count++;
    return count;
  };

  return (
    <div className="space-y-4">
      {/* 主要搜索栏 */}
      <div className="flex gap-3">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索学科名称、代码或描述..."
            className="pl-10 pr-4"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyDown={handleSearchKeyDown}
          />
        </div>
        
        <Button onClick={handleSearch} className="px-6">
          搜索
        </Button>
        
        <Button
          variant="outline"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          筛选
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary" className="ml-1">
              {getActiveFilterCount()}
            </Badge>
          )}
        </Button>
      </div>

      {/* 高级筛选器 */}
      {showAdvancedFilters && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">高级筛选</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4 mr-1" />
              清除筛选
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 状态筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">状态</label>
              <Select
                value={queryParams.is_active === undefined ? 'all' : String(queryParams.is_active)}
                onValueChange={(value) => 
                  onFilterChange('is_active', value === 'all' ? undefined : value === 'true')
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  {SUBJECT_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={String(option.value)} value={String(option.value)}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* 排序方式 */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">排序方式</label>
              <Select
                value={queryParams.order_by ? `${queryParams.order_by}-${queryParams.order_direction}` : ''}
                onValueChange={(value) => {
                  const [orderBy, orderDirection] = value.split('-');
                  onFilterChange('order_by', orderBy);
                  onFilterChange('order_direction', orderDirection);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="默认排序" />
                </SelectTrigger>
                <SelectContent>
                  {SUBJECT_SORT_OPTIONS.map((option) => (
                    <React.Fragment key={option.value}>
                      <SelectItem value={`${option.value}-asc`}>
                        <div className="flex items-center gap-2">
                          <SortAsc className="h-3 w-3" />
                          {option.label} 升序
                        </div>
                      </SelectItem>
                      <SelectItem value={`${option.value}-desc`}>
                        <div className="flex items-center gap-2">
                          <SortDesc className="h-3 w-3" />
                          {option.label} 降序
                        </div>
                      </SelectItem>
                    </React.Fragment>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* 活跃筛选器标签 */}
      {hasActiveFilters() && (
        <div className="flex flex-wrap gap-2">
          {queryParams.is_active !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1">
              状态: {queryParams.is_active ? '启用' : '禁用'}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onFilterChange('is_active', undefined)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {queryParams.order_by && (
            <Badge variant="secondary" className="flex items-center gap-1">
              排序: {queryParams.order_by} {queryParams.order_direction === 'asc' ? '↑' : '↓'}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => {
                  onFilterChange('order_by', undefined);
                  onFilterChange('order_direction', undefined);
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {searchInput.trim() !== '' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              搜索: {searchInput}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => {
                  setSearchInput('');
                  onSearch('');
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedSearchFilter; 