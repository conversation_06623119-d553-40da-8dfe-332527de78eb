import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface SubjectTablePaginationProps {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

const SubjectTablePagination: React.FC<SubjectTablePaginationProps> = ({ pagination }) => {
  const totalPages = Math.ceil(pagination.total / pagination.pageSize);

  return (
    <div className="flex flex-wrap items-center gap-4 mt-2">
      <div className="text-sm text-gray-500 whitespace-nowrap">
        显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
        {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
        {pagination.total} 条记录
      </div>
      
      {/* 分页大小选择器和跳页 */}
      <div className="flex flex-wrap gap-3 items-center">
        <select
          className="border rounded px-2 py-1 text-sm min-w-[80px]"
          value={pagination.pageSize}
          onChange={e => pagination.onChange(1, Number(e.target.value))}
        >
          {[10, 20, 50, 100].map(size => (
            <option key={size} value={size}>{size} 条/页</option>
          ))}
        </select>
        <span className="text-sm">跳转到</span>
        <input
          type="number"
          min={1}
          max={totalPages}
          defaultValue={pagination.current}
          onBlur={e => {
            let page = Number(e.target.value);
            if (page < 1) page = 1;
            if (page > totalPages) page = totalPages;
            if (page !== pagination.current) {
              pagination.onChange(page, pagination.pageSize);
            }
          }}
          className="border rounded px-2 py-1 w-16 text-sm text-center"
        />
        <span className="text-sm">页</span>
      </div>
      
      {/* 分页按钮 */}
      <div className="flex items-center">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => {
                  if (pagination.current > 1) {
                    pagination.onChange(pagination.current - 1, pagination.pageSize);
                  }
                }}
                className={pagination.current === 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <PaginationItem key={page}>
                <PaginationLink
                  isActive={page === pagination.current}
                  onClick={() => pagination.onChange(page, pagination.pageSize)}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext
                onClick={() => {
                  if (pagination.current < totalPages) {
                    pagination.onChange(pagination.current + 1, pagination.pageSize);
                  }
                }}
                className={pagination.current === totalPages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default SubjectTablePagination; 