import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Subject, SubjectTableProps } from '@/types/subject';
import SubjectTablePagination from './SubjectTablePagination';
import SubjectTableLoading from './SubjectTableLoading';
import SubjectTableEmpty from './SubjectTableEmpty';
import SubjectTableRow from './SubjectTableRow';

const SubjectTable: React.FC<SubjectTableProps> = ({
  subjects,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  pagination,
}) => {
  if (loading) {
    return <SubjectTableLoading />;
  }

  if (!Array.isArray(subjects) || subjects.length === 0) {
    return <SubjectTableEmpty />;
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">学科代码</TableHead>
              <TableHead className="w-[120px]">学科名称</TableHead>
              <TableHead className="min-w-[200px]">描述</TableHead>
              <TableHead className="w-[80px] text-center">排序</TableHead>
              <TableHead className="w-[80px] text-center">状态</TableHead>
              <TableHead className="w-[80px] text-center">题目数</TableHead>
              <TableHead className="w-[80px] text-center">试卷数</TableHead>
              <TableHead className="w-[150px]">创建时间</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subjects.map((subject) => (
              <SubjectTableRow
                key={subject.id}
                subject={subject}
                onEdit={onEdit}
                onDelete={onDelete}
                onToggleStatus={onToggleStatus}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination && pagination.total > 0 && (
        <SubjectTablePagination pagination={pagination} />
      )}
    </div>
  );
};

export default SubjectTable;