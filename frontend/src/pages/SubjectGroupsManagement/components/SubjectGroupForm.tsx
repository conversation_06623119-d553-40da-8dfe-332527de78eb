import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, BookOpen, Info, AlertCircle, User, FileText } from 'lucide-react';
import { toast } from 'sonner';

import { SubjectGroupFormProps, SubjectGroupFormData, SubjectGroupFormErrors } from '@/types/subjectGroup';
import { SubjectSummary } from '@/types/subject';
import { subjectApi } from '@/services/subjectApi';
import teachersApi from "@/services/teacherApi.ts";

const SubjectGroupForm: React.FC<SubjectGroupFormProps> = ({
  subjectGroup,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formData, setFormData] = useState<SubjectGroupFormData>({
    group_name: '',
    subject_code: '',
    description: '',
    leader_user_id: '',
  });

  const [errors, setErrors] = useState<SubjectGroupFormErrors>({});
  const [subjects, setSubjects] = useState<SubjectSummary[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);

  // Load form data
  useEffect(() => {
    if (open) {
      loadFormData().then( );
      if (subjectGroup) {
        setFormData({
          group_name: subjectGroup.name,
          subject_code: subjectGroup.subject,
          description: subjectGroup.description || '',
          leader_user_id: subjectGroup.leader_id || '',
        });
      } else {
        setFormData({
          group_name: '',
          subject_code: '',
          description: '',
          leader_user_id: '',
        });
      }
      setErrors({});
    }
  }, [open, subjectGroup]);

  const loadFormData = async () => {
    try {
      const subjectsResponse = await subjectApi.getSubjectSummaries();
      if (subjectsResponse.success && subjectsResponse.data) {
        setSubjects(subjectsResponse.data);
      } else {
        setSubjects([]);
      }
      // 调用教师简要信息接口
      const teachersResponse = await teachersApi.getTeacherSummaries('tenant_zhanghan', true);
      if (teachersResponse.success && teachersResponse.data) {
        setTeachers(teachersResponse.data);
      } else {
        setTeachers([]);
      }
    } catch (error) {
      console.error('Failed to load form data:', error);
      toast.error('加载数据失败');
    }
  };

  const handleInputChange = (field: keyof SubjectGroupFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (field === 'group_name' && errors.name) {
      setErrors(prev => ({ ...prev, name: '' }));
    } else if (field === 'subject_code' && errors.subject) {
      setErrors(prev => ({ ...prev, subject: '' }));
    } else if (field === 'description' && errors.description) {
      setErrors(prev => ({ ...prev, description: '' }));
    } else if (field === 'leader_user_id' && errors.leader_user_id) {
      setErrors(prev => ({ ...prev, leader_user_id: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: SubjectGroupFormErrors = {};

    if (!formData.group_name.trim()) {
      newErrors.name = '请输入学科组名称';
    } else if (formData.group_name.trim().length < 2) {
      newErrors.name = '学科组名称至少2个字符';
    }

    if (!formData.subject_code) {
      newErrors.subject = '请选择学科';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      // 字段映射，确保与后端一致
      await onSubmit({
        group_name: formData.group_name.trim(),
        subject_code: formData.subject_code,
        description: formData.description?.trim() || undefined,
        leader_user_id: formData.leader_user_id && formData.leader_user_id !== 'none' ? formData.leader_user_id : undefined,
      });
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const isEditing = !!subjectGroup;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 rounded-full bg-blue-50">
              <BookOpen className="h-5 w-5 text-blue-600" />
            </div>
            {isEditing ? '编辑学科组' : '创建学科组'}
          </DialogTitle>
          <DialogDescription className="text-base">
            {isEditing
              ? '修改学科组的基本信息，确保信息准确完整'
              : '创建一个新的学科组，统一管理特定学科的教学工作和人员配置'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-8 py-4">
          {/* 基本信息区域 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2 border-b">
              <Info className="h-4 w-4 text-blue-600" />
              <h3 className="font-semibold text-gray-900">基本信息</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 学科组名称 */}
              <div className="space-y-3">
                <Label htmlFor="name" className="text-sm font-medium flex items-center gap-1">
                  学科组名称
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.group_name}
                  onChange={(e) => handleInputChange('group_name', e.target.value)}
                  placeholder="例如：高中数学组"
                  className={`transition-colors ${errors.name ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'}`}
                />
                {errors.name && (
                  <div className="flex items-center gap-1 text-sm text-red-600">
                    <AlertCircle className="h-3 w-3" />
                    {errors.name}
                  </div>
                )}
              </div>

              {/* 学科选择 */}
              <div className="space-y-3">
                <Label htmlFor="subject" className="text-sm font-medium flex items-center gap-1">
                  所属学科
                  <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.subject_code}
                  onValueChange={(value) => handleInputChange('subject_code', value)}
                >
                  <SelectTrigger className={`transition-colors ${errors.subject ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'}`}>
                    <SelectValue placeholder="请选择学科" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.filter(s => s.is_active).map((subject) => (
                      <SelectItem key={subject.code} value={subject.code}>
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-4 w-4 text-blue-500" />
                          {subject.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.subject && (
                  <div className="flex items-center gap-1 text-sm text-red-600">
                    <AlertCircle className="h-3 w-3" />
                    {errors.subject}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 人员配置区域 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2 border-b">
              <User className="h-4 w-4 text-green-600" />
              <h3 className="font-semibold text-gray-900">人员配置</h3>
            </div>

            {/* 学科组组长 */}
            <div className="space-y-3">
              <Label htmlFor="leader_id" className="text-sm font-medium">
                学科组组长
                <span className="text-gray-500 text-xs ml-1">(可选)</span>
              </Label>
              <Select
                value={formData.leader_user_id}
                onValueChange={(value) => handleInputChange('leader_user_id', value)}
              >
                <SelectTrigger className="focus:border-blue-500">
                  <SelectValue placeholder="请选择组长，可稍后指定" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">暂不指定组长</SelectItem>
                  {teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-green-500" />
                        {teacher.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                组长负责协调学科组的教学工作和人员管理
              </p>
            </div>
          </div>

          {/* 详细信息区域 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 pb-2 border-b">
              <FileText className="h-4 w-4 text-purple-600" />
              <h3 className="font-semibold text-gray-900">详细信息</h3>
            </div>

            {/* 描述 */}
            <div className="space-y-3">
              <Label htmlFor="description" className="text-sm font-medium">
                学科组描述
                <span className="text-gray-500 text-xs ml-1">(可选)</span>
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="描述学科组的职责、目标或特色..."
                rows={4}
                className="resize-none focus:border-blue-500"
              />
              <p className="text-xs text-gray-500">
                详细描述有助于其他教师了解学科组的定位和职责
              </p>
            </div>
          </div>

          <DialogFooter className="gap-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="min-w-[100px]"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? '保存更改' : '创建学科组'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectGroupForm;