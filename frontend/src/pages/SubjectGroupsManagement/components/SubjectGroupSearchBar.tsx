import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Search,
  Filter,
  X,
  Grid3X3,
  List
} from 'lucide-react';
import { SubjectSummary } from '@/types/subject';

export interface SearchFilters {
  keyword: string;
  subject: string;
  status: string;
  hasLeader: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export type ViewMode = 'table' | 'card';

interface SubjectGroupSearchBarProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  subjects: SubjectSummary[];
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

const SubjectGroupSearchBar: React.FC<SubjectGroupSearchBarProps> = ({
  filters,
  onFiltersChange,
  subjects,
  viewMode,
  onViewModeChange
}) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [localKeyword, setLocalKeyword] = useState(filters.keyword);

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localKeyword !== filters.keyword) {
        onFiltersChange({ ...filters, keyword: localKeyword });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [localKeyword]);

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const clearFilters = () => {
    const clearedFilters: SearchFilters = {
      keyword: '',
      subject: 'all',
      status: 'all',
      hasLeader: 'all',
      sortBy: 'created_at',
      sortOrder: 'desc'
    };
    setLocalKeyword('');
    onFiltersChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.subject && filters.subject !== 'all') count++;
    if (filters.status && filters.status !== 'all') count++;
    if (filters.hasLeader && filters.hasLeader !== 'all') count++;
    return count;
  };

  const viewModeOptions = [
    { value: 'table' as ViewMode, icon: List, label: '表格视图' },
    { value: 'card' as ViewMode, icon: Grid3X3, label: '卡片视图' }
  ];

  return (
    <div className="space-y-4">
      {/* 主搜索栏 */}
      <div className="flex items-center space-x-4">
        {/* 搜索输入框 */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索学科组名称、学科或组长..."
            value={localKeyword}
            onChange={(e) => setLocalKeyword(e.target.value)}
            className="pl-10 pr-4"
          />
          {localKeyword && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => setLocalKeyword('')}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* 高级筛选 */}
        <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <Filter className="w-4 h-4 mr-2" />
              筛选
              {getActiveFiltersCount() > 0 && (
                <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">高级筛选</h4>
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  清空
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* 学科筛选 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">学科</label>
                  <Select
                    value={filters.subject}
                    onValueChange={(value) => handleFilterChange('subject', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="全部学科" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部学科</SelectItem>
                      {subjects.map((subject) => (
                        <SelectItem key={subject.code} value={subject.code}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 状态筛选 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">状态</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="全部状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="active">活跃</SelectItem>
                      <SelectItem value="inactive">停用</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 组长筛选 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">组长</label>
                  <Select
                    value={filters.hasLeader}
                    onValueChange={(value) => handleFilterChange('hasLeader', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="全部" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="yes">已分配</SelectItem>
                      <SelectItem value="no">未分配</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 排序方式 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">排序</label>
                  <Select
                    value={`${filters.sortBy}_${filters.sortOrder}`}
                    onValueChange={(value) => {
                      const [sortBy, sortOrder] = value.split('_');
                      handleFilterChange('sortBy', sortBy);
                      handleFilterChange('sortOrder', sortOrder);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="created_at_desc">创建时间 (新到旧)</SelectItem>
                      <SelectItem value="created_at_asc">创建时间 (旧到新)</SelectItem>
                      <SelectItem value="group_name_asc">名称 (A-Z)</SelectItem>
                      <SelectItem value="group_name_desc">名称 (Z-A)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>



        {/* 视图切换 */}
        <div className="flex items-center border rounded-lg p-1">
          {viewModeOptions.map((option) => {
            const Icon = option.icon;
            return (
              <Button
                key={option.value}
                variant={viewMode === option.value ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange(option.value)}
                className="h-8 w-8 p-0"
                title={option.label}
              >
                <Icon className="h-4 w-4" />
              </Button>
            );
          })}
        </div>


      </div>

      {/* 活跃筛选标签 */}
      {getActiveFiltersCount() > 0 && (
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">已应用筛选:</span>
          {filters.subject && filters.subject !== 'all' && (
            <Badge variant="secondary" className="text-xs">
              学科: {subjects.find(s => s.code === filters.subject)?.name || filters.subject}
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-3 w-3 p-0"
                onClick={() => handleFilterChange('subject', 'all')}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          {filters.status && filters.status !== 'all' && (
            <Badge variant="secondary" className="text-xs">
              状态: {filters.status === 'active' ? '活跃' : '停用'}
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-3 w-3 p-0"
                onClick={() => handleFilterChange('status', 'all')}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          {filters.hasLeader && filters.hasLeader !== 'all' && (
            <Badge variant="secondary" className="text-xs">
              组长: {filters.hasLeader === 'yes' ? '已分配' : '未分配'}
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-3 w-3 p-0"
                onClick={() => handleFilterChange('hasLeader', 'all')}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

export default SubjectGroupSearchBar;
