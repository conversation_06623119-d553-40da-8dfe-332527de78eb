import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  BookOpen,
  Users,
  Calendar,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Crown,
  Activity,
  CheckCircle2,
  Settings
} from 'lucide-react';
import { SubjectGroupTableProps } from '@/types/subjectGroup';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import {cn} from "@/lib/utils.ts";

interface EnhancedSubjectGroupTableProps extends Omit<SubjectGroupTableProps, 'onToggleStatus'> {
  onViewDetails?: (subjectGroup: SubjectGroupsDetail) => void;
  onAssignLeader?: (subjectGroup: SubjectGroupsDetail) => void;
  onManageMembers?: (subjectGroup: SubjectGroupsDetail) => void;
  onToggleStatus?: (subjectGroup: SubjectGroupsDetail) => void;
}

const SubjectGroupTable: React.FC<EnhancedSubjectGroupTableProps> = ({
  subjectGroups,
  loading = false,
  onEdit,
  onDelete,
  onViewDetails,
  onAssignLeader,
  onManageMembers,
  onToggleStatus
}) => {
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);
  if (loading) {
    return (
      <div className="space-y-4 p-6">
        <div className="space-y-3">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-4 p-4 bg-white/50 rounded-lg border border-gray-100">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-3 w-1/6" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-8 w-24" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (subjectGroups.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="mx-auto w-24 h-24 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-full flex items-center justify-center mb-6">
          <BookOpen className="h-12 w-12 text-blue-500" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无学科组</h3>
        <p className="text-gray-500 mb-6">开始创建第一个学科组来管理教师团队</p>
        <div className="flex justify-center space-x-4 text-sm text-gray-400">
          <div className="flex items-center space-x-1">
            <Crown className="h-4 w-4" />
            <span>组长管理</span>
          </div>
          <div className="flex items-center space-x-1">
            <Users className="h-4 w-4" />
            <span>成员协作</span>
          </div>
          <div className="flex items-center space-x-1">
            <Activity className="h-4 w-4" />
            <span>状态跟踪</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white/90 backdrop-blur-sm rounded-lg border border-gray-200/60 shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-gradient-to-r from-gray-50/80 to-blue-50/30 border-b border-gray-200/60">
              <TableHead className="font-semibold text-gray-700">学科组信息</TableHead>
              <TableHead className="font-semibold text-gray-700">学科</TableHead>
              <TableHead className="font-semibold text-gray-700">组长</TableHead>
              <TableHead className="font-semibold text-gray-700">状态</TableHead>
              <TableHead className="font-semibold text-gray-700">成员统计</TableHead>
              <TableHead className="font-semibold text-gray-700">创建时间</TableHead>
              <TableHead className="text-right font-semibold text-gray-700">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subjectGroups.map((subjectGroup) => {
              const isHovered = hoveredRow === String(subjectGroup.id);

              return (
                <TableRow
                  key={String(subjectGroup.id)}
                  className={cn(
                    "group transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/20 border-b border-gray-100/60",
                    isHovered && "shadow-sm"
                  )}
                  onMouseEnter={() => setHoveredRow(String(subjectGroup.id))}
                  onMouseLeave={() => setHoveredRow(null)}
                >

                  {/* 学科组信息 */}
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-3">
                      <div className={cn(
                        "p-2 rounded-lg transition-colors",
                        subjectGroup.is_active
                          ? "bg-gradient-to-br from-blue-100 to-indigo-100"
                          : "bg-gray-100"
                      )}>
                        <BookOpen className={cn(
                          "h-4 w-4",
                          subjectGroup.is_active ? "text-blue-600" : "text-gray-500"
                        )} />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {subjectGroup.group_name}
                        </div>
                        {subjectGroup.description && (
                          <div className="text-xs text-gray-500 mt-1 line-clamp-1">
                            {subjectGroup.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>

                  {/* 学科 */}
                  <TableCell>
                    <Badge
                      variant="outline"
                      className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200/60 shadow-sm"
                    >
                      {subjectGroup.subject_name || subjectGroup.subject_code}
                    </Badge>
                  </TableCell>

                  {/* 组长 */}
                  <TableCell>
                    {subjectGroup.teacher_name ? (
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-7 w-7 ring-2 ring-white shadow-sm">
                          <AvatarImage src="" />
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs">
                            {subjectGroup.teacher_name.slice(-2)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center space-x-1">
                            <Crown className="h-3 w-3 text-yellow-500" />
                            <span className="text-sm font-medium text-gray-900">
                              {subjectGroup.teacher_name}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">学科组长</div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 text-gray-500">
                        <div className="h-7 w-7 rounded-full bg-gray-100 flex items-center justify-center">
                          <Users className="h-3 w-3" />
                        </div>
                        <div>
                          <div className="text-sm">未指定组长</div>
                          <div className="text-xs">待分配</div>
                        </div>
                      </div>
                    )}
                  </TableCell>

                  {/* 状态 */}
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        "h-2 w-2 rounded-full animate-pulse",
                        subjectGroup.is_active ? "bg-green-400" : "bg-gray-400"
                      )} />
                      <Badge
                        variant={subjectGroup.is_active ? "default" : "secondary"}
                        className={cn(
                          "text-xs shadow-sm cursor-pointer transition-colors",
                          subjectGroup.is_active
                            ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200/60 hover:from-green-200 hover:to-emerald-200"
                            : "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-600 border-gray-200/60 hover:from-gray-200 hover:to-slate-200"
                        )}
                        onClick={() => onToggleStatus?.(subjectGroup)}
                      >
                        <Activity className="h-3 w-3 mr-1" />
                        {subjectGroup.is_active ? '活跃' : '停用'}
                      </Badge>
                    </div>
                  </TableCell>

                  {/* 成员统计 */}
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1 bg-blue-50/80 px-2 py-1 rounded-full">
                          <Users className="h-3 w-3 text-blue-600" />
                          <span className="text-xs font-medium text-blue-700">{subjectGroup.member_count || 0} 成员</span>
                        </div>
                        {(subjectGroup.active_member_count || 0) > 0 && (
                          <div className="flex items-center space-x-1 bg-green-50/80 px-2 py-1 rounded-full">
                            <div className="h-2 w-2 rounded-full bg-green-400"></div>
                            <span className="text-xs font-medium text-green-700">{subjectGroup.active_member_count} 活跃</span>
                          </div>
                        )}
                      </div>
                      {onManageMembers && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          onClick={() => onManageMembers(subjectGroup)}
                        >
                          <Settings className="h-3 w-3 mr-1" />
                          管理
                        </Button>
                      )}
                    </div>
                  </TableCell>

                  {/* 创建时间 */}
                  <TableCell>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {subjectGroup.created_at
                          ? new Date(subjectGroup.created_at as string).toLocaleDateString('zh-CN', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })
                          : '未知'
                        }
                      </span>
                    </div>
                  </TableCell>

                  {/* 操作 */}
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-1">
                      {/* 快速操作按钮 */}
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        {onViewDetails && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600"
                            onClick={() => onViewDetails(subjectGroup)}
                            title="查看详情"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600"
                          onClick={() => onEdit(subjectGroup)}
                          title="编辑"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* 更多操作菜单 */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem onClick={() => onEdit(subjectGroup)}>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑学科组
                          </DropdownMenuItem>

                          {onViewDetails && (
                            <DropdownMenuItem onClick={() => onViewDetails(subjectGroup)}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看详情
                            </DropdownMenuItem>
                          )}

                          {onManageMembers && (
                            <DropdownMenuItem onClick={() => onManageMembers(subjectGroup)}>
                              <Users className="h-4 w-4 mr-2" />
                              成员管理
                            </DropdownMenuItem>
                          )}

                          {onAssignLeader && !subjectGroup.teacher_name && (
                            <DropdownMenuItem onClick={() => onAssignLeader(subjectGroup)}>
                              <Crown className="h-4 w-4 mr-2" />
                              分配组长
                            </DropdownMenuItem>
                          )}

                          {onToggleStatus && (
                            <DropdownMenuItem onClick={() => onToggleStatus(subjectGroup)}>
                              <Activity className="h-4 w-4 mr-2" />
                              {subjectGroup.is_active ? '停用' : '启用'}
                            </DropdownMenuItem>
                          )}

                          <DropdownMenuSeparator />

                          <DropdownMenuItem
                            onClick={() => onDelete(String(subjectGroup.id))}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除学科组
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* 底部统计信息 */}
      {subjectGroups.length > 0 && (
        <div className="flex items-center justify-between px-6 py-3 bg-gradient-to-r from-gray-50/80 to-blue-50/30 border-t border-gray-200/60 text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>共 {subjectGroups.length} 个学科组</span>
            <span>•</span>
            <span>{subjectGroups.filter(g => g.is_active).length} 个活跃</span>
            <span>•</span>
            <span>{subjectGroups.filter(g => g.teacher_name).length} 个已分配组长</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubjectGroupTable;