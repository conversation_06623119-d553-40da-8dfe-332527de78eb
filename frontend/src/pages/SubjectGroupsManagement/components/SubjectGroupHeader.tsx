import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Users, 
  BookOpen, 
  TrendingUp, 
  Activity,
  Download,
  Settings
} from 'lucide-react';

interface SubjectGroupHeaderProps {
  totalGroups: number;
  activeGroups: number;
  totalTeachers: number;
  totalSubjects: number;
  onCreateGroup: () => void;
  onExport?: () => void;
  onSettings?: () => void;
}

const SubjectGroupHeader: React.FC<SubjectGroupHeaderProps> = ({
  totalGroups,
  activeGroups,
  totalTeachers,
  totalSubjects,
  onCreateGroup,
  onExport,
  onSettings
}) => {
  const stats = [
    {
      title: '学科组总数',
      value: totalGroups,
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: '活跃学科组',
      value: activeGroups,
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+8%',
      changeType: 'positive' as const
    },
    {
      title: '教师总数',
      value: totalTeachers,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15%',
      changeType: 'positive' as const
    },
    {
      title: '涉及学科',
      value: totalSubjects,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '0%',
      changeType: 'neutral' as const
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题和操作区 */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            学科组管理
          </h1>
          <p className="text-gray-600">
            统一管理学校的学科组织架构，优化教学资源配置
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {onSettings && (
            <Button variant="outline" size="sm" onClick={onSettings}>
              <Settings className="w-4 h-4 mr-2" />
              设置
            </Button>
          )}
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="w-4 h-4 mr-2" />
              导出
            </Button>
          )}
          <Button onClick={onCreateGroup} className="shadow-sm">
            <Plus className="w-4 h-4 mr-2" />
            新建学科组
          </Button>
        </div>
      </div>

      {/* 统计概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-600">
                      {stat.title}
                    </p>
                    <div className="flex items-baseline space-x-2">
                      <p className="text-2xl font-bold text-gray-900">
                        {stat.value.toLocaleString()}
                      </p>
                      <Badge 
                        variant={stat.changeType === 'positive' ? 'default' : 'secondary'}
                        className={`text-xs ${
                          stat.changeType === 'positive' 
                            ? 'bg-green-100 text-green-700 hover:bg-green-100' 
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        {stat.change}
                      </Badge>
                    </div>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                
                {/* 装饰性渐变背景 */}
                <div className={`absolute top-0 right-0 w-20 h-20 ${stat.bgColor} opacity-20 rounded-full -translate-y-10 translate-x-10`} />
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default SubjectGroupHeader;
