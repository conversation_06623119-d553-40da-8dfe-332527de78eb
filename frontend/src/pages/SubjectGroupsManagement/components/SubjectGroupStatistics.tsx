import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import {
  Users,
  BookOpen,
  Building2,
  TrendingUp,
  Activity,
  Target,
  Calendar,
  Award,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  RefreshCw,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

import { SubjectGroupStatistics as StatisticsType } from '@/types/subjectGroup';
import { subjectGroupApi } from '@/services/subjectGroupApi';

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];
const GRADIENT_COLORS = [
  { start: '#3B82F6', end: '#1D4ED8' },
  { start: '#10B981', end: '#059669' },
  { start: '#F59E0B', end: '#D97706' },
  { start: '#EF4444', end: '#DC2626' },
  { start: '#8B5CF6', end: '#7C3AED' },
  { start: '#06B6D4', end: '#0891B2' }
];

const SubjectGroupStatistics: React.FC = () => {
  const [statistics, setStatistics] = useState<StatisticsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'line'>('bar');

  const loadStatistics = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response = await subjectGroupApi.getSubjectGroupStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
      toast.error('加载统计数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadStatistics(true);
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    toast.success('统计报告导出功能开发中...');
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  if (loading) {
    return (
      <div className="space-y-8">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-[200px]" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-[80px]" />
            <Skeleton className="h-9 w-[80px]" />
          </div>
        </div>

        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[80px]" />
                    <Skeleton className="h-8 w-[60px]" />
                  </div>
                  <Skeleton className="h-12 w-12 rounded-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <Skeleton className="h-6 w-[180px]" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[350px]" />
            </CardContent>
          </Card>
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <Skeleton className="h-6 w-[180px]" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[350px]" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="p-4 rounded-full bg-gray-100 mb-4">
          <BarChart3 className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无统计数据</h3>
        <p className="text-gray-500 mb-6">请先创建学科组，然后查看详细的数据分析</p>
        <Button onClick={() => loadStatistics()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          重新加载
        </Button>
      </div>
    );
  }

  // Prepare enhanced chart data
  const subjectDistributionData = statistics.subject_distribution.map((item, index) => ({
    name: item.subject_name,
    groups: item.group_count,
    teachers: item.teacher_count,
    classes: item.class_count,
    color: COLORS[index % COLORS.length],
    fill: COLORS[index % COLORS.length]
  }));

  const statusData = [
    { name: '活跃学科组', value: statistics.active_groups, color: COLORS[1], fill: COLORS[1] },
    { name: '停用学科组', value: statistics.inactive_groups, color: COLORS[3], fill: COLORS[3] },
  ];

  const performanceData = statistics.subject_distribution.map((item, index) => ({
    subject: item.subject_name,
    efficiency: Math.floor(Math.random() * 30) + 70, // Mock data
    satisfaction: Math.floor(Math.random() * 20) + 80, // Mock data
    color: COLORS[index % COLORS.length]
  }));

  return (
    <div className="space-y-6">
      {/* 概览统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总学科组数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total_groups}</div>
            <p className="text-xs text-muted-foreground">
              学校内所有学科组
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">启用学科组</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.active_groups}</div>
            <p className="text-xs text-muted-foreground">
              正在使用的学科组
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">启用率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics.total_groups > 0
                ? Math.round((statistics.active_groups / statistics.total_groups) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              学科组启用比率
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 学科分布柱状图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              学科分布统计
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={subjectDistributionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="groups" name="学科组数" fill="#3B82F6" />
                <Bar dataKey="teachers" name="教师数" fill="#10B981" />
                <Bar dataKey="classes" name="班级数" fill="#F59E0B" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 状态分布饼图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              状态分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value, percent }) =>
                      `${name}: ${value} (${percent ? (percent * 100).toFixed(0) : '0'}%)`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 详细列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            学科组详细统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {statistics.subject_distribution.map((item, index) => (
              <div
                key={item.subject}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold">{item.subject_name}</h4>
                    <p className="text-sm text-gray-500">学科代码: {item.subject}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="font-semibold">{item.group_count}</div>
                    <div className="text-gray-500">学科组</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{item.teacher_count}</div>
                    <div className="text-gray-500">教师</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{item.class_count}</div>
                    <div className="text-gray-500">班级</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubjectGroupStatistics;