import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Crown, 
  Search, 
  Users, 
  BookOpen,
  AlertCircle,
  CheckCircle,
  Loader2,
  User
} from 'lucide-react';
import { toast } from 'sonner';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import { TeacherSummary } from '@/types/teacher';
import { teacherApi } from '@/services/teacherApi';
import { userIdentityApi } from '@/services/roleApi';
import { SubjectGroupsApi } from '@/services/SubjectGroupsApi';
import { useAuth } from '@/contexts/AuthContext';

interface SubjectGroupLeaderAssignmentProps {
  open: boolean;
  onClose: () => void;
  subjectGroup: SubjectGroupsDetail | null;
  onAssignmentComplete: () => void;
}

const SubjectGroupLeaderAssignment: React.FC<SubjectGroupLeaderAssignmentProps> = ({
  open,
  onClose,
  subjectGroup,
  onAssignmentComplete
}) => {
  const { tenant } = useAuth();
  const tenant_name = tenant?.schema_name ?? '';
  
  const [teachers, setTeachers] = useState<TeacherSummary[]>([]);
  const [filteredTeachers, setFilteredTeachers] = useState<TeacherSummary[]>([]);
  const [selectedTeacherId, setSelectedTeacherId] = useState<string>('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [loading, setLoading] = useState(false);
  const [teachersLoading, setTeachersLoading] = useState(false);
  const [assignmentType, setAssignmentType] = useState<'assign' | 'remove'>('assign');

  // 加载教师列表
  const loadTeachers = async () => {
    try {
      setTeachersLoading(true);
      const response = await teacherApi.getTeacherSummaries(tenant_name, true);
      if (response.success && response.data) {
        setTeachers(response.data);
        setFilteredTeachers(response.data);
      }
    } catch (error) {
      console.error('Failed to load teachers:', error);
      toast.error('加载教师列表失败');
    } finally {
      setTeachersLoading(false);
    }
  };

  // 筛选教师
  useEffect(() => {
    if (!searchKeyword) {
      setFilteredTeachers(teachers);
    } else {
      const filtered = teachers.filter(teacher =>
        teacher.teacher_name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        teacher.employee_id.toLowerCase().includes(searchKeyword.toLowerCase())
      );
      setFilteredTeachers(filtered);
    }
  }, [searchKeyword, teachers]);

  // 初始化
  useEffect(() => {
    if (open && subjectGroup) {
      loadTeachers();
      setSearchKeyword('');
      setSelectedTeacherId('');
      setAssignmentType(subjectGroup.leader_user_id ? 'remove' : 'assign');
    }
  }, [open, subjectGroup]);

  // 分配学科组长
  const handleAssignLeader = async () => {
    if (!subjectGroup || !selectedTeacherId) return;

    try {
      setLoading(true);

      const selectedTeacher = teachers.find(t => t.id === selectedTeacherId);
      if (!selectedTeacher?.user_id) {
        toast.error('所选教师没有关联的用户账号');
        return;
      }

      // 1. 更新学科组的组长信息
      await SubjectGroupsApi.updateSubjectGroups(tenant_name, {
        id: subjectGroup.id,
        group_name: subjectGroup.group_name,
        subject_code: subjectGroup.subject_code,
        description: subjectGroup.description,
        leader_user_id: selectedTeacher.user_id
      });

      // 2. 分配学科组长角色
      try {
        await userIdentityApi.assignRole({
          user_id: selectedTeacher.user_id,
          tenant_id: tenant?.tenant_id || '',
          role_id: 'subject_leader', // 学科组长角色ID
          target_type: 'subject_group',
          target_id: String(subjectGroup.id),
          subject: subjectGroup.subject_code,
          display_name: `${subjectGroup.group_name}组长`
        });
      } catch (roleError) {
        console.warn('Role assignment failed, but subject group leader was updated:', roleError);
        // 角色分配失败不影响学科组长的设置
      }

      toast.success(`成功分配 ${selectedTeacher.teacher_name} 为 ${subjectGroup.group_name} 组长`);
      onAssignmentComplete();
      onClose();
    } catch (error) {
      console.error('Failed to assign leader:', error);
      toast.error('分配学科组长失败');
    } finally {
      setLoading(false);
    }
  };

  // 移除学科组长
  const handleRemoveLeader = async () => {
    if (!subjectGroup?.leader_user_id) return;

    try {
      setLoading(true);

      // 1. 更新学科组信息，移除组长
      await SubjectGroupsApi.updateSubjectGroups(tenant_name, {
        id: subjectGroup.id,
        group_name: subjectGroup.group_name,
        subject_code: subjectGroup.subject_code,
        description: subjectGroup.description,
        leader_user_id: null
      });

      // 2. 移除学科组长角色 (如果需要的话)
      // TODO: 实现移除角色的API调用
      // 这里可能需要查询用户的身份ID然后移除

      toast.success(`成功移除 ${subjectGroup.teacher_name} 的组长职务`);
      onAssignmentComplete();
      onClose();
    } catch (error) {
      console.error('Failed to remove leader:', error);
      toast.error('移除学科组长失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (assignmentType === 'assign') {
      handleAssignLeader();
    } else {
      handleRemoveLeader();
    }
  };

  if (!subjectGroup) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-yellow-50">
              <Crown className="h-5 w-5 text-yellow-600" />
            </div>
            学科组长管理
          </DialogTitle>
          <DialogDescription>
            为 "{subjectGroup.group_name}" 分配或更换学科组长
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 学科组信息 */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200/60">
            <div className="flex items-center gap-3">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900">{subjectGroup.group_name}</h3>
                <p className="text-sm text-gray-600">
                  学科：{subjectGroup.subject_name || subjectGroup.subject_code}
                </p>
              </div>
            </div>
          </div>

          {/* 当前组长信息 */}
          {subjectGroup.teacher_name && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200/60">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="" />
                    <AvatarFallback className="bg-gradient-to-br from-green-500 to-blue-600 text-white">
                      {subjectGroup.teacher_name.slice(-2)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-yellow-500" />
                      <span className="font-semibold text-gray-900">
                        {subjectGroup.teacher_name}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">当前学科组长</p>
                  </div>
                </div>
                <Badge className="bg-green-100 text-green-700">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  在任
                </Badge>
              </div>
            </div>
          )}

          {/* 操作类型选择 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">操作类型</Label>
            <Select value={assignmentType} onValueChange={(value) => setAssignmentType(value as 'assign' | 'remove')}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="assign">
                  {subjectGroup.teacher_name ? '更换组长' : '分配组长'}
                </SelectItem>
                {subjectGroup.teacher_name && (
                  <SelectItem value="remove">移除组长</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* 教师选择 */}
          {assignmentType === 'assign' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">选择新组长</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索教师姓名或工号..."
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* 教师列表 */}
              <div className="max-h-64 overflow-y-auto space-y-2 border rounded-lg p-2">
                {teachersLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-500">加载教师列表...</span>
                  </div>
                ) : filteredTeachers.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p>没有找到符合条件的教师</p>
                  </div>
                ) : (
                  filteredTeachers.map((teacher) => (
                    <div
                      key={teacher.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedTeacherId === teacher.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedTeacherId(teacher.id)}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="" />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs">
                          {teacher.teacher_name.slice(-2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">
                            {teacher.teacher_name}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {teacher.employee_id}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">
                          {teacher.title || '教师'}
                        </p>
                      </div>
                      {selectedTeacherId === teacher.id && (
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* 移除确认 */}
          {assignmentType === 'remove' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900">确认移除组长</h4>
                  <p className="text-sm text-red-700 mt-1">
                    将移除 {subjectGroup.teacher_name} 的学科组长职务，该操作不可撤销。
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={loading || (assignmentType === 'assign' && !selectedTeacherId)}
            variant={assignmentType === 'remove' ? 'destructive' : 'default'}
          >
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {assignmentType === 'assign' 
              ? (subjectGroup.teacher_name ? '更换组长' : '分配组长')
              : '移除组长'
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectGroupLeaderAssignment;
