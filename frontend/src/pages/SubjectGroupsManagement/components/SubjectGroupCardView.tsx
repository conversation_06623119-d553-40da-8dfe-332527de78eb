import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import {
  BookOpen,
  Users,
  Calendar,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Activity,
  Settings,
  Crown,
  CheckCircle2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SubjectGroupsDetail } from '@/types/subjectGroups';

interface SubjectGroupCardViewProps {
  subjectGroups: SubjectGroupsDetail[];
  loading?: boolean;
  onEdit: (subjectGroup: SubjectGroupsDetail) => void;
  onDelete: (id: string) => void;
  onViewDetails?: (subjectGroup: SubjectGroupsDetail) => void;
  onAssignLeader?: (subjectGroup: SubjectGroupsDetail) => void;
  onManageMembers?: (subjectGroup: SubjectGroupsDetail) => void;
  onToggleStatus?: (subjectGroup: SubjectGroupsDetail) => void;
}

const SubjectGroupCardView: React.FC<SubjectGroupCardViewProps> = ({
  subjectGroups,
  loading = false,
  onEdit,
  onDelete,
  onViewDetails,
  onAssignLeader,
  onManageMembers,
  onToggleStatus
}) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="animate-pulse bg-white/80 backdrop-blur-sm border border-gray-200/60">
            <div className="h-1 w-full bg-gray-200 rounded-t-lg" />
            <CardHeader className="pb-3 pt-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2 flex-1">
                  <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                  <div className="flex space-x-2">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="h-4 bg-gray-200 rounded w-12"></div>
                  </div>
                </div>
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <div className="h-9 w-9 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-1">
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    <div className="h-2 bg-gray-200 rounded w-1/3"></div>
                  </div>
                </div>
              </div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
              <div className="flex justify-between">
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (subjectGroups.length === 0) {
    return (
      <div className="text-center py-12">
        <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-semibold text-gray-900">暂无学科组</h3>
        <p className="mt-1 text-sm text-gray-500">开始创建第一个学科组吧</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {subjectGroups.map((subjectGroup) => {
        return (
          <Card
            key={String(subjectGroup.id)}
            className="group hover:shadow-xl transition-all duration-300 border shadow-sm hover:shadow-2xl hover:scale-[1.02] cursor-pointer bg-white/80 backdrop-blur-sm hover:bg-white relative border-gray-200/60 hover:border-blue-200/60"
            onClick={() => onViewDetails?.(subjectGroup)}
          >

            {/* 顶部装饰条 */}
            <div className={`h-1 w-full ${
              subjectGroup.is_active
                ? 'bg-gradient-to-r from-green-400 to-blue-500'
                : 'bg-gradient-to-r from-gray-300 to-gray-400'
            }`} />

            <CardHeader className="pb-3 pt-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2 flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors leading-tight">
                      {subjectGroup.group_name}
                    </h3>
                    {subjectGroup.is_active && (
                      <div className="flex items-center">
                        <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse" />
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="outline"
                      className="text-xs bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200/60 shadow-sm"
                    >
                      <BookOpen className="h-3 w-3 mr-1" />
                      {subjectGroup.subject_name || subjectGroup.subject_code}
                    </Badge>
                    <Badge
                      variant={subjectGroup.is_active ? "default" : "secondary"}
                      className={`text-xs shadow-sm cursor-pointer transition-all hover:scale-105 ${
                        subjectGroup.is_active
                          ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200/60 hover:from-green-200 hover:to-emerald-200'
                          : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-600 border-gray-200/60 hover:from-gray-200 hover:to-slate-200'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleStatus?.(subjectGroup);
                      }}
                    >
                      <Activity className="h-3 w-3 mr-1" />
                      {subjectGroup.is_active ? '活跃' : '停用'}
                    </Badge>
                  </div>
                </div>

                {/* 快速操作按钮 */}
                <div className="flex items-center space-x-1">
                  {/* 查看详情按钮 */}
                  {onViewDetails && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewDetails(subjectGroup);
                      }}
                      title="查看详情"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}

                  {/* 更多操作菜单 */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onEdit(subjectGroup);
                      }}>
                        <Edit className="h-4 w-4 mr-2" />
                        编辑学科组
                      </DropdownMenuItem>

                      {onViewDetails && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onViewDetails(subjectGroup);
                        }}>
                          <Eye className="h-4 w-4 mr-2" />
                          查看详情
                        </DropdownMenuItem>
                      )}

                      {onManageMembers && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onManageMembers(subjectGroup);
                        }}>
                          <Users className="h-4 w-4 mr-2" />
                          成员管理
                        </DropdownMenuItem>
                      )}

                      {onAssignLeader && !subjectGroup.teacher_name && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onAssignLeader(subjectGroup);
                        }}>
                          <Crown className="h-4 w-4 mr-2" />
                          分配组长
                        </DropdownMenuItem>
                      )}

                      {onToggleStatus && (
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onToggleStatus(subjectGroup);
                        }}>
                          <Activity className="h-4 w-4 mr-2" />
                          {subjectGroup.is_active ? '停用' : '启用'}
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(String(subjectGroup.id));
                        }}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除学科组
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-5 pt-4">
              {/* 团队信息 */}
              <div className="bg-gradient-to-r from-gray-50/80 to-blue-50/30 rounded-lg p-3 border border-gray-100/60 hover:from-blue-50/50 hover:to-indigo-50/40 transition-colors">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700">团队成员</span>
                  </div>
                  {onManageMembers && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      onClick={(e) => {
                        e.stopPropagation();
                        onManageMembers(subjectGroup);
                      }}
                    >
                      管理成员
                    </Button>
                  )}
                </div>
                
                {/* 组长信息 */}
                {subjectGroup.teacher_name ? (
                  <div className="flex items-center space-x-3 mb-2">
                    <Avatar className="h-8 w-8 ring-2 ring-white shadow-sm">
                      <AvatarImage src="" />
                      <AvatarFallback className="bg-gradient-to-br from-amber-500 to-orange-600 text-white text-xs font-medium">
                        {subjectGroup.teacher_name.slice(-2)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-1">
                        <Crown className="h-3 w-3 text-amber-500" />
                        <span className="text-sm font-medium text-gray-900 truncate">
                          {subjectGroup.teacher_name}
                        </span>
                      </div>
                      <p className="text-xs text-amber-600 font-medium">组长</p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3 mb-2 p-2 bg-gray-50/50 rounded border border-dashed border-gray-200">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <Crown className="h-3 w-3 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">暂无组长</p>
                      <p className="text-xs text-gray-400">需要分配组长</p>
                    </div>
                  </div>
                )}
                
                {/* 成员统计 */}
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                      <span className="text-gray-600">总成员: <span className="font-medium text-gray-900">{subjectGroup.member_count || 0}</span></span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="h-2 w-2 rounded-full bg-green-400"></div>
                      <span className="text-gray-600">活跃: <span className="font-medium text-green-700">{subjectGroup.active_member_count || 0}</span></span>
                    </div>
                  </div>
                </div>
              </div>


              {/* 描述信息 */}
              {subjectGroup.description && (
                <div className="bg-gradient-to-r from-slate-50/80 to-gray-50/60 rounded-lg p-3 border border-gray-100/60">
                  <p className="text-sm text-gray-700 line-clamp-2 leading-relaxed">
                    {subjectGroup.description}
                  </p>
                </div>
              )}

              {/* 底部信息 */}
              <div className="flex items-center justify-between pt-3 border-t border-gray-100/30">
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <div className="flex items-center space-x-1 bg-gray-50/80 px-2 py-1 rounded-full">
                    <Calendar className="h-3 w-3 text-gray-500" />
                    <span className="font-medium">
                      {subjectGroup.created_at
                        ? new Date(subjectGroup.created_at as string).toLocaleDateString('zh-CN', {
                            month: 'short',
                            day: 'numeric'
                          })
                        : '未知'
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* 悬停时的装饰性元素 */}
              <div className="absolute -bottom-2 -right-2 w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-full opacity-0 group-hover:opacity-30 transition-all duration-300 transform group-hover:scale-110" />
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default SubjectGroupCardView;
