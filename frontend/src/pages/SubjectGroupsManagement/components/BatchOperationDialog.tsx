import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Trash2, 
  Edit, 
  Power, 
  PowerOff,
  Users,
  AlertTriangle
} from 'lucide-react';
import { SubjectGroupsDetail } from '@/types/subjectGroups';

export type BatchOperation = 'delete' | 'activate' | 'deactivate' | 'assign_leader' | 'update_description';

interface BatchOperationDialogProps {
  open: boolean;
  onClose: () => void;
  selectedGroups: SubjectGroupsDetail[];
  onConfirm: (operation: BatchOperation, data?: any) => void;
  loading?: boolean;
}

const BatchOperationDialog: React.FC<BatchOperationDialogProps> = ({
  open,
  onClose,
  selectedGroups,
  onConfirm,
  loading = false
}) => {
  const [operation, setOperation] = useState<BatchOperation>('activate');
  const [leaderId, setLeaderId] = useState('');
  const [description, setDescription] = useState('');

  const handleConfirm = () => {
    let data: any = undefined;
    
    switch (operation) {
      case 'assign_leader':
        data = { leaderId };
        break;
      case 'update_description':
        data = { description };
        break;
    }
    
    onConfirm(operation, data);
  };

  const getOperationConfig = (op: BatchOperation) => {
    switch (op) {
      case 'delete':
        return {
          title: '批量删除学科组',
          description: '此操作将永久删除选中的学科组，且无法撤销。',
          icon: Trash2,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          confirmText: '确认删除',
          confirmVariant: 'destructive' as const
        };
      case 'activate':
        return {
          title: '批量启用学科组',
          description: '将选中的学科组设置为启用状态。',
          icon: Power,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          confirmText: '确认启用',
          confirmVariant: 'default' as const
        };
      case 'deactivate':
        return {
          title: '批量停用学科组',
          description: '将选中的学科组设置为停用状态。',
          icon: PowerOff,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          confirmText: '确认停用',
          confirmVariant: 'default' as const
        };
      case 'assign_leader':
        return {
          title: '批量分配组长',
          description: '为选中的学科组分配组长。',
          icon: Users,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          confirmText: '确认分配',
          confirmVariant: 'default' as const
        };
      case 'update_description':
        return {
          title: '批量更新描述',
          description: '为选中的学科组更新描述信息。',
          icon: Edit,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          confirmText: '确认更新',
          confirmVariant: 'default' as const
        };
    }
  };

  const config = getOperationConfig(operation);
  const Icon = config.icon;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${config.bgColor}`}>
              <Icon className={`h-5 w-5 ${config.color}`} />
            </div>
            批量操作
          </DialogTitle>
          <DialogDescription>
            已选择 {selectedGroups.length} 个学科组进行批量操作
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 操作类型选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">操作类型</label>
            <Select value={operation} onValueChange={(value) => setOperation(value as BatchOperation)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="activate">启用学科组</SelectItem>
                <SelectItem value="deactivate">停用学科组</SelectItem>
                <SelectItem value="assign_leader">分配组长</SelectItem>
                <SelectItem value="update_description">更新描述</SelectItem>
                <SelectItem value="delete">删除学科组</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 操作说明 */}
          <div className={`p-4 rounded-lg ${config.bgColor} border`}>
            <div className="flex items-start gap-3">
              <Icon className={`h-5 w-5 ${config.color} mt-0.5`} />
              <div>
                <h4 className="font-medium text-gray-900">{config.title}</h4>
                <p className="text-sm text-gray-600 mt-1">{config.description}</p>
              </div>
            </div>
          </div>

          {/* 额外输入字段 */}
          {operation === 'assign_leader' && (
            <div className="space-y-2">
              <label className="text-sm font-medium">选择组长</label>
              <Select value={leaderId} onValueChange={setLeaderId}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择组长" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="teacher1">张老师</SelectItem>
                  <SelectItem value="teacher2">李老师</SelectItem>
                  <SelectItem value="teacher3">王老师</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {operation === 'update_description' && (
            <div className="space-y-2">
              <label className="text-sm font-medium">描述信息</label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="请输入描述信息"
                rows={3}
              />
            </div>
          )}

          {/* 选中的学科组列表 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">选中的学科组</label>
            <div className="max-h-32 overflow-y-auto space-y-2 p-3 bg-gray-50 rounded-lg">
              {selectedGroups.map((group) => (
                <div key={String(group.id)} className="flex items-center justify-between">
                  <span className="text-sm">{group.group_name}</span>
                  <Badge variant="outline" className="text-xs">
                    {group.subject_name || group.subject_code}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* 警告信息 */}
          {operation === 'delete' && (
            <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
              <div className="text-sm text-red-700">
                <p className="font-medium">警告：此操作不可撤销</p>
                <p>删除后的学科组数据将无法恢复，请谨慎操作。</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button 
            variant={config.confirmVariant}
            onClick={handleConfirm}
            disabled={loading || (operation === 'assign_leader' && !leaderId)}
          >
            {loading ? '处理中...' : config.confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BatchOperationDialog;
