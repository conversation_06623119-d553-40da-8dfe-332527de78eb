import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Users,
  Search,
  Crown,
  Shield,
  User,
  Trash2,
  UserPlus,
  Loader2,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import {
  SubjectGroupMember,
  getRoleConfig,
  SubjectGroupMemberStats
} from '@/types/subjectGroupMember';
import { TeacherListVO } from '@/types/teacher';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { teacherApi } from '@/services/teacherApi';
import { roleApi } from '@/services/roleApi';
import { useAuth } from '@/contexts/AuthContext';
import { SimpleRole } from '@/types/role';

interface SubjectGroupMemberManagementProps {
  open: boolean;
  onClose: () => void;
  subjectGroup: SubjectGroupsDetail | null;
  onMembershipChange: () => void;
}

const SubjectGroupMemberManagement: React.FC<SubjectGroupMemberManagementProps> = ({
  open,
  onClose,
  subjectGroup,
  onMembershipChange
}) => {
  const { tenant } = useAuth();
  const tenant_name = tenant?.schema_name ?? '';
  
  const [members, setMembers] = useState<SubjectGroupMember[]>([]);
  const [availableTeachers, setAvailableTeachers] = useState<TeacherListVO[]>([]);
  const [stats, setStats] = useState<SubjectGroupMemberStats | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedTeachers, setSelectedTeachers] = useState<string[]>([]);
  const [defaultRole, setDefaultRole] = useState<string>('subject_group_member');
  const [availableRoles, setAvailableRoles] = useState<SimpleRole[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);

  // 获取角色图标组件
  const getRoleIcon = (roleCode: string) => {
    const config = getRoleConfig(roleCode);
    switch (config.icon) {
      case 'Crown':
        return Crown;
      case 'Shield':
        return Shield;
      default:
        return User;
    }
  };

  // 加载学科组角色
  const loadSubjectGroupRoles = async () => {
    setRolesLoading(true);
    try {
      const response = await roleApi.getSubjectGroupRoles();
      if (response.success && response.data && response.data.length > 0) {
        setAvailableRoles(response.data);
        // 如果当前默认角色不在可用角色列表中，设置为第一个可用角色
        if (!response.data.some(role => role.code === defaultRole)) {
          setDefaultRole(response.data[0].code);
        }
      } else {
        console.warn('获取学科组角色失败:', response.message);
      }
    } catch (error) {
      console.error('加载学科组角色失败:', error);
    } finally {
      setRolesLoading(false);
    }
  };

  // 加载学科组成员
  const loadMembers = async () => {
    if (!subjectGroup) return;

    try {
      const response = await SubjectGroupsApi.getMembers(tenant_name, String(subjectGroup.id));
      if (response.success && response.data) {
        setMembers(response.data);
      }
    } catch (error) {
      console.error('Failed to load members:', error);
    }
  };



  // 加载可用教师（排除已经是成员的教师）
  const loadAvailableTeachers = async () => {
    if (!subjectGroup || !tenant?.tenant_id) return;

    try {
      const response = await teacherApi.pageAllTeacher(tenant.tenant_id, tenant_name, {
        page_params: {
          page: 1,
          page_size: 1000 // 获取足够多的教师数据
        },
        name_like: searchKeyword || undefined,
        is_active: true
      });

      if (response.success && response.data) {
        // 获取当前成员的教师ID列表
        const memberTeacherIds = members.map(member => member.teacher_id);

        // 过滤掉已经是成员的教师
        const availableTeachers = response.data.filter(teacher =>
          !memberTeacherIds.includes(teacher.id)
        );

        setAvailableTeachers(availableTeachers);
      }
    } catch (error) {
      console.error('Failed to load available teachers:', error);
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    if (!subjectGroup || !members) return;

    try {
      // 临时从成员数据计算统计信息
      const stats = {
        total_members: members.length,
        leaders: members.filter(m => m.role_code === 'subject_group_leader').length,
        deputy_leaders: members.filter(m => m.role_code === 'subject_group_deputy_leader').length,
        regular_members: members.filter(m => m.role_code === 'subject_group_member').length,
        active_members: members.filter(m => m.is_active).length,
        inactive_members: members.filter(m => !m.is_active).length,
      };
      setStats(stats);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (open && subjectGroup) {
      setLoading(true);
      Promise.all([
        loadMembers(),
        loadStats(),
        loadSubjectGroupRoles()
      ]).finally(() => {
        setLoading(false);
      });
    }
  }, [open, subjectGroup]);

  // 当成员列表或搜索关键词变化时，重新加载可用教师
  useEffect(() => {
    if (open && subjectGroup) {
      loadAvailableTeachers();
    }
  }, [members, searchKeyword, open, subjectGroup]);

  // 添加成员
  const handleAddMembers = async () => {
    if (!subjectGroup || selectedTeachers.length === 0) return;

    try {
      setLoading(true);
      
      const response = await SubjectGroupsApi.batchAddMembers(
        tenant_name,
        String(subjectGroup.id),
        selectedTeachers.map(teacherId => ({
          teacher_id: teacherId,
          role_code: defaultRole
        }))
      );

      if (response.success) {
        toast.success(`成功添加 ${selectedTeachers.length} 名成员`);
        setSelectedTeachers([]);
        await loadMembers();
        await loadStats();
        onMembershipChange();
      } else {
        toast.error(response.message || '添加成员失败');
      }
    } catch (error) {
      console.error('Failed to add members:', error);
      toast.error('添加成员失败');
    } finally {
      setLoading(false);
    }
  };

  // 移除成员
  const handleRemoveMember = async (memberId: string) => {
    if (!subjectGroup) return;

    try {
      const response = await SubjectGroupsApi.removeMember(tenant_name, String(subjectGroup.id), memberId);
      if (response.success) {
        toast.success('成功移除成员');
        await loadMembers();
        await loadStats();
        onMembershipChange();
      } else {
        toast.error(response.message || '移除成员失败');
      }
    } catch (error) {
      console.error('Failed to remove member:', error);
      toast.error('移除成员失败');
    }
  };

  // 更新成员角色
  const handleUpdateMemberRole = async (memberId: string, newRoleCode: string) => {
    if (!subjectGroup) return;

    try {
      const response = await SubjectGroupsApi.updateMemberRole(
        tenant_name,
        String(subjectGroup.id),
        memberId,
        { role_code: newRoleCode }
      );
      
      if (response.success) {
        toast.success('成功更新成员角色');
        await loadMembers();
        await loadStats();
        onMembershipChange();
      } else {
        toast.error(response.message || '更新成员角色失败');
      }
    } catch (error) {
      console.error('Failed to update member role:', error);
      toast.error('更新成员角色失败');
    }
  };




  if (!subjectGroup) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-blue-50">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            学科组成员管理
          </DialogTitle>
          <DialogDescription>
            管理 {subjectGroup.group_name} 的成员和角色分配
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="members" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="members">成员管理</TabsTrigger>
            <TabsTrigger value="add">添加成员</TabsTrigger>
          </TabsList>

          {/* 成员管理标签页 */}
          <TabsContent value="members" className="space-y-6">
            {/* 统计信息 */}
            {stats && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200/60">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-600">总成员</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 mt-1">{stats.total_members}</p>
                </div>
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200/60">
                  <div className="flex items-center gap-2">
                    <Crown className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium text-gray-600">组长</span>
                  </div>
                  <p className="text-2xl font-bold text-yellow-600 mt-1">{stats.leaders}</p>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200/60">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-gray-600">活跃</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 mt-1">{stats.active_members}</p>
                </div>
              </div>
            )}

            {/* 成员列表 */}
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">成员列表</h3>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                  <span className="ml-2 text-gray-500">加载中...</span>
                </div>
              ) : members.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p>暂无成员</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {members.map((member) => {
                    const roleConfig = getRoleConfig(member.role_code);
                    const RoleIcon = getRoleIcon(member.role_code);
                    
                    return (
                      <div
                        key={member.id}
                        className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={member.avatar} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                              {member.teacher_name.slice(-2)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-900">
                                {member.teacher_name}
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {member.employee_id}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge 
                                className={`text-xs ${roleConfig.color} ${roleConfig.bgColor} border-0`}
                              >
                                <RoleIcon className="h-3 w-3 mr-1" />
                                {roleConfig.label}
                              </Badge>
                              {member.title && (
                                <span className="text-xs text-gray-500">{member.title}</span>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Select
                            value={member.role_code}
                            onValueChange={(value) => handleUpdateMemberRole(member.id, value)}
                          >
                            <SelectTrigger className="w-24 h-8 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {availableRoles?.map((role) => (
                                <SelectItem key={role.code} value={role.code}>
                                  {role.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveMember(member.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>

          {/* 添加成员标签页 */}
          <TabsContent value="add" className="space-y-6">


            {/* 搜索和选择教师 */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索教师姓名或工号..."
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={defaultRole} onValueChange={setDefaultRole}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {rolesLoading ? (
                      <SelectItem value="" disabled>加载中...</SelectItem>
                    ) : (
                      availableRoles?.map((role) => (
                        <SelectItem key={role.code} value={role.code}>
                          {role.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* 可选教师列表 */}
              <div className="max-h-64 overflow-y-auto space-y-2 border rounded-lg p-2">
                {availableTeachers.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p>没有找到可添加的教师</p>
                  </div>
                ) : (
                  availableTeachers.map((teacher) => (
                    <div
                      key={teacher.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedTeachers.includes(teacher.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => {
                        if (selectedTeachers.includes(teacher.id)) {
                          setSelectedTeachers(prev => prev.filter(id => id !== teacher.id));
                        } else {
                          setSelectedTeachers(prev => [...prev, teacher.id]);
                        }
                      }}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs">
                          {teacher.teacher_name.slice(-2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">
                            {teacher.teacher_name}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {teacher.employee_id}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">{teacher.title || '教师'}</p>
                      </div>
                      {selectedTeachers.includes(teacher.id) && (
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                  ))
                )}
              </div>

              {/* 添加按钮 */}
              {selectedTeachers.length > 0 && (
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <span className="text-sm text-blue-700">
                    已选择 {selectedTeachers.length} 名教师，将以{
                      availableRoles.find(role => role.code === defaultRole)?.name || 
                      getRoleConfig(defaultRole).label
                    } 角色加入
                  </span>
                  <Button onClick={handleAddMembers} disabled={loading}>
                    {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <UserPlus className="h-4 w-4 mr-2" />
                    添加成员
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectGroupMemberManagement;
