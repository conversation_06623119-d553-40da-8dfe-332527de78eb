import React, { useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { subjectApi } from '@/services/subjectApi';
import SubjectGroupForm from './components/SubjectGroupForm';
import SubjectGroupStatistics from './components/SubjectGroupStatistics';
import SubjectGroupTable from './components/SubjectGroupTable';
import SubjectGroupHeader from './components/SubjectGroupHeader';
import SubjectGroupCardView from './components/SubjectGroupCardView';
import SubjectGroupSearchBar, { <PERSON>Filters, ViewMode } from './components/SubjectGroupSearchBar';

import SubjectGroupMemberManagement from './components/SubjectGroupMemberManagement';
import {CreateSubjectGroupsParams, SubjectGroupsDetail} from "@/types/subjectGroups.ts";
import { SubjectSummary } from '@/types/subject';
import {useAuth} from "@/contexts/AuthContext.tsx";

const SubjectGroupsManagementPage: React.FC = () => {
  const { tenant} = useAuth();
  const tenant_name = tenant?.schema_name??'';

  // State management
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [subjects, setSubjects] = useState<SubjectSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [editingSubjectGroup, setEditingSubjectGroup] = useState<any | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // New state for enhanced features
  const [viewMode, setViewMode] = useState<ViewMode>('card');
  const [filters, setFilters] = useState<SearchFilters>({
    keyword: '',
    subject: 'all',
    status: 'all',
    hasLeader: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });



  // Member management state
  const [memberManagementOpen, setMemberManagementOpen] = useState(false);
  const [managementSubjectGroup, setManagementSubjectGroup] = useState<SubjectGroupsDetail | null>(null);

  // Load subject groups
  const loadSubjectGroups = async () => {
    try {
      setLoading(true);
      const response = await SubjectGroupsApi.findAll(tenant_name);
      if (response.success && response.data) {
        setSubjectGroups(response.data);
      }
    } catch (error) {
      console.error('Failed to load subject groups:', error);
      toast.error('加载学科组列表失败');
    } finally {
      setLoading(false);
    }
  };

  // Load subjects for filtering
  const loadSubjects = async () => {
    try {
      const response = await subjectApi.getSubjectSummaries(true); // Only load active subjects
      if (response.success && response.data) {
        setSubjects(response.data);
      }
    } catch (error) {
      console.error('Failed to load subjects:', error);
    }
  };

  useEffect(() => {
    loadSubjectGroups();
    loadSubjects();
  }, [tenant_name]);

  // Filter and sort subject groups
  const filteredSubjectGroups = useMemo(() => {
    let filtered = [...subjectGroups];

    // Apply keyword filter
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      filtered = filtered.filter(group =>
        group.group_name.toLowerCase().includes(keyword) ||
        group.subject_code.toLowerCase().includes(keyword) ||
        group.subject_name?.toLowerCase().includes(keyword) ||
        group.teacher_name?.toLowerCase().includes(keyword) ||
        group.description?.toLowerCase().includes(keyword)
      );
    }

    // Apply subject filter
    if (filters.subject && filters.subject !== 'all') {
      filtered = filtered.filter(group => group.subject_code === filters.subject);
    }

    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      const isActive = filters.status === 'active';
      filtered = filtered.filter(group => group.is_active === isActive);
    }

    // Apply leader filter
    if (filters.hasLeader && filters.hasLeader !== 'all') {
      const hasLeader = filters.hasLeader === 'yes';
      filtered = filtered.filter(group => hasLeader ? !!group.teacher_name : !group.teacher_name);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (filters.sortBy) {
        case 'group_name':
          aValue = a.group_name;
          bValue = b.group_name;
          break;
        case 'created_at':
        default:
          aValue = new Date(a.created_at as string || 0);
          bValue = new Date(b.created_at as string || 0);
          break;
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [subjectGroups, filters]);

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalGroups = subjectGroups.length;
    const activeGroups = subjectGroups.filter(g => g.is_active).length;
    const totalTeachers = subjectGroups.filter(g => g.teacher_name).length;
    const totalSubjects = new Set(subjectGroups.map(g => g.subject_code)).size;

    return {
      totalGroups,
      activeGroups,
      totalTeachers,
      totalSubjects
    };
  }, [subjectGroups]);

  // Event handlers
  const handleCreateSubjectGroup = () => {
    setEditingSubjectGroup(undefined);
    setFormOpen(true);
  };

  const handleEditSubjectGroup = (subjectGroup: SubjectGroupsDetail) => {
    const formData = {
      id: subjectGroup.id,
      name: subjectGroup.group_name,
      subject: subjectGroup.subject_code,
      description: subjectGroup.description || '',
      leader_id: subjectGroup.leader_user_id || ''
    };
    setEditingSubjectGroup(formData);
    setFormOpen(true);
  };

  const handleViewDetails = (subjectGroup: SubjectGroupsDetail) => {
    // TODO: Implement view details functionality
    console.log('View details for:', subjectGroup);
  };



  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export subject groups');
  };


  const handleManageMembers = (subjectGroup: SubjectGroupsDetail) => {
    setManagementSubjectGroup(subjectGroup);
    setMemberManagementOpen(true);
  };

  const handleMemberManagementComplete = () => {
    loadSubjectGroups();
    setMemberManagementOpen(false);
    setManagementSubjectGroup(null);
  };

  // 处理表单提交
  const handleFormSubmit = async (data: CreateSubjectGroupsParams) => {
    try {
      setFormLoading(true);

      if (editingSubjectGroup) {
        // 更新学科组
        const updateParams = {
          id: editingSubjectGroup.id,
          group_name: data.group_name,
          subject_code: data.subject_code,
          description: data.description || null,
          // 确保 leader_user_id 被正确传递，即使为空
          leader_user_id: data.leader_user_id
        };

        console.log('更新学科组参数:', updateParams); // 添加日志
        const response = await SubjectGroupsApi.updateSubjectGroups(tenant_name, updateParams);
        if (response.success) {
          toast.success('学科组更新成功');
          setFormOpen(false);
          setEditingSubjectGroup(undefined); // 清除编辑状态
          await loadSubjectGroups();
        }
      } else {
        // 创建新学科组
        console.log('创建学科组参数:', data); // 添加日志
        const response = await SubjectGroupsApi.createSubjectGroups(tenant_name, data);
        if (response.success) {
          toast.success('学科组创建成功');
          setFormOpen(false);
          await loadSubjectGroups();
        }
      }
    } catch (error: any) {
      console.error('Failed to save subject group:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subjectGroupToDelete, setSubjectGroupToDelete] = useState<string | null>(null);

  const handleDeleteSubjectGroup = (id: string) => {
    setSubjectGroupToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteSubjectGroup = async () => {
    if (!subjectGroupToDelete) return;

    try {
        const response = await SubjectGroupsApi.deleteSubjectGroup(tenant_name, subjectGroupToDelete);
        if (response.success) {
            toast.success('学科组删除成功');
            await loadSubjectGroups();
        }
    } catch (error: any) {
        console.error('Failed to delete subject group:', error);
        toast.error(error.response?.data?.message || '删除失败');
    } finally {
        setDeleteDialogOpen(false);
        setSubjectGroupToDelete(null);
    }
  };



  // // Handle export
  // const handleExport = async () => {
  //   try {
  //     const blob = await subjectGroupApi.exportSubjectGroups(queryParams);
  //     const url = window.URL.createObjectURL(blob);
  //     const a = document.createElement('a');
  //     a.href = url;
  //     a.download = `subject-groups-${new Date().toISOString().split('T')[0]}.xlsx`;
  //     document.body.appendChild(a);
  //     a.click();
  //     window.URL.revokeObjectURL(url);
  //     document.body.removeChild(a);
  //     toast.success('导出成功');
  //   } catch (error) {
  //     console.error('Failed to export subject groups:', error);
  //     toast.error('导出失败');
  //   }
  // };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50/80 via-blue-50/30 to-indigo-50/40">
      <div className="space-y-8 p-6 max-w-7xl mx-auto">
        {/* Enhanced Page Header with Statistics */}
        <SubjectGroupHeader
          totalGroups={statistics.totalGroups}
          activeGroups={statistics.activeGroups}
          totalTeachers={statistics.totalTeachers}
          totalSubjects={statistics.totalSubjects}
          onCreateGroup={handleCreateSubjectGroup}
          onExport={handleExport}
        />

        <Tabs defaultValue="list" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="list">学科组管理</TabsTrigger>
            <TabsTrigger value="statistics">数据分析</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-6">
            {/* Enhanced Search and Filter Bar */}
            <SubjectGroupSearchBar
              filters={filters}
              onFiltersChange={setFilters}
              subjects={subjects}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />

            {/* Dynamic Content Based on View Mode */}
            <div className="bg-white/90 backdrop-blur-sm rounded-lg border border-gray-200/60 shadow-sm">
              {viewMode === 'table' ? (
                <SubjectGroupTable
                  subjectGroups={filteredSubjectGroups}
                  loading={loading}
                  onEdit={handleEditSubjectGroup}
                  onDelete={handleDeleteSubjectGroup}
                  onViewDetails={handleViewDetails}
                  onManageMembers={handleManageMembers}
                  onToggleStatus={(subjectGroup) => {
                    // TODO: Implement toggle status functionality
                    console.log('Toggle status for:', subjectGroup);
                  }}
                />
              ) : (
                <div className="p-6">
                  <SubjectGroupCardView
                    subjectGroups={filteredSubjectGroups}
                    loading={loading}
                    onEdit={handleEditSubjectGroup}
                    onDelete={handleDeleteSubjectGroup}
                    onViewDetails={handleViewDetails}
                    onManageMembers={handleManageMembers}
                    onToggleStatus={(subjectGroup) => {
                      // TODO: Implement toggle status functionality
                      console.log('Toggle status for:', subjectGroup);
                    }}
                  />
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="statistics">
            <SubjectGroupStatistics />
          </TabsContent>
        </Tabs>

        {/* Form Dialog */}
        <SubjectGroupForm
          subjectGroup={editingSubjectGroup}
          open={formOpen}
          onClose={() => {
            setFormOpen(false);
            setEditingSubjectGroup(undefined);
          }}
          onSubmit={handleFormSubmit}
          loading={formLoading}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认删除</AlertDialogTitle>
              <AlertDialogDescription>
                此操作将永久删除该学科组，且无法撤销。请确认是否继续？
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDeleteSubjectGroup}>
                确认删除
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>




        {/* Member Management Dialog */}
        <SubjectGroupMemberManagement
          open={memberManagementOpen}
          onClose={() => setMemberManagementOpen(false)}
          subjectGroup={managementSubjectGroup}
          onMembershipChange={handleMemberManagementComplete}
        />
      </div>
    </div>
  );
};

export default SubjectGroupsManagementPage;

