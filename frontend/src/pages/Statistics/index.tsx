
import { useState } from 'react';
import { usePageFullWidth } from '@/hooks/useLayoutWidth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  BookOpen, 
  Target, 
  Clock,
  Award
} from 'lucide-react';

interface AnalyticsData {
  totalExams: number;
  totalStudents: number;
  averageScore: number;
  completionRate: number;
  topSubjects: Array<{ subject: string; count: number; avgScore: number }>;
  recentTrends: Array<{ period: string; score: number; participation: number }>;
  gradingStats: {
    pending: number;
    completed: number;
    avgGradingTime: number;
  };
}

const mockAnalyticsData: AnalyticsData = {
  totalExams: 45,
  totalStudents: 1200,
  averageScore: 78.5,
  completionRate: 89.2,
  topSubjects: [
    { subject: '数学', count: 15, avgScore: 82.3 },
    { subject: '语文', count: 12, avgScore: 76.8 },
    { subject: '英语', count: 10, avgScore: 74.2 },
    { subject: '物理', count: 8, avgScore: 80.1 },
  ],
  recentTrends: [
    { period: '本周', score: 78.5, participation: 89.2 },
    { period: '上周', score: 76.2, participation: 87.5 },
    { period: '两周前', score: 79.1, participation: 91.0 },
    { period: '三周前', score: 75.8, participation: 88.3 },
  ],
  gradingStats: {
    pending: 23,
    completed: 156,
    avgGradingTime: 2.5
  }
};

export default function StatisticsPage() {
  // Use full width for statistics dashboard to better display charts and data
  // usePageWidth('full');
  usePageFullWidth();
  
  const [selectedPeriod, setSelectedPeriod] = useState('本月');
  const [selectedSubject, setSelectedSubject] = useState('全部');
  const [analytics] = useState<AnalyticsData>(mockAnalyticsData);

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (current < previous) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  const getTrendPercentage = (current: number, previous: number) => {
    const change = ((current - previous) / previous) * 100;
    return change.toFixed(1);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">统计分析</h1>
          <p className="text-gray-600">查看考试数据和学习分析报告</p>
        </div>
        <div className="flex gap-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="本周">本周</SelectItem>
              <SelectItem value="本月">本月</SelectItem>
              <SelectItem value="本季度">本季度</SelectItem>
              <SelectItem value="本年">本年</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="全部">全部学科</SelectItem>
              <SelectItem value="数学">数学</SelectItem>
              <SelectItem value="语文">语文</SelectItem>
              <SelectItem value="英语">英语</SelectItem>
              <SelectItem value="物理">物理</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">考试总数</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalExams}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(analytics.totalExams, 38)}
              <span className="ml-1">+{getTrendPercentage(analytics.totalExams, 38)}% 较上月</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">参与学生</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalStudents}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(analytics.totalStudents, 1150)}
              <span className="ml-1">+{getTrendPercentage(analytics.totalStudents, 1150)}% 较上月</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.averageScore}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(analytics.averageScore, 76.2)}
              <span className="ml-1">+{getTrendPercentage(analytics.averageScore, 76.2)}% 较上月</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成率</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.completionRate}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(analytics.completionRate, 87.5)}
              <span className="ml-1">+{getTrendPercentage(analytics.completionRate, 87.5)}% 较上月</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 学科分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              学科表现分析
            </CardTitle>
            <CardDescription>各学科考试次数和平均成绩</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topSubjects.map((subject, index) => (
                <div key={subject.subject} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span className="text-sm font-semibold text-blue-600">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{subject.subject}</p>
                      <p className="text-sm text-gray-500">{subject.count} 场考试</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{subject.avgScore}%</p>
                    <Badge variant={subject.avgScore >= 80 ? 'default' : subject.avgScore >= 70 ? 'outline' : `secondary`}>
                      {subject.avgScore >= 80 ? '优秀' : subject.avgScore >= 70 ? '良好' : '需改进'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 趋势分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              成绩趋势分析
            </CardTitle>
            <CardDescription>最近四周的成绩和参与度变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.recentTrends.map((trend, index) => (
                <div key={trend.period} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{trend.period}</p>
                    <p className="text-sm text-gray-500">
                      参与度: {trend.participation}%
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold">{trend.score}%</p>
                    {index > 0 && (
                      <div className="flex items-center text-xs">
                        {getTrendIcon(trend.score, analytics.recentTrends[index - 1].score)}
                        <span className="ml-1">
                          {getTrendPercentage(trend.score, analytics.recentTrends[index - 1].score)}%
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 阅卷统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            阅卷效率统计
          </CardTitle>
          <CardDescription>阅卷进度和效率分析</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-500">
                {analytics.gradingStats.pending}
              </div>
              <p className="text-sm text-gray-600">待阅卷试卷</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-500">
                {analytics.gradingStats.completed}
              </div>
              <p className="text-sm text-gray-600">已完成阅卷</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-500">
                {analytics.gradingStats.avgGradingTime}h
              </div>
              <p className="text-sm text-gray-600">平均阅卷时间</p>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div className="text-center">
            <p className="text-sm text-gray-600">
              阅卷完成率: <span className="font-semibold">
                {Math.round((analytics.gradingStats.completed / (analytics.gradingStats.completed + analytics.gradingStats.pending)) * 100)}%
              </span>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
