import React from 'react';
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
    Phone,
    Calendar,
    Shield,
    GraduationCap,
    BookOpen,
    CheckCircle,
    XCircle,
    Clock,
    User as UserIcon,
    Activity,
} from 'lucide-react';
import { User } from '@/types/user';

interface UserDetailDialogProps {
    user: User | null;
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onManageRoles?: (user: User) => void;
}

const UserDetailDialog: React.FC<UserDetailDialogProps> = ({
    user,
    open,
    onOpenChange,
    onManageRoles,
}) => {
    if (!user) return null;

    const getUserInitials = (username: string) => {
        return username.slice(0, 2).toUpperCase();
    };

    const getRoleBadges = (user: User) => {
        const badges = [];
        if (user.is_admin) badges.push({ label: '管理员', variant: 'destructive' as const, icon: Shield });
        if (user.is_teacher) badges.push({ label: '教师', variant: 'default' as const, icon: GraduationCap });
        if (user.is_student) badges.push({ label: '学生', variant: 'secondary' as const, icon: BookOpen });
        return badges;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            weekday: 'long'
        });
    };

    const getAccountAge = (createdAt: string) => {
        const created = new Date(createdAt);
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - created.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 30) {
            return `${diffDays} 天`;
        } else if (diffDays < 365) {
            return `${Math.floor(diffDays / 30)} 个月`;
        } else {
            return `${Math.floor(diffDays / 365)} 年`;
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center text-xl">
                        <UserIcon className="mr-2 h-5 w-5" />
                        用户详情
                    </DialogTitle>
                    <DialogDescription>
                        查看 {user.username} 的详细信息和账号状态
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6 py-4">
                    {/* 用户基本信息卡片 */}
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-start space-x-4">
                                <Avatar className="h-16 w-16">
                                    <AvatarFallback className="text-lg font-semibold">
                                        {getUserInitials(user.username)}
                                    </AvatarFallback>
                                </Avatar>
                                <div className="flex-1 space-y-3">
                                    <div>
                                        <h3 className="text-2xl font-bold">{user.username}</h3>
                                        <div className="flex items-center text-muted-foreground mt-1">
                                            <Phone className="h-4 w-4 mr-2" />
                                            {user.phone_number}
                                        </div>
                                    </div>
                                    
                                    {/* 角色标签 */}
                                    <div className="flex flex-wrap gap-2">
                                        {getRoleBadges(user).map((badge, index) => (
                                            <Badge key={index} variant={badge.variant}>
                                                <badge.icon className="h-4 w-4 mr-1" />
                                                {badge.label}
                                            </Badge>
                                        ))}
                                    </div>

                                    {/* 状态指示器 */}
                                    <div className="flex items-center space-x-4">
                                        <div className="flex items-center">
                                            {user.is_active ? (
                                                <>
                                                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                                                    <span className="text-green-700 font-medium">账号活跃</span>
                                                </>
                                            ) : (
                                                <>
                                                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                                                    <span className="text-red-700 font-medium">账号禁用</span>
                                                </>
                                            )}
                                        </div>
                                        
                                        {user.phone_verified && (
                                            <div className="flex items-center">
                                                <CheckCircle className="h-5 w-5 text-blue-500 mr-2" />
                                                <span className="text-blue-700 font-medium">手机已验证</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* 账号信息 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center text-lg">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    账号信息
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">用户ID</span>
                                        <span className="text-sm font-mono">{user.user_id}</span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">创建时间</span>
                                        <span className="text-sm">{formatDate(user.created_at)}</span>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">账号年龄</span>
                                        <span className="text-sm font-medium">{getAccountAge(user.created_at)}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center text-lg">
                                    <Activity className="mr-2 h-5 w-5" />
                                    权限状态
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">管理员权限</span>
                                        <Badge variant={user.is_admin ? "destructive" : "outline"}>
                                            {user.is_admin ? "是" : "否"}
                                        </Badge>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">教师权限</span>
                                        <Badge variant={user.is_teacher ? "default" : "outline"}>
                                            {user.is_teacher ? "是" : "否"}
                                        </Badge>
                                    </div>
                                    
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">学生权限</span>
                                        <Badge variant={user.is_student ? "secondary" : "outline"}>
                                            {user.is_student ? "是" : "否"}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* 联系信息 */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center text-lg">
                                <Phone className="mr-2 h-5 w-5" />
                                联系信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">手机号码</label>
                                        <div className="flex items-center mt-1">
                                            <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                                            <span className="text-sm">{user.phone_number}</span>
                                            {user.phone_verified && (
                                                <CheckCircle className="h-4 w-4 ml-2 text-green-500" />
                                            )}
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="space-y-3">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">验证状态</label>
                                        <div className="flex items-center mt-1">
                                            {user.phone_verified ? (
                                                <>
                                                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                                    <span className="text-sm text-green-700">已验证</span>
                                                </>
                                            ) : (
                                                <>
                                                    <XCircle className="h-4 w-4 mr-2 text-red-500" />
                                                    <span className="text-sm text-red-700">未验证</span>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* 角色权限详情 */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between text-lg">
                                <div className="flex items-center">
                                    <Shield className="mr-2 h-5 w-5" />
                                    角色权限
                                </div>
                                {onManageRoles && (
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => onManageRoles(user)}
                                    >
                                        <Shield className="h-4 w-4" />
                                        管理角色
                                    </Button>
                                )}
                            </CardTitle>
                            <CardDescription>
                                用户在系统中被分配的角色和权限信息
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {/* 显示用户身份 */}
                            {user.identities && user.identities.length > 0 ? (
                                <div className="space-y-3">
                                    {user.identities.map((identity) => (
                                        <div key={identity.id} className="p-3 border rounded-lg">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="flex items-center gap-2">
                                                    <h5 className="font-medium">{identity.role.name}</h5>
                                                    <Badge variant="outline" className="text-xs">
                                                        {identity.role.category}
                                                    </Badge>
                                                    <Badge variant="secondary" className="text-xs">
                                                        级别 {identity.role.level}
                                                    </Badge>
                                                </div>
                                            </div>
                                            <p className="text-sm text-muted-foreground mb-2">
                                                {identity.role.description}
                                            </p>
                                            {identity.target_type && (
                                                <div className="text-xs text-muted-foreground">
                                                    作用范围: {identity.target_type}
                                                    {identity.subject && ` - ${identity.subject}`}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {/* 回退到简单角色显示 */}
                                    <div className="flex flex-wrap gap-2">
                                        {getRoleBadges(user).map((badge, index) => (
                                            <Badge key={index} variant={badge.variant} className="px-3 py-1">
                                                <badge.icon className="h-3 w-3 mr-1" />
                                                {badge.label}
                                            </Badge>
                                        ))}
                                        {!user.is_admin && !user.is_teacher && !user.is_student && (
                                            <Badge variant="outline" className="px-3 py-1 text-muted-foreground">
                                                未分配角色
                                            </Badge>
                                        )}
                                    </div>
                                    {onManageRoles && (
                                        <div className="pt-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onManageRoles(user)}
                                                className="text-xs"
                                            >
                                                立即分配角色
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* 安全信息 */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center text-lg">
                                <Shield className="mr-2 h-5 w-5" />
                                安全信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="p-4 border rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h4 className="font-medium">账号状态</h4>
                                            <p className="text-sm text-muted-foreground">
                                                {user.is_active ? '账号正常，可以正常登录使用系统' : '账号已被禁用，无法登录系统'}
                                            </p>
                                        </div>
                                        <div className="flex items-center">
                                            {user.is_active ? (
                                                <CheckCircle className="h-6 w-6 text-green-500" />
                                            ) : (
                                                <XCircle className="h-6 w-6 text-red-500" />
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="p-4 border rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h4 className="font-medium">手机验证</h4>
                                            <p className="text-sm text-muted-foreground">
                                                {user.phone_verified ? '手机号已通过验证' : '手机号尚未验证'}
                                            </p>
                                        </div>
                                        <div className="flex items-center">
                                            {user.phone_verified ? (
                                                <CheckCircle className="h-6 w-6 text-green-500" />
                                            ) : (
                                                <Clock className="h-6 w-6 text-orange-500" />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default UserDetailDialog;