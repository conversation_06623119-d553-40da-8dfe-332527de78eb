import { useState, useEffect, useCallback } from 'react';
import { toast } from "sonner";
import { User } from '@/types/user';
import { getUsers, createUser, updateUser } from '@/services/userApi';

type ViewMode = 'grid' | 'list';
type UserFormData = Partial<User> & {
  password?: string;
  confirmPassword?: string;
};

export const useUserManagement = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserFormData | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [activeTab, setActiveTab] = useState('all');
  const [detailUser, setDetailUser] = useState<User | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);
  const [isImportExportDialogOpen, setIsImportExportDialogOpen] = useState(false);
  const [isRoleManagerOpen, setIsRoleManagerOpen] = useState(false);
  const [roleManagerUser, setRoleManagerUser] = useState<User | null>(null);

  // 获取用户列表
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const data = await getUsers();
      setUsers(data);
    } catch (error) {
      console.error("获取用户列表失败", error);
      toast.error("获取用户列表失败");
    } finally {
      setLoading(false);
    }
  }, []);

  // 筛选用户
  const filterUsers = useCallback(() => {
    let filtered = users;

    // Tab 过滤
    if (activeTab !== 'all') {
      filtered = filtered.filter(user => {
        switch (activeTab) {
          case 'active':
            return user.is_active;
          case 'inactive':
            return !user.is_active;
          case 'admin':
            return user.is_admin;
          case 'teacher':
            return user.is_teacher;
          case 'student':
            return user.is_student;
          default:
            return true;
        }
      });
    }

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.phone_number.includes(searchTerm)
      );
    }

    // 角色过滤
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => {
        switch (roleFilter) {
          case 'admin':
            return user.is_admin;
          case 'teacher':
            return user.is_teacher;
          case 'student':
            return user.is_student;
          default:
            return true;
        }
      });
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => {
        switch (statusFilter) {
          case 'active':
            return user.is_active;
          case 'inactive':
            return !user.is_active;
          case 'verified':
            return user.phone_verified;
          case 'unverified':
            return !user.phone_verified;
          default:
            return true;
        }
      });
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter, statusFilter, activeTab]);

  // 表单验证
  const validateForm = useCallback((userData: UserFormData): string | null => {
    if (!userData.username?.trim()) {
      return '请输入用户名';
    }
    if (!userData.phone_number?.trim()) {
      return '请输入手机号';
    }
    if (!/^1[3-9]\d{9}$/.test(userData.phone_number)) {
      return '请输入有效的手机号';
    }
    if (!userData.user_id && !userData.password) {
      return '请输入密码';
    }
    if (userData.password && userData.password.length < 6) {
      return '密码长度至少6位';
    }
    if (userData.password !== userData.confirmPassword) {
      return '两次输入的密码不一致';
    }
    return null;
  }, []);

  // 保存用户
  const handleSave = useCallback(async () => {
    if (!selectedUser) return;
    
    const validationError = validateForm(selectedUser);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    try {
      if (selectedUser.user_id) {
        await updateUser({
          user_id: selectedUser.user_id,
          password: selectedUser.password || undefined,
          phone: selectedUser.phone_number,
          is_active: selectedUser.is_active,
        });
        toast.success("用户更新成功");
      } else {
        await createUser({
          username: selectedUser.username!,
          phone: selectedUser.phone_number!,
          password: selectedUser.password!,
        });
        toast.success("用户创建成功");
      }
      await fetchUsers();
      setIsDialogOpen(false);
      setSelectedUser(null);
    } catch (err: any) {
      toast.error("保存用户失败: " + (err.response?.data?.message || err.message));
      console.error("保存用户失败", err);
    }
  }, [selectedUser, validateForm, fetchUsers]);

  // 编辑用户
  const handleEdit = useCallback((user: User) => {
    setSelectedUser({
      ...user,
      password: '',
      confirmPassword: ''
    });
    setIsDialogOpen(true);
  }, []);

  // 创建用户
  const handleCreate = useCallback(() => {
    setSelectedUser({
      username: '',
      phone_number: '',
      password: '',
      confirmPassword: '',
      is_active: true
    });
    setIsDialogOpen(true);
  }, []);

  // 查看详情
  const handleViewDetail = useCallback((user: User) => {
    setDetailUser(user);
    setIsDetailDialogOpen(true);
  }, []);

  // 批量操作
  const handleBatchAction = useCallback(async (action: string, users: User[]) => {
    console.log('批量操作:', action, users);
    await new Promise(resolve => setTimeout(resolve, 1000));
    await fetchUsers();
    setSelectedUsers([]);
  }, [fetchUsers]);

  // 切换用户选择
  const toggleUserSelection = useCallback((user: User) => {
    setSelectedUsers(prev => {
      const isSelected = prev.some(u => u.user_id === user.user_id);
      if (isSelected) {
        return prev.filter(u => u.user_id !== user.user_id);
      } else {
        return [...prev, user];
      }
    });
  }, []);

  // 全选/取消全选
  const selectAllUsers = useCallback(() => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers([...filteredUsers]);
    }
  }, [selectedUsers.length, filteredUsers]);

  // 管理用户角色
  const handleManageRoles = useCallback((user: User) => {
    setRoleManagerUser(user);
    setIsRoleManagerOpen(true);
  }, []);

  // 角色更新后的回调
  const handleRolesUpdated = useCallback(() => {
    fetchUsers(); // 重新获取用户列表以更新角色信息
  }, [fetchUsers]);

  // 获取统计数据
  const getStats = useCallback(() => {
    const total = users.length;
    const active = users.filter(u => u.is_active).length;
    const verified = users.filter(u => u.phone_verified).length;
    const admins = users.filter(u => u.is_admin).length;
    const teachers = users.filter(u => u.is_teacher).length;
    const students = users.filter(u => u.is_student).length;

    return { total, active, verified, admins, teachers, students };
  }, [users]);

  // 监听数据变化，自动筛选
  useEffect(() => {
    filterUsers();
  }, [filterUsers]);

  // 初始化加载
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    // 状态
    users,
    filteredUsers,
    loading,
    selectedUsers,
    viewMode,
    activeTab,
    searchTerm,
    roleFilter,
    statusFilter,
    isDialogOpen,
    selectedUser,
    isDetailDialogOpen,
    detailUser,
    isBatchDialogOpen,
    isImportExportDialogOpen,
    isRoleManagerOpen,
    roleManagerUser,
    
    // 统计数据
    stats: getStats(),
    
    // 方法
    setSearchTerm,
    setRoleFilter,
    setStatusFilter,
    setViewMode,
    setActiveTab,
    setIsDialogOpen,
    setIsDetailDialogOpen,
    setIsBatchDialogOpen,
    setIsImportExportDialogOpen,
    setIsRoleManagerOpen,
    setSelectedUser,
    
    // 操作
    handleCreate,
    handleEdit,
    handleViewDetail,
    handleSave,
    handleBatchAction,
    handleManageRoles,
    handleRolesUpdated,
    toggleUserSelection,
    selectAllUsers,
    fetchUsers,
  };
}; 