import React, { useState, useRef } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Ta<PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>ist,
    Ta<PERSON>Trigger,
} from "@/components/ui/tabs";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Upload,
    Download,
    FileText,
    CheckCircle,
    XCircle,
    AlertTriangle,
    Users,
    FileSpreadsheet,
    Info,
} from 'lucide-react';
import { toast } from "sonner";
import { User } from '@/types/user';

interface UserImportExportProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    users: User[];
    onImportComplete: () => void;
}

interface ImportResult {
    success: number;
    failed: number;
    errors: Array<{
        row: number;
        error: string;
        data: any;
    }>;
}

const UserImportExport: React.FC<UserImportExportProps> = ({
    open,
    onOpenChange,
    users,
    onImportComplete,
}) => {
    const [activeTab, setActiveTab] = useState('export');
    const [importFile, setImportFile] = useState<File | null>(null);
    const [importing, setImporting] = useState(false);
    const [exporting, setExporting] = useState(false);
    const [importProgress, setImportProgress] = useState(0);
    const [importResult, setImportResult] = useState<ImportResult | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                toast.error('请选择 CSV 格式的文件');
                return;
            }
            setImportFile(file);
            setImportResult(null);
        }
    };

    const handleImport = async () => {
        if (!importFile) {
            toast.error('请先选择要导入的文件');
            return;
        }

        setImporting(true);
        setImportProgress(0);

        try {
            // 模拟导入过程
            const formData = new FormData();
            formData.append('file', importFile);

            // 模拟进度更新
            const progressInterval = setInterval(() => {
                setImportProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return prev;
                    }
                    return prev + 10;
                });
            }, 200);

            // 这里应该调用实际的导入 API
            await new Promise(resolve => setTimeout(resolve, 2000));

            clearInterval(progressInterval);
            setImportProgress(100);

            // 模拟导入结果
            const mockResult: ImportResult = {
                success: 8,
                failed: 2,
                errors: [
                    {
                        row: 3,
                        error: '手机号格式不正确',
                        data: { username: 'user3', phone: '123456' }
                    },
                    {
                        row: 7,
                        error: '用户名已存在',
                        data: { username: 'admin', phone: '13800138000' }
                    }
                ]
            };

            setImportResult(mockResult);
            toast.success(`导入完成！成功 ${mockResult.success} 条，失败 ${mockResult.failed} 条`);

            if (mockResult.success > 0) {
                onImportComplete();
            }
        } catch (error) {
            toast.error('导入失败');
            console.error('导入失败:', error);
        } finally {
            setImporting(false);
        }
    };

    const handleExport = async () => {
        setExporting(true);

        try {
            // 准备导出数据
            const exportData = users.map(user => ({
                '用户名': user.username,
                '手机号': user.phone_number,
                '是否管理员': user.is_admin ? '是' : '否',
                '是否教师': user.is_teacher ? '是' : '否',
                '是否学生': user.is_student ? '是' : '否',
                '账号状态': user.is_active ? '活跃' : '禁用',
                '手机验证': user.phone_verified ? '已验证' : '未验证',
                '创建时间': new Date(user.created_at).toLocaleDateString('zh-CN'),
                '角色': user.roles?.join(', ') || ''
            }));

            // 转换为 CSV 格式
            const headers = Object.keys(exportData[0] || {});
            const csvContent = [
                headers.join(','),
                ...exportData.map(row =>
                    headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
                )
            ].join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `用户数据_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success(`已导出 ${users.length} 条用户数据`);
        } catch (error) {
            toast.error('导出失败');
            console.error('导出失败:', error);
        } finally {
            setExporting(false);
        }
    };

    const downloadTemplate = () => {
        const template = [
            ['用户名', '手机号', '密码', '是否管理员', '是否教师', '是否学生'],
            ['user1', '13800138001', 'password123', '否', '是', '否'],
            ['user2', '13800138002', 'password123', '否', '否', '是'],
        ];

        const csvContent = template.map(row =>
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', '用户导入模板.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('模板文件已下载');
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center">
                        <FileSpreadsheet className="mr-2 h-5 w-5" />
                        用户数据导入导出
                    </DialogTitle>
                    <DialogDescription>
                        批量导入用户数据或导出现有用户信息
                    </DialogDescription>
                </DialogHeader>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="export">导出数据</TabsTrigger>
                        <TabsTrigger value="import">导入数据</TabsTrigger>
                    </TabsList>

                    <TabsContent value="export" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Download className="mr-2 h-5 w-5" />
                                    导出用户数据
                                </CardTitle>
                                <CardDescription>
                                    将当前系统中的用户数据导出为 CSV 文件
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                                        <div>
                                            <h4 className="font-medium text-blue-900">导出说明</h4>
                                            <ul className="text-sm text-blue-800 mt-2 space-y-1">
                                                <li>• 将导出所有用户的基本信息和权限设置</li>
                                                <li>• 导出格式为 CSV，可用 Excel 等软件打开</li>
                                                <li>• 不会导出用户密码等敏感信息</li>
                                                <li>• 共计 {users.length} 条用户数据</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div>
                                        <h4 className="font-medium">用户数据统计</h4>
                                        <p className="text-sm text-muted-foreground">
                                            总计 {users.length} 个用户，包含完整的用户信息和权限数据
                                        </p>
                                    </div>
                                    <Badge variant="outline" className="text-lg px-3 py-1">
                                        {users.length}
                                    </Badge>
                                </div>

                                <Button
                                    onClick={handleExport}
                                    disabled={exporting || users.length === 0}
                                    className="w-full"
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    {exporting ? '导出中...' : '导出用户数据'}
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="import" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Upload className="mr-2 h-5 w-5" />
                                    导入用户数据
                                </CardTitle>
                                <CardDescription>
                                    从 CSV 文件批量导入用户数据到系统中
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* 导入说明 */}
                                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                        <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                                        <div>
                                            <h4 className="font-medium text-orange-900">导入要求</h4>
                                            <ul className="text-sm text-orange-800 mt-2 space-y-1">
                                                <li>• 文件格式必须为 CSV</li>
                                                <li>• 必须包含：用户名、手机号、密码</li>
                                                <li>• 用户名不能重复</li>
                                                <li>• 手机号必须为11位数字</li>
                                                <li>• 密码长度至少6位</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                {/* 模板下载 */}
                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div>
                                        <h4 className="font-medium">下载导入模板</h4>
                                        <p className="text-sm text-muted-foreground">
                                            下载标准的 CSV 模板文件，按照格式填写数据
                                        </p>
                                    </div>
                                    <Button variant="outline" onClick={downloadTemplate}>
                                        <FileText className="mr-2 h-4 w-4" />
                                        下载模板
                                    </Button>
                                </div>

                                {/* 文件选择 */}
                                <div className="space-y-2">
                                    <Label htmlFor="import-file">选择导入文件</Label>
                                    <div className="flex items-center space-x-2">
                                        <Input
                                            id="import-file"
                                            type="file"
                                            accept=".csv"
                                            onChange={handleFileSelect}
                                            ref={fileInputRef}
                                            className="flex-1"
                                        />
                                        {importFile && (
                                            <Badge variant="outline">
                                                {importFile.name}
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                {/* 导入进度 */}
                                {importing && (
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <Label>导入进度</Label>
                                            <span className="text-sm text-muted-foreground">
                                                {importProgress}%
                                            </span>
                                        </div>
                                        <Progress value={importProgress} className="w-full" />
                                    </div>
                                )}

                                {/* 导入结果 */}
                                {importResult && (
                                    <div className="space-y-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
                                                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                                                <p className="font-medium text-green-900">成功导入</p>
                                                <p className="text-2xl font-bold text-green-600">
                                                    {importResult.success}
                                                </p>
                                            </div>
                                            <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-center">
                                                <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                                                <p className="font-medium text-red-900">导入失败</p>
                                                <p className="text-2xl font-bold text-red-600">
                                                    {importResult.failed}
                                                </p>
                                            </div>
                                        </div>

                                        {/* 错误详情 */}
                                        {importResult.errors.length > 0 && (
                                            <div>
                                                <h4 className="font-medium mb-2">错误详情</h4>
                                                <div className="border rounded-lg overflow-hidden">
                                                    <Table>
                                                        <TableHeader>
                                                            <TableRow>
                                                                <TableHead>行号</TableHead>
                                                                <TableHead>错误信息</TableHead>
                                                                <TableHead>数据</TableHead>
                                                            </TableRow>
                                                        </TableHeader>
                                                        <TableBody>
                                                            {importResult.errors.map((error, index) => (
                                                                <TableRow key={index}>
                                                                    <TableCell>{error.row}</TableCell>
                                                                    <TableCell className="text-red-600">
                                                                        {error.error}
                                                                    </TableCell>
                                                                    <TableCell className="text-sm text-muted-foreground">
                                                                        {JSON.stringify(error.data)}
                                                                    </TableCell>
                                                                </TableRow>
                                                            ))}
                                                        </TableBody>
                                                    </Table>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}

                                <Button
                                    onClick={handleImport}
                                    disabled={!importFile || importing}
                                    className="w-full"
                                >
                                    <Upload className="mr-2 h-4 w-4" />
                                    {importing ? '导入中...' : '开始导入'}
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        关闭
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default UserImportExport;