import React from 'react';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useUserManagement } from './hooks/useUserManagement';
import {
  PageHeader,
  UserStats,
  UserFilters,
  UserTabs,
  UserList,
  UserForm,
  UserRoleManager,
} from './components';
import UserDetailDialog from './UserDetailDialog';
import UserBatchActions from './UserBatchActions';
import UserImportExport from './UserImportExport';

const UserManagementPage: React.FC = () => {
  const {
    // 状态
    users,
    filteredUsers,
    loading,
    selectedUsers,
    viewMode,
    activeTab,
    searchTerm,
    roleFilter,
    statusFilter,
    isDialogOpen,
    selectedUser,
    isDetailDialogOpen,
    detailUser,
    isBatchDialogOpen,
    isImportExportDialogOpen,
    isRoleManagerOpen,
    roleManagerUser,
    stats,
    
    // 方法
    setSearchTerm,
    setRoleFilter,
    setStatusFilter,
    setViewMode,
    setActiveTab,
    setIsDialogOpen,
    setIsDetailDialogOpen,
    setIsBatchDialogOpen,
    setIsImportExportDialogOpen,
    setIsRoleManagerOpen,
    setSelectedUser,
    
    // 操作
    handleCreate,
    handleEdit,
    handleViewDetail,
    handleSave,
    handleBatchAction,
    handleManageRoles,
    handleRolesUpdated,
    toggleUserSelection,
    selectAllUsers,
    fetchUsers,
  } = useUserManagement();

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <PageHeader
        selectedUsersCount={selectedUsers.length}
        onImportExport={() => setIsImportExportDialogOpen(true)}
        onBatchActions={() => setIsBatchDialogOpen(true)}
        onCreateUser={handleCreate}
      />

      {/* 统计卡片 */}
      <UserStats stats={stats} />

      {/* 主要内容区域 */}
      <Card>
        <CardHeader>
          <UserFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            roleFilter={roleFilter}
            onRoleFilterChange={setRoleFilter}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            filteredUsersCount={filteredUsers.length}
            selectedUsersCount={selectedUsers.length}
            onSelectAll={selectAllUsers}
          />
        </CardHeader>
        
        <CardContent>
          {/* 标签页 */}
          <UserTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            stats={stats}
          />

          {/* 用户列表 */}
          <UserList
            users={filteredUsers}
            loading={loading}
            viewMode={viewMode}
            selectedUsers={selectedUsers}
            searchTerm={searchTerm}
            roleFilter={roleFilter}
            statusFilter={statusFilter}
            onToggleSelection={toggleUserSelection}
            onEdit={handleEdit}
            onViewDetail={handleViewDetail}
            onCreateUser={handleCreate}
            onManageRoles={handleManageRoles}
          />
        </CardContent>
      </Card>

      {/* 创建/编辑用户对话框 */}
      <UserForm
        user={selectedUser}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSave={handleSave}
        onUserChange={setSelectedUser}
      />

      {/* 用户详情对话框 */}
      <UserDetailDialog
        user={detailUser}
        open={isDetailDialogOpen}
        onOpenChange={setIsDetailDialogOpen}
        onManageRoles={handleManageRoles}
      />

      {/* 批量操作对话框 */}
      <UserBatchActions
        selectedUsers={selectedUsers}
        open={isBatchDialogOpen}
        onOpenChange={setIsBatchDialogOpen}
        onBatchUpdate={handleBatchAction}
      />

      {/* 导入导出对话框 */}
      <UserImportExport
        open={isImportExportDialogOpen}
        onOpenChange={setIsImportExportDialogOpen}
        users={users}
        onImportComplete={fetchUsers}
      />

      {/* 用户角色管理对话框 */}
      <UserRoleManager
        user={roleManagerUser}
        open={isRoleManagerOpen}
        onOpenChange={setIsRoleManagerOpen}
        onRolesUpdated={handleRolesUpdated}
      />
    </div>
  );
};

export default UserManagementPage;