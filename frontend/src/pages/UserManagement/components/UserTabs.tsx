import React from 'react';
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface UserTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  stats: {
    total: number;
    active: number;
    admins: number;
    teachers: number;
    students: number;
  };
}

const UserTabs: React.FC<UserTabsProps> = ({ activeTab, onTabChange, stats }) => {
  const tabItems = [
    { value: 'all', label: '全部', count: stats.total },
    { value: 'active', label: '活跃', count: stats.active },
    { value: 'inactive', label: '禁用', count: stats.total - stats.active },
    { value: 'admin', label: '管理员', count: stats.admins },
    { value: 'teacher', label: '教师', count: stats.teachers },
    { value: 'student', label: '学生', count: stats.students },
  ];

  return (
    <Tabs value={activeTab} onValueChange={onTabChang<PERSON>} className="mb-6">
      <TabsList className="grid w-full grid-cols-6">
        {tabItems.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value}>
            {tab.label} ({tab.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
};

export default UserTabs; 