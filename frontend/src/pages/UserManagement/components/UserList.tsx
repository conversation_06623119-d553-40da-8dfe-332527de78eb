import React from 'react';
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, UserX } from 'lucide-react';
import { User } from '@/types/user';
import UserCard from './UserCard';

interface UserListProps {
  users: User[];
  loading: boolean;
  viewMode: 'grid' | 'list';
  selectedUsers: User[];
  searchTerm: string;
  roleFilter: string;
  statusFilter: string;
  onToggleSelection: (user: User) => void;
  onEdit: (user: User) => void;
  onViewDetail: (user: User) => void;
  onCreateUser: () => void;
  onManageRoles?: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({
  users,
  loading,
  viewMode,
  selectedUsers,
  searchTerm,
  roleFilter,
  statusFilter,
  onToggleSelection,
  onEdit,
  onViewDetail,
  onCreateUser,
  onManageRoles,
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3">加载中...</span>
      </div>
    );
  }

  if (users.length === 0) {
    const hasFilters = searchTerm || roleFilter !== 'all' || statusFilter !== 'all';
    
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <UserX className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">没有找到用户</h3>
        <p className="text-muted-foreground mb-4">
          {hasFilters 
            ? '尝试调整搜索条件或筛选器' 
            : '开始创建第一个用户'
          }
        </p>
        {!hasFilters && (
          <Button onClick={onCreateUser}>
            <PlusCircle className="mr-2 h-4 w-4" />
            创建用户
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={viewMode === 'grid' 
      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
      : "space-y-4"
    }>
      {users.map((user) => (
        <UserCard
          key={user.user_id}
          user={user}
          isSelected={selectedUsers.some(u => u.user_id === user.user_id)}
          onToggleSelection={onToggleSelection}
          onEdit={onEdit}
          onViewDetail={onViewDetail}
          onManageRoles={onManageRoles}
        />
      ))}
    </div>
  );
};

export default UserList; 