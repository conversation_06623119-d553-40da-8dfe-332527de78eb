import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Phone,
  Calendar,
  Shield,
  GraduationCap,
  BookOpen,
  CheckCircle,
  XCircle,
  Users,
  Building,
  UserCheck,
  Settings,
} from 'lucide-react';
import { User } from '@/types/user';

interface UserCardProps {
  user: User;
  isSelected: boolean;
  onToggleSelection: (user: User) => void;
  onEdit: (user: User) => void;
  onViewDetail: (user: User) => void;
  onManageRoles?: (user: User) => void;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  isSelected,
  onToggleSelection,
  onEdit,
  onViewDetail,
  onManageRoles,
}) => {
  const getUserInitials = (username: string) => {
    return username.slice(0, 2).toUpperCase();
  };

  const getRoleBadges = (user: User) => {
    const badges = [];

    // 优先显示用户身份中的角色
    if (user.identities && user.identities.length > 0) {
      // 显示前3个角色，如果有更多则显示数量
      const visibleIdentities = user.identities.slice(0, 3);
      visibleIdentities.forEach(identity => {
        const role = identity.role;
        let variant: 'default' | 'secondary' | 'destructive' | 'outline' = 'outline';
        let icon = Users;

        // 根据角色分类设置样式
        switch (role.category) {
          case 'system':
            variant = 'destructive';
            icon = Shield;
            break;
          case 'tenant':
            variant = 'default';
            icon = Building;
            break;
          case 'school':
            variant = 'default';
            icon = GraduationCap;
            break;
          case 'business':
            variant = 'secondary';
            icon = Users;
            break;
          case 'class_grade':
            variant = 'secondary';
            icon = BookOpen;
            break;
          case 'end_user':
            variant = 'outline';
            icon = UserCheck;
            break;
        }

        badges.push({
          label: identity.display_name || role.name,
          variant,
          icon,
          level: role.level,
        });
      });

      // 如果有更多角色，显示数量
      if (user.identities.length > 3) {
        badges.push({
          label: `+${user.identities.length - 3}`,
          variant: 'outline' as const,
          icon: MoreHorizontal,
        });
      }
    } else {
      // 回退到原有的简单角色显示
      if (user.is_admin) badges.push({ label: '管理员', variant: 'destructive' as const, icon: Shield });
      if (user.is_teacher) badges.push({ label: '教师', variant: 'default' as const, icon: GraduationCap });
      if (user.is_student) badges.push({ label: '学生', variant: 'secondary' as const, icon: BookOpen });
    }

    return badges;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className={`hover:shadow-md transition-all duration-200 cursor-pointer ${
      isSelected ? 'ring-2 ring-primary bg-primary/5' : ''
    }`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div 
            className="flex items-center space-x-3 flex-1"
            onClick={() => onToggleSelection(user)}
          >
            <div className="relative">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="text-sm font-medium">
                  {getUserInitials(user.username)}
                </AvatarFallback>
              </Avatar>
              {isSelected && (
                <div className="absolute -top-1 -right-1 h-5 w-5 bg-primary rounded-full flex items-center justify-center">
                  <CheckCircle className="h-3 w-3 text-primary-foreground" />
                </div>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-lg">{user.username}</h3>
              <div className="flex items-center text-sm text-muted-foreground mt-1">
                <Phone className="h-3 w-3 mr-1" />
                {user.phone_number}
              </div>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onViewDetail(user)}>
                <Eye className="mr-2 h-4 w-4" />
                查看详情
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit(user)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              {onManageRoles && (
                <DropdownMenuItem onClick={() => onManageRoles(user)}>
                  <Settings className="mr-2 h-4 w-4" />
                  管理角色
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-red-600"
                onClick={() => console.log('删除功能正在开发中')}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-3">
          {/* 角色标签 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">角色权限</span>
              {user.identities && user.identities.length > 0 && (
                <span className="text-xs text-muted-foreground">
                  {user.identities.length} 个角色
                </span>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {getRoleBadges(user).map((badge, index) => (
                <Badge key={index} variant={badge.variant} className="text-xs">
                  <badge.icon className="h-3 w-3 mr-1" />
                  {badge.label}
                  {badge.level && (
                    <span className="ml-1 opacity-75">L{badge.level}</span>
                  )}
                </Badge>
              ))}
              {(!user.identities || user.identities.length === 0) &&
               !user.is_admin && !user.is_teacher && !user.is_student && (
                <Badge variant="outline" className="text-xs text-muted-foreground">
                  未分配角色
                </Badge>
              )}
            </div>
          </div>

          {/* 状态信息 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                {user.is_active ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className="text-sm">
                  {user.is_active ? '活跃' : '禁用'}
                </span>
              </div>
              {user.phone_verified && (
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-blue-500 mr-1" />
                  <span className="text-sm text-blue-600">已验证</span>
                </div>
              )}
            </div>
          </div>

          {/* 创建时间 */}
          <div className="flex items-center text-xs text-muted-foreground pt-2 border-t">
            <Calendar className="h-3 w-3 mr-1" />
            创建于 {formatDate(user.created_at)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserCard; 