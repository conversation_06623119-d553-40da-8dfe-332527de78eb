import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Download, Settings, PlusCircle } from 'lucide-react';

interface PageHeaderProps {
  selectedUsersCount: number;
  onImportExport: () => void;
  onBatchActions: () => void;
  onCreateUser: () => void;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  selectedUsersCount,
  onImportExport,
  onBatchActions,
  onCreateUser,
}) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
        <p className="text-muted-foreground mt-2">
          管理系统中的所有用户账号、角色和权限设置
        </p>
      </div>
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          size="sm"
          onClick={onImportExport}
        >
          <Download className="mr-2 h-4 w-4" />
          导入/导出
        </Button>
        {selectedUsersCount > 0 && (
          <Button 
            variant="outline" 
            size="sm"
            onClick={onBatchActions}
          >
            <Settings className="mr-2 h-4 w-4" />
            批量操作 ({selectedUsersCount})
          </Button>
        )}
        <Button onClick={onCreateUser}>
          <PlusCircle className="mr-2 h-4 w-4" />
          添加用户
        </Button>
      </div>
    </div>
  );
};

export default PageHeader; 