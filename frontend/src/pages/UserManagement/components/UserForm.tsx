import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Edit,
  PlusCircle,
  Eye,
  EyeOff,
  Shield,
  GraduationCap,
  BookOpen,
  Users,
  UserCheck,
  Settings,
  Info
} from 'lucide-react';
import { User, RoleAssignment } from '@/types/user';
import { RoleSelect } from '@/components/role';
import { useRoleManagement } from '@/hooks/useRoleManagement';

type UserFormData = Partial<User> & {
  password?: string;
  confirmPassword?: string;
  roleAssignments?: RoleAssignment[];
};

interface UserFormProps {
  user: UserFormData | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: () => void;
  onUserChange: (user: UserFormData) => void;
}

const UserForm: React.FC<UserFormProps> = ({
  user,
  open,
  onOpenChange,
  onSave,
  onUserChange,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const { roles, loading: rolesLoading, fetchUserIdentities } = useRoleManagement();

  // 初始化角色分配
  useEffect(() => {
    if (user && user.user_id && !user.roleAssignments) {
      fetchUserIdentities(user.user_id).then(identities => {
        const assignments: RoleAssignment[] = identities.map(identity => ({
          role_id: identity.role_id,
          target_type: identity.target_type,
          target_id: identity.target_id,
          subject: identity.subject,
          display_name: identity.display_name || identity.role.name,
        }));
        onUserChange({ ...user, roleAssignments: assignments });
      });
    } else if (user && !user.user_id && !user.roleAssignments) {
      // 新用户，初始化空的角色分配
      onUserChange({ ...user, roleAssignments: [] });
    }
  }, [user?.user_id, fetchUserIdentities, onUserChange]);

  if (!user) return null;

  const getRoleBadges = (user: UserFormData) => {
    const badges = [];
    if (user.is_admin) badges.push({ label: '管理员', variant: 'destructive' as const, icon: Shield });
    if (user.is_teacher) badges.push({ label: '教师', variant: 'default' as const, icon: GraduationCap });
    if (user.is_student) badges.push({ label: '学生', variant: 'secondary' as const, icon: BookOpen });
    return badges;
  };

  // 处理角色分配变化
  const handleRoleAssignmentsChange = (assignments: RoleAssignment[]) => {
    onUserChange({ ...user, roleAssignments: assignments });
  };

  // 获取角色统计信息
  const getRoleStats = () => {
    const assignments = user.roleAssignments || [];
    return {
      total: assignments.length,
      systemRoles: assignments.filter(a => {
        const role = roles.find(r => r.id === a.role_id);
        return role?.category === 'system';
      }).length,
      businessRoles: assignments.filter(a => {
        const role = roles.find(r => r.id === a.role_id);
        return role?.category === 'business' || role?.category === 'school';
      }).length,
    };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const roleStats = getRoleStats();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            {user.user_id ? (
              <>
                <Edit className="mr-2 h-5 w-5" />
                编辑用户
              </>
            ) : (
              <>
                <PlusCircle className="mr-2 h-5 w-5" />
                创建用户
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {user.user_id
              ? '修改用户的基本信息和角色权限设置'
              : '创建一个新的用户账号，设置基本信息和角色权限'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                基本信息
              </TabsTrigger>
              <TabsTrigger value="roles" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                角色权限
                {roleStats.total > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {roleStats.total}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="flex-1 overflow-y-auto space-y-6 mt-4">
              {/* 基本信息 */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <h4 className="text-sm font-semibold">基本信息</h4>
                  <div className="flex-1 h-px bg-border"></div>
                </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium">
                  用户名 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="username"
                  value={user.username || ''}
                  onChange={(e) => onUserChange({ 
                    ...user, 
                    username: e.target.value 
                  })}
                  disabled={!!user.user_id}
                  placeholder="请输入用户名"
                  className="w-full"
                />
                {user.user_id && (
                  <p className="text-xs text-muted-foreground">
                    用户名创建后不可修改
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone_number" className="text-sm font-medium">
                  手机号 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone_number"
                  value={user.phone_number || ''}
                  onChange={(e) => onUserChange({ 
                    ...user, 
                    phone_number: e.target.value 
                  })}
                  placeholder="请输入11位手机号"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* 密码设置 */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <h4 className="text-sm font-semibold">密码设置</h4>
              <div className="flex-1 h-px bg-border"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium">
                  {user.user_id ? '新密码' : '密码'} 
                  {!user.user_id && <span className="text-red-500"> *</span>}
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={user.password || ''}
                    onChange={(e) => onUserChange({ 
                      ...user, 
                      password: e.target.value 
                    })}
                    placeholder={user.user_id ? '留空则不修改密码' : '请输入密码'}
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  密码长度至少6位
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-sm font-medium">
                  确认密码
                  {!user.user_id && <span className="text-red-500"> *</span>}
                </Label>
                <Input
                  id="confirmPassword"
                  type={showPassword ? 'text' : 'password'}
                  value={user.confirmPassword || ''}
                  onChange={(e) => onUserChange({ 
                    ...user, 
                    confirmPassword: e.target.value 
                  })}
                  placeholder="请再次输入密码"
                />
              </div>
            </div>
          </div>

          {/* 状态设置 */}
          {user.user_id && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-semibold">状态设置</h4>
                <div className="flex-1 h-px bg-border"></div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">账号状态</Label>
                    <p className="text-sm text-muted-foreground">
                      控制用户是否可以登录系统
                    </p>
                  </div>
                  <Switch
                    checked={!!user.is_active}
                    onCheckedChange={(checked) => 
                      onUserChange({ ...user, is_active: checked })
                    }
                  />
                </div>
              </div>
            </div>
          )}

          {/* 用户信息预览 */}
          {user.user_id && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-semibold">用户信息</h4>
                <div className="flex-1 h-px bg-border"></div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <p className="text-xs text-muted-foreground">创建时间</p>
                  <p className="text-sm font-medium">
                    {formatDate(user.created_at!)}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">手机验证</p>
                  <p className="text-sm font-medium">
                    {user.phone_verified ? '已验证' : '未验证'}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">角色</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {getRoleBadges(user as User).map((badge, index) => (
                      <Badge key={index} variant={badge.variant} className="text-xs">
                        <badge.icon className="h-3 w-3 mr-1" />
                        {badge.label}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
            </TabsContent>

            <TabsContent value="roles" className="flex-1 overflow-y-auto space-y-6 mt-4">
              {/* 角色分配 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-semibold">角色分配</h4>
                    <div className="flex-1 h-px bg-border"></div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {roleStats.total} 个角色
                  </Badge>
                </div>

                {/* 角色选择器 */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">选择角色</Label>
                  <RoleSelect
                    availableRoles={roles}
                    selectedAssignments={user.roleAssignments || []}
                    onAssignmentsChange={handleRoleAssignmentsChange}
                    multiple={true}
                    placeholder="选择用户角色..."
                    disabled={rolesLoading}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    可以为用户分配多个角色，角色决定了用户在系统中的权限范围
                  </p>
                </div>

                {/* 角色统计 */}
                {roleStats.total > 0 && (
                  <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                    <div className="text-center">
                      <p className="text-lg font-semibold text-primary">{roleStats.total}</p>
                      <p className="text-xs text-muted-foreground">总角色数</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold text-orange-600">{roleStats.systemRoles}</p>
                      <p className="text-xs text-muted-foreground">系统角色</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold text-blue-600">{roleStats.businessRoles}</p>
                      <p className="text-xs text-muted-foreground">业务角色</p>
                    </div>
                  </div>
                )}
              </div>

              {/* 已分配角色详情 */}
              {user.roleAssignments && user.roleAssignments.length > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-semibold">角色详情</h4>
                    <div className="flex-1 h-px bg-border"></div>
                  </div>

                  <div className="space-y-3">
                    {user.roleAssignments.map((assignment, index) => {
                      const role = roles.find(r => r.id === assignment.role_id);
                      if (!role) return null;

                      return (
                        <div key={assignment.role_id} className="p-4 border rounded-lg">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h5 className="font-medium">{role.name}</h5>
                                <Badge variant="outline" className="text-xs">
                                  {role.category}
                                </Badge>
                                <Badge variant="secondary" className="text-xs">
                                  级别 {role.level}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{role.description}</p>
                            </div>
                          </div>

                          {assignment.target_type && (
                            <div className="mt-2 p-2 bg-muted/50 rounded text-xs">
                              <span className="font-medium">作用范围：</span>
                              {assignment.target_type === 'school' && '全校'}
                              {assignment.target_type === 'grade' && '年级'}
                              {assignment.target_type === 'class' && '班级'}
                              {assignment.target_type === 'subject_group' && '学科组'}
                              {assignment.subject && ` - ${assignment.subject}`}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* 权限说明 */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <h4 className="text-sm font-semibold">权限说明</h4>
                  <div className="flex-1 h-px bg-border"></div>
                </div>

                <div className="p-4 border rounded-lg bg-blue-50/50">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="space-y-2">
                      <h5 className="font-medium text-blue-900">角色权限说明</h5>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>• 系统角色：拥有系统级别的管理权限</li>
                        <li>• 租户角色：在特定租户内的管理权限</li>
                        <li>• 学校角色：学校内的教学和管理权限</li>
                        <li>• 业务角色：具体业务功能的操作权限</li>
                        <li>• 用户可以同时拥有多个角色，权限会进行合并</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="w-full sm:w-auto">
            取消
          </Button>
          <Button onClick={onSave} className="w-full sm:w-auto">
            {user.user_id ? '保存更改' : '创建用户'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserForm; 