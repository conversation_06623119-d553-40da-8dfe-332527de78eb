import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Users, UserCheck, Phone, Shield, GraduationCap, BookOpen } from 'lucide-react';

interface UserStatsProps {
  stats: {
    total: number;
    active: number;
    verified: number;
    admins: number;
    teachers: number;
    students: number;
  };
}

const UserStats: React.FC<UserStatsProps> = ({ stats }) => {
  const statItems = [
    { label: '总用户', value: stats.total, icon: Users, color: 'text-muted-foreground' },
    { label: '活跃', value: stats.active, icon: UserCheck, color: 'text-green-600' },
    { label: '已验证', value: stats.verified, icon: Phone, color: 'text-blue-600' },
    { label: '管理员', value: stats.admins, icon: Shield, color: 'text-purple-600' },
    { label: '教师', value: stats.teachers, icon: GraduationCap, color: 'text-orange-600' },
    { label: '学生', value: stats.students, icon: BookOpen, color: 'text-indigo-600' },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      {statItems.map((item, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{item.label}</p>
                <p className={`text-2xl font-bold ${item.color}`}>{item.value}</p>
              </div>
              <item.icon className={`h-8 w-8 ${item.color}`} />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default UserStats; 