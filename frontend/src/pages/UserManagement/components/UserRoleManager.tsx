import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Users,
  Shield,
  GraduationCap,
  BookOpen,
  Building,
  UserCheck,
  Plus,
  Trash2,
  AlertTriangle,
  Calendar,
  Target,
  Info,
} from 'lucide-react';
import { toast } from 'sonner';
import { User, RoleAssignment } from '@/types/user';
import { UserIdentity, Role } from '@/types/role';
import { RoleSelector } from '@/components/role';
import { useRoleManagement } from '@/hooks/useRoleManagement';
import { useTenantContext } from '@/hooks/useTenantContext';

interface UserRoleManagerProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRolesUpdated?: () => void;
}

const UserRoleManager: React.FC<UserRoleManagerProps> = ({
  user,
  open,
  onOpenChange,
  onRolesUpdated,
}) => {
  const [userIdentities, setUserIdentities] = useState<UserIdentity[]>([]);
  const [loading, setLoading] = useState(false);
  const [showRoleSelector, setShowRoleSelector] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [identityToRemove, setIdentityToRemove] = useState<UserIdentity | null>(null);
  const [selectedRoleAssignments, setSelectedRoleAssignments] = useState<RoleAssignment[]>([]);

  const {
    roles,
    fetchUserIdentities,
    assignRolesToUser,
    removeUserRole,
    getRoleInfo,
  } = useRoleManagement();

  const { currentTenant, canAssignRoles } = useTenantContext();

  // 加载用户身份
  const loadUserIdentities = async () => {
    if (!user?.user_id) return;

    setLoading(true);
    try {
      console.log('🔍 开始加载用户身份，用户ID:', user.user_id);
      const identities = await fetchUserIdentities(user.user_id);
      console.log('📋 获取到的用户身份:', identities);
      console.log('🎭 当前可用角色:', roles);
      setUserIdentities(identities);
    } catch (error) {
      console.error('加载用户身份失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && user) {
      loadUserIdentities().then();
    }
  }, [open, user]);

  // 获取角色图标
  const getRoleIcon = (role: Role) => {
    switch (role.category) {
      case 'system':
        return <Shield className="h-4 w-4" />;
      case 'tenant':
        return <Building className="h-4 w-4" />;
      case 'school':
        return <GraduationCap className="h-4 w-4" />;
      case 'business':
        return <Users className="h-4 w-4" />;
      case 'class_grade':
        return <BookOpen className="h-4 w-4" />;
      case 'end_user':
        return <UserCheck className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  // 获取角色级别颜色
  const getRoleLevelColor = (level: number) => {
    if (level <= 2) return 'bg-red-100 text-red-800';
    if (level <= 4) return 'bg-orange-100 text-orange-800';
    if (level <= 6) return 'bg-blue-100 text-blue-800';
    if (level <= 8) return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 处理添加角色
  const handleAddRoles = async (assignments: RoleAssignment[]) => {
    if (!user?.user_id) return;

    if (!canAssignRoles) {
      toast.error('您没有权限分配角色');
      return;
    }

    const success = await assignRolesToUser(user.user_id, assignments);

    if (success) {
      await loadUserIdentities();
      onRolesUpdated?.();
      setShowRoleSelector(false);
      setSelectedRoleAssignments([]); // 清空选择
    }
  };

  // 处理移除角色
  const handleRemoveRole = async () => {
    if (!identityToRemove) return;

    const success = await removeUserRole(identityToRemove.id);
    
    if (success) {
      await loadUserIdentities();
      onRolesUpdated?.();
      setShowRemoveDialog(false);
      setIdentityToRemove(null);
    }
  };

  // 确认移除角色
  const confirmRemoveRole = (identity: UserIdentity) => {
    setIdentityToRemove(identity);
    setShowRemoveDialog(true);
  };

  // 获取目标类型显示名称和图标
  const getTargetTypeInfo = (targetType: string) => {
    const typeInfo: Record<string, { name: string; icon: React.ReactNode; color: string }> = {
      school: {
        name: '全校范围',
        icon: <Building className="h-3 w-3" />,
        color: 'text-blue-600 bg-blue-50'
      },
      grade: {
        name: '年级范围',
        icon: <GraduationCap className="h-3 w-3" />,
        color: 'text-green-600 bg-green-50'
      },
      class: {
        name: '班级范围',
        icon: <BookOpen className="h-3 w-3" />,
        color: 'text-orange-600 bg-orange-50'
      },
      subject_group: {
        name: '学科组范围',
        icon: <Users className="h-3 w-3" />,
        color: 'text-purple-600 bg-purple-50'
      },
      student: {
        name: '学生个体',
        icon: <UserCheck className="h-3 w-3" />,
        color: 'text-pink-600 bg-pink-50'
      },
      teacher: {
        name: '教师个体',
        icon: <UserCheck className="h-3 w-3" />,
        color: 'text-indigo-600 bg-indigo-50'
      },
    };
    return typeInfo[targetType] || {
      name: targetType,
      icon: <Target className="h-3 w-3" />,
      color: 'text-gray-600 bg-gray-50'
    };
  };

  // 获取统计信息
  const getStats = () => {
    // 计算总角色数（每个身份可能有多个角色）
    const totalRoles = userIdentities?.reduce((sum, identity) => sum + identity.roles.length, 0) || 0;

    // 计算系统角色数
    const systemRoles = userIdentities?.reduce((count, identity) => {
      const systemRoleCount = identity.roles.filter(roleInfo => {
        const role = getRoleInfo(roleInfo.id);
        return role?.category === 'system';
      }).length;
      return count + systemRoleCount;
    }, 0) || 0;

    // 计算业务角色数
    const businessRoles = userIdentities?.reduce((count, identity) => {
      const businessRoleCount = identity.roles.filter(roleInfo => {
        const role = getRoleInfo(roleInfo.id);
        return role?.category === 'business' || role?.category === 'school';
      }).length;
      return count + businessRoleCount;
    }, 0) || 0;

    return { total: totalRoles, systemRoles, businessRoles };
  };

  if (!user) return null;

  const stats = getStats();

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              管理用户角色
            </DialogTitle>
            <DialogDescription>
              管理 {user.username} 在 {currentTenant?.tenantName || '当前租户'} 中的角色分配和权限设置
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden space-y-6">
            {/* 用户和租户信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 用户信息 */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="font-semibold text-primary">
                      {user.username.slice(0, 2).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold">{user.username}</h3>
                    <p className="text-sm text-muted-foreground">{user.phone_number}</p>
                  </div>
                </div>
              </div>

              {/* 租户信息 */}
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-900">{currentTenant?.tenantName || '未知租户'}</h3>
                    <p className="text-sm text-blue-600">当前管理租户</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-primary">{stats.total}</p>
                <p className="text-sm text-muted-foreground">总角色数</p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-orange-600">{stats.systemRoles}</p>
                <p className="text-sm text-muted-foreground">系统角色</p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{stats.businessRoles}</p>
                <p className="text-sm text-muted-foreground">业务角色</p>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">已分配角色</h4>
                {!canAssignRoles && (
                  <Badge variant="outline" className="text-xs text-muted-foreground">
                    <Info className="h-3 w-3 mr-1" />
                    只读模式
                  </Badge>
                )}
              </div>
              {canAssignRoles && (
                <Button
                  onClick={() => setShowRoleSelector(true)}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  添加角色
                </Button>
              )}
            </div>

            {/* 角色列表 */}
            <div className="h-[400px] overflow-y-auto border rounded-md p-2">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2">加载中...</span>
                </div>
              ) : userIdentities.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>该用户尚未分配任何角色</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowRoleSelector(true)}
                    className="mt-2"
                  >
                    立即分配角色
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {userIdentities.map((identity) => {
                    console.log('🔍 处理身份:', identity);

                    // 处理多角色情况，为每个角色创建一个显示项
                    return identity.roles?.map((roleInfo, roleIndex) => {
                      console.log('🎭 处理角色信息:', roleInfo);

                      // 尝试从完整角色列表中获取详细信息
                      const fullRole = getRoleInfo(roleInfo.id);
                      const displayRole: Role = fullRole || {
                        id: roleInfo.id,
                        name: roleInfo.name,
                        code: roleInfo.code,
                        description: roleInfo.description || '',
                        category: 'unknown' as any,
                        level: 5,
                        is_active: true,
                        permissions: [],
                        is_system: false,
                        created_by: '',
                        created_at: '',
                        updated_at: ''
                      };

                      return (
                        <div key={`${identity.id}-${roleIndex}`} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              {/* 角色基本信息 */}
                              <div className="flex items-center gap-2 mb-3">
                                {getRoleIcon(displayRole)}
                                <h5 className="font-semibold text-lg">{displayRole.name}</h5>
                                <Badge variant="outline" className={getRoleLevelColor(displayRole.level)}>
                                  级别 {displayRole.level}
                                </Badge>
                                <Badge variant="secondary" className="text-xs">
                                  {displayRole.category}
                                </Badge>
                              </div>

                              {/* 角色描述 */}
                              <p className="text-sm text-muted-foreground mb-3">
                                {displayRole.description}
                              </p>

                              {/* 作用范围 */}
                              {identity.target_type && (
                                <div className="flex items-center gap-2 text-xs">
                                  {(() => {
                                    const typeInfo = getTargetTypeInfo(identity.target_type);
                                    return (
                                      <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${typeInfo.color}`}>
                                        {typeInfo.icon}
                                        <span className="font-medium">{typeInfo.name}</span>
                                        {identity.subject && (
                                          <span className="ml-1">- {identity.subject}</span>
                                        )}
                                      </div>
                                    );
                                  })()}
                                </div>
                              )}

                              {/* 分配时间 */}
                              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                <Calendar className="h-3 w-3" />
                                <span>分配时间: {formatDate(identity.created_at)}</span>
                              </div>
                            </div>

                            {canAssignRoles && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => confirmRemoveRole(identity)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    });
                  }).flat()}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 角色选择器 */}
      <RoleSelector
        open={showRoleSelector}
        onOpenChange={setShowRoleSelector}
        availableRoles={roles}
        selectedAssignments={selectedRoleAssignments}
        onAssignmentsChange={setSelectedRoleAssignments}
        assignedRoleIds={userIdentities.flatMap(identity => identity.roles?.map(role => role.id))}
        onConfirm={handleAddRoles}
        onCancel={() => {
          setShowRoleSelector(false);
          setSelectedRoleAssignments([]); // 清空选择
        }}
        title="为用户添加角色"
        description={`为 ${user.username} 选择要分配的角色`}
      />

      {/* 移除确认对话框 */}
      <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="mr-2 h-5 w-5" />
              确认移除角色
            </AlertDialogTitle>
            <AlertDialogDescription>
              您即将移除用户 <strong>{user.username}</strong> 的身份，包含以下角色：
              <strong>
                {identityToRemove && identityToRemove.roles?.map(role => role.name).join(', ')}
              </strong>
              <br />
              <br />
              移除后，用户将失去该身份对应的所有权限。此操作不可撤销，请谨慎操作。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveRole}
              className="bg-red-600 hover:bg-red-700"
            >
              确认移除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default UserRoleManager;
