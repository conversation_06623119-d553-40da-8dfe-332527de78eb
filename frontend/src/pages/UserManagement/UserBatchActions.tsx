import React, { useState } from 'react';
import {
    Di<PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Users,
    UserCheck,
    UserX,
    Trash2,
    Download,
    Upload,
    Settings,
    AlertTriangle,
    CheckCircle,
} from 'lucide-react';
import { toast } from "sonner";
import { User, RoleAssignment } from '@/types/user';
import { RoleSelector } from '@/components/role';
import { useRoleManagement } from '@/hooks/useRoleManagement';

interface UserBatchActionsProps {
    selectedUsers: User[];
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onBatchUpdate: (action: string, users: User[]) => Promise<void>;
}

type BatchAction = 'activate' | 'deactivate' | 'delete' | 'export' | 'assign-role' | 'remove-role';

const UserBatchActions: React.FC<UserBatchActionsProps> = ({
    selectedUsers,
    open,
    onOpenChange,
    onBatchUpdate,
}) => {
    const [selectedAction, setSelectedAction] = useState<BatchAction | ''>('');
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [loading, setLoading] = useState(false);
    const [roleToAssign, setRoleToAssign] = useState<string>('');
  const [showRoleSelector, setShowRoleSelector] = useState(false);
  const [roleAssignments, setRoleAssignments] = useState<RoleAssignment[]>([]);
  const [roleOperation, setRoleOperation] = useState<'assign' | 'remove'>('assign');

  const { roles, batchRoleOperation } = useRoleManagement();

    const handleActionSelect = (action: BatchAction) => {
        setSelectedAction(action);
        if (action === 'delete') {
            setShowConfirmDialog(true);
        } else if (action === 'assign-role') {
            setRoleOperation('assign');
            setRoleAssignments([]);
            setShowRoleSelector(true);
        } else if (action === 'remove-role') {
            setRoleOperation('remove');
            setRoleAssignments([]);
            setShowRoleSelector(true);
        } else {
            handleExecuteAction(action);
        }
    };

    const handleExecuteAction = async (action: BatchAction) => {
        if (selectedUsers.length === 0) {
            toast.error('请先选择要操作的用户');
            return;
        }

        setLoading(true);
        try {
            await onBatchUpdate(action, selectedUsers);
            
            let message = '';
            switch (action) {
                case 'activate':
                    message = `已激活 ${selectedUsers.length} 个用户`;
                    break;
                case 'deactivate':
                    message = `已禁用 ${selectedUsers.length} 个用户`;
                    break;
                case 'delete':
                    message = `已删除 ${selectedUsers.length} 个用户`;
                    break;
                case 'export':
                    message = `已导出 ${selectedUsers.length} 个用户的数据`;
                    break;
                case 'assign-role':
                    message = `已为 ${selectedUsers.length} 个用户分配角色`;
                    break;
                case 'remove-role':
                    message = `已为 ${selectedUsers.length} 个用户移除角色`;
                    break;
            }
            
            toast.success(message);
            onOpenChange(false);
            setSelectedAction('');
            setRoleToAssign('');
        } catch (error) {
            toast.error('批量操作失败');
            console.error('批量操作失败:', error);
        } finally {
            setLoading(false);
            setShowConfirmDialog(false);
        }
    };

    // 处理角色操作
    const handleRoleOperation = async (assignments: RoleAssignment[]) => {
        if (selectedUsers.length === 0 || assignments.length === 0) {
            toast.error('请选择用户和角色');
            return;
        }

        setLoading(true);
        try {
            const userIds = selectedUsers.map(user => user.user_id);
            const success = await batchRoleOperation({
                user_ids: userIds,
                operation: roleOperation,
                role_assignments: assignments,
            });

            if (success) {
                const message = roleOperation === 'assign'
                    ? `已为 ${selectedUsers.length} 个用户分配 ${assignments.length} 个角色`
                    : `已为 ${selectedUsers.length} 个用户移除 ${assignments.length} 个角色`;
                toast.success(message);
                onOpenChange(false);
                setSelectedAction('');
                setRoleAssignments([]);
                setShowRoleSelector(false);
                // 触发父组件刷新
                await onBatchUpdate(roleOperation === 'assign' ? 'assign-role' : 'remove-role', selectedUsers);
            }
        } catch (error) {
            toast.error('角色操作失败');
            console.error('角色操作失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const getActionDescription = (action: BatchAction) => {
        switch (action) {
            case 'activate':
                return '激活选中的用户，使其能够正常登录系统';
            case 'deactivate':
                return '禁用选中的用户，阻止其登录系统';
            case 'delete':
                return '永久删除选中的用户及其相关数据，此操作不可撤销';
            case 'export':
                return '导出选中用户的基本信息到 Excel 文件';
            case 'assign-role':
                return '为选中的用户批量分配指定角色';
            case 'remove-role':
                return '为选中的用户批量移除指定角色';
            default:
                return '';
        }
    };

    const getActionIcon = (action: BatchAction) => {
        switch (action) {
            case 'activate':
                return <UserCheck className="h-4 w-4" />;
            case 'deactivate':
                return <UserX className="h-4 w-4" />;
            case 'delete':
                return <Trash2 className="h-4 w-4" />;
            case 'export':
                return <Download className="h-4 w-4" />;
            case 'assign-role':
                return <Settings className="h-4 w-4" />;
            case 'remove-role':
                return <UserX className="h-4 w-4" />;
            default:
                return null;
        }
    };

    const getActionColor = (action: BatchAction) => {
        switch (action) {
            case 'activate':
                return 'text-green-600 hover:text-green-700';
            case 'deactivate':
                return 'text-orange-600 hover:text-orange-700';
            case 'delete':
                return 'text-red-600 hover:text-red-700';
            case 'export':
                return 'text-blue-600 hover:text-blue-700';
            case 'assign-role':
                return 'text-purple-600 hover:text-purple-700';
            case 'remove-role':
                return 'text-red-600 hover:text-red-700';
            default:
                return '';
        }
    };

    const getUserStats = () => {
        const active = selectedUsers.filter(u => u.is_active).length;
        const inactive = selectedUsers.length - active;
        const verified = selectedUsers.filter(u => u.phone_verified).length;
        const admins = selectedUsers.filter(u => u.is_admin).length;
        const teachers = selectedUsers.filter(u => u.is_teacher).length;
        const students = selectedUsers.filter(u => u.is_student).length;
        
        return { active, inactive, verified, admins, teachers, students };
    };

    const stats = getUserStats();

    return (
        <>
            <Dialog open={open} onOpenChange={onOpenChange}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle className="flex items-center">
                            <Users className="mr-2 h-5 w-5" />
                            批量操作用户
                        </DialogTitle>
                        <DialogDescription>
                            对选中的 {selectedUsers.length} 个用户执行批量操作
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-6 py-4">
                        {/* 选中用户统计 */}
                        <div className="p-4 bg-muted/50 rounded-lg">
                            <h4 className="font-medium mb-3">选中用户统计</h4>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                                    <p className="text-sm text-muted-foreground">活跃用户</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-orange-600">{stats.inactive}</p>
                                    <p className="text-sm text-muted-foreground">禁用用户</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-blue-600">{stats.verified}</p>
                                    <p className="text-sm text-muted-foreground">已验证</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-purple-600">{stats.admins}</p>
                                    <p className="text-sm text-muted-foreground">管理员</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-orange-600">{stats.teachers}</p>
                                    <p className="text-sm text-muted-foreground">教师</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-indigo-600">{stats.students}</p>
                                    <p className="text-sm text-muted-foreground">学生</p>
                                </div>
                            </div>
                        </div>

                        {/* 用户列表预览 */}
                        <div>
                            <h4 className="font-medium mb-3">选中的用户</h4>
                            <div className="max-h-32 overflow-y-auto border rounded-lg p-3">
                                <div className="flex flex-wrap gap-2">
                                    {selectedUsers.map((user) => (
                                        <Badge key={user.user_id} variant="outline" className="text-xs">
                                            {user.username}
                                        </Badge>
                                    ))}
                                </div>
                            </div>
                        </div>

                        <Separator />

                        {/* 操作选择 */}
                        <div className="space-y-4">
                            <h4 className="font-medium">选择操作</h4>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {(['activate', 'deactivate', 'export', 'assign-role', 'remove-role'] as BatchAction[]).map((action) => (
                                    <Button
                                        key={action}
                                        variant="outline"
                                        className={`justify-start h-auto p-4 ${getActionColor(action)}`}
                                        onClick={() => handleActionSelect(action)}
                                        disabled={loading}
                                    >
                                        <div className="flex items-start space-x-3">
                                            {getActionIcon(action)}
                                            <div className="text-left">
                                                <div className="font-medium">
                                                    {action === 'activate' && '激活用户'}
                                                    {action === 'deactivate' && '禁用用户'}
                                                    {action === 'export' && '导出数据'}
                                                    {action === 'assign-role' && '分配角色'}
                                                    {action === 'remove-role' && '移除角色'}
                                                </div>
                                                <div className="text-xs text-muted-foreground mt-1">
                                                    {getActionDescription(action)}
                                                </div>
                                            </div>
                                        </div>
                                    </Button>
                                ))}
                            </div>

                            {/* 角色分配选择器 */}
                            {selectedAction === 'assign-role' && (
                                <div className="space-y-3 p-4 border rounded-lg">
                                    <h5 className="font-medium">选择要分配的角色</h5>
                                    <Select value={roleToAssign} onValueChange={setRoleToAssign}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="请选择角色" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="admin">管理员</SelectItem>
                                            <SelectItem value="teacher">教师</SelectItem>
                                            <SelectItem value="student">学生</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Button
                                        onClick={() => handleExecuteAction('assign-role')}
                                        disabled={!roleToAssign || loading}
                                        className="w-full"
                                    >
                                        {loading ? '处理中...' : '确认分配角色'}
                                    </Button>
                                </div>
                            )}

                            {/* 危险操作 */}
                            <div className="pt-4 border-t">
                                <h5 className="font-medium text-red-600 mb-3">危险操作</h5>
                                <Button
                                    variant="outline"
                                    className="w-full justify-start h-auto p-4 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                                    onClick={() => handleActionSelect('delete')}
                                    disabled={loading}
                                >
                                    <div className="flex items-start space-x-3">
                                        <Trash2 className="h-4 w-4" />
                                        <div className="text-left">
                                            <div className="font-medium">删除用户</div>
                                            <div className="text-xs text-muted-foreground mt-1">
                                                永久删除选中的用户及其相关数据，此操作不可撤销
                                            </div>
                                        </div>
                                    </div>
                                </Button>
                            </div>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" onClick={() => onOpenChange(false)}>
                            取消
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* 删除确认对话框 */}
            <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center text-red-600">
                            <AlertTriangle className="mr-2 h-5 w-5" />
                            确认删除用户
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            您即将删除 <strong>{selectedUsers.length}</strong> 个用户。
                            此操作将永久删除这些用户的所有数据，包括：
                            <ul className="list-disc list-inside mt-2 space-y-1">
                                <li>用户基本信息</li>
                                <li>角色和权限设置</li>
                                <li>相关的业务数据</li>
                            </ul>
                            <p className="mt-3 font-medium text-red-600">
                                此操作不可撤销，请谨慎操作！
                            </p>
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => handleExecuteAction('delete')}
                            className="bg-red-600 hover:bg-red-700"
                            disabled={loading}
                        >
                            {loading ? '删除中...' : '确认删除'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* 角色选择器 */}
            <RoleSelector
                open={showRoleSelector}
                onOpenChange={setShowRoleSelector}
                availableRoles={roles}
                selectedAssignments={roleAssignments}
                onAssignmentsChange={setRoleAssignments}
                onConfirm={handleRoleOperation}
                onCancel={() => setShowRoleSelector(false)}
                title={roleOperation === 'assign' ? '批量分配角色' : '批量移除角色'}
                description={`为选中的 ${selectedUsers.length} 个用户${roleOperation === 'assign' ? '分配' : '移除'}角色`}
            />
        </>
    );
};

export default UserBatchActions;