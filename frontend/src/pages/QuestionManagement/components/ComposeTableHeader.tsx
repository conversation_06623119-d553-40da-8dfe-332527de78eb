import {FC, useEffect, useRef, useState} from "react";
import {CardTitle} from "@/components/ui/card.tsx";
import {Plus} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";
import {
    ComposeQuestionTypeQueryParams,
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary
} from "@/types";
import {EducationStageSummary} from "@/types/educationalStage.ts";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";

interface ComposeTableHeaderProps {
    onCreate: () => void;
    questionTypeSummaries: QuestionTypeSummary[];
    subjectSummaries: SubjectSummary[];
    gradeLevelSummaries: GradeLevelSummary[];
    educationStageSummaries: EducationStageSummary[];
    onFilterChange: (filter:ComposeQuestionTypeQueryParams) => void;
}

const ComposeTableHeader: FC<ComposeTableHeaderProps> = ({
                                                             onCreate,
                                                             questionTypeSummaries,
                                                             subjectSummaries,
                                                             gradeLevelSummaries,
                                                             educationStageSummaries,
                                                             onFilterChange
                                                         }) => {

    const [questionTypeSelect, setQuestionTypeSelect] = useState<string | undefined>(undefined);
    const [subjectSelect, setSubjectSelect] = useState<string | undefined>(undefined);
    const [gradeLevelSelect, setGradeLevelSelect] = useState<string | undefined>(undefined);
    const [educationStageSelect, setEducationStageSelect] = useState<string | undefined>(undefined);
    
    const firstRender = useRef(true);
    useEffect(() => {
        if (firstRender.current) {
            firstRender.current = false;
            return;
        }

        onFilterChange({
            question_type_code: questionTypeSelect === '' ? undefined : questionTypeSelect,
            subject_code: subjectSelect === '' ? undefined : subjectSelect,
            grade_level_code: gradeLevelSelect === '' ? undefined : gradeLevelSelect,
            education_stage_code: educationStageSelect === '' ? undefined : educationStageSelect,
        });
    }, [questionTypeSelect, subjectSelect, gradeLevelSelect, educationStageSelect]);


    return (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="text-lg">题型关联列表</CardTitle>
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                <div className="flex gap-2">
                    <Select
                        value={questionTypeSelect}
                        onValueChange={(value) =>
                            setQuestionTypeSelect(value === 'none' ? '' : value)
                        }
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择题型"/>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem key={''} value={'none'}>未选择</SelectItem>
                            {questionTypeSummaries.map((questionType) => (
                                <SelectItem key={String(questionType.code)} value={String(questionType.code)}>
                                    {questionType.type_name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <Select
                        value={subjectSelect}
                        onValueChange={(value) =>
                            setSubjectSelect(value === 'none' ? '' : value)
                        }
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择学科"/>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem key={''} value={'none'}>未选择</SelectItem>
                            {subjectSummaries.map((subject) => (
                                <SelectItem key={String(subject.code)} value={String(subject.code)}>
                                    {subject.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <Select
                        value={educationStageSelect}
                        onValueChange={(value) =>
                            setEducationStageSelect(value === 'none' ? '' : value)
                        }
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择学段"/>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem key={''} value={'none'}>未选择</SelectItem>
                            {educationStageSummaries.map((es) => (
                                <SelectItem key={String(es.code)} value={String(es.code)}>
                                    {es.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <Select
                        value={gradeLevelSelect}
                        onValueChange={(value) =>
                            setGradeLevelSelect(value === 'none' ? '' : value)
                        }
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择年级"/>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem key={''} value={'none'}>未选择</SelectItem>
                            {gradeLevelSummaries.map((gradeLevel) => (
                                <SelectItem key={String(gradeLevel.code)} value={String(gradeLevel.code)}>
                                    {gradeLevel.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                </div>

                <div className="flex space-x-2">
                    <Button onClick={onCreate}>
                        <Plus className="w-4 h-4 mr-2"/>
                        新增题型关联
                    </Button>
                </div>
            </div>
        </div>
    );

};

export default ComposeTableHeader;
