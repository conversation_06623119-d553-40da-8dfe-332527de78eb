import {useEffect, useState} from "react";
import {useNavigate} from "react-router-dom";
import {useAuth} from "@/contexts/AuthContext.tsx";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Label} from "@/components/ui/label";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {toast} from "sonner";
import {getTenantSummaries} from "@/services/tenantApi.ts";
import {bindIdentities} from "@/services/identityApi.ts";
import {Checkbox} from "@/components/ui/checkbox.tsx";
import {Tenant} from "@/types";

const IdentityBindingPage = () => {
    const navigate = useNavigate();
    const {token} = useAuth();

    const [tenants, setTenants] = useState<Tenant[]>([]);
    const [selectedTenantId, setSelectedTenantId] = useState<string>("");
    const [identitySelectValue, setIdentitySelectValue] = useState<"student" | "teacher" | "">("");
    const [isParent, setIsParent] = useState(false);
    const [extendInfo, setExtendInfo] = useState("");

    useEffect(() => {
        if (!token) {
            navigate("/login");
            return;
        }
        fetchTenants();
    }, [token]);

    const fetchTenants = async () => {
        try {
            let res = await getTenantSummaries()
            setTenants(res.data ?? [])
        } catch (error: any) {
            toast.info(error.response?.data?.message || "加载失败", {description: "获取租户列表失败"});
        }
    };

    const handleBind = async () => {
        const identityType = identitySelectValue === "student" && isParent ? "parent" : identitySelectValue;

        try {
            await bindIdentities({
                tenant_id: selectedTenantId,
                identity_type: identityType,
                extend_info: extendInfo,
            });
            toast("申请成功");
            navigate('/identitySelect');
        } catch (error: any) {
            toast.error(error.response?.data?.message || "绑定身份失败");
        }
    };

    return (
        <div className="flex justify-center mt-10">
            <div className="absolute inset-0 overflow-hidden -z-10">
                <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-200/30 rounded-full blur-3xl"></div>
                <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-200/30 rounded-full blur-3xl"></div>
            </div>
            <Card className="w-full max-w-xl shadow-xl">
                <CardHeader>
                    <CardTitle className="text-xl">绑定身份</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* 学校选择 */}
                    <div>
                        <Label>选择学校</Label>
                        <Select onValueChange={setSelectedTenantId}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="请选择学校"/>
                            </SelectTrigger>
                            <SelectContent>
                                {tenants.map((tenant) => (
                                    <SelectItem key={tenant.id} value={tenant.id.toString()}>
                                        {tenant.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* 身份选择 */}
                    <div>
                        <Label>身份类型</Label>
                        <Select onValueChange={(val) => {
                            setIdentitySelectValue(val as "student" | "teacher");
                            setIsParent(false);
                        }}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="请选择身份"/>
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="student">学生</SelectItem>
                                <SelectItem value="teacher">老师</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* 学号 */}
                    {identitySelectValue !== "teacher" && (
                        <div>
                            <Label>{"学号"}</Label>
                            <Input
                                placeholder={"请输入学号"}
                                value={extendInfo}
                                onChange={(e) => setExtendInfo(e.target.value)}
                            />
                        </div>
                    )}

                    {identitySelectValue === "student" && (
                        <div className="flex items-center space-x-2">
                            <Checkbox id="isParent" checked={isParent} onCheckedChange={(val) => setIsParent(!!val)}/>
                            <Label htmlFor="isParent">我是该学生的家长</Label>
                        </div>
                    )}

                    {/* 提交按钮 */}
                    <div className="flex gap-4 pt-4">
                        <Button
                            variant="outline"
                            className="flex-1"

                            onClick={() => navigate(-1)}
                        >
                            取消
                        </Button>

                        <Button
                            className="flex-1"
                            disabled={
                                !selectedTenantId ||
                                !identitySelectValue ||
                                (identitySelectValue === "student" && !extendInfo.trim())
                            }
                            onClick={handleBind}
                        >
                            申请绑定
                        </Button>

                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default IdentityBindingPage;
