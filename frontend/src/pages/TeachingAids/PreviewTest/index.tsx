import {useParams} from "react-router-dom";
import {useEffect, useState} from "react";
import {AlertDialog, AlertDialogDescription} from "@/components/ui/alert-dialog.tsx";
import {AlertCircle} from "lucide-react";
import {Skeleton} from "@/components/ui/skeleton.tsx";

const BookPreviewPage = () => {
    const {id} = useParams<{ id: string }>();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const loadBook = async () => {
        try {
            setLoading(true)
            // todo 加载目录、加载第一章节
            setError(null)
        } catch (e) {
            setError(e as string)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        if (!id) {
            setError('书本ID缺失');
            setLoading(false);
            return;
        }

        loadBook()

        // const fetchData = async () => {
        //     try {
        //         setLoading(true);
        //         const [aidData, chaptersData] = await Promise.all([
        //             teachingAidApi.getTeachingAid(id),
        //             chapterApi.getChapters(id),
        //         ]);
        //         setTeachingAid(aidData);
        //         setChapters(chaptersData);
        //         setError(null);
        //     } catch (err) {
        //         setError('Failed to load teaching aid data.');
        //         console.error('Error fetching data:', err);
        //     } finally {
        //         setLoading(false);
        //     }
        // };
        //
        // fetchData();

    }, [id]);

    if (loading) {
        return (
            <div className="p-6 space-y-4">
                <Skeleton className="h-8 w-1/2"/>
                <Skeleton className="h-4 w-1/3"/>
                <div className="flex gap-4 h-[70vh]">
                    <Skeleton className="w-1/4 rounded-lg"/>
                    <Skeleton className="w-3/4 rounded-lg"/>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-6">
                <AlertDialog defaultOpen>
                    <div className='flex items-center gap-2'>
                        <AlertCircle className="h-4 w-4"/>
                        <AlertDialogDescription>{error}</AlertDialogDescription>
                    </div>
                </AlertDialog>
            </div>
        );
    }

    return (
        <div>
            <h1>BookPreviewPage Component</h1>
            {/*    todo 双栏，左侧目录，右侧当前选中章节*/}
        </div>
    );
};

export default BookPreviewPage;
