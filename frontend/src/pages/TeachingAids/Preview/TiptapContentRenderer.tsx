import PureMathRenderer from '@/components/math/PureMathRenderer';
import React, { Fragment } from 'react';
import { Node } from './tiptap-types';
import QuestionNodeComponent from './QuestionNodeComponent';
import QuestionCardComponent from './QuestionCardComponent';

interface TiptapContentRendererProps {
  content: {
    type: 'doc';
    content: Node[];
  };
}

// 递归渲染函数
export const renderNode = (node: Node, index: number): React.JSX.Element => {
  const key = `node-${index}`;

  // 1. 处理文本节点
  if (node.type === 'text' && node.text) {
    let textElement: React.ReactNode = node.text;

    // 处理标记 (bold, italic, etc.)
    if (node.marks) {
      node.marks.forEach((mark) => {
        switch (mark.type) {
          case 'bold':
            textElement = <strong>{textElement}</strong>;
            break;
          case 'italic':
            textElement = <em>{textElement}</em>;
            break;
          case 'underline':
            textElement = <u>{textElement}</u>;
            break;
          case 'strike':
            textElement = <s>{textElement}</s>;
            break;
          case 'link':
            textElement = (
              <a href={mark.attrs?.href} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                {textElement}
              </a>
            );
            break;
          default:
            break;
        }
      });
    }
    
    // For text nodes, we return a Fragment containing the potentially styled text.
    // This ensures that every rendered node from the map has a key.
    return <Fragment key={key}>{textElement}</Fragment>;
  }

  // 2. 处理其他类型的节点 (段落, 标题, 图片等)
  let children: any[] = [];
  if (node.type !== 'public_resource-node') {
    children = node.content ? node.content.map(renderNode) : [];
  }

  switch (node.type) {
    case 'paragraph':
      return <p key={key} className="mb-4 leading-relaxed">{children}</p>;

    case 'heading': {
      const level = node.attrs?.level || 1;
      const headingClasses: Record<number, string> = {
        1: 'text-4xl font-bold mb-6 mt-8',
        2: 'text-3xl font-semibold mb-5 mt-7',
        3: 'text-2xl font-semibold mb-4 mt-6',
        4: 'text-xl font-semibold mb-3 mt-5',
        5: 'text-lg font-semibold mb-2 mt-4',
        6: 'text-base font-semibold mb-2 mt-3',
      };
      const Tag = `h${level}` as keyof React.JSX.IntrinsicElements;
      return <Tag key={key} className={headingClasses[level]}>{children}</Tag>;
    }

    case 'image':
    case 'resizable-image':
      return (
        <img
          key={key}
          src={node.attrs?.src}
          alt={node.attrs?.alt || ''}
          title={node.attrs?.title || ''}
          className="my-6 rounded-lg shadow-md max-w-full h-auto mx-auto"
          style={{
            height: node.attrs?.height || 100,
            width: node.attrs?.width || 100,
          }}
        />
      );

    case 'bulletList':
      return <ul key={key} className="list-disc list-outside mb-4 pl-8">{children}</ul>;

    case 'orderedList':
      return <ol key={key} className="list-decimal list-outside mb-4 pl-8">{children}</ol>;

    case 'listItem':
      return <li key={key}>{children}</li>;

    case 'blockquote':
      return (
        <blockquote key={key} className="border-l-4 border-gray-300 pl-4 py-2 my-4 italic text-gray-600">
          {children}
        </blockquote>
      );

    case 'horizontalRule':
      return <hr key={key} className="my-8 border-gray-300" />;

    case '-mathml':
      return <PureMathRenderer key={key} content={`$${node.attrs?.mathml || ''}$`} />;

    case 'public_resource-card':
    case 'answer-sheet':
      return <QuestionCardComponent key={key} node={node} />;

    case 'public_resource-node':
      return <QuestionNodeComponent key={key} node={node}>{children}</QuestionNodeComponent>;

    default:
      // 对于未明确处理的节点，渲染其子节点，避免内容丢失
      return <Fragment key={key}>{children}</Fragment>;
  }
};

const TiptapContentRenderer: React.FC<TiptapContentRendererProps> = ({ content }) => {
  if (!content || !content.content) {
    return null;
  }

  return (
    <div className="prose prose-lg max-w-none">
      {content.content.map(renderNode)}
    </div>
  );
};

export default TiptapContentRenderer;
