import React from 'react';
import Mathdown from "@/components/question-card/mathdown/Mathdown.tsx";
import {Card, CardContent} from "@/components/ui/card";
import {useBasket} from "@/contexts/QuestionBasketContext.tsx";

interface Question {
    id?: string;
    questionId: number;
    material?: string | null;
    stem?: string | null;
    choices?: string[] | null;
    subQuestions?: SubQuestion[] | null;
}

interface SubQuestion {
    id?: string;
    question_id: number;
    material?: string | null;
    stem?: string | null;
    choices?: string[] | null;
    sub_questions?: SubQuestion[] | null;
}

// The component that receives the Tiptap node needs a prop type.
interface QuestionNodeProps {
    node: {
        attrs: Question;
    };
    children: React.ReactNode;
}

/**
 * A component to display a single public_resource
 * @param question The public_resource object to render.
 * @param index Optional index for sub-questions to display a number like (1), (2).
 */
const QuestionDisplay: React.FC<{ question: Question; index?: number }> = ({question, index}) => {

    const hasSubQuestions = question.subQuestions && question.subQuestions.length > 0;
    const hasRenderableContent = question.material || question.stem || (question.choices && question.choices.length > 0) || hasSubQuestions;

    if (!hasRenderableContent) {
        return null;
    }

    return (
        <div className="my-4">

            {/* Display index for sub-questions */}
            {index !== undefined && (
                <h4 className="font-semibold mb-2">{`(${index + 1})`}</h4>
            )}

            {/* Render material if it exists */}
            {question.material && <Mathdown content={question.material}/>}

            {/* Render stem if it exists */}
            {question.stem && <Mathdown content={question.stem}/>}

            {/* Render choices if they exist */}
            {question.choices && question.choices.length > 0 && (
                <div className="my-2">
                    {question.choices.map((choice, choiceIndex) => (
                        <div key={choiceIndex} className="flex items-start mb-2">
                            <span className="font-semibold mr-2">{String.fromCharCode(65 + choiceIndex)}.</span>
                            <Mathdown content={choice}/>
                        </div>
                    ))}
                </div>
            )}

            {/* Recursively render sub-questions if they exist */}
            {hasSubQuestions && (
                <div className="mt-4 pl-6 border-l-2 border-slate-200">
                    {question.subQuestions?.map((sub, subIndex) => (
                        <SubQuestionDisplay key={sub.question_id} question={sub} index={subIndex}/>
                    ))}
                </div>
            )}
        </div>
    );
};

/**
 * A component to display a single sub-public_resource
 * @param question The public_resource object to render.
 * @param index Optional index for sub-questions to display a number like (1), (2).
 */
const SubQuestionDisplay: React.FC<{ question: SubQuestion; index?: number }> = ({question, index}) => {
    const hasSubQuestions = question.sub_questions && question.sub_questions.length > 0;
    const hasRenderableContent = question.material || question.stem || (question.choices && question.choices.length > 0) || hasSubQuestions;

    if (!hasRenderableContent) {
        return null;
    }

    return (
        <div className="my-4">
            {/* Display index for sub-questions */}
            {index !== undefined && (
                <h4 className="font-semibold mb-2">{`(${index + 1})`}</h4>
            )}

            {/* Render material if it exists */}
            {question.material && <Mathdown content={question.material}/>}

            {/* Render stem if it exists */}
            {question.stem && <Mathdown content={question.stem}/>}

            {/* Render choices if they exist */}
            {question.choices && question.choices.length > 0 && (
                <div className="my-2">
                    {question.choices.map((choice, choiceIndex) => (
                        <div key={choiceIndex} className="flex items-start mb-2">
                            <span className="font-semibold mr-2">{String.fromCharCode(65 + choiceIndex)}.</span>
                            <Mathdown content={choice}/>
                        </div>
                    ))}
                </div>
            )}

            {/* Recursively render sub-questions if they exist */}
            {hasSubQuestions && (
                <div className="mt-4 pl-6 border-l-2 border-slate-200">
                    {question.sub_questions?.map((sub, subIndex) => (
                        <SubQuestionDisplay key={sub.question_id} question={sub} index={subIndex}/>
                    ))}
                </div>
            )}
        </div>
    );
};

/**
 * The main component that integrates with Tiptap.
 * It wraps the public_resource preview in a styled Card.
 */
const QuestionNodeComponent: React.FC<QuestionNodeProps> = ({node, children}) => {
    // console.log('QuestionNodeComponent', node, children);
    let {AddRemoveButton} = useBasket()
    return (
        <>
            <>{children}</>
            <Card className="my-4 bg-slate-50 group border-gray-300 relative">
                <div className="flex justify-between items-start">
                    <CardContent className="overflow-x-auto flex-1">
                        <QuestionDisplay question={node.attrs}/>
                    </CardContent>
                    {node.question_content && (
                        <div className="ml-2 mt-2 flex-shrink-0">
                            <AddRemoveButton question={node}/>
                        </div>
                    )}
                </div>
            </Card>
        </>
    );
};

export default QuestionNodeComponent;