// Defines the interfaces for Tiptap nodes and marks to be shared across components.

import {QuestionItemData} from "@/components/question-card/store/paperDataStore.ts";

export interface Mark {
  type: string;
  attrs?: Record<string, any>;
}

export interface Node {
  type: string;
  attrs?: Record<string, any>;
  content?: Node[];
  question_content?:QuestionItemData[];
  text?: string;
  marks?: Mark[];
}
