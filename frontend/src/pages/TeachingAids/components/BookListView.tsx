import {FC, useEffect, useState} from "react";
import {
    BookQueryParams,
    BookUpdatePayload,
    DEFAULT_BOOK_QUERY,
    GradeLevelSummary,
    SubjectSummary, TeachingAid,
    teachingAidApi
} from "@/types";
import BookListFilters from "@/pages/TeachingAids/components/BookListFilters.tsx";
import {toast} from "sonner";
import {Book} from "@/types/teachingAid.ts";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import Pagination from "@/components/Pagination.tsx";
import BookCardView from "@/pages/TeachingAids/components/BookCardView.tsx";
import {Skeleton} from "@/components/ui/skeleton.tsx";
import {Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle} from "@/components/ui/dialog.tsx";
import {Label} from "@/components/ui/label.tsx";
import {Input} from "@/components/ui/input.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";
import {Button} from "@/components/ui/button.tsx";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip.tsx";
import {HelpCircle} from "lucide-react";
import {useNavigate} from "react-router-dom";

type BookListViewProps = {
    subjectOptions: SubjectSummary[],
    gradeLevelOptions: GradeLevelSummary[],
};

const BookListView: FC<BookListViewProps> = ({subjectOptions, gradeLevelOptions}) => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [books, setBooks] = useState<Book[]>([])
    const [bookQueryParams, setBookQueryParams] = useState<BookQueryParams>(DEFAULT_BOOK_QUERY)
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });

    const loadBooks = async (params?: Partial<BookQueryParams>) => {
        try {
            setLoading(true)
            const finalParams = {...bookQueryParams, ...params};
            let response = await teachingAidApi.getTeachingAidsTest({...finalParams})
            if (response.success && response.data) {
                setBooks(response.data);
                setPagination({
                    current: response.pagination.page,
                    pageSize: response.pagination.page_size,
                    total: response.pagination.total,
                    totalPages: response.pagination.total_pages,
                });
            }
            setBookQueryParams(finalParams)
        } catch (error) {
            console.error('Failed to load papers:', error);
            toast.error('加载试卷列表失败');
        } finally {
            setLoading(false);
        }
    }

    const handleFilterChange = (key: keyof BookQueryParams, value: any) => {
        const newParams = {...bookQueryParams, [key]: value, page: 1};
        loadBooks(newParams);
    };

    const handleClearFilters = () => {
        loadBooks({
            search: '',
            subject_code: '',
            grade_level_code: '',
        })
    }

    const handlePaginationChange = async (newPage: number, pageSize: number) => {
        loadBooks({page: newPage, page_size: pageSize});
    };

    const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
    const [updateBook, setUpdateBook] = useState<BookUpdatePayload | null>(null)

    const onEdit = (book: Book) => {
        let updateForm: BookUpdatePayload = {
            id: book.id,
            title: book.title,
            authors: book.authors?.join('，') || '',
            subject_code: book.subjectCode || '',
            grade_level_code: book.gradeLevelCode || '',
        }
        setUpdateBook(updateForm)
        setIsUpdateDialogOpen(true)
    };

    const handleUpdateTeachingAid = async () => {
        if (updateBook === null) {
            return
        }
        await teachingAidApi.updateTeachingAidTest(updateBook);
        toast.success('更新成功')
        setIsUpdateDialogOpen(false)
        loadBooks()
    }

    const openPreviewPage = (id:string) => {
        navigate(`/teaching-aids/book/${id}`);
    };

    useEffect(() => {
        loadBooks()
    }, []);

    return (
        <div>
            <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>更新教辅</DialogTitle>
                        <DialogDescription>
                            更新教辅基本信息
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="create-title">标题</Label>
                                <Input
                                    id="create-title"
                                    value={updateBook?.title}
                                    onChange={(e) => setUpdateBook({...updateBook!, title: e.target.value})}
                                    placeholder="教辅标题"
                                />
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-1 col-span-1 mt-2">
                                    <Label>作者</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <HelpCircle className="w-4 h-4 text-muted-foreground cursor-pointer"/>
                                            </TooltipTrigger>
                                            <TooltipContent side="top">
                                                <p>多作者请用中文逗号分割</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Input
                                    id="create-title"
                                    value={updateBook?.authors}
                                    onChange={(e) => setUpdateBook({...updateBook!, authors: e.target.value})}
                                    placeholder="作者"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="create-subject">学科</Label>
                                <Select value={updateBook?.subject_code}
                                        onValueChange={(value) => setUpdateBook({...updateBook!, subject_code: value})}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="选择学科"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {subjectOptions.filter(s => s.is_active).map((subject) => (
                                            <SelectItem key={subject.code} value={subject.code}>
                                                {subject.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="create-grade">年级</Label>
                                <Select value={updateBook?.grade_level_code} onValueChange={(value) => setUpdateBook({
                                    ...updateBook!,
                                    grade_level_code: value
                                })}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="选择年级"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {gradeLevelOptions.filter(g => g.is_active).map((grade_level) => (
                                            <SelectItem key={grade_level.code} value={grade_level.code}>
                                                {grade_level.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
                            取消
                        </Button>
                        <Button onClick={handleUpdateTeachingAid}>
                            更新
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>


            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <BookListFilters
                        params={bookQueryParams}
                        onFilterChange={handleFilterChange}
                        clearFilters={handleClearFilters}
                        subjectOptions={subjectOptions}
                        gradeLevelOptions={gradeLevelOptions}
                    />
                </div>
            </div>

            <div className="transition-all duration-300 ease-in-out mt-2">
                <Card className="flex flex-col flex-grow">
                    <CardHeader className="flex flex-row items-center justify-between">
                        <div className="flex items-center gap-4">
                            <CardTitle>书本列表</CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent className="flex-grow min-h-[200px]">
                        <div className="h-full">
                            {loading ?
                                <div className="grid grid-cols-4 gap-12">
                                    <Skeleton className="h-96"/>
                                    <Skeleton className="h-96"/>
                                    <Skeleton className="h-96"/>
                                    <Skeleton className="h-96"/>
                                </div> :
                                <BookCardView
                                    books={books}
                                    subjectOptions={subjectOptions}
                                    gradeLevelOptions={gradeLevelOptions}
                                    onEdit={onEdit}
                                    onPreview={openPreviewPage}
                                />
                            }

                            <div className="flex justify-center">
                                <Pagination
                                    total={pagination.total}
                                    current={pagination.current}
                                    pageSize={pagination.pageSize}
                                    onChange={handlePaginationChange}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default BookListView;
