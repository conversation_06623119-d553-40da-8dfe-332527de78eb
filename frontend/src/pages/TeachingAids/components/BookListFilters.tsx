import {BookQueryParams} from "@/services/teachingAidsApi.ts";
import {FC, useState} from "react";
import {Filter, Search, X} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";
import {Label} from "@/components/ui/label.tsx";
import {Input} from "@/components/ui/input.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";
import {GradeLevelSummary, SubjectSummary} from "@/types";

interface BookListFiltersProps {
    params: BookQueryParams;
    onFilterChange: (key: keyof BookQueryParams, value: any) => void;
    clearFilters: () => void;
    subjectOptions:SubjectSummary[];
    gradeLevelOptions:GradeLevelSummary[];
}

const BookListFilters:FC<BookListFiltersProps> = ({params, onFilterChange, clearFilters,subjectOptions,gradeLevelOptions}) => {
    const hasActiveFilters = params.search;
    const [searchInput, setSearchInput] = useState<string>(params.search??'')

    return (
        <div className="space-y-6">
            {/* 筛选器头部 */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg">
                        <Filter className="h-4 w-4 text-white"/>
                    </div>
                    <div>
                        <h3 className="font-semibold text-slate-900 dark:text-white">高级筛选</h3>
                        <p className="text-sm text-slate-600 dark:text-slate-400">精确筛选您需要的租户</p>
                    </div>
                </div>
                {hasActiveFilters && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={clearFilters}
                        className="h-9 px-4 bg-white/50 hover:bg-white border-slate-200 hover:border-slate-300"
                    >
                        <X className="h-4 w-4 mr-2"/>
                        清除筛选
                    </Button>
                )}
            </div>

            {/* 筛选器内容 */}
            <div className="grid gap-6 md:grid-cols-3">

                {/* 搜索框 */}
                <div className="space-y-1">
                    <Label
                        htmlFor="search"
                        className="text-sm font-medium text-slate-700 dark:text-slate-300"
                    >
                        关键词搜索
                    </Label>
                    <div className="relative flex">
                        <Search
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 group-focus-within:text-indigo-500 transition-colors"
                        />
                        <Input
                            id="search"
                            placeholder="搜索书本名称、代码..."
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            className="pl-10 pr-20 bg-white/70 border-slate-200 focus:bg-white focus:border-indigo-300 focus:ring-indigo-200 transition-all flex-1"
                        />
                        <Button
                            onClick={() => onFilterChange("search", searchInput)}
                            className="absolute right-1 h-[calc(100%_-_4px)] px-3 mt-0.5"
                        >
                            搜索
                        </Button>
                    </div>
                </div>

                <div className="space-y-2">
                    <Label>学科</Label>
                    <Select value={params.subject_code} onValueChange={(value)=>onFilterChange("subject_code", value)}>
                        <SelectTrigger>
                            <SelectValue placeholder="全部学科" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="allSubject">全部学科</SelectItem>
                            {subjectOptions.filter(s => s.is_active).map((subject) => (
                                <SelectItem key={subject.code} value={subject.code}>
                                    {subject.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="space-y-2">
                    <Label>年级</Label>
                    <Select value={params.grade_level_code} onValueChange={(value)=>onFilterChange("grade_level_code", value)}>
                        <SelectTrigger>
                            <SelectValue placeholder="全部年级" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="allGrade">全部年级</SelectItem>
                            {gradeLevelOptions.filter(g => g.is_active).map((grade_level) => (
                                <SelectItem key={grade_level.code} value={grade_level.code }>
                                    {grade_level.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {/*<div className="space-y-2">*/}
                {/*    <Label>状态</Label>*/}
                {/*    <Select value={selectedStatus} onValueChange={setSelectedStatus}>*/}
                {/*        <SelectTrigger>*/}
                {/*            <SelectValue placeholder="全部状态"/>*/}
                {/*        </SelectTrigger>*/}
                {/*        <SelectContent>*/}
                {/*            <SelectItem value="allStatus">全部状态</SelectItem>*/}
                {/*            <SelectItem value="draft">草稿</SelectItem>*/}
                {/*            <SelectItem value="published">已发布</SelectItem>*/}
                {/*            <SelectItem value="archived">已归档</SelectItem>*/}
                {/*        </SelectContent>*/}
                {/*    </Select>*/}
                {/*</div>*/}
            </div>

        </div>
    );
};

export default BookListFilters;
