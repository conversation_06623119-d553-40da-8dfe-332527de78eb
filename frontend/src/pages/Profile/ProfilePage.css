/* Profile Page Styles */
.profile-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dark .profile-page {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
}

/* Grid Pattern Background */
.bg-grid-pattern {
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .bg-grid-pattern {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}

/* Card Hover Effects */
.profile-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: perspective(1000px) rotateX(0deg);
}

.profile-card:hover {
  transform: perspective(1000px) rotateX(-2deg) translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Status Indicator Animation */
.status-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Function Grid Responsive */
@media (max-width: 768px) {
  .function-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Smooth Transitions */
.smooth-transition {
  transition: all 0.3s ease-in-out;
}

/* Card Glassmorphism Effect */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.dark .glass-card {
  background: rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Enhanced Shadows */
.enhanced-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
              0 2px 4px -1px rgba(0, 0, 0, 0.06),
              0 0 0 1px rgba(0, 0, 0, 0.05);
}

.dark .enhanced-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 
              0 2px 4px -1px rgba(0, 0, 0, 0.2),
              0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Progress Bar Enhancement */
.enhanced-progress {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(59, 130, 246, 0.2) 50%, 
    rgba(59, 130, 246, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Typography */
@media (max-width: 640px) {
  .responsive-title {
    font-size: 1.5rem;
  }
}

@media (min-width: 640px) {
  .responsive-title {
    font-size: 1.875rem;
  }
}

/* Quick Action Button Enhancement */
.quick-action-btn {
  position: relative;
  overflow: hidden;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quick-action-btn:hover::before {
  left: 100%;
}

/* Achievement Badge Animation */
.achievement-badge {
  transition: transform 0.3s ease;
}

.achievement-badge:hover {
  transform: scale(1.05) rotate(5deg);
}