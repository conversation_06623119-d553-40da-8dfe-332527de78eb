import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Settings,
  Palette,
  Bell,
  Globe,
  Monitor,
  Sun,
  Moon,
  Smartphone,
  Volume2,
  VolumeX,
  Lock,
} from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { ChangePasswordCard } from './ChangePasswordCard';

const languageOptions = [
  { value: 'zh-CN', label: '简体中文', flag: '🇨🇳' },
  { value: 'en-US', label: 'English', flag: '🇺🇸' },
  { value: 'zh-TW', label: '繁體中文', flag: '🇹🇼' },
];

export const SystemPreferencesCard: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const [notifications, setNotifications] = useState(true);
  const [sounds, setSounds] = useState(true);
  const [language, setLanguage] = useState('zh-CN');

  const themeOptions = [
    { value: 'light', label: '浅色主题', icon: Sun },
    { value: 'dark', label: '深色主题', icon: Moon },
    { value: 'system', label: '跟随系统', icon: Monitor },
  ];

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>系统设置</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Theme Settings */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Palette className="h-4 w-4 text-purple-600" />
            <Label className="text-sm font-medium">主题模式</Label>
          </div>
          <RadioGroup
            value={theme}
            onValueChange={(value) => setTheme(value as 'light' | 'dark' | 'system')}
            className="grid grid-cols-1 gap-2"
          >
            {themeOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <RadioGroupItem value={option.value} id={option.value} />
                <Label htmlFor={option.value} className="flex items-center space-x-2 cursor-pointer flex-1">
                  <option.icon className="h-4 w-4" />
                  <span className="text-sm">{option.label}</span>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <Separator />

        {/* Language Settings */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Globe className="h-4 w-4 text-blue-600" />
            <Label className="text-sm font-medium">界面语言</Label>
          </div>
          <RadioGroup
            value={language}
            onValueChange={setLanguage}
            className="grid grid-cols-1 gap-2"
          >
            {languageOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <RadioGroupItem value={option.value} id={option.value} />
                <Label htmlFor={option.value} className="flex items-center space-x-2 cursor-pointer flex-1">
                  <span className="text-lg">{option.flag}</span>
                  <span className="text-sm">{option.label}</span>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <Separator />

        {/* Notification Settings */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Bell className="h-4 w-4 text-green-600" />
            <Label className="text-sm font-medium">通知设置</Label>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
              <div className="flex items-center space-x-2">
                <Bell className="h-4 w-4 text-gray-500" />
                <span className="text-sm">推送通知</span>
              </div>
              <Switch
                checked={notifications}
                onCheckedChange={setNotifications}
              />
            </div>
            
            <div className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
              <div className="flex items-center space-x-2">
                {sounds ? (
                  <Volume2 className="h-4 w-4 text-gray-500" />
                ) : (
                  <VolumeX className="h-4 w-4 text-gray-500" />
                )}
                <span className="text-sm">通知声音</span>
              </div>
              <Switch
                checked={sounds}
                onCheckedChange={setSounds}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Security Actions */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Lock className="h-4 w-4 text-red-600" />
            <Label className="text-sm font-medium">账户安全</Label>
          </div>
          <div className="space-y-2">
            <ChangePasswordCard />
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Smartphone className="h-4 w-4 mr-2" />
              移动端同步
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Settings className="h-4 w-4 mr-2" />
              高级设置
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};