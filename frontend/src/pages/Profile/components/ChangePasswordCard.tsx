import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Lock,
  Eye,
  EyeOff,
  Shield,
  CheckCircle,
  AlertCircle,
  Smartphone,
  Clock,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { changePassword, sendVerificationCode } from '@/services/authApi';
import { toast } from 'sonner';

interface PasswordStrength {
  score: number;
  feedback: string[];
  level: 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';
}

interface ChangePasswordCardProps {
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  trigger?: React.ReactNode;
}

export const ChangePasswordCard: React.FC<ChangePasswordCardProps> = ({ 
  className, 
  open, 
  onOpenChange, 
  trigger 
}) => {
  const { currentUserData, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    verificationCode: '',
  });
  
  const [useVerification, setUseVerification] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 密码强度检查
  const checkPasswordStrength = (password: string): PasswordStrength => {
    let score = 0;
    const feedback: string[] = [];
    
    if (password.length >= 8) score += 1;
    else feedback.push('至少8个字符');
    
    if (password.length >= 12) score += 1;
    else if (password.length >= 8) feedback.push('建议使用更长的密码');
    
    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('包含小写字母');
    
    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('包含大写字母');
    
    if (/\d/.test(password)) score += 1;
    else feedback.push('包含数字');
    
    if (/[@$!%*?&]/.test(password)) score += 1;
    else feedback.push('包含特殊字符 (@$!%*?&)');
    
    let level: PasswordStrength['level'] = 'weak';
    if (score >= 5) level = 'very-strong';
    else if (score >= 4) level = 'strong';
    else if (score >= 3) level = 'good';
    else if (score >= 2) level = 'fair';
    
    return { score, feedback, level };
  };

  const passwordStrength = checkPasswordStrength(formData.newPassword);

  const getStrengthColor = (level: string) => {
    switch (level) {
      case 'very-strong': return 'text-green-600 bg-green-100';
      case 'strong': return 'text-blue-600 bg-blue-100';
      case 'good': return 'text-yellow-600 bg-yellow-100';
      case 'fair': return 'text-orange-600 bg-orange-100';
      default: return 'text-red-600 bg-red-100';
    }
  };

  const getStrengthText = (level: string) => {
    switch (level) {
      case 'very-strong': return '非常强';
      case 'strong': return '强';
      case 'good': return '良好';
      case 'fair': return '一般';
      default: return '弱';
    }
  };

  const handleSendVerificationCode = async () => {
    if (!currentUserData?.phone_number) {
      toast.error('未找到手机号码');
      return;
    }

    try {
      setLoading(true);
      await sendVerificationCode({
        phone_number: currentUserData.phone_number,
        code_type: 'password_change',
      });
      
      setVerificationSent(true);
      setCountdown(60);
      
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      toast.success('验证码已发送');
    } catch (error: any) {
      toast.error(error.response?.data?.message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.currentPassword) {
      newErrors.currentPassword = '请输入当前密码';
    }
    
    if (!formData.newPassword) {
      newErrors.newPassword = '请输入新密码';
    } else if (passwordStrength.level === 'weak') {
      newErrors.newPassword = '密码强度太弱，请选择更强的密码';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }
    
    if (formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = '新密码不能与当前密码相同';
    }
    
    if (useVerification && !formData.verificationCode) {
      newErrors.verificationCode = '请输入验证码';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    try {
      setLoading(true);
      
      const response = await changePassword({
        current_password: formData.currentPassword,
        new_password: formData.newPassword,
        verification_code: useVerification ? formData.verificationCode : undefined,
      });
      
      if (response.success) {
        toast.success('密码修改成功！即将重新登录...');
        
        // 延迟2秒后登出，让用户看到成功消息
        setTimeout(() => {
          logout();
        }, 2000);
        
        setIsOpen(false);
        resetForm();
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '密码修改失败';
      toast.error(errorMessage);
      
      // 如果是密码错误，清空当前密码字段
      if (errorMessage.includes('密码') || errorMessage.includes('credentials')) {
        setFormData(prev => ({ ...prev, currentPassword: '' }));
      }
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      verificationCode: '',
    });
    setUseVerification(false);
    setVerificationSent(false);
    setCountdown(0);
    setErrors({});
    setShowPasswords({
      current: false,
      new: false,
      confirm: false,
    });
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const dialogOpen = open !== undefined ? open : isOpen;
  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen);
    } else {
      setIsOpen(newOpen);
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {trigger && (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      )}
      {!trigger && (
        <DialogTrigger asChild>
          <div className="flex items-center cursor-pointer w-full">
            <Lock className="mr-2 h-4 w-4" />
            <span>修改密码</span>
          </div>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>修改密码</span>
          </DialogTitle>
          <DialogDescription>
            为了保护您的账户安全，请输入当前密码并设置新密码
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 当前密码 */}
          <div className="space-y-2">
            <Label htmlFor="currentPassword">当前密码</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showPasswords.current ? 'text' : 'password'}
                value={formData.currentPassword}
                onChange={(e) => setFormData(prev => ({ ...prev, currentPassword: e.target.value }))}
                placeholder="请输入当前密码"
                className={errors.currentPassword ? 'border-red-500' : ''}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('current')}
              >
                {showPasswords.current ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.currentPassword && (
              <p className="text-sm text-red-600">{errors.currentPassword}</p>
            )}
          </div>

          <Separator />

          {/* 新密码 */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">新密码</Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showPasswords.new ? 'text' : 'password'}
                value={formData.newPassword}
                onChange={(e) => setFormData(prev => ({ ...prev, newPassword: e.target.value }))}
                placeholder="请输入新密码"
                className={errors.newPassword ? 'border-red-500' : ''}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('new')}
              >
                {showPasswords.new ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-600">{errors.newPassword}</p>
            )}
            
            {/* 密码强度指示器 */}
            {formData.newPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">密码强度</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStrengthColor(passwordStrength.level)}`}>
                    {getStrengthText(passwordStrength.level)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      passwordStrength.level === 'very-strong' ? 'bg-green-500' :
                      passwordStrength.level === 'strong' ? 'bg-blue-500' :
                      passwordStrength.level === 'good' ? 'bg-yellow-500' :
                      passwordStrength.level === 'fair' ? 'bg-orange-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${(passwordStrength.score / 6) * 100}%` }}
                  />
                </div>
                {passwordStrength.feedback.length > 0 && (
                  <div className="text-xs text-gray-600">
                    建议: {passwordStrength.feedback.join('、')}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 确认新密码 */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">确认新密码</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showPasswords.confirm ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="请再次输入新密码"
                className={errors.confirmPassword ? 'border-red-500' : ''}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('confirm')}
              >
                {showPasswords.confirm ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword}</p>
            )}
            {formData.confirmPassword && formData.newPassword === formData.confirmPassword && (
              <div className="flex items-center space-x-1 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">密码匹配</span>
              </div>
            )}
          </div>

          <Separator />

          {/* 短信验证选项 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Smartphone className="h-4 w-4 text-blue-600" />
                <Label htmlFor="useVerification" className="text-sm font-medium">
                  启用短信验证 (推荐)
                </Label>
              </div>
              <Switch
                id="useVerification"
                checked={useVerification}
                onCheckedChange={setUseVerification}
              />
            </div>
            
            {useVerification && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Input
                    placeholder="请输入验证码"
                    value={formData.verificationCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, verificationCode: e.target.value }))}
                    className={errors.verificationCode ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleSendVerificationCode}
                    disabled={loading || countdown > 0}
                    className="whitespace-nowrap"
                  >
                    {countdown > 0 ? (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{countdown}s</span>
                      </div>
                    ) : (
                      '发送验证码'
                    )}
                  </Button>
                </div>
                {errors.verificationCode && (
                  <p className="text-sm text-red-600">{errors.verificationCode}</p>
                )}
                {verificationSent && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      验证码已发送至 {currentUserData?.phone_number?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </div>

          {/* 安全提示 */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              修改密码后，您需要重新登录。所有设备上的登录状态将被清除。
            </AlertDescription>
          </Alert>

          {/* 提交按钮 */}
          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || !formData.currentPassword || !formData.newPassword || !formData.confirmPassword}
              className="flex-1"
            >
              {loading ? '修改中...' : '确认修改'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
