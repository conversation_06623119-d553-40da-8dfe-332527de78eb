import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { User, School, ChevronDown, Settings, LogOut, Palette } from 'lucide-react';

interface IdentityOverviewCardProps {
  user: any;
  identity: any;
}

export const IdentityOverviewCard: React.FC<IdentityOverviewCardProps> = ({
  user,
  identity,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getRoleColor = (role: string) => {
    const colors = {
      'student': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'teacher': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'principal': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'admin': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    };
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Card className="profile-card relative overflow-hidden bg-gradient-to-r from-white to-blue-50 dark:from-gray-900 dark:to-blue-950 border-0 enhanced-shadow">
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      <CardContent className="p-4 sm:p-8">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-6 lg:space-y-0">
          {/* User Info Section */}
          <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
            <div className="relative">
              <Avatar className="h-16 w-16 sm:h-20 sm:w-20 ring-4 ring-white shadow-lg">
                <AvatarImage src={user?.avatar} alt={user?.username} />
                <AvatarFallback className="text-lg sm:text-xl font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                  {user?.username?.charAt(0)?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1 h-5 w-5 sm:h-6 sm:w-6 bg-green-400 rounded-full border-2 sm:border-3 border-white status-indicator" />
            </div>
            
            <div className="flex-1 text-center sm:text-left">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 mb-2">
                <h1 className="responsive-title font-bold text-gray-900 dark:text-white">
                  {identity?.display_name || user?.username}
                </h1>
                <Badge className={`${getRoleColor(identity?.role_name)} font-medium`}>
                  {identity?.role_display_name}
                </Badge>
              </div>
              
              <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-sm text-gray-600 dark:text-gray-300">
                <div className="flex items-center justify-center sm:justify-start space-x-1">
                  <School className="h-4 w-4" />
                  <span>{identity?.tenant_name}</span>
                </div>
                <Separator orientation="vertical" className="hidden sm:block h-4" />
                <div className="flex items-center justify-center sm:justify-start space-x-1">
                  <User className="h-4 w-4" />
                  <span>{user?.phone}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-center space-x-3">
            <Button variant="outline" size="sm" className="hidden sm:flex">
              <Settings className="h-4 w-4 mr-2" />
              账户设置
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  切换身份
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem>
                  <User className="h-4 w-4 mr-2" />
                  学生身份
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <User className="h-4 w-4 mr-2" />
                  教师身份
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Palette className="h-4 w-4 mr-2" />
                  主题设置
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="h-4 w-4 mr-2" />
                  退出登录
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};