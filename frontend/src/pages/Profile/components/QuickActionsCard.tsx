import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  FileText,
  Users,
  BookOpen,
  Clock,
  CheckCircle2,
  AlertCircle,
  TrendingUp,
} from 'lucide-react';

const quickActions = [
  {
    title: '待批改作业',
    count: 12,
    priority: 'high',
    icon: FileText,
    action: '立即批改',
    color: 'text-red-600',
    bgColor: 'bg-red-50 dark:bg-red-950',
  },
  {
    title: '今日课程',
    count: 3,
    priority: 'normal',
    icon: Calendar,
    action: '查看课表',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-950',
  },
  {
    title: '班级管理',
    count: 2,
    priority: 'normal',
    icon: Users,
    action: '管理班级',
    color: 'text-green-600',
    bgColor: 'bg-green-50 dark:bg-green-950',
  },
  {
    title: '系统通知',
    count: 5,
    priority: 'low',
    icon: AlertCircle,
    action: '查看详情',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-950',
  },
];

const recentTasks = [
  {
    title: '完成数学作业批改',
    time: '2小时前',
    status: 'completed',
    icon: CheckCircle2,
  },
  {
    title: '更新班级通知',
    time: '5小时前',
    status: 'completed',
    icon: CheckCircle2,
  },
  {
    title: '准备明日课件',
    time: '进行中',
    status: 'in-progress',
    icon: Clock,
  },
];

export const QuickActionsCard: React.FC = () => {
  const getPriorityBadge = (priority: string) => {
    const variants = {
      high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      normal: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      low: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    };
    return variants[priority as keyof typeof variants];
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>快捷操作</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {quickActions.map((action, index) => (
              <div key={index} className={`p-4 rounded-lg ${action.bgColor} border border-gray-200 dark:border-gray-700`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <action.icon className={`h-5 w-5 ${action.color}`} />
                    <span className="font-medium text-gray-900 dark:text-white">
                      {action.title}
                    </span>
                  </div>
                  <Badge className={getPriorityBadge(action.priority)}>
                    {action.count}
                  </Badge>
                </div>
                <Button size="sm" className="w-full" variant="outline">
                  {action.action}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Tasks */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>最近任务</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentTasks.map((task, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <task.icon className={`h-4 w-4 ${task.status === 'completed' ? 'text-green-500' : 'text-blue-500'}`} />
                <div className="flex-1">
                  <div className="font-medium text-sm text-gray-900 dark:text-white">
                    {task.title}
                  </div>
                  <div className="text-xs text-gray-500">{task.time}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};