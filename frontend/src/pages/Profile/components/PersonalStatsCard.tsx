import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  Users,
  BookOpen,
  Award,
  TrendingUp,
  Calendar,
  Clock,
} from 'lucide-react';

const statsData = [
  {
    title: '本周工作量',
    value: '85%',
    progress: 85,
    icon: BarChart3,
    description: '已完成 17/20 项任务',
    trend: '+12%',
    color: 'text-blue-600',
    progressColor: 'bg-blue-500',
  },
  {
    title: '学生互动',
    value: '156',
    progress: 78,
    icon: Users,
    description: '本月互动次数',
    trend: '+23%',
    color: 'text-green-600',
    progressColor: 'bg-green-500',
  },
  {
    title: '课程完成',
    value: '24',
    progress: 92,
    icon: BookOpen,
    description: '本学期已完成课程',
    trend: '+8%',
    color: 'text-purple-600',
    progressColor: 'bg-purple-500',
  },
];

const achievements = [
  {
    title: '优秀教师',
    description: '连续3个月评分超过4.8',
    icon: Award,
    earned: true,
  },
  {
    title: '高效批改',
    description: '作业批改及时率100%',
    icon: Clock,
    earned: true,
  },
  {
    title: '学生喜爱',
    description: '学生好评率95%以上',
    icon: Users,
    earned: false,
  },
];

export const PersonalStatsCard: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Statistics Overview */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-blue-950">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>个人统计</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {statsData.map((stat, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <stat.icon className={`h-4 w-4 ${stat.color}`} />
                    <span className="font-medium text-sm">{stat.title}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold">{stat.value}</span>
                    <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full dark:bg-green-900 dark:text-green-300">
                      {stat.trend}
                    </span>
                  </div>
                </div>
                <Progress value={stat.progress} className="h-2" />
                <p className="text-xs text-gray-500 dark:text-gray-400">{stat.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-purple-50 dark:from-gray-900 dark:to-purple-950">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>成就徽章</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {achievements.map((achievement, index) => (
              <div
                key={index}
                className={`flex items-center space-x-3 p-3 rounded-lg border-2 transition-all ${
                  achievement.earned
                    ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950'
                    : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                }`}
              >
                <achievement.icon
                  className={`h-5 w-5 ${
                    achievement.earned ? 'text-yellow-600' : 'text-gray-400'
                  }`}
                />
                <div className="flex-1">
                  <div className="font-medium text-sm">{achievement.title}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {achievement.description}
                  </div>
                </div>
                {achievement.earned && (
                  <div className="text-yellow-500">
                    <Award className="h-4 w-4" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};