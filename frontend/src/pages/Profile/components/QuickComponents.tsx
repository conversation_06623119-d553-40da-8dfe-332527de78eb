import React from 'react';

// Placeholder components for quick implementation
export const ProfileFunctionGrid: React.FC = () => (
  <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-lg">
    <h2 className="text-xl font-semibold mb-4">功能中心</h2>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
        <h3 className="font-medium">身份管理</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">管理多重身份和权限</p>
      </div>
      <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg">
        <h3 className="font-medium">账户设置</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">基础信息和安全设置</p>
      </div>
    </div>
  </div>
);

export const QuickActionsCard: React.FC = () => (
  <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-lg">
    <h2 className="text-lg font-semibold mb-4">快捷操作</h2>
    <div className="space-y-3">
      <button className="w-full p-3 bg-red-50 dark:bg-red-950 rounded-lg text-left">
        <span className="font-medium">待批改作业</span>
        <span className="float-right bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">12</span>
      </button>
      <button className="w-full p-3 bg-blue-50 dark:bg-blue-950 rounded-lg text-left">
        <span className="font-medium">今日课程</span>
        <span className="float-right bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">3</span>
      </button>
    </div>
  </div>
);

export const PersonalStatsCard: React.FC = () => (
  <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-lg">
    <h2 className="text-lg font-semibold mb-4">个人统计</h2>
    <div className="space-y-4">
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm">本周工作量</span>
          <span className="font-bold">85%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-blue-500 h-2 rounded-full" style={{width: '85%'}}></div>
        </div>
      </div>
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm">学生互动</span>
          <span className="font-bold">156</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-green-500 h-2 rounded-full" style={{width: '78%'}}></div>
        </div>
      </div>
    </div>
  </div>
);

export const RecentActivityCard: React.FC = () => (
  <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-lg">
    <h2 className="text-lg font-semibold mb-4">最近活动</h2>
    <div className="space-y-4">
      <div className="flex items-center space-x-3">
        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        <div>
          <p className="font-medium text-sm">完成数学作业批改</p>
          <p className="text-xs text-gray-500">2小时前</p>
        </div>
      </div>
      <div className="flex items-center space-x-3">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        <div>
          <p className="font-medium text-sm">更新班级通知</p>
          <p className="text-xs text-gray-500">5小时前</p>
        </div>
      </div>
    </div>
  </div>
);

export const SystemPreferencesCard: React.FC = () => (
  <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-lg">
    <h2 className="text-lg font-semibold mb-4">系统设置</h2>
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <span className="text-sm">推送通知</span>
        <button className="w-10 h-6 bg-blue-500 rounded-full relative">
          <div className="w-4 h-4 bg-white rounded-full absolute top-1 right-1"></div>
        </button>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-sm">深色主题</span>
        <button className="w-10 h-6 bg-gray-300 rounded-full relative">
          <div className="w-4 h-4 bg-white rounded-full absolute top-1 left-1"></div>
        </button>
      </div>
    </div>
  </div>
);