import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  User,
  Shield,
  BarChart3,
  Settings,
  HelpCircle,
  Users,
  BookOpen,
  Calendar,
  FileText,
  Bell,
  Palette,
  Key,
} from 'lucide-react';

const functionCategories = [
  {
    title: '身份管理',
    description: '管理多重身份和权限',
    icon: User,
    color: 'from-blue-500 to-blue-600',
    items: [
      { name: '身份绑定', icon: Users, description: '绑定新的教育身份' },
      { name: '角色切换', icon: User, description: '在不同身份间切换' },
      { name: '权限查看', icon: Shield, description: '查看当前权限范围' },
    ]
  },
  {
    title: '账户设置',
    description: '基础信息和安全设置',
    icon: Settings,
    color: 'from-green-500 to-green-600',
    items: [
      { name: '个人资料', icon: User, description: '编辑基本信息' },
      { name: '安全设置', icon: Key, description: '密码和安全选项' },
      { name: '通知偏好', icon: Bell, description: '通知提醒设置' },
    ]
  },
  {
    title: '我的工作',
    description: '角色相关的快捷操作',
    icon: BookOpen,
    color: 'from-purple-500 to-purple-600',
    items: [
      { name: '我的班级', icon: Users, description: '班级管理和信息' },
      { name: '课程表', icon: Calendar, description: '查看教学安排' },
      { name: '作业批改', icon: FileText, description: '批改待处理作业' },
    ]
  },
  {
    title: '数据统计',
    description: '个人相关数据分析',
    icon: BarChart3,
    color: 'from-orange-500 to-orange-600',
    items: [
      { name: '学习报告', icon: BarChart3, description: '个人学习统计' },
      { name: '教学分析', icon: BarChart3, description: '教学效果分析' },
      { name: '活动记录', icon: Calendar, description: '平台活动历史' },
    ]
  },
  {
    title: '系统偏好',
    description: '界面和交互设置',
    icon: Palette,
    color: 'from-pink-500 to-pink-600',
    items: [
      { name: '主题设置', icon: Palette, description: '深浅色主题切换' },
      { name: '语言设置', icon: Settings, description: '界面语言选择' },
      { name: '显示设置', icon: Settings, description: '页面显示偏好' },
    ]
  },
  {
    title: '帮助中心',
    description: '教程、反馈和文档',
    icon: HelpCircle,
    color: 'from-teal-500 to-teal-600',
    items: [
      { name: '使用教程', icon: BookOpen, description: '平台使用指南' },
      { name: '意见反馈', icon: FileText, description: '提交建议和问题' },
      { name: '联系支持', icon: HelpCircle, description: '技术支持联系' },
    ]
  },
];

export const ProfileFunctionGrid: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">功能中心</h2>
        <span className="text-sm text-gray-500">个性化功能面板</span>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {functionCategories.map((category, index) => (
          <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white shadow-md`}>
                  <category.icon className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-lg">{category.title}</CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{category.description}</p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-2">
                {category.items.map((item, itemIndex) => (
                  <Button
                    key={itemIndex}
                    variant="ghost"
                    className="w-full justify-start h-auto p-3 hover:bg-gray-100 dark:hover:bg-gray-700 group-hover:bg-gray-50 dark:group-hover:bg-gray-750"
                  >
                    <item.icon className="h-4 w-4 mr-3 text-gray-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium text-sm">{item.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{item.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};