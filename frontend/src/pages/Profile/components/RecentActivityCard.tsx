import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Activity,
  FileText,
  Users,
  Calendar,
  MessageCircle,
  BookOpen,
  Clock,
  MoreHorizontal,
} from 'lucide-react';

const recentActivities = [
  {
    id: 1,
    type: 'homework',
    title: '批改了三年级数学作业',
    description: '共批改32份作业，平均分85分',
    time: '2小时前',
    icon: FileText,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100 dark:bg-blue-900',
    status: 'completed',
  },
  {
    id: 2,
    type: 'class',
    title: '更新了班级公告',
    description: '发布了明日春游活动通知',
    time: '5小时前',
    icon: Users,
    color: 'text-green-600',
    bgColor: 'bg-green-100 dark:bg-green-900',
    status: 'published',
  },
  {
    id: 3,
    type: 'schedule',
    title: '安排了期中考试',
    description: '数学期中考试 - 下周三上午',
    time: '1天前',
    icon: Calendar,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100 dark:bg-purple-900',
    status: 'scheduled',
  },
  {
    id: 4,
    type: 'interaction',
    title: '回复了家长留言',
    description: '关于学生课堂表现的询问',
    time: '2天前',
    icon: MessageCircle,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100 dark:bg-orange-900',
    status: 'replied',
  },
  {
    id: 5,
    type: 'course',
    title: '完成了课程准备',
    description: '《分数的认识》教学课件制作',
    time: '3天前',
    icon: BookOpen,
    color: 'text-teal-600',
    bgColor: 'bg-teal-100 dark:bg-teal-900',
    status: 'completed',
  },
];

const getStatusBadge = (status: string) => {
  const statusConfig = {
    completed: { label: '已完成', className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' },
    published: { label: '已发布', className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' },
    scheduled: { label: '已安排', className: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' },
    replied: { label: '已回复', className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300' },
  };
  
  return statusConfig[status as keyof typeof statusConfig] || statusConfig.completed;
};

export const RecentActivityCard: React.FC = () => {
  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>最近活动</span>
          </CardTitle>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div className={`p-2 rounded-lg ${activity.bgColor} flex-shrink-0`}>
                <activity.icon className={`h-4 w-4 ${activity.color}`} />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-sm text-gray-900 dark:text-white truncate">
                    {activity.title}
                  </h4>
                  <Badge className={`ml-2 text-xs ${getStatusBadge(activity.status).className}`}>
                    {getStatusBadge(activity.status).label}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {activity.description}
                </p>
                
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {activity.time}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 text-center">
          <Button variant="outline" size="sm">
            查看更多活动
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};