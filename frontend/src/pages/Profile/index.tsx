import React from 'react';
import './ProfilePage.css';
import { useAuth } from '@/contexts/AuthContext';
import { IdentityOverviewCard } from './components/IdentityOverviewCard';
import { ProfileFunctionGrid } from './components/ProfileFunctionGrid';
import { QuickActionsCard } from './components/QuickActionsCard';
import { PersonalStatsCard } from './components/PersonalStatsCard';
import { RecentActivityCard } from './components/RecentActivityCard';
import { SystemPreferencesCard } from './components/SystemPreferencesCard';

const ProfilePage: React.FC = () => {
  const { user, identity } = useAuth();

  return (
    <div className="profile-page min-h-screen">
      <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-4 sm:space-y-6">
        {/* Header Section with Identity Overview */}
        <div className="relative">
          <IdentityOverviewCard user={user} identity={identity} />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
          {/* Left Column - Primary Functions */}
          <div className="xl:col-span-2 space-y-4 sm:space-y-6">
            <ProfileFunctionGrid />
            <RecentActivityCard />
          </div>

          {/* Right Column - Quick Actions & Stats */}
          <div className="space-y-4 sm:space-y-6">
            <QuickActionsCard />
            <PersonalStatsCard />
            <SystemPreferencesCard />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;