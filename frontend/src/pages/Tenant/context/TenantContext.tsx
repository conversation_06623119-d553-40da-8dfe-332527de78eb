import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { toast } from 'sonner';
import { createTenant, updateTenant, deleteTenant } from '@/services/tenantApi';
import { Tenant } from '@/types/tenant.ts';

// 状态类型定义
interface TenantState {
  tenants: Tenant[];
  filteredTenants: Tenant[];
  loading: boolean;
  error: string | null;
  selectedTenants: string[];
  filters: {
    search: string;
    status: string;
    type: string;
  };
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

// Action类型定义
type TenantAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TENANTS'; payload: Tenant[] }
  | { type: 'SET_FILTERED_TENANTS'; payload: Tenant[] }
  | { type: 'SET_SELECTED_TENANTS'; payload: string[] }
  | { type: 'SET_FILTERS'; payload: Partial<TenantState['filters']> }
  | { type: 'SET_PAGINATION'; payload: Partial<TenantState['pagination']> }
  | { type: 'ADD_TENANT'; payload: Tenant }
  | { type: 'UPDATE_TENANT'; payload: Tenant }
  | { type: 'REMOVE_TENANT'; payload: string };

// 初始状态
const initialState: TenantState = {
  tenants: [],
  filteredTenants: [],
  loading: false,
  error: null,
  selectedTenants: [],
  filters: {
    search: '',
    status: 'all',
    type: 'all',
  },
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
};

// Reducer函数
function tenantReducer(state: TenantState, action: TenantAction): TenantState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_TENANTS':
      return { ...state, tenants: action.payload };
    case 'SET_FILTERED_TENANTS':
      return { ...state, filteredTenants: action.payload };
    case 'SET_SELECTED_TENANTS':
      return { ...state, selectedTenants: action.payload };
    case 'SET_FILTERS':
      return { ...state, filters: { ...state.filters, ...action.payload } };
    case 'SET_PAGINATION':
      return { ...state, pagination: { ...state.pagination, ...action.payload } };
    case 'ADD_TENANT':
      return {
        ...state,
        tenants: [...state.tenants, action.payload],
        filteredTenants: [...state.filteredTenants, action.payload],
      };
    case 'UPDATE_TENANT':
      return {
        ...state,
        tenants: state.tenants.map(t => t.id === action.payload.id ? action.payload : t),
        filteredTenants: state.filteredTenants.map(t => t.id === action.payload.id ? action.payload : t),
      };
    case 'REMOVE_TENANT':
      return {
        ...state,
        tenants: state.tenants.filter(t => t.id !== action.payload),
        filteredTenants: state.filteredTenants.filter(t => t.id !== action.payload),
        selectedTenants: state.selectedTenants.filter(id => id !== action.payload),
      };
    default:
      return state;
  }
}

// Context类型定义
interface TenantContextType {
  state: TenantState;
  dispatch: React.Dispatch<TenantAction>;
  fetchTenants: () => Promise<void>;
  createTenantAction: (tenantData: Omit<Tenant, 'id'>) => Promise<void>;
  updateTenantAction: (id: string, tenantData: Partial<Tenant>) => Promise<void>;
  deleteTenantAction: (id: string) => Promise<void>;
  bulkDeleteTenants: (ids: string[]) => Promise<void>;
  applyFilters: () => void;
  clearFilters: () => void;
  selectTenant: (id: string) => void;
  selectAllTenants: () => void;
  clearSelection: () => void;
}

// 创建Context
const TenantContext = createContext<TenantContextType | undefined>(undefined);

// Provider组件
export const TenantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(tenantReducer, initialState);

  // 获取租户列表
  const fetchTenants = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });
    
    try {
        // let res = await getTenants();
      const tenants:Tenant[] = [];
      dispatch({ type: 'SET_TENANTS', payload: tenants });
      dispatch({ type: 'SET_FILTERED_TENANTS', payload: tenants });
      dispatch({ type: 'SET_PAGINATION', payload: { total: tenants.length } });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取租户列表失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // 创建租户
  const createTenantAction = async (tenantData: Omit<Tenant, 'id'>) => {
    try {
      const newTenant = await createTenant(tenantData);
      dispatch({ type: 'ADD_TENANT', payload: newTenant });
      toast.success('租户创建成功');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建租户失败';
      toast.error(errorMessage);
      throw error;
    }
  };

  // 更新租户
  const updateTenantAction = async (id: string, tenantData: Partial<Tenant>) => {
    try {
      const updatedTenant = await updateTenant(id, tenantData);
      dispatch({ type: 'UPDATE_TENANT', payload: updatedTenant });
      toast.success('租户更新成功');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '更新租户失败';
      toast.error(errorMessage);
      throw error;
    }
  };

  // 删除租户
  const deleteTenantAction = async (id: string) => {
    try {
      await deleteTenant(id);
      dispatch({ type: 'REMOVE_TENANT', payload: id });
      toast.success('租户删除成功');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除租户失败';
      toast.error(errorMessage);
      throw error;
    }
  };

  // 批量删除租户
  const bulkDeleteTenants = async (ids: string[]) => {
    try {
      await Promise.all(ids.map(id => deleteTenant(id)));
      ids.forEach(id => dispatch({ type: 'REMOVE_TENANT', payload: id }));
      toast.success(`成功删除 ${ids.length} 个租户`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '批量删除租户失败';
      toast.error(errorMessage);
      throw error;
    }
  };

  // 应用筛选
  const applyFilters = () => {
    let filtered = state.tenants;

    if (state.filters.search) {
      const searchLower = state.filters.search.toLowerCase();
      filtered = filtered.filter(tenant =>
        tenant.name.toLowerCase().includes(searchLower) ||
        tenant.schemaName.toLowerCase().includes(searchLower)
      );
    }

    if (state.filters.status && state.filters.status !== 'all') {
      filtered = filtered.filter(tenant => tenant.status === state.filters.status);
    }

    if (state.filters.type && state.filters.type !== 'all') {
      filtered = filtered.filter(tenant => tenant.tenantType === state.filters.type);
    }

    dispatch({ type: 'SET_FILTERED_TENANTS', payload: filtered });
    dispatch({ type: 'SET_PAGINATION', payload: { total: filtered.length, page: 1 } });
  };

  // 清除筛选
  const clearFilters = () => {
    dispatch({ type: 'SET_FILTERS', payload: { search: '', status: 'all', type: 'all' } });
    dispatch({ type: 'SET_FILTERED_TENANTS', payload: state.tenants });
    dispatch({ type: 'SET_PAGINATION', payload: { total: state.tenants.length, page: 1 } });
  };

  // 选择租户
  const selectTenant = (id: string) => {
    const newSelection = state.selectedTenants.includes(id)
      ? state.selectedTenants.filter(selectedId => selectedId !== id)
      : [...state.selectedTenants, id];
    dispatch({ type: 'SET_SELECTED_TENANTS', payload: newSelection });
  };

  // 全选租户
  const selectAllTenants = () => {
    const allIds = state.filteredTenants.map(tenant => tenant.id);
    dispatch({ type: 'SET_SELECTED_TENANTS', payload: allIds });
  };

  // 清除选择
  const clearSelection = () => {
    dispatch({ type: 'SET_SELECTED_TENANTS', payload: [] });
  };

  // 初始化加载
  useEffect(() => {
    fetchTenants();
  }, []);

  // 筛选器变化时重新应用筛选
  useEffect(() => {
    applyFilters();
  }, [state.filters]);

  const contextValue: TenantContextType = {
    state,
    dispatch,
    fetchTenants,
    createTenantAction,
    updateTenantAction,
    deleteTenantAction,
    bulkDeleteTenants,
    applyFilters,
    clearFilters,
    selectTenant,
    selectAllTenants,
    clearSelection,
  };

  return (
    <TenantContext.Provider value={contextValue}>
      {children}
    </TenantContext.Provider>
  );
};

// 自定义Hook
export const useTenant = () => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}; 