import {FC, useState} from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { TenantFormDialog } from './TenantFormDialog';
import {
  PlusCircle,
  MoreHorizontal,
  Trash2,
  Upload,
  Settings,
} from 'lucide-react';
import {Tenant} from "@/types";

interface TenantActionsProps {
    createTenantAction: (tenantData: Omit<Tenant, 'id'>) => Promise<void>;
    selectedTenants: string[];
    clearSelection: () => void;
    bulkDeleteTenants:(ids:string[])=>void;
}

export const TenantActions: FC<TenantActionsProps> = ({createTenantAction,selectedTenants,clearSelection,bulkDeleteTenants}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleBulkDelete = async () => {
    try {
      await bulkDeleteTenants(selectedTenants);
      setShowDeleteDialog(false);
      clearSelection();
    } catch (error) {
      // 错误已在 Context 中处理
    }
  };

  // const handleExport = () => {
  //   const csvContent = generateCSV(state.filteredTenants);
  //   downloadCSV(csvContent, 'tenants.csv');
  //   toast.success('租户数据导出成功');
  // };

  // const generateCSV = (tenants: Tenant[]) => {
  //   const headers = ['ID', '名称', '代码', 'Schema名称', '类型', '状态'];
  //   const rows = tenants.map(tenant => [
  //     tenant.id,
  //     tenant.name,
  //     tenant.schemaName.replace(tenant.schemaName.split('_')[0] + '_', ''),
  //     tenant.schemaName,
  //     tenant.tenantType,
  //     tenant.status === 'active' ? '启用' : '已删除'
  //   ]);
  //
  //   return [headers, ...rows]
  //     .map(row => row.map(cell => `"${cell}"`).join(','))
  //     .join('\n');
  // };
  //
  // const downloadCSV = (content: string, filename: string) => {
  //   const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  //   const link = document.createElement('a');
  //   const url = URL.createObjectURL(blob);
  //   link.setAttribute('href', url);
  //   link.setAttribute('download', filename);
  //   link.style.visibility = 'hidden';
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // };

  const hasSelection = selectedTenants.length > 0;

  return (
    <div className="flex flex-col sm:flex-row gap-2">
      {/* 创建租户按钮 */}
      <Button onClick={() => setShowCreateDialog(true)}>
        <PlusCircle className="h-4 w-4 mr-2" />
        创建租户
      </Button>

      {/* 批量操作下拉菜单 */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" disabled={!hasSelection}>
            <MoreHorizontal className="h-4 w-4 mr-2" />
            批量操作
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setShowDeleteDialog(true)}>
            <Trash2 className="h-4 w-4 mr-2" />
            批量删除 ({selectedTenants.length})
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {/*<DropdownMenuItem onClick={handleExport}>*/}
          {/*  <Download className="h-4 w-4 mr-2" />*/}
          {/*  导出数据*/}
          {/*</DropdownMenuItem>*/}
          <DropdownMenuItem>
            <Upload className="h-4 w-4 mr-2" />
            导入数据
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Settings className="h-4 w-4 mr-2" />
            高级设置
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 创建租户对话框 */}
      <TenantFormDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        mode="create"
        createTenantAction={createTenantAction}
      />

      {/* 批量删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认批量删除</AlertDialogTitle>
            <AlertDialogDescription>
              您即将删除选中的 {selectedTenants.length} 个租户。
              此操作不可撤销，请确认是否继续？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleBulkDelete} className="bg-red-600 hover:bg-red-700">
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}; 