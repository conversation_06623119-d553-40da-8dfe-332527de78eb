import {FC, useState} from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {Button} from '@/components/ui/button';
import {Badge} from '@/components/ui/badge';
import {Tenant, TenantQueryParams} from '@/types/tenant.ts';
import {TenantFormDialog} from './TenantFormDialog';
import {TenantDeleteDialog} from './TenantDeleteDialog';
import Pagination from '@/components/Pagination';
import {Pencil, Trash2, Eye, MoreHorizontal, Users} from 'lucide-react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {Skeleton} from "@/components/ui/skeleton.tsx";
import {TenantUserAccessDialog} from "@/pages/Tenant/components/TenantUserAccessDialog.tsx";
import {Checkbox} from "@/components/ui/checkbox.tsx";

interface TenantTableProps {
    loading: boolean;
    tenants: Tenant[];
    pagination: {
        current: number,
        pageSize: number,
        total: number,
        totalPages: number,
    };
    onQuery: (queryParams: TenantQueryParams) => void;
    selectedTenants: string[];
    selectTenant: (tenantId: string) => void;
    selectSomeTenants: (tenantIds: string[]) => void;
    selectAllTenants: () => void;
    deleteTenantAction: (tenantId: string) => void;
    updateTenantAction: (id: string, tenantData: Partial<Tenant>) => Promise<void>;
}

export const TenantTable: FC<TenantTableProps> = ({
                                                      loading,
                                                      tenants,
                                                      onQuery,
                                                      pagination,
                                                      selectedTenants,
                                                      selectTenant,
                                                      selectSomeTenants,
                                                      selectAllTenants,
                                                      deleteTenantAction,
                                                      updateTenantAction
                                                  }) => {
    const [editingTenant, setEditingTenant] = useState<Tenant | null>(null);
    const [deletingTenant, setDeletingTenant] = useState<Tenant | null>(null);

    const [selectedTenantForUsers, setSelectedTenantForUsers] = useState<Tenant | null>(null);

    // 全选状态
    const allSelected = tenants.length > 0 &&
        tenants.every(tenant => selectedTenants.includes(tenant.id));
    const someSelected = tenants.some(tenant => selectedTenants.includes(tenant.id));
    //
    const handleSelectAll = () => {
        if (allSelected) {
            // 取消全选当前页
            const currentPageIds = tenants.map(tenant => tenant.id);
            const newSelection = selectedTenants.filter(id => !currentPageIds.includes(id));
            selectSomeTenants(newSelection); // 这里需要修改 Context 来支持批量选择
        } else {
            // 全选当前页
            selectAllTenants()
        }
    };


    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge variant="default" className="bg-green-100 text-green-800">启用</Badge>;
            case 'deleted':
                return <Badge variant="secondary" className="bg-gray-100 text-gray-600">已删除</Badge>;
            default:
                return <Badge variant="outline">未知</Badge>;
        }
    };

    const getTypeBadge = (type: string) => {
        switch (type) {
            case 'school':
                return <Badge variant="outline" className="border-purple-200 text-purple-700">学校</Badge>;
            case 'standard':
                return <Badge variant="outline" className="border-blue-200 text-blue-700">标准</Badge>;
            default:
                return <Badge variant="outline">未知</Badge>;
        }
    };

    const formatSchemaName = (schemaName: string) => {
        const parts = schemaName.split('_');
        return parts.length > 1 ? parts.slice(1).join('_') : schemaName;
    };

    const handlePaginationChange = async (newPage: number, pageSize: number) => {
        await onQuery({page: newPage, page_size: pageSize});
    };

    if (loading) {
        return (
            <div className="space-y-4">
                {Array.from({length: 5}).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-4 w-4"/>
                        <Skeleton className="h-4 flex-1"/>
                        <Skeleton className="h-4 w-20"/>
                        <Skeleton className="h-4 w-32"/>
                        <Skeleton className="h-4 w-16"/>
                        <Skeleton className="h-4 w-16"/>
                        <Skeleton className="h-4 w-20"/>
                    </div>
                ))}
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* 表格 */}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-12">
                                <Checkbox
                                    checked={allSelected ? true : (someSelected ? "indeterminate" : false)}
                                    onCheckedChange={handleSelectAll}
                                />
                            </TableHead>
                            <TableHead>名称</TableHead>
                            <TableHead>代码</TableHead>
                            <TableHead>Schema名称</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>状态</TableHead>
                            <TableHead className="text-right">操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {tenants.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                                    暂无数据
                                </TableCell>
                            </TableRow>
                        ) : (
                            tenants.map((tenant) => (
                                <TableRow key={tenant.id}>
                                    <TableCell>
                                        <Checkbox
                                            checked={selectedTenants.includes(tenant.id)}
                                            onCheckedChange={() => selectTenant(tenant.id)}
                                        />
                                    </TableCell>
                                    <TableCell className="font-medium">{tenant.name}</TableCell>
                                    <TableCell className="font-mono text-sm">
                                        {formatSchemaName(tenant.schemaName)}
                                    </TableCell>
                                    <TableCell className="font-mono text-xs text-muted-foreground">
                                        {tenant.schemaName}
                                    </TableCell>
                                    <TableCell>{getTypeBadge(tenant.tenantType)}</TableCell>
                                    <TableCell>{getStatusBadge(tenant.status)}</TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm">
                                                    <MoreHorizontal className="h-4 w-4"/>
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => setEditingTenant(tenant)}>
                                                    <Pencil className="h-4 w-4 mr-2"/>
                                                    编辑
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => {
                                                    setSelectedTenantForUsers(tenant);
                                                }}>
                                                    <Users className="h-4 w-4 mr-2"/>
                                                    管理用户
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Eye className="h-4 w-4 mr-2"/>
                                                    查看详情
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator/>
                                                <DropdownMenuItem
                                                    onClick={() => setDeletingTenant(tenant)}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="h-4 w-4 mr-2"/>
                                                    删除
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* 分页 */}
            <div className="flex justify-center">
                <Pagination
                    total={pagination.total}
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    onChange={handlePaginationChange}
                />
            </div>

            {/* 编辑对话框 */}
            <TenantFormDialog
                open={!!editingTenant}
                onOpenChange={(open) => !open && setEditingTenant(null)}
                mode="edit"
                tenant={editingTenant}
                updateTenantAction={updateTenantAction}
            />

            {/* 删除确认对话框 */}
            {deletingTenant && (
                <TenantDeleteDialog
                    tenant={deletingTenant}
                    open={!!deletingTenant}
                    onOpenChange={(open: boolean) => !open && setDeletingTenant(null)}
                    onConfirm={async () => {
                        deleteTenantAction(deletingTenant.id);
                        setDeletingTenant(null);
                    }}
                />
            )}

            {/* 用户编辑对话框 */}
            {selectedTenantForUsers && (
                <TenantUserAccessDialog
                    tenant={selectedTenantForUsers}
                    open={!!selectedTenantForUsers}
                    onOpenChange={(open: boolean) => !open && setSelectedTenantForUsers(null)}
                />
            )}
        </div>
    );
}; 