import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tenant, TenantUserAccess } from '@/types/tenant.ts';
import { AddUserToTenantDialog } from './AddUserToTenantDialog';
import { EditUserAccessDialog } from './EditUserAccessDialog';
import {
  getTenantUsers,
  addUserToTenant,
  removeUserFromTenant
} from '@/services/tenantUserApi';
import { 
  Users, 
  UserPlus, 
  Shield, 
  Eye, 
  User, 
  Ban,
  Search,
  Filter,
  Trash2,
  Edit
} from 'lucide-react';
import { toast } from 'sonner';

interface TenantUserAccessDialogProps {
  tenant: Tenant;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const TenantUserAccessDialog: React.FC<TenantUserAccessDialogProps> = ({
  tenant,
  open,
  onOpenChange,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [accessTypeFilter, setAccessTypeFilter] = useState('all');
  const [userAccesses, setUserAccesses] = useState<TenantUserAccess[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddUserDialog, setShowAddUserDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingUserAccess, setEditingUserAccess] = useState<TenantUserAccess | null>(null);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // 获取租户用户列表
  useEffect(() => {
    if (open) {
      fetchTenantUsers();
    }
  }, [open, tenant.id]);

  const fetchTenantUsers = async () => {
    setLoading(true);
    try {
      const paginatedUsers = await getTenantUsers(tenant.id);
      setUserAccesses(Array.isArray(paginatedUsers?.data) ? paginatedUsers.data : []);
    } catch (error) {
      console.error('获取租户用户列表失败:', error);
      toast.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const getAccessTypeBadge = (accessType: string) => {
    switch (accessType) {
      case 'admin':
        return (
          <Badge variant="default" className="bg-red-100 text-red-800 border-red-200">
            <Shield className="h-3 w-3 mr-1" />
            管理员
          </Badge>
        );
      case 'member':
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800 border-blue-200">
            <User className="h-3 w-3 mr-1" />
            成员
          </Badge>
        );
      case 'viewer':
        return (
          <Badge variant="outline" className="border-green-200 text-green-700">
            <Eye className="h-3 w-3 mr-1" />
            查看者
          </Badge>
        );
      case 'suspended':
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-600 border-gray-200">
            <Ban className="h-3 w-3 mr-1" />
            已暂停
          </Badge>
        );
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  const getUserInitials = (username: string) => {
    return username
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const filteredUserAccesses = (userAccesses ?? []).filter(access => {
    const username = access.username || '';
    const phoneNumber = access.phone_number || '';
    const matchesSearch = username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         phoneNumber.includes(searchTerm);
    const matchesAccessType = accessTypeFilter === 'all' || access.access_type === accessTypeFilter;
    return matchesSearch && matchesAccessType;
  });

  // 获取已存在的用户ID列表
  const existingUserIds = (userAccesses ?? []).map(access => access.user_id);

  // 处理添加用户
  const handleAddUser = async (userId: string, accessType: string) => {
    try {
      const request = {
        userId,
        tenantId: tenant.id,
        accessType: accessType as any,
      };

      const newUserAccess = await addUserToTenant(tenant.id, request);
      setUserAccesses(prev => [...(prev ?? []), newUserAccess]);
      toast.success('用户添加成功');
    } catch (error) {
      console.error('添加用户失败:', error);
      toast.error('添加用户失败，请重试');
      throw error;
    }
  };

  // 处理编辑用户权限
  const handleEditUserAccess = (access: TenantUserAccess) => {
    setEditingUserAccess(access);
    setShowEditDialog(true);
  };

  // 处理权限更新
  const handleUpdateUserAccess = (updatedAccess: TenantUserAccess) => {
    setUserAccesses(prev =>
      prev.map(access =>
        access.id === updatedAccess.id ? updatedAccess : access
      )
    );
  };

  // 处理移除用户
  const handleRemoveUser = async (access: TenantUserAccess) => {
    try {
      await removeUserFromTenant(tenant.id, access.user_id);
      setUserAccesses(prev => (prev ?? []).filter(item => item.id !== access.id));
      toast.success('用户移除成功');
    } catch (error) {
      console.error('移除用户失败:', error);
      toast.error('移除用户失败，请重试');
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              租户用户管理 - {tenant.name}
            </DialogTitle>
            <DialogDescription>
              管理租户 {tenant.name} 的用户访问权限，包括添加、编辑和移除用户
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex flex-col space-y-4">
            {/* 搜索和筛选 */}
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索用户名、邮箱或手机号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={accessTypeFilter} onValueChange={setAccessTypeFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="筛选访问类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="admin">管理员</SelectItem>
                  <SelectItem value="member">成员</SelectItem>
                  <SelectItem value="viewer">查看者</SelectItem>
                  <SelectItem value="suspended">已暂停</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => setShowAddUserDialog(true)}>
                <UserPlus className="h-4 w-4 mr-2" />
                添加用户
              </Button>
            </div>

            {/* 用户列表 */}
            <div className="flex-1 overflow-auto border rounded-lg">
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">加载中...</p>
                </div>
              ) : filteredUserAccesses.length === 0 ? (
                <div className="p-8 text-center">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无用户数据</h3>
                  <p className="text-gray-500">
                    {searchTerm || accessTypeFilter !== 'all'
                      ? '当前筛选条件下没有找到匹配的用户'
                      : '该租户还没有关联任何用户'}
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {filteredUserAccesses.map((access) => (
                    <div key={access.id} className="p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                              {getUserInitials(access.username || '')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{access.username}</div>
                            <div className="text-sm text-muted-foreground">
                              {access.phone_number}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          {getAccessTypeBadge(access.access_type)}
                          <div className="text-sm text-muted-foreground">
                            授权于: {formatDate(access.granted_at)}
                          </div>
                          <div className="flex space-x-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleEditUserAccess(access)}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              编辑权限
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="text-red-600"
                              onClick={() => handleRemoveUser(access)}
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              移除
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加用户对话框 */}
      <AddUserToTenantDialog
        tenant={tenant}
        open={showAddUserDialog}
        onOpenChange={setShowAddUserDialog}
        onAddUser={handleAddUser}
        existingUserIds={existingUserIds}
      />

      {/* 编辑用户权限对话框 */}
      <EditUserAccessDialog
        userAccess={editingUserAccess}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onUpdate={handleUpdateUserAccess}
        tenantId={tenant.id}
      />
    </>
  );
};