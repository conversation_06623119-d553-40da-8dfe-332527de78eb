import React, { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Tenant } from '@/types/tenant.ts';
import { AlertTriangle } from 'lucide-react';

interface TenantDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tenant: Tenant | null;
  onConfirm: () => Promise<void>;
}

export const TenantDeleteDialog: React.FC<TenantDeleteDialogProps> = ({
  open,
  onOpenChange,
  tenant,
  onConfirm,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      // 错误已在父组件中处理
    } finally {
      setIsDeleting(false);
    }
  };

  if (!tenant) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            确认删除租户
          </AlertDialogTitle>
          <AlertDialogDescription>
            您即将删除租户 <strong>"{tenant.name}"</strong>。
            <br />
            <br />
            此操作将：
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>删除租户的所有数据</li>
              <li>移除相关的数据库Schema</li>
              <li>清除所有用户权限</li>
            </ul>
            <br />
            <strong className="text-red-600">此操作不可撤销，请谨慎操作！</strong>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? '删除中...' : '确认删除'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}; 