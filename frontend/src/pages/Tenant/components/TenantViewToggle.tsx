import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, Grid3X3, LayoutGrid } from 'lucide-react';
import { cn } from '@/lib/utils';

type ViewMode = 'table' | 'card';

interface TenantViewToggleProps {
  viewMode: ViewMode;
  onViewChange: (mode: ViewMode) => void;
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const TenantViewToggle: React.FC<TenantViewToggleProps> = ({
  viewMode,
  onViewChange,
  className,
  showLabel = true,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: {
      container: 'p-0.5',
      button: 'h-7 px-2 text-xs',
      icon: 'h-3 w-3',
      gap: 'mr-1'
    },
    md: {
      container: 'p-1',
      button: 'h-8 px-3 text-sm',
      icon: 'h-4 w-4',
      gap: 'mr-2'
    },
    lg: {
      container: 'p-1.5',
      button: 'h-10 px-4',
      icon: 'h-4 w-4',
      gap: 'mr-2'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={cn(
      'inline-flex items-center bg-muted/50 rounded-lg border border-border/50 shadow-sm',
      currentSize.container,
      className
    )}>
      <Button
        variant={viewMode === 'table' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('table')}
        className={cn(
          'relative transition-all duration-200 ease-in-out',
          currentSize.button,
          viewMode === 'table' 
            ? 'bg-background text-foreground shadow-sm border border-border/20' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted/80'
        )}
      >
        <Table className={cn(currentSize.icon, showLabel && currentSize.gap)} />
        {showLabel && '表格'}
      </Button>
      
      <Button
        variant={viewMode === 'card' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('card')}
        className={cn(
          'relative transition-all duration-200 ease-in-out',
          currentSize.button,
          viewMode === 'card' 
            ? 'bg-background text-foreground shadow-sm border border-border/20' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted/80'
        )}
      >
        <LayoutGrid className={cn(currentSize.icon, showLabel && currentSize.gap)} />
        {showLabel && '卡片'}
      </Button>
    </div>
  );
};

// 紧凑版本的视图切换组件，用于空间受限的场景
export const TenantViewToggleCompact: React.FC<Omit<TenantViewToggleProps, 'showLabel' | 'size'>> = ({
  viewMode,
  onViewChange,
  className
}) => {
  return (
    <div className={cn(
      'inline-flex items-center bg-muted/30 rounded-md border border-border/30',
      className
    )}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange('table')}
        className={cn(
          'h-7 w-7 p-0 rounded-l-md rounded-r-none border-r border-border/30',
          viewMode === 'table' 
            ? 'bg-primary text-primary-foreground' 
            : 'text-muted-foreground hover:text-foreground'
        )}
        title="表格视图"
      >
        <Table className="h-3.5 w-3.5" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange('card')}
        className={cn(
          'h-7 w-7 p-0 rounded-r-md rounded-l-none',
          viewMode === 'card' 
            ? 'bg-primary text-primary-foreground' 
            : 'text-muted-foreground hover:text-foreground'
        )}
        title="卡片视图"
      >
        <LayoutGrid className="h-3.5 w-3.5" />
      </Button>
    </div>
  );
};

// 带状态指示器的视图切换组件
export const TenantViewToggleWithIndicator: React.FC<TenantViewToggleProps & {
  itemCount?: number;
  selectedCount?: number;
}> = ({
  viewMode,
  onViewChange,
  className,
  showLabel = true,
  size = 'md',
  itemCount,
  selectedCount
}) => {
  return (
    <div className={cn('flex items-center gap-3', className)}>
      <TenantViewToggle
        viewMode={viewMode}
        onViewChange={onViewChange}
        showLabel={showLabel}
        size={size}
      />
      
      {(itemCount !== undefined || selectedCount !== undefined) && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          {itemCount !== undefined && (
            <Badge variant="secondary" className="text-xs">
              {itemCount} 项
            </Badge>
          )}
          {selectedCount !== undefined && selectedCount > 0 && (
            <Badge variant="default" className="text-xs bg-primary/10 text-primary">
              已选 {selectedCount}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};
