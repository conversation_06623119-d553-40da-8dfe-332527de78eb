import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tenant } from '@/types/tenant.ts';
import { User } from '@/types/user';
import { getUsers } from '@/services/userApi';
import { 
  Users, 
  Search,
  UserPlus,
  Shield,
  User as UserIcon,
  Eye
} from 'lucide-react';

interface AddUserToTenantDialogProps {
  tenant: Tenant;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddUser: (userId: string, accessType: string) => Promise<void>;
  existingUserIds: string[]; // 已存在的用户ID列表
}

export const AddUserToTenantDialog: React.FC<AddUserToTenantDialogProps> = ({
  tenant,
  open,
  onOpenChange,
  onAddUser,
  existingUserIds,
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedAccessType, setSelectedAccessType] = useState<string>('member');
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取用户列表
  useEffect(() => {
    if (open) {
      fetchUsers();
    }
  }, [open]);

  // 筛选用户
  useEffect(() => {
    if (users.length > 0) {
      const filtered = users.filter(user => {
        const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            user.phone_number.includes(searchTerm);
        const notAlreadyAdded = !existingUserIds.includes(user.user_id);
        return matchesSearch && notAlreadyAdded;
      });
      setFilteredUsers(filtered);
    }
  }, [users, searchTerm, existingUserIds]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const userList = await getUsers();
      setUsers(userList);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      // 这里可以添加错误提示
    } finally {
      setLoading(false);
    }
  };

  const getUserInitials = (username: string) => {
    return username
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getAccessTypeLabel = (accessType: string) => {
    switch (accessType) {
      case 'admin':
        return '管理员';
      case 'member':
        return '成员';
      case 'viewer':
        return '查看者';
      default:
        return '成员';
    }
  };

  const getAccessTypeIcon = (accessType: string) => {
    switch (accessType) {
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'member':
        return <UserIcon className="h-4 w-4" />;
      case 'viewer':
        return <Eye className="h-4 w-4" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };

  const handleAddUser = async () => {
    if (!selectedUser) return;

    setIsSubmitting(true);
    try {
      await onAddUser(selectedUser.user_id, selectedAccessType);
      onOpenChange(false);
      // 重置状态
      setSelectedUser(null);
      setSelectedAccessType('member');
      setSearchTerm('');
    } catch (error) {
      console.error('添加用户失败:', error);
      // 这里可以添加错误提示
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            添加用户到租户 - {tenant.name}
          </DialogTitle>
          <DialogDescription>
            选择要添加到租户的用户，并设置其访问权限
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* 搜索用户 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索用户名或手机号..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* 用户列表 */}
          <div className="flex-1 overflow-auto border rounded-lg">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">加载用户中...</p>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="p-8 text-center">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到可用用户</h3>
                <p className="text-gray-500">
                  {searchTerm
                    ? '当前搜索条件下没有找到匹配的用户'
                    : '所有用户都已经添加到该租户了'}
                </p>
              </div>
            ) : (
              <div className="divide-y">
                {filteredUsers.map((user) => (
                  <div 
                    key={user.user_id} 
                    className={`p-4 cursor-pointer transition-colors ${
                      selectedUser?.user_id === user.user_id 
                        ? 'bg-primary/10 border-l-4 border-l-primary' 
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleUserSelect(user)}
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                          {getUserInitials(user.username)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">{user.username}</div>
                        <div className="text-sm text-muted-foreground">
                          {user.phone_number} • {user.is_active ? '活跃' : '非活跃'}
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          {user.is_admin && <Badge variant="outline" className="text-xs">管理员</Badge>}
                          {user.is_teacher && <Badge variant="outline" className="text-xs">教师</Badge>}
                          {user.is_student && <Badge variant="outline" className="text-xs">学生</Badge>}
                        </div>
                      </div>
                      {selectedUser?.user_id === user.user_id && (
                        <div className="text-primary">
                          <UserPlus className="h-5 w-5" />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 权限设置 */}
          {selectedUser && (
            <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
              <Label className="text-sm font-medium">为 {selectedUser.username} 设置访问权限</Label>
              <Select value={selectedAccessType} onValueChange={setSelectedAccessType}>
                <SelectTrigger>
                  <SelectValue placeholder="选择访问权限" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">
                    <div className="flex items-center space-x-2">
                      {getAccessTypeIcon('admin')}
                      <span>管理员 - 完全控制权限</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="member">
                    <div className="flex items-center space-x-2">
                      {getAccessTypeIcon('member')}
                      <span>成员 - 标准操作权限</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="viewer">
                    <div className="flex items-center space-x-2">
                      {getAccessTypeIcon('viewer')}
                      <span>查看者 - 只读权限</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <div className="text-sm text-muted-foreground">
                当前选择: <Badge variant="outline">{getAccessTypeLabel(selectedAccessType)}</Badge>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleAddUser}
            disabled={!selectedUser || isSubmitting}
          >
            {isSubmitting ? '添加中...' : '添加用户'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 