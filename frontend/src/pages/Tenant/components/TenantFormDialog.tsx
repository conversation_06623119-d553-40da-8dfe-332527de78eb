import React, {useState, useEffect} from 'react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {Tenant} from '@/types/tenant.ts';
import {AlertDialog, AlertDialogDescription} from '@/components/ui/alert-dialog';
import {AlertCircle} from 'lucide-react';

interface TenantFormDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    mode: 'create' | 'edit';
    tenant?: Tenant | null;
    createTenantAction?: (tenantData: Omit<Tenant, 'id'>) => Promise<void>;
    updateTenantAction?: (id: string, tenantData: Partial<Tenant>) => Promise<void>;
}

export const TenantFormDialog: React.FC<TenantFormDialogProps> = ({
                                                                      open,
                                                                      onOpenChange,
                                                                      mode,
                                                                      tenant,
                                                                      createTenantAction,
                                                                      updateTenantAction
                                                                  }) => {
    const [formData, setFormData] = useState<Partial<Tenant>>({
        name: '',
        schemaName: '',
        tenantType: 'standard',
        status: 'active',
    });
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    // 初始化表单数据
    useEffect(() => {
        if (tenant && mode === 'edit') {
            setFormData({
                name: tenant.name,
                schemaName: tenant.schemaName,
                tenantType: tenant.tenantType,
                status: tenant.status,
            });
        } else {
            setFormData({
                name: '',
                schemaName: '',
                tenantType: 'standard',
                status: 'active',
            });
        }
        setErrors({});
    }, [tenant, mode, open]);

    // 验证函数
    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.name?.trim()) {
            newErrors.name = '租户名称不能为空';
        }

        if (mode === 'create' && !formData.schemaName?.trim()) {
            newErrors.schemaName = 'Schema名称不能为空';
        }

        if (mode === 'create' && formData.schemaName) {
            if (!/^[a-z0-9_]+$/.test(formData.schemaName)) {
                newErrors.schemaName = 'Schema名称只能包含小写字母、数字和下划线';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // 处理表单提交
    const handleSubmit = async () => {
        if (!validateForm()) return;

        setIsSubmitting(true);
        try {
            if (mode === 'create') {
                await createTenantAction?.(formData as Omit<Tenant, 'id'>);
            } else if (tenant) {
                await updateTenantAction?.(tenant.id, formData);
            }
            onOpenChange(false);
        } catch (error) {
            // 错误已在 Context 中处理
        } finally {
            setIsSubmitting(false);
        }
    };

    // 处理输入变化
    const handleInputChange = (field: keyof Tenant, value: string) => {
        setFormData(prev => ({...prev, [field]: value}));
        // 清除对应字段的错误
        if (errors[field]) {
            setErrors(prev => ({...prev, [field]: ''}));
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>
                        {mode === 'create' ? '创建租户' : '编辑租户'}
                    </DialogTitle>
                    <DialogDescription>
                        {mode === 'create'
                            ? '创建一个新的租户机构，系统将自动生成相应的数据库Schema。'
                            : '修改租户的基本信息。'
                        }
                    </DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                    {/* 租户名称 */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                            名称 <span className="text-red-500">*</span>
                        </Label>
                        <div className="col-span-3 space-y-2">
                            <Input
                                id="name"
                                value={formData.name || ''}
                                onChange={(e) => handleInputChange('name', e.target.value)}
                                placeholder="请输入租户名称"
                                className={errors.name ? 'border-red-500' : ''}
                            />
                            {errors.name && (
                                <p className="text-red-500 text-sm">{errors.name}</p>
                            )}
                        </div>
                    </div>

                    {/* Schema名称（仅创建时显示） */}
                    {mode === 'create' && (
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="schemaName" className="text-right">
                                Schema名称 <span className="text-red-500">*</span>
                            </Label>
                            <div className="col-span-3 space-y-2">
                                <Input
                                    id="schemaName"
                                    value={formData.schemaName || ''}
                                    onChange={(e) => handleInputChange('schemaName', e.target.value)}
                                    placeholder="请输入Schema名称"
                                    className={errors.schemaName ? 'border-red-500' : ''}
                                />
                                {errors.schemaName && (
                                    <p className="text-red-500 text-sm">{errors.schemaName}</p>
                                )}
                                <p className="text-xs text-muted-foreground">
                                    只能包含小写字母、数字和下划线，建议使用有意义的标识符
                                </p>
                            </div>
                        </div>
                    )}

                    {/* 租户类型 */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="tenantType" className="text-right">
                            类型
                        </Label>
                        <div className="col-span-3">
                            <Select
                                value={formData.tenantType || 'standard'}
                                onValueChange={(value) => handleInputChange('tenantType', value)}
                            >
                                <SelectTrigger id="tenantType">
                                    <SelectValue placeholder="请选择租户类型"/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="standard">标准企业</SelectItem>
                                    <SelectItem value="school">教育机构</SelectItem>
                                    <SelectItem value="unknown">未知类型</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* 状态（仅编辑时显示） */}
                    {mode === 'edit' && (
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="status" className="text-right">
                                状态
                            </Label>
                            <div className="col-span-3">
                                <Select
                                    value={formData.status || 'active'}
                                    onValueChange={(value) => handleInputChange('status', value)}
                                >
                                    <SelectTrigger id="status">
                                        <SelectValue placeholder="请选择状态"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="active">启用</SelectItem>
                                        <SelectItem value="deleted">已删除</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    )}

                    {/* 提示信息 */}
                    {mode === 'create' && (
                        <AlertDialog>
                            <AlertCircle className="h-4 w-4"/>
                            <AlertDialogDescription>
                                创建租户后，系统将自动创建对应的数据库Schema和初始化基础数据。
                                此操作可能需要几分钟时间。
                            </AlertDialogDescription>
                        </AlertDialog>
                    )}
                </div>

                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={isSubmitting}
                    >
                        取消
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? '保存中...' : '保存'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}; 