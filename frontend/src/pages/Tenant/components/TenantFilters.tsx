import {FC, useState} from 'react';
import {Input} from '@/components/ui/input';
import {Button} from '@/components/ui/button';
import {Label} from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {Search, X, Filter} from 'lucide-react';
import {TenantQueryParams} from "@/types";

interface TenantFiltersProps {
    params: TenantQueryParams;
    onFilterChange: (key: keyof TenantQueryParams, value: any) => void;
    clearFilters: () => void;
}

export const TenantFilters: FC<TenantFiltersProps> = ({params, onFilterChange, clearFilters}) => {

    const hasActiveFilters = params.search || (params.status && params.status !== 'all') || (params.tenant_type && params.tenant_type !== 'all');
    const [searchInput, setSearchInput] = useState<string>('')
    return (
        <div className="space-y-6">
            {/* 筛选器头部 */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg">
                        <Filter className="h-4 w-4 text-white"/>
                    </div>
                    <div>
                        <h3 className="font-semibold text-slate-900 dark:text-white">高级筛选</h3>
                        <p className="text-sm text-slate-600 dark:text-slate-400">精确筛选您需要的租户</p>
                    </div>
                </div>
                {hasActiveFilters && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={clearFilters}
                        className="h-9 px-4 bg-white/50 hover:bg-white border-slate-200 hover:border-slate-300"
                    >
                        <X className="h-4 w-4 mr-2"/>
                        清除筛选
                    </Button>
                )}
            </div>

            {/* 筛选器内容 */}
            <div className="grid gap-6 md:grid-cols-3">

                {/* 搜索框 */}
                <div className="space-y-1">
                    <Label
                        htmlFor="search"
                        className="text-sm font-medium text-slate-700 dark:text-slate-300"
                    >
                        关键词搜索
                    </Label>
                    <div className="relative flex">
                        <Search
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 group-focus-within:text-indigo-500 transition-colors"
                        />
                        <Input
                            id="search"
                            placeholder="搜索租户名称、代码..."
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            className="pl-10 pr-20 bg-white/70 border-slate-200 focus:bg-white focus:border-indigo-300 focus:ring-indigo-200 transition-all flex-1"
                        />
                        <Button
                            onClick={() => onFilterChange("search", searchInput)}
                            className="absolute right-1 h-[calc(100%_-_4px)] px-3 mt-0.5"
                        >
                            搜索
                        </Button>
                    </div>
                </div>

                {/* 状态筛选 */}
                <div className="space-y-1">
                    <Label htmlFor="status" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        运行状态
                    </Label>
                    <Select
                        value={params.status || "all"}
                        onValueChange={(value) => onFilterChange("status", value)}
                    >
                        <SelectTrigger
                            id="status"
                            className="bg-white/70 border-slate-200 focus:bg-white focus:border-indigo-300 transition-all"
                        >
                            <SelectValue placeholder="选择状态" />
                        </SelectTrigger>
                        <SelectContent className="bg-white/95 backdrop-blur-sm border-slate-200">
                            <SelectItem value="all" className="focus:bg-indigo-50">全部状态</SelectItem>
                            <SelectItem value="active" className="focus:bg-green-50">
                                <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 bg-green-500 rounded-full" />
                                    启用中
                                </div>
                            </SelectItem>
                            <SelectItem value="deleted" className="focus:bg-red-50">
                                <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 bg-red-500 rounded-full" />
                                    已删除
                                </div>
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {/* 类型筛选 */}
                <div className="space-y-1">
                    <Label htmlFor="type" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        租户类型
                    </Label>
                    <Select
                        value={params.tenant_type || "all"}
                        onValueChange={(value) => onFilterChange("tenant_type", value)}
                    >
                        <SelectTrigger
                            id="type"
                            className="bg-white/70 border-slate-200 focus:bg-white focus:border-indigo-300 transition-all"
                        >
                            <SelectValue placeholder="选择类型" />
                        </SelectTrigger>
                        <SelectContent className="bg-white/95 backdrop-blur-sm border-slate-200">
                            <SelectItem value="all" className="focus:bg-indigo-50">全部类型</SelectItem>
                            <SelectItem value="school" className="focus:bg-blue-50">
                                <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 bg-blue-500 rounded-full" />
                                    学校机构
                                </div>
                            </SelectItem>
                            <SelectItem value="standard" className="focus:bg-purple-50">
                                <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 bg-purple-500 rounded-full" />
                                    标准企业
                                </div>
                            </SelectItem>
                            <SelectItem value="unknown" className="focus:bg-gray-50">
                                <div className="flex items-center gap-2">
                                    <div className="h-2 w-2 bg-gray-500 rounded-full" />
                                    未知类型
                                </div>
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>

            </div>

            {/* 活跃筛选器标签 */}
            {hasActiveFilters && (
                <div className="space-y-3">
                    <div className="flex items-center gap-2">
                        <div className="h-px flex-1 bg-gradient-to-r from-transparent via-slate-200 to-transparent"/>
                        <span
                            className="text-xs font-medium text-slate-500 bg-white px-3 py-1 rounded-full border border-slate-200">
              当前筛选条件
            </span>
                        <div className="h-px flex-1 bg-gradient-to-r from-transparent via-slate-200 to-transparent"/>
                    </div>

                    <div className="flex flex-wrap gap-3">
                        {params.search && (
                            <div
                                className="group flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 text-blue-800 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-all">
                                <Search className="h-3 w-3"/>
                                <span>搜索: {params.search}</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 hover:bg-blue-200 rounded-full ml-1 opacity-70 group-hover:opacity-100 transition-opacity"
                                    onClick={() => onFilterChange('search', null)}
                                >
                                    <X className="h-3 w-3"/>
                                </Button>
                            </div>
                        )}
                        {params.status && params.status !== 'all' && (
                            <div
                                className="group flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-800 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-all">
                                <div className="h-2 w-2 bg-green-500 rounded-full"/>
                                <span>状态: {params.status === 'active' ? '启用中' : '已删除'}</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 hover:bg-green-200 rounded-full ml-1 opacity-70 group-hover:opacity-100 transition-opacity"
                                    onClick={() => onFilterChange('status', 'all')}
                                >
                                    <X className="h-3 w-3"/>
                                </Button>
                            </div>
                        )}
                        {params.tenant_type && params.tenant_type !== 'all' && (
                            <div
                                className="group flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 text-purple-800 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-all">
                                <div className="h-2 w-2 bg-purple-500 rounded-full"/>
                                <span>类型: {params.tenant_type === 'school' ? '学校机构' : params.tenant_type === 'standard' ? '标准企业' : '未知类型'}</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 hover:bg-purple-200 rounded-full ml-1 opacity-70 group-hover:opacity-100 transition-opacity"
                                    onClick={() => onFilterChange('tenant_type', 'all')}
                                >
                                    <X className="h-3 w-3"/>
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            )}

        </div>
    );
}; 