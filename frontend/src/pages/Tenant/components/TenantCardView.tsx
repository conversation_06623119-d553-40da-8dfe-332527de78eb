import React, {useState} from 'react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Badge} from '@/components/ui/badge';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {Skeleton} from '@/components/ui/skeleton';
import {Tenant, TenantQueryParams} from '@/types/tenant.ts';
import {TenantFormDialog} from './TenantFormDialog';
import {TenantDeleteDialog} from './TenantDeleteDialog';
import {TenantUserAccessDialog} from './TenantUserAccessDialog';
import {
    Pencil,
    Trash2,
    Users,
    Database,
    Calendar,
    Building2,
    School,
    CheckCircle,
    XCircle,
    Clock,
    MoreHorizontal, Table
} from 'lucide-react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TenantCardViewProps {
    params: TenantQueryParams;
    loading: boolean;
    tenants: Tenant[];
    pagination: {
        current: number,
        pageSize: number,
        total: number,
        totalPages: number,
    };
    selectedTenants: string[];
    selectTenant: (tenantId: string) => void;
    onQuery: (queryParams: TenantQueryParams) => void;
    onViewChange: (view: 'table' | 'card') => void;
    deleteTenantAction: (tenantId: string) => void;
    updateTenantAction: (id: string, tenantData: Partial<Tenant>) => Promise<void>;
}

export const TenantCardView: React.FC<TenantCardViewProps> = ({
                                                                  params,
                                                                  loading,
                                                                  onViewChange,
                                                                  updateTenantAction,
                                                                  tenants,
                                                                  pagination,
                                                                  selectedTenants,
                                                                  selectTenant,
                                                                  deleteTenantAction
                                                              }) => {
    // const { state, selectTenant, deleteTenantAction } = useTenant();
    const [editingTenant, setEditingTenant] = useState<Tenant | null>(null);
    const [deletingTenant, setDeletingTenant] = useState<Tenant | null>(null);
    const [selectedTenantForUsers, setSelectedTenantForUsers] = useState<Tenant | null>(null);

    // 分页计算
    const {current, total, totalPages} = pagination;

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return (
                    <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
                        <CheckCircle className="h-3 w-3 mr-1"/>
                        启用
                    </Badge>
                );
            case 'deleted':
                return (
                    <Badge variant="secondary" className="bg-gray-100 text-gray-600 border-gray-200">
                        <XCircle className="h-3 w-3 mr-1"/>
                        已删除
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline">
                        <Clock className="h-3 w-3 mr-1"/>
                        未知
                    </Badge>
                );
        }
    };

    const getTypeBadge = (type: string) => {
        switch (type) {
            case 'school':
                return (
                    <Badge variant="outline" className="border-purple-200 text-purple-700 bg-purple-50">
                        <School className="h-3 w-3 mr-1"/>
                        学校
                    </Badge>
                );
            case 'standard':
                return (
                    <Badge variant="outline" className="border-blue-200 text-blue-700 bg-blue-50">
                        <Building2 className="h-3 w-3 mr-1"/>
                        标准
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline">
                        <Building2 className="h-3 w-3 mr-1"/>
                        未知
                    </Badge>
                );
        }
    };

    const formatSchemaName = (schemaName: string) => {
        const parts = schemaName.split('_');
        return parts.length > 1 ? parts.slice(1).join('_') : schemaName;
    };

    const getTenantInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    };

    if (loading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({length: 8}).map((_, i) => (
                    <Card key={i} className="h-64">
                        <CardHeader>
                            <div className="flex items-center space-x-3">
                                <Skeleton className="h-12 w-12 rounded-full"/>
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-32"/>
                                    <Skeleton className="h-3 w-24"/>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <Skeleton className="h-3 w-full"/>
                            <Skeleton className="h-3 w-3/4"/>
                            <Skeleton className="h-3 w-1/2"/>
                            <div className="flex space-x-2">
                                <Skeleton className="h-6 w-16"/>
                                <Skeleton className="h-6 w-20"/>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    if (tenants.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <Building2 className="h-12 w-12 text-gray-400"/>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无租户数据</h3>
                <p className="text-gray-500 mb-4">
                    {params.search || params.status !== 'all' || params.tenant_type !== 'all'
                        ? '当前筛选条件下没有找到匹配的租户'
                        : '还没有创建任何租户，开始创建第一个租户吧'}
                </p>
                <div className="flex justify-center space-x-3">
                    <Button variant="outline" onClick={() => onViewChange('table')}>
                        <Table className="h-4 w-4 mr-2"/>
                        切换到表格视图
                    </Button>
                    {!(params.search || params.status !== 'all' || params.tenant_type !== 'all') && (
                        <Button>
                            创建租户
                        </Button>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* 租户卡片网格 */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
                {tenants.map((tenant) => (
                    <Card
                        key={tenant.id}
                        className={`min-h-72 transition-all duration-300 ease-out hover:shadow-xl hover:shadow-primary/5 hover:-translate-y-1 cursor-pointer group ${
                            selectedTenants.includes(tenant.id as any)
                                ? 'ring-2 ring-primary ring-offset-2 shadow-lg'
                                : 'hover:border-primary/20'
                        }`}
                        onClick={() => selectTenant(tenant.id)}
                    >
                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between gap-3">
                                <div className="flex items-start space-x-3 flex-1 min-w-0">
                                    <Avatar
                                        className="h-12 w-12 transition-transform duration-200 group-hover:scale-105 flex-shrink-0">
                                        <AvatarFallback
                                            className="bg-primary/10 text-primary font-semibold group-hover:bg-primary/20 transition-colors">
                                            {getTenantInitials(tenant.name)}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div className="min-w-0 flex-1">
                                        <CardTitle
                                            className="text-base font-semibold leading-tight line-clamp-2 mb-1 break-words">
                                            {tenant.name}
                                        </CardTitle>
                                        <CardDescription className="text-xs text-muted-foreground truncate">
                                            {formatSchemaName(tenant.schemaName)}
                                        </CardDescription>
                                    </div>
                                </div>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <MoreHorizontal className="h-4 w-4"/>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={(e) => {
                                            e.stopPropagation();
                                            setEditingTenant(tenant);
                                        }}>
                                            <Pencil className="h-4 w-4 mr-2"/>
                                            编辑租户
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={(e) => {
                                            e.stopPropagation();
                                            setSelectedTenantForUsers(tenant);
                                        }}>
                                            <Users className="h-4 w-4 mr-2"/>
                                            管理用户
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator/>
                                        <DropdownMenuItem
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setDeletingTenant(tenant);
                                            }}
                                            className="text-red-600"
                                        >
                                            <Trash2 className="h-4 w-4 mr-2"/>
                                            删除租户
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </CardHeader>

                        <CardContent className="space-y-3">
                            {/* 状态和类型标签 */}
                            <div className="flex flex-wrap gap-2">
                                {getStatusBadge(tenant.status)}
                                {getTypeBadge(tenant.tenantType)}
                            </div>

                            {/* 租户信息 */}
                            <div className="space-y-2 text-sm">
                                <div className="flex items-center space-x-2 text-muted-foreground">
                                    <Database className="h-3 w-3"/>
                                    <span className="truncate">Schema: {tenant.schemaName}</span>
                                </div>
                                <div className="flex items-center space-x-2 text-muted-foreground">
                                    <Calendar className="h-3 w-3"/>
                                    <span>创建于: {formatDate(tenant.createdAt)}</span>
                                </div>
                            </div>

                            {/* 快速操作按钮 */}
                            <div
                                className="flex space-x-2 pt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <Button
                                    size="sm"
                                    variant="outline"
                                    className="flex-1 text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedTenantForUsers(tenant);
                                    }}
                                >
                                    <Users className="h-3 w-3 mr-1"/>
                                    用户管理
                                </Button>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    className="flex-1 text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setEditingTenant(tenant);
                                    }}
                                >
                                    <Pencil className="h-3 w-3 mr-1"/>
                                    编辑
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* 分页信息 */}
            {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-3 py-4">
                    <div
                        className="flex items-center space-x-2 text-sm text-muted-foreground bg-muted/30 px-3 py-1.5 rounded-lg border">
            <span className="font-medium">
              第 {current} 页，共 {totalPages} 页
            </span>
                        <span className="text-muted-foreground/60">•</span>
                        <span>
              共 {total} 个租户
            </span>
                    </div>
                </div>
            )}

            {/* 对话框 */}
            {editingTenant && (
                <TenantFormDialog
                    tenant={editingTenant}
                    open={!!editingTenant}
                    onOpenChange={(open: boolean) => !open && setEditingTenant(null)}
                    mode="edit"
                    updateTenantAction={updateTenantAction}
                />
            )}

            {deletingTenant && (
                <TenantDeleteDialog
                    tenant={deletingTenant}
                    open={!!deletingTenant}
                    onOpenChange={(open: boolean) => !open && setDeletingTenant(null)}
                    onConfirm={async () => {
                        await deleteTenantAction(deletingTenant.id as any);
                        setDeletingTenant(null);
                    }}
                />
            )}

            {selectedTenantForUsers && (
                <TenantUserAccessDialog
                    tenant={selectedTenantForUsers}
                    open={!!selectedTenantForUsers}
                    onOpenChange={(open: boolean) => !open && setSelectedTenantForUsers(null)}
                />
            )}
        </div>
    );
}; 