import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Shield, 
  Eye, 
  User, 
  Ban,
  Calendar,
  Clock,
  AlertTriangle,
  Info,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { TenantUserAccess, UpdateTenantUserAccessRequest } from '@/types/tenant.ts';
import { updateTenantUserAccess } from '@/services/tenantUserApi';

interface EditUserAccessDialogProps {
  userAccess: TenantUserAccess | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (updatedAccess: TenantUserAccess) => void;
  tenantId: string;
}

interface AccessTypeOption {
  value: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  permissions: string[];
}

const accessTypeOptions: AccessTypeOption[] = [
  {
    value: 'admin',
    label: '管理员',
    description: '拥有租户的完全管理权限，可以管理所有用户和设置',
    icon: <Shield className="h-4 w-4" />,
    color: 'bg-red-100 text-red-800 border-red-200',
    permissions: [
      '管理租户设置',
      '添加/移除用户',
      '修改用户权限',
      '查看所有数据',
      '导出数据',
      '管理计费信息'
    ]
  },
  {
    value: 'member',
    label: '成员',
    description: '标准用户权限，可以使用大部分功能但不能管理其他用户',
    icon: <User className="h-4 w-4" />,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    permissions: [
      '查看和编辑自己的数据',
      '使用核心功能',
      '创建和管理内容',
      '参与协作',
      '查看报告'
    ]
  },
  {
    value: 'viewer',
    label: '查看者',
    description: '只读权限，只能查看数据但不能进行修改操作',
    icon: <Eye className="h-4 w-4" />,
    color: 'bg-green-100 text-green-800 border-green-200',
    permissions: [
      '查看数据',
      '查看报告',
      '导出个人数据',
      '查看公共内容'
    ]
  },
  {
    value: 'suspended',
    label: '已暂停',
    description: '暂停访问权限，用户无法登录或使用任何功能',
    icon: <Ban className="h-4 w-4" />,
    color: 'bg-gray-100 text-gray-600 border-gray-200',
    permissions: [
      '无法登录',
      '无法访问任何功能',
      '数据保留但不可访问'
    ]
  }
];

export const EditUserAccessDialog: React.FC<EditUserAccessDialogProps> = ({
  userAccess,
  open,
  onOpenChange,
  onUpdate,
  tenantId,
}) => {
  const [selectedAccessType, setSelectedAccessType] = useState('');
  const [notes, setNotes] = useState('');
  const [notifyUser, setNotifyUser] = useState(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (userAccess) {
      setSelectedAccessType(userAccess.access_type);
      setNotes(userAccess.notes || '');
    }
  }, [userAccess]);

  const getUserInitials = (username: string) => {
    return username
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getAccessTypeOption = (accessType: string) => {
    return accessTypeOptions.find(option => option.value === accessType);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleSave = async () => {
    if (!userAccess || !selectedAccessType) return;

    setLoading(true);
    try {
      const updateData: UpdateTenantUserAccessRequest = {
        access_type: selectedAccessType as 'member' | 'admin' | 'viewer' | 'suspended',
        expires_at: selectedAccessType === 'suspended' ? undefined : userAccess.expires_at,
        notes: notes.trim() || undefined,
        notify_user: notifyUser
      };

      const updatedAccess = await updateTenantUserAccess(
        tenantId,
        userAccess.user_id,
        updateData
      );

      onUpdate(updatedAccess);
      onOpenChange(false);
      
      const accessOption = getAccessTypeOption(selectedAccessType);
      toast.success(`用户权限已更新为 ${accessOption?.label}`);
    } catch (error) {
      console.error('更新用户权限失败:', error);
      toast.error('更新用户权限失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (userAccess) {
      setSelectedAccessType(userAccess.access_type);
      setNotes(userAccess.notes || '');
    }
    setNotifyUser(true);
    onOpenChange(false);
  };

  if (!userAccess) return null;

  const currentOption = getAccessTypeOption(userAccess.access_type);
  const selectedOption = getAccessTypeOption(selectedAccessType);
  const hasChanges = selectedAccessType !== userAccess.access_type || 
                    notes.trim() !== (userAccess.notes || '').trim();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            编辑用户权限
          </DialogTitle>
          <DialogDescription>
            修改用户在租户中的访问权限和角色设置
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 用户信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">用户信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                    {getUserInitials(userAccess.username || '')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-medium">{userAccess.username}</h3>
                  <p className="text-sm text-muted-foreground">{userAccess.phone_number}</p>
                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      加入时间: {formatDate(userAccess.granted_at)}
                    </div>
                    {userAccess.last_accessed_at && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        最后访问: {formatDate(userAccess.last_accessed_at)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground mb-1">当前权限</div>
                  <Badge className={currentOption?.color}>
                    {currentOption?.icon}
                    <span className="ml-1">{currentOption?.label}</span>
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 权限选择 */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">访问权限类型</Label>
              <p className="text-sm text-muted-foreground mt-1">
                选择用户在租户中的权限级别
              </p>
            </div>

            <Select value={selectedAccessType} onValueChange={setSelectedAccessType}>
              <SelectTrigger>
                <SelectValue placeholder="选择权限类型" />
              </SelectTrigger>
              <SelectContent>
                {accessTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* 权限详情 */}
            {selectedOption && (
              <Card className="border-l-4 border-l-primary">
                <CardContent className="pt-4">
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      {selectedOption.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{selectedOption.label}</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        {selectedOption.description}
                      </p>
                      <div className="mt-3">
                        <h5 className="text-sm font-medium mb-2">权限范围：</h5>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {selectedOption.permissions.map((permission, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <CheckCircle className="h-3 w-3 text-green-600" />
                              {permission}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="notes">备注说明（可选）</Label>
            <Textarea
              id="notes"
              placeholder="添加关于此权限变更的备注..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* 通知设置 */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Info className="h-4 w-4 text-blue-600" />
              <div>
                <div className="font-medium">通知用户</div>
                <div className="text-sm text-muted-foreground">
                  向用户发送权限变更通知
                </div>
              </div>
            </div>
            <Switch
              checked={notifyUser}
              onCheckedChange={setNotifyUser}
            />
          </div>

          {/* 变更警告 */}
          {hasChanges && selectedAccessType === 'suspended' && (
            <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium text-yellow-800">权限暂停警告</div>
                <div className="text-yellow-700 mt-1">
                  暂停用户权限后，该用户将无法登录或访问任何功能。请确认这是您想要的操作。
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={loading}>
            取消
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges || loading}
            className="min-w-20"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                保存中...
              </div>
            ) : (
              '保存更改'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
