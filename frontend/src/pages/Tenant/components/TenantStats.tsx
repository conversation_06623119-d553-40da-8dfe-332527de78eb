import {FC, useMemo} from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, School, Users, CheckCircle } from 'lucide-react';
import {TenantStatsRes} from "@/types";

interface TenantStatsProps {
    stats: TenantStatsRes;
}

export const TenantStats: FC<TenantStatsProps> = ({stats}) => {
    const activePercentage = useMemo(() => {
        return stats.total > 0 ? Math.round((stats.active / stats.total) * 100) : 0
    }, [stats]);

  const statCards = [
    {
      title: '总租户数',
      value: stats.total,
      description: '系统中的所有租户',
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '活跃租户',
      value: stats.active,
      description: `${activePercentage}% 的租户处于活跃状态`,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '学校类型',
      value: stats.schools,
      description: '教育机构租户',
      icon: School,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: '标准类型',
      value: stats.standard,
      description: '标准企业租户',
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => {
        const IconComponent = stat.icon;
        return (
          <Card key={index} className="relative overflow-hidden border-0 shadow-lg bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 group">
            {/* 背景装饰 */}
            <div className={`absolute inset-0 bg-gradient-to-br ${stat.bgColor.replace('bg-', 'from-').replace('-50', '-50/20')} to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />

            <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-3">
              <div className="space-y-1">
                <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
                  {stat.title}
                </CardTitle>
                <div className="text-2xl font-bold text-slate-900 dark:text-white">
                  {stat.value}
                </div>
              </div>
              <div className={`relative p-3 rounded-xl ${stat.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                <IconComponent className={`h-5 w-5 ${stat.color}`} />
                {/* 光晕效果 */}
                <div className={`absolute inset-0 rounded-xl ${stat.bgColor} blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300`} />
              </div>
            </CardHeader>
            <CardContent className="relative pt-0">
              <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                {stat.description}
              </p>

              {/* 进度指示器 */}
              {stat.title === '活跃租户' && (
                <div className="mt-3 space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-slate-500">活跃度</span>
                    <span className="font-medium text-green-600">{activePercentage}%</span>
                  </div>
                  <div className="h-1.5 bg-slate-100 dark:bg-slate-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full transition-all duration-1000 ease-out"
                      style={{ width: `${activePercentage}%` }}
                    />
                  </div>
                </div>
              )}
            </CardContent>

            {/* 边框光效 */}
            <div className="absolute inset-0 rounded-lg border border-white/20 group-hover:border-white/40 transition-colors duration-300" />
          </Card>
        );
      })}
    </div>
  );
}; 