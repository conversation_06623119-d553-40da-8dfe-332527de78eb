import React, {useEffect, useState} from 'react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {TenantStats} from './TenantStats';
import {TenantFilters} from './TenantFilters';
import {TenantTable} from './TenantTable';
import {TenantCardView} from './TenantCardView';
import {TenantActions} from './TenantActions';
import {TenantViewToggle} from './TenantViewToggle';
import {RefreshCw, Table} from 'lucide-react';
import {DEFAULT_TENANT_QUERY, Tenant, TenantQueryParams, TenantStatsRes} from "@/types";
import {toast} from "sonner";
import {createTenant, deleteTenant, getTenants, getTenantStats, updateTenant} from "@/services/tenantApi.ts";

type ViewMode = 'table' | 'card';

export const TenantDashboard: React.FC = () => {
    const [viewMode, setViewMode] = useState<ViewMode>('table');

    const [loading, setLoading] = useState(false);
    const [tenants, setTenants] = useState<Tenant[]>([])
    const [tenantQueryParams, setTenantQueryParams] = useState<TenantQueryParams>(DEFAULT_TENANT_QUERY)
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });

    const [stats, setStats] = useState<TenantStatsRes>({active: 0, deleted: 0, schools: 0, standard: 0, total: 0})
    const [selectedTenants, setSelectedTenants] = useState<string[]>([])

    const loadTenants = async (params?: Partial<TenantQueryParams>) => {
        try {
            setLoading(true)
            const finalParams = {...tenantQueryParams, ...params};
            let response = await getTenants(finalParams)
            if (response.success && response.data) {
                setTenants(response.data);
                setPagination({
                    current: response.pagination.page,
                    pageSize: response.pagination.page_size,
                    total: response.pagination.total,
                    totalPages: response.pagination.total_pages,
                });
            }
            let res = await getTenantStats()
            if (res.success && res.data) {
                setStats(res.data)
            }
            setTenantQueryParams(finalParams)

        } catch (error) {
            console.error('Failed to load question types:', error);
            toast.error('加载题型列表失败');
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key: keyof TenantQueryParams, value: any) => {
        const newParams = {...tenantQueryParams, [key]: value, page: 1};
        loadTenants(newParams);
    };

    const handleClearFilters = () => {
        loadTenants({
            is_active: null,
            status: 'all',
            tenant_type: 'all',
        })
    }

    const selectTenant = (id: string) => {
        const newSelection = selectedTenants.includes(id)
            ? selectedTenants.filter(selectedId => selectedId !== id)
            : [...selectedTenants, id];
        setSelectedTenants(newSelection)
    }

    const selectSomeTenants = (ids: string[]) => {
        setSelectedTenants(ids)
    }

    const selectAllTenants = () => {
        const result = [...new Set(tenants.map(tenant => tenant.id).concat(selectedTenants))];
        setSelectedTenants(result)
    }

    const clearSelection = () => {
        setSelectedTenants([])
    }

    // 创建租户
    const createTenantAction = async (tenantData: Omit<Tenant, 'id'>) => {
        try {
            await createTenant(tenantData);
            await loadTenants();
            toast.success('租户创建成功');
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '创建租户失败';
            toast.error(errorMessage);
            throw error;
        }
    };

    // 更新租户
    const updateTenantAction = async (id: string, tenantData: Partial<Tenant>) => {
        try {
            await updateTenant(id, tenantData);
            await loadTenants();
            toast.success('租户更新成功');
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '更新租户失败';
            toast.error(errorMessage);
            throw error;
        }
    };

    // 删除租户
    const deleteTenantAction = async (id: string) => {
        try {
            await deleteTenant(id);
            await loadTenants();
            toast.success('租户删除成功');
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '删除租户失败';
            toast.error(errorMessage);
            throw error;
        }
    };

    // 批量删除租户
    const bulkDeleteTenants = async (ids: string[]) => {
        try {
            await Promise.all(ids.map(id => deleteTenant(id)));
            await loadTenants();
            toast.success(`成功删除 ${ids.length} 个租户`);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '批量删除租户失败';
            toast.error(errorMessage);
            throw error;
        }
    };

    useEffect(() => {
        loadTenants()
    }, []);


    const handleViewChange = (mode: ViewMode) => {
        setViewMode(mode);
    };

    return (
        <div className="space-y-6">
            {/* 页面标题和操作 */}
            <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
                <div className="flex-1">
                    <h1 className="text-3xl font-bold tracking-tight">租户管理</h1>
                    <p className="text-muted-foreground mt-1">
                        管理系统中的所有租户机构，支持多租户架构的完整生命周期管理
                    </p>
                </div>

                {/* 操作区域 */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                    {/* 视图切换 */}
                    <TenantViewToggle
                        viewMode={viewMode}
                        onViewChange={handleViewChange}
                        size="md"
                    />

                    {/* 刷新按钮 */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadTenants()}
                        disabled={loading}
                        className="w-full sm:w-auto"
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`}/>
                        刷新
                    </Button>
                </div>
            </div>

            {/* 错误提示 */}
            {/*{error && (*/}
            {/*    <AlertDialog>*/}
            {/*        <AlertDialogDescription*/}
            {/*            className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg text-red-800">*/}
            {/*            <AlertCircle className="h-4 w-4"/>*/}
            {/*            <span>{error}</span>*/}
            {/*        </AlertDialogDescription>*/}
            {/*    </AlertDialog>*/}
            {/*)}*/}

            {/* 统计卡片 */}
            <TenantStats stats={stats}/>

            {/* 筛选器和操作按钮 */}
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <TenantFilters
                        params={tenantQueryParams}
                        onFilterChange={handleFilterChange}
                        clearFilters={handleClearFilters}
                    />
                </div>
                <div className="lg:w-auto">
                    <TenantActions
                        createTenantAction={createTenantAction}
                        selectedTenants={selectedTenants}
                        clearSelection={clearSelection}
                        bulkDeleteTenants={bulkDeleteTenants}/>
                </div>
            </div>

            {/* 数据视图 */}
            <div className="transition-all duration-300 ease-in-out">
                {viewMode === 'table' ? (
                    <Card className="animate-in fade-in-0 duration-300">
                        <CardHeader>
                            <div className="flex justify-between items-center">
                                <div>
                                    <CardTitle className="flex items-center gap-2">
                                        <Table className="h-5 w-5 text-muted-foreground"/>
                                        租户列表
                                    </CardTitle>
                                    <CardDescription>
                                        共 {pagination.total} 个租户
                                        {selectedTenants.length > 0 && (
                                            <span className="ml-2 text-primary font-medium">
                                            (已选择 {selectedTenants.length} 个)
                                          </span>
                                        )}
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <TenantTable
                                loading={loading}
                                tenants={tenants}
                                onQuery={loadTenants}
                                pagination={pagination}
                                selectedTenants={selectedTenants}
                                selectTenant={selectTenant}
                                selectSomeTenants={selectSomeTenants}
                                selectAllTenants={selectAllTenants}
                                deleteTenantAction={deleteTenantAction}
                                updateTenantAction={updateTenantAction}
                            />
                        </CardContent>
                    </Card>
                ) : (
                    <div className="animate-in fade-in-0 duration-300">
                        <TenantCardView
                            onViewChange={handleViewChange}
                            loading={loading}
                            tenants={tenants}
                            onQuery={loadTenants}
                            pagination={pagination}
                            selectedTenants={selectedTenants}
                            selectTenant={selectTenant}
                            deleteTenantAction={deleteTenantAction}
                            updateTenantAction={updateTenantAction}
                            params={tenantQueryParams}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

