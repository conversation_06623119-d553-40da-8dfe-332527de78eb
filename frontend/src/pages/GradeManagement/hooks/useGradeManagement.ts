import { useState, useEffect, useCallback, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'sonner';
import { gradeApi } from '@/services/gradeApi';
import { 
  GradeLevel, 
  GradeLevelQueryParams, 
  GradeLevelFormData,
  DEFAULT_GRADE_QUERY
} from '@/types/grade';

export const useGradeManagement = () => {
  const [searchParams] = useSearchParams();
  const stageCode = searchParams.get('stage_code');
  
  // State management
  const [grades, setGrades] = useState<GradeLevel[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<GradeLevelQueryParams>(() => ({
    ...DEFAULT_GRADE_QUERY,
    ...(stageCode ? { stage_code: stageCode } : {})
  }));
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingGrade, setEditingGrade] = useState<GradeLevel | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  
  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [gradeToDelete, setGradeToDelete] = useState<string | null>(null);

  // Use ref to store current query params to avoid infinite loop
  const queryParamsRef = useRef(queryParams);
  queryParamsRef.current = queryParams;

  // Load grades data
  const loadGrades = useCallback(async (params?: Partial<GradeLevelQueryParams>) => {
    try {
      setLoading(true);
      const finalParams = { 
        ...queryParamsRef.current, 
        ...params,
        ...(stageCode ? { stage_code: stageCode } : {})
      };
      
      const response = await gradeApi.getGrades(finalParams);
      
      if (response.success && response.data) {
        setGrades(response.data);
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.page_size,
          total: response.pagination.total,
          totalPages: response.pagination.total_pages,
        });
        setQueryParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load grades:', error);
      toast.error('加载年级列表失败');
    } finally {
      setLoading(false);
    }
  }, [stageCode]);

  useEffect(() => {
    loadGrades();
  }, [loadGrades]);

  // Handle search
  const handleSearch = useCallback((searchTerm: string) => {
    loadGrades({...queryParamsRef.current, search: searchTerm, page: 1});
  }, [loadGrades]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof GradeLevelQueryParams, value: any) => {
    const newParams = { ...queryParamsRef.current, [key]: value, page: 1 };
    loadGrades(newParams);
  }, [loadGrades]);

  // Handle pagination
  const handlePageChange = useCallback((page: number, pageSize: number) => {
    loadGrades({...queryParamsRef.current, page, page_size: pageSize});
  }, [loadGrades]);

  // Handle create grade
  const handleCreateGrade = useCallback(() => {
    setEditingGrade(undefined);
    setFormOpen(true);
  }, []);

  // Handle edit grade
  const handleEditGrade = useCallback((grade: GradeLevel) => {
    setEditingGrade(grade);
    setFormOpen(true);
  }, []);

  // Handle form submit
  const handleFormSubmit = useCallback(async (data: GradeLevelFormData) => {
    try {
      setFormLoading(true);
      
      if (editingGrade) {
        const response = await gradeApi.updateGrade(editingGrade.id, data);
        if (response.success) {
          toast.success('年级更新成功');
          setFormOpen(false);
          await loadGrades();
        }
      } else {
        const response = await gradeApi.createGrade(data);
        if (response.success) {
          toast.success('年级创建成功');
          setFormOpen(false);
          await loadGrades();
        }
      }
    } catch (error: any) {
      console.error('Failed to save grade:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  }, [editingGrade, loadGrades]);

  // Handle delete grade
  const handleDeleteGrade = useCallback((id: string) => {
    setGradeToDelete(id);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeleteGrade = useCallback(async () => {
    if (!gradeToDelete) return;

    try {
      const response = await gradeApi.deleteGrade(gradeToDelete);
      if (response.success) {
        toast.success('年级删除成功');
        await loadGrades();
      }
    } catch (error: any) {
      console.error('Failed to delete grade:', error);
      toast.error(error.response?.data?.message || '删除失败');
    } finally {
      setDeleteDialogOpen(false);
      setGradeToDelete(null);
    }
  }, [gradeToDelete, loadGrades]);

  // Handle toggle status
  const handleToggleStatus = useCallback(async (id: string, isActive: boolean) => {
    try {
      const response = await gradeApi.toggleGradeStatus(id, isActive);
      if (response.success) {
        toast.success(`年级已${isActive ? '启用' : '禁用'}`);
        await loadGrades();
      }
    } catch (error: any) {
      console.error('Failed to toggle grade status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  }, [loadGrades]);

  // Handle export
  const handleExport = useCallback(async () => {
    try {
      const blob = await gradeApi.exportGrades(queryParamsRef.current);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `grades-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export grades:', error);
      toast.error('导出接口开发中');
    }
  }, []);

  return {
    // State
    grades,
    loading,
    queryParams,
    pagination,
    formOpen,
    editingGrade,
    formLoading,
    deleteDialogOpen,
    gradeToDelete,
    stageCode,
    
    // Actions
    loadGrades,
    handleSearch,
    handleFilterChange,
    handlePageChange,
    handleCreateGrade,
    handleEditGrade,
    handleFormSubmit,
    handleDeleteGrade,
    confirmDeleteGrade,
    handleToggleStatus,
    handleExport,
    
    // Setters
    setFormOpen,
    setDeleteDialogOpen,
  };
}; 