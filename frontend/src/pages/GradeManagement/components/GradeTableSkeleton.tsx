import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const GradeTableSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">年级代码</TableHead>
              <TableHead className="w-[120px]">年级名称</TableHead>
              <TableHead className="w-[100px]">学段代码</TableHead>
              <TableHead className="w-[80px] text-center">排序级别</TableHead>
              <TableHead className="w-[80px] text-center">学生数量</TableHead>
              <TableHead className="w-[80px] text-center">班级数量</TableHead>
              <TableHead className="w-[80px] text-center">状态</TableHead>
              <TableHead className="w-[150px]">创建时间</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                {Array.from({ length: 9 }).map((_, cellIndex) => (
                  <TableCell key={cellIndex}>
                    <div className="h-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default GradeTableSkeleton; 