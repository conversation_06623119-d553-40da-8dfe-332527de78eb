import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Download, ArrowLeft } from 'lucide-react';

interface GradeHeaderProps {
  stageCode?: string | null;
  onCreateGrade: () => void;
  onExport: () => void;
}

const GradeHeader: React.FC<GradeHeaderProps> = ({
  stageCode,
  onCreateGrade,
  onExport
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        {stageCode && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => window.history.back()}
            className="px-0 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回学段管理
          </Button>
        )}
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">
            {stageCode ? `${stageCode} - 年级管理` : '年级管理'}
          </h1>
          {stageCode && (
            <span className="text-sm text-muted-foreground">
              当前学段: {stageCode}
            </span>
          )}
        </div>
      </div>
      <div className="flex space-x-2">
        <Button variant="outline" onClick={onExport}>
          <Download className="w-4 h-4 mr-2" />
          导出
        </Button>
        <Button onClick={onCreateGrade}>
          <Plus className="w-4 h-4 mr-2" />
          新增年级
        </Button>
      </div>
    </div>
  );
};

export default GradeHeader; 