import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Trash2, Eye, EyeOff, BarChart3 } from 'lucide-react';
import { GradeLevel } from '@/types/grade';

interface GradeTableRowProps {
  grade: GradeLevel;
  onEdit: (grade: GradeLevel) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
}

const GradeTableRow: React.FC<GradeTableRowProps> = ({
  grade,
  onEdit,
  onDelete,
  onToggleStatus
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <TableRow className="hover:bg-muted/50 transition-colors">
      <TableCell className="font-mono">
        <Badge variant="outline" className="text-xs">
          {grade.code}
        </Badge>
      </TableCell>
      
      <TableCell className="font-medium">
        <div className="flex flex-col">
          <span>{grade.name}</span>
          {grade.description && (
            <span className="text-xs text-muted-foreground truncate max-w-[200px]">
              {grade.description}
            </span>
          )}
        </div>
      </TableCell>
      
      <TableCell>
        <Badge variant="secondary" className="text-xs">
          {grade.education_stage_code || '-'}
        </Badge>
      </TableCell>
      
      <TableCell className="text-center">
        <Badge variant="outline" className="text-xs">
          {grade.order_level}
        </Badge>
      </TableCell>
      
      <TableCell className="text-center">
        <span className="text-sm font-medium">{grade.student_count}</span>
      </TableCell>
      
      <TableCell className="text-center">
        <span className="text-sm font-medium">{grade.class_count}</span>
      </TableCell>
      
      <TableCell className="text-center">
        <Badge variant={grade.is_active ? "default" : "secondary"}>
          {grade.is_active ? "启用" : "禁用"}
        </Badge>
      </TableCell>
      
      <TableCell className="text-sm text-muted-foreground">
        {formatDate(grade.created_at)}
      </TableCell>
      
      <TableCell className="text-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => onEdit(grade)}>
              <Edit className="mr-2 h-4 w-4" />
              编辑年级
            </DropdownMenuItem>
            
            <DropdownMenuItem 
              onClick={() => onToggleStatus(grade.id, !grade.is_active)}
              className={grade.is_active ? "text-orange-600" : "text-green-600"}
            >
              {grade.is_active ? (
                <>
                  <EyeOff className="mr-2 h-4 w-4" />
                  禁用年级
                </>
              ) : (
                <>
                  <Eye className="mr-2 h-4 w-4" />
                  启用年级
                </>
              )}
            </DropdownMenuItem>
            
            <DropdownMenuItem 
              onClick={() => onDelete(grade.id)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              删除年级
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
};

export default GradeTableRow; 