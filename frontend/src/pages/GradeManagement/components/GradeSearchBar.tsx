import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, Filter, X } from 'lucide-react';
import { GradeLevelQueryParams, GRADE_SORT_OPTIONS, GRADE_STATUS_OPTIONS } from '@/types/grade';
import ViewToggle from './ViewToggle';

type ViewMode = 'table' | 'card';

interface GradeSearchBarProps {
  queryParams: GradeLevelQueryParams;
  viewMode: ViewMode;
  onSearch: (searchTerm: string) => void;
  onFilterChange: (key: keyof GradeLevelQueryParams, value: any) => void;
  onClearFilters: () => void;
  onViewModeChange: (mode: ViewMode) => void;
}

const GradeSearchBar: React.FC<GradeSearchBarProps> = ({
  queryParams,
  viewMode,
  onSearch,
  onFilterChange,
  onClearFilters,
  onViewModeChange
}) => {
  const [searchInput, setSearchInput] = React.useState(queryParams.search || '');
  const [showFilters, setShowFilters] = React.useState(false);

  const handleSearch = () => {
    onSearch(searchInput);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const hasActiveFilters = queryParams.is_active !== undefined || 
                          queryParams.order_by !== undefined || 
                          queryParams.order_direction !== undefined;

  return (
    <div className="space-y-4">
      {/* Search Bar and View Toggle */}
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-3 flex-1">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索年级名称或代码..."
              className="pl-10 pr-4"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleSearch} className="px-6">
              搜索
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              筛选
            </Button>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                onClick={onClearFilters}
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
                清除
              </Button>
            )}
          </div>
        </div>
        
        <ViewToggle
          viewMode={viewMode}
          onViewModeChange={onViewModeChange}
        />
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg border">
          <div className="space-y-2">
            <label className="text-sm font-medium">状态筛选</label>
            <Select
              value={queryParams.is_active === undefined ? 'all' : String(queryParams.is_active)}
              onValueChange={(value) => 
                onFilterChange('is_active', value === 'all' ? undefined : value === 'true')
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="全部状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                {GRADE_STATUS_OPTIONS.map((option) => (
                  <SelectItem key={String(option.value)} value={String(option.value)}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">排序字段</label>
            <Select
              value={queryParams.order_by || ''}
              onValueChange={(value) => onFilterChange('order_by', value || undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择排序字段" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">默认排序</SelectItem>
                {GRADE_SORT_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">排序方向</label>
            <Select
              value={queryParams.order_direction || ''}
              onValueChange={(value) => onFilterChange('order_direction', value || undefined)}
              disabled={!queryParams.order_by}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择排序方向" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">升序 ↑</SelectItem>
                <SelectItem value="desc">降序 ↓</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  );
};

export default GradeSearchBar; 