import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Trash2, Eye, EyeOff, Users, BookOpen } from 'lucide-react';
import { GradeLevel } from '@/types/grade';

interface GradeCardViewProps {
  grades: GradeLevel[];
  onEdit: (grade: GradeLevel) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
}

const GradeCardView: React.FC<GradeCardViewProps> = ({
  grades,
  onEdit,
  onDelete,
  onToggleStatus
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {grades.map((grade) => (
        <Card key={grade.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs font-mono">
                    {grade.code}
                  </Badge>
                  <Badge variant={grade.is_active ? "default" : "secondary"} className="text-xs">
                    {grade.is_active ? "启用" : "禁用"}
                  </Badge>
                </div>
                <h3 className="font-semibold text-lg">{grade.name}</h3>
                {grade.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {grade.description}
                  </p>
                )}
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={() => onEdit(grade)}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑年级
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem 
                    onClick={() => onToggleStatus(grade.id, !grade.is_active)}
                    className={grade.is_active ? "text-orange-600" : "text-green-600"}
                  >
                    {grade.is_active ? (
                      <>
                        <EyeOff className="mr-2 h-4 w-4" />
                        禁用年级
                      </>
                    ) : (
                      <>
                        <Eye className="mr-2 h-4 w-4" />
                        启用年级
                      </>
                    )}
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem 
                    onClick={() => onDelete(grade.id)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除年级
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">学段代码</span>
                <Badge variant="secondary" className="text-xs">
                  {grade.education_stage_code || '-'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">排序级别</span>
                <Badge variant="outline" className="text-xs">
                  {grade.order_level}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">学生</span>
                  <span className="font-medium">{grade.student_count}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">班级</span>
                  <span className="font-medium">{grade.class_count}</span>
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground border-t pt-2">
                创建于 {formatDate(grade.created_at)}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default GradeCardView; 