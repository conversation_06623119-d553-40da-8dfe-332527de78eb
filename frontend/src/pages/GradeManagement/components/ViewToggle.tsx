import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Table, Grid3X3 } from 'lucide-react';

type ViewMode = 'table' | 'card';

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

const ViewToggle: React.FC<ViewToggleProps> = ({ viewMode, onViewModeChange }) => {
  return (
    <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
      <Button
        variant={viewMode === 'table' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewModeChange('table')}
        className="h-8 px-3"
      >
        <Table className="h-4 w-4 mr-2" />
        表格
      </Button>
      <Button
        variant={viewMode === 'card' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewModeChange('card')}
        className="h-8 px-3"
      >
        <Grid3X3 className="h-4 w-4 mr-2" />
        卡片
      </Button>
    </div>
  );
};

export default ViewToggle; 