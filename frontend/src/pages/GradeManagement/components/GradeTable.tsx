import React from 'react';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { GradeLevel } from '@/types/grade';
import GradeTableRow from './GradeTableRow';
import GradeTableSkeleton from './GradeTableSkeleton';
import GradeEmptyState from './GradeEmptyState';

interface GradeTableProps {
  grades: GradeLevel[];
  loading: boolean;
  onEdit: (grade: GradeLevel) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  onCreateGrade: () => void;
  onClearSearch: () => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  hasSearchQuery: boolean;
}

const GradeTable: React.FC<GradeTableProps> = ({
  grades,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  onCreateGrade,
  onClearSearch,
  pagination,
  hasSearchQuery
}) => {
  if (loading) {
    return <GradeTableSkeleton />;
  }

  if (!Array.isArray(grades) || grades.length === 0) {
    return (
      <GradeEmptyState
        onCreateGrade={onCreateGrade}
        onClearSearch={onClearSearch}
        hasSearchQuery={hasSearchQuery}
      />
    );
  }

  const totalPages = pagination ? Math.ceil(pagination.total / pagination.pageSize) : 0;

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">年级代码</TableHead>
              <TableHead className="w-[120px]">年级名称</TableHead>
              <TableHead className="w-[100px]">学段代码</TableHead>
              <TableHead className="w-[80px] text-center">排序级别</TableHead>
              <TableHead className="w-[80px] text-center">学生数量</TableHead>
              <TableHead className="w-[80px] text-center">班级数量</TableHead>
              <TableHead className="w-[80px] text-center">状态</TableHead>
              <TableHead className="w-[150px]">创建时间</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {grades.map((grade) => (
              <GradeTableRow
                key={grade.id}
                grade={grade}
                onEdit={onEdit}
                onDelete={onDelete}
                onToggleStatus={onToggleStatus}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            共 {pagination.total} 条记录，第 {pagination.current} / {totalPages} 页
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
                  className={pagination.current <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                if (totalPages <= 5) {
                  return (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => pagination.onChange(page, pagination.pageSize)}
                        isActive={page === pagination.current}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  );
                }
                
                // Show first page, last page, current page, and pages around current
                if (page === 1 || page === totalPages || 
                    (page >= pagination.current - 1 && page <= pagination.current + 1)) {
                  return (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => pagination.onChange(page, pagination.pageSize)}
                        isActive={page === pagination.current}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  );
                }
                
                // Show ellipsis
                if (page === 2 || page === totalPages - 1) {
                  return (
                    <PaginationItem key={page}>
                      <span className="px-3 py-2 text-muted-foreground">...</span>
                    </PaginationItem>
                  );
                }
                
                return null;
              })}
              
              <PaginationItem>
                <PaginationNext
                  onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
                  className={pagination.current >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default GradeTable;