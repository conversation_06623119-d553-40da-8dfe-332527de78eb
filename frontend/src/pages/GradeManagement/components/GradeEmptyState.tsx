import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Search } from 'lucide-react';

interface GradeEmptyStateProps {
  onCreateGrade: () => void;
  onClearSearch: () => void;
  hasSearchQuery: boolean;
}

const GradeEmptyState: React.FC<GradeEmptyStateProps> = ({
  onCreateGrade,
  onClearSearch,
  hasSearchQuery
}) => {
  if (hasSearchQuery) {
    return (
      <div className="text-center py-12">
        <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium text-muted-foreground mb-2">
          未找到匹配的年级
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          请尝试调整搜索条件或清除筛选器
        </p>
        <Button variant="outline" onClick={onClearSearch}>
          清除搜索条件
        </Button>
      </div>
    );
  }

  return (
    <div className="text-center py-12">
      <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
        <svg
          className="h-12 w-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={1}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-muted-foreground mb-2">
        暂无年级数据
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        开始创建第一个年级来管理您的教育体系
      </p>
      <Button onClick={onCreateGrade}>
        <Plus className="mr-2 h-4 w-4" />
        新增年级
      </Button>
    </div>
  );
};

export default GradeEmptyState; 