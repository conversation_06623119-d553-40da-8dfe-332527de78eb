import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { useGradeManagement } from './hooks/useGradeManagement';
import GradeHeader from './components/GradeHeader';
import GradeSearchBar from './components/GradeSearchBar';
import GradeTable from './components/GradeTable';
import GradeCardView from './components/GradeCardView';
import GradeForm from './components/GradeForm';
import GradeStatistics from './components/GradeStatistics';


type ViewMode = 'table' | 'card';

const GradeManagementPage: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  
  // 使用年级管理 hook
  const {
    grades,
    loading,
    queryParams,
    pagination,
    formOpen,
    editingGrade,
    formLoading,
    deleteDialogOpen,
    gradeToDelete,
    stageCode,
    handleSearch,
    handleFilterChange,
    handlePageChange,
    handleCreateGrade,
    handleEditGrade,
    handleFormSubmit,
    handleDeleteGrade,
    confirmDeleteGrade,
    handleToggleStatus,
    handleExport,
    setFormOpen,
    setDeleteDialogOpen,
  } = useGradeManagement();

  const handleClearFilters = () => {
    handleFilterChange('search', '');
    handleFilterChange('is_active', undefined);
    handleFilterChange('stage_code', '');
  };

  const hasSearchQuery = Boolean(queryParams.search);

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <GradeHeader
        stageCode={stageCode}
        onCreateGrade={handleCreateGrade}
        onExport={handleExport}
      />



      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">年级列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">年级列表</CardTitle>
              
              <GradeSearchBar
                queryParams={queryParams}
                viewMode={viewMode}
                onSearch={handleSearch}
                onFilterChange={handleFilterChange}
                onClearFilters={handleClearFilters}
                onViewModeChange={setViewMode}
              />
            </CardHeader>
            
            <CardContent>
              {viewMode === 'table' ? (
                <GradeTable
                  grades={grades}
                  loading={loading}
                  onEdit={handleEditGrade}
                  onDelete={handleDeleteGrade}
                  onToggleStatus={handleToggleStatus}
                  onCreateGrade={handleCreateGrade}
                  onClearSearch={handleClearFilters}
                  pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    onChange: handlePageChange,
                  }}
                  hasSearchQuery={hasSearchQuery}
                />
              ) : (
                <GradeCardView
                  grades={grades}
                  onEdit={handleEditGrade}
                  onDelete={handleDeleteGrade}
                  onToggleStatus={handleToggleStatus}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <GradeStatistics />
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除该年级吗？此操作不可恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteGrade}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Grade Form Modal */}
      <GradeForm
        grade={editingGrade}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />
    </div>
  );
};

export default GradeManagementPage;