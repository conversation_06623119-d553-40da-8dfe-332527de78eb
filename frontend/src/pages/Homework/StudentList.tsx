import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { homeworkApi } from '@/services/homeworkApi';
import { homeworkPapersApi } from '@/services/homeworkPapersApi';
import { homeworkStudentsApi } from '@/services/homeworkStudentsApi';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import { TeachingAid, teachingAidApi } from '@/types';
import { Homework, HomeworkStatusEnum, UpdateHomeworkParams } from '@/types/homework';
import { HomeworkStudentsStatusEnum, HomeworkStudentsWithStudentBaseInfo, PageStudentsByHomeworkIdParams } from '@/types/homeworkStudents';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import {HomeworkClassSummary, TeachingClassesDetail} from '@/types/teachingClasses';
import { ChevronRight, RefreshCw, Trash2, Users } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import BatchBindToHomework from './components/BatchBindToHomework';
import { AdministrativeClassesApi } from '@/services/administrativeClassesApi';
import { AdministrativeClassesDetail } from '@/types/administrativeClasses';
/**
 * TODO 根据班级查询学生待开发
 * TODO 后端待联查学科组名字
 */
const DetailPage: React.FC = () => {
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const schema_name = identityInfo?.schema_name || '';
  const navigate = useNavigate();

  const { homeworkId } = useParams<{ homeworkId: string }>();
  // 查询条件
  const [searchName, setSearchName] = useState('');
  const [searchAdminClass, setSearchAdminClass] = useState('all');
  const [searchNumber, setSearchNumber] = useState('');
  // 分页
  const [queryParams, setQueryParams] = useState<PageStudentsByHomeworkIdParams>({
    homework_id: homeworkId as String,
    page_params: {
      page: 1,
      page_size: 10,
    },
    name_like: undefined,
    student_number: undefined,
    administrative_class_id: undefined,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  // 对话框
  const [isBatchAddDialogOpen, setIsBatchAddDialogOpen] = useState(false);
  const [isBatchUnbindDialogOpen, setIsBatchUnbindDialogOpen] = useState(false);
  const [deleteStudentDialog, setDeleteStudentDialog] = useState<{ open: boolean; student?: any }>({ open: false });
  //数据
  const [homework, setHomework] = useState<Homework>();
  const [students, setStudents] = useState<HomeworkStudentsWithStudentBaseInfo[]>([]);
  const [classList, setClassList] = useState<TeachingClassesDetail[]>([]);
  const [homeworkClassMap, setHomeworkClassMap] = useState<Record<string, HomeworkClassSummary>>({})
  const [textbookList, setTextbookList] = useState<TeachingAid[]>([]);
  const [administrativeClasses, setAdministrativeClasses] = useState<AdministrativeClassesDetail[]>([]);
  // 选中学生id数组
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  // 是否全选
  const isAllSelected = students.length > 0 && selectedIds.length === students.length;
  useEffect(() => {
    if (homeworkId) {
      loadInitialData();

    }
  }, [homeworkId]);
  const loadInitialData = async () => {
    //测试查询所有教辅
    teachingAidApi.getTeachingAids().then((res) => {
      console.log('textbooks', res);
      setTextbookList(res);
    });
    //加载学生数据
    loadPageStudents();
    //根据id查询作业信息
    homeworkApi.getHomeworkById(tenantId, schema_name, homeworkId as string).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setHomework(data);
    });
    //查询教学班列表
    //查询教学班列表
    homeworkStudentsApi.getHomeworkClassList(tenantId, schema_name, homeworkId as string).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      const classMap = (data??[]).reduce<Record<string, HomeworkClassSummary>>((acc, u) => {
        acc[u.class_id.toString()] = u;
        return acc;
      }, {});
      setHomeworkClassMap(classMap);
    })
    TeachingClassesApi.getUserClassList(tenantId, schema_name).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setClassList(Array.isArray(data) ? data : []);
    });
    loadAdministrativeClasses();
  };
  //根据作业id分页查询学生信息
  const loadPageStudents = async (params?: Partial<PageStudentsByHomeworkIdParams>) => {
    const finalParams = {
      ...queryParams,
      ...params,
    }
    homeworkStudentsApi.pageStudentsByHomeworkId(tenantId, schema_name, finalParams).then(res => {
      const { success, data, message, pagination } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setStudents(Array.isArray(data) ? data : []);
      setPagination({
        current: pagination?.page || 1,
        pageSize: pagination?.page_size || 10,
        total: pagination?.total || 0,
        totalPages: pagination?.total_pages || 1,
      });
    })
  }
  const loadAdministrativeClasses = async () => {
    AdministrativeClassesApi.getUserClassList(tenantId, schema_name).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setAdministrativeClasses(Array.isArray(data) ? data : []);
    });
  }
  const handlePageChange = (page: number, pageSize?: number) => {
    loadPageStudents({
      ...queryParams,
      page_params: {
        page,
        page_size: pageSize || queryParams.page_params.page_size,
      },
    });
  };
  const handleSearch = () => {
    loadPageStudents({
      ...queryParams,
      name_like: searchName,
      student_number: searchNumber,
      administrative_class_id: searchAdminClass === 'all' ? undefined : searchAdminClass,
    });
  };

  // 单个勾选/取消
  const handleSelect = (id: string) => {
    setSelectedIds((prev) => (prev.includes(id) ? prev.filter((_id) => _id !== id) : [...prev, id]));
  };
  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(students.map((s) => s.student_base_info?.id.toString() || ''));
    } else {
      setSelectedIds([]);
    }
  };
  //移除学生
  const handleUnbind = async () => {
    if (selectedIds.length === 0) {
      return;
    }
    const { success, message } = await homeworkStudentsApi.batchUnbindStudentsFromHomework(tenantId, schema_name, {
      homework_id: homeworkId as String,
      student_id_list: selectedIds,
    });
    if (!success) {
      console.error('报错:' + message);
      return;
    }
    setSelectedIds([]);
    setIsBatchUnbindDialogOpen(false);
    loadInitialData();
  };

  const handleBatchBindStudent = async (studentIds: string[], classId: string) => {
    homeworkStudentsApi.batchBindStudentsToHomework(tenantId, schema_name, {
      homework_id: homeworkId as String,
      student_id_list: studentIds,
      class_id: classId,
    }).then(res => {
      const { success, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setIsBatchAddDialogOpen(false);
      loadInitialData();
    })
  }

  // 删除学生
  const openDeleteStudentDialog = (student: any) => {
    setDeleteStudentDialog({ open: true, student });
  };
  const handleDeleteStudent = () => {
    // 实际开发时调用接口
    setDeleteStudentDialog({ open: false });
  };

  const getStatus = (status: HomeworkStudentsStatusEnum) => {
    switch (status) {
      case HomeworkStudentsStatusEnum.Unsubmitted:
        return <Badge variant={'default'}>缺考</Badge>;
      case HomeworkStudentsStatusEnum.Error:
        return <Badge variant={'destructive'}>异常</Badge>;
      case HomeworkStudentsStatusEnum.Done:
        return <Badge variant={'outline'}>已绑定</Badge>;
      default:
        return '未知';
    }
  };

  return (
    <div className="space-y-6">
      <div>
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
          <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate('/homework-management')}>
            作业管理
          </Button>
          <ChevronRight className="h-4 w-4" />
          <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate(`/homework-setting/${homeworkId}`)}>
            {homework?.homework_name}
          </Button>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground font-medium">学生列表</span>
        </nav>
      </div>
      {/* 删除学生对话框 */}
      <Dialog open={deleteStudentDialog.open} onOpenChange={(open) => setDeleteStudentDialog({ open })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认移除学生</DialogTitle>
            <DialogDescription>你确定要移除学生 {deleteStudentDialog.student?.student_base_info?.name} 吗？</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setDeleteStudentDialog({ open: false })}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteStudent}>
              移除
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* 学生列表 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            {/* 搜索栏 */}
            <div className="flex gap-4 justify-center items-center">
              <div>
                <Input placeholder="搜索学生姓名" value={searchName} onChange={(e) => setSearchName(e.target.value)} className="max-w-xs" />
              </div>
              <div>
                <Input placeholder="学号" value={searchNumber} onChange={(e) => setSearchNumber(e.target.value)} className="max-w-xs" />
              </div>
              <Select value={searchAdminClass} onValueChange={setSearchAdminClass}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="班级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部班级</SelectItem>
                  {Object.entries(homeworkClassMap).map(([cid, item]) => (
                    <SelectItem key={cid} value={cid}>
                      {item.class_name + " (" + item.student_count + "人)" }
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={handleSearch}>
                <RefreshCw className="h-4 w-4 mr-2" />
                查询
              </Button>
            </div>
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={() => setIsBatchAddDialogOpen(true)}>
              <Users className="h-4 w-4 mr-2" />
              批量添加学生
            </Button>
            <Button variant="destructive" size="sm" className={selectedIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''} onClick={() => setIsBatchUnbindDialogOpen(true)}>
              <Users className="h-4 w-4 mr-2" />
              批量解绑学生
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* 学生表格 */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-8">
                    <Checkbox checked={isAllSelected} onCheckedChange={handleSelectAll} />
                  </TableHead>
                  <TableHead>学生姓名</TableHead>
                  <TableHead>班级</TableHead>
                  <TableHead>学号</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>性别</TableHead>
                  <TableHead>电话</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {students.map((student) => (
                  <TableRow key={student.id.toString()}>
                    <TableCell>
                      <Checkbox checked={selectedIds.includes(student.student_base_info?.id.toString() || '')} onCheckedChange={() => handleSelect(student.student_base_info?.id.toString() || '')} />
                    </TableCell>
                    <TableCell className="font-medium">
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0 h-auto min-h-0 text-blue-600 font-bold"
                        onClick={() => {
                          if (student.status !== HomeworkStudentsStatusEnum.Unsubmitted) {
                            navigate(`/homework-scan/scan-image-preview/${student.student_base_info?.id}`, {
                              state: {
                                entry: 'student',
                              },
                            });
                          }
                        }}
                      >
                        {student.student_base_info?.student_name}
                      </Button>
                    </TableCell>
                    <TableCell>{ student.class_id.toString() in homeworkClassMap ? homeworkClassMap[student.class_id.toString()].class_name : '-'}</TableCell>
                    <TableCell>{student.student_base_info?.student_number}</TableCell>
                    <TableCell>{getStatus(student.status as HomeworkStudentsStatusEnum)}</TableCell>
                    <TableCell>{student.student_base_info?.gender}</TableCell>
                    <TableCell>{student.student_base_info?.phone}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" onClick={() => openDeleteStudentDialog(student)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        {pagination.total > 0 && (
          <div className="flex flex-wrap items-center gap-4 mt-2 ml-6 mb-4">
            <div className="text-sm text-gray-500 whitespace-nowrap">
              显示第 {(pagination.current - 1) * pagination.pageSize + 1} - {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条记录
            </div>
            {/* 分页大小选择器和跳页 */}
            <div className="flex flex-wrap gap-3 items-center">
              <select className="border rounded px-2 py-1 text-sm min-w-[80px]" value={pagination.pageSize} onChange={(e) => handlePageChange(1, Number(e.target.value))}>
                {[5, 10, 20, 50, 100].map((size) => (
                  <option key={size} value={size}>
                    {size} 条/页
                  </option>
                ))}
              </select>
              <span className="text-sm">跳转到</span>
              <input
                type="number"
                min={1}
                max={Math.ceil(pagination.total / pagination.pageSize)}
                defaultValue={pagination.current}
                onBlur={(e) => {
                  let page = Number(e.target.value);
                  if (page < 1) page = 1;
                  if (page > Math.ceil(pagination.total / pagination.pageSize)) page = Math.ceil(pagination.total / pagination.pageSize);
                  if (page !== pagination.current) {
                    handlePageChange(page, pagination.pageSize);
                  }
                }}
                className="border rounded px-2 py-1 w-16 text-sm text-center"
              />
              <span className="text-sm">页</span>
            </div>
            {/* 分页按钮 */}
            <div className="flex items-center">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => {
                        if (pagination.current > 1) {
                          handlePageChange(pagination.current - 1, pagination.pageSize);
                        }
                      }}
                      className={pagination.current === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                  {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink isActive={page === pagination.current} onClick={() => handlePageChange(page, pagination.pageSize)} className="cursor-pointer">
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => {
                        if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                          handlePageChange(pagination.current + 1, pagination.pageSize);
                        }
                      }}
                      className={pagination.current === Math.ceil(pagination.total / pagination.pageSize) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        )}
      </Card>
      {/* 批量添加学生对话框 */}
      <BatchBindToHomework open={isBatchAddDialogOpen} onOpenChange={setIsBatchAddDialogOpen} onBind={handleBatchBindStudent} classList={classList} tenantId={tenantId} tenantName={schema_name} />
      {/* 批量解绑学生对话框*/}
      <Dialog open={isBatchUnbindDialogOpen} onOpenChange={(open) => setIsBatchUnbindDialogOpen(open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认移除学生</DialogTitle>
            <DialogDescription>
              你确定要移除学生 {selectedIds.map((id) => students.find((s) => s.student_base_info?.id.toString() === id)?.student_base_info?.student_name).join(',')} 吗？
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsBatchUnbindDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleUnbind}>
              移除
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DetailPage;
