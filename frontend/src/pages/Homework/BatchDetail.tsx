import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronRight, AlertTriangle, CheckCircle, Upload, Edit, RefreshCw, Trash2, Eye, Plus } from 'lucide-react';
import { scanApi, BatchImageList, Page, type rebindStudentParams } from '@/services/scanApi';
import PaginationComponent from '@/components/Pagination';
import { useHomeworkStore } from '@/stores';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import ImageOperationDialog from './components/ImageOperationDialog';
import { homeworkStudentsApi } from '@/services/homeworkStudentsApi';
import { HomeworkStudentsWithStudentBaseInfo, FindAllByHomeworkIdParams } from '@/types/homeworkStudents';
import { usePageFullWidth } from '@/hooks/useLayoutWidth.tsx';
import { toast } from 'sonner';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

interface BatchDetailProps {}

const BatchDetail: React.FC<BatchDetailProps> = () => {
  usePageFullWidth();
  const { batchNo } = useParams<{ batchNo: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const tenantName = identityInfo?.schema_name || '';

  // 从路由状态获取批次信息
  const { batch } = location.state || {};

  // 状态管理
  const [batchImages, setBatchImages] = useState<BatchImageList[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const [batchData, setBatchData] = useState<BatchImageList[] | null>(null);

  const { homework } = useHomeworkStore();
  // 批次详情筛选和配置
  const [imageStatusFilter, setImageStatusFilter] = useState('all');
  const [imagesPerRow, setImagesPerRow] = useState(2);
  // 分页状态
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(10);

  const statusList = [
    {
      label: '未发布',
      value: 'Undistributed',
    },
    {
      label: '未绑定学生信息',
      value: 'Unbound',
    },
    {
      label: '重复页',
      value: 'Duplicate',
    },
    {
      label: '异常错误',
      value: 'Error',
    },
    {
      label: '已完成',
      value: 'Done',
    },
  ];

  // 图片预览相关状态
  const [previewImageIndex, setPreviewImageIndex] = useState(0);
  const [previewImages, setPreviewImages] = useState<Page[]>([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  // 图片操作相关状态
  const [operationDialogOpen, setOperationDialogOpen] = useState(false);
  const [operationType, setOperationType] = useState<'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add' | null>(null);
  const [currentOperationImage, setCurrentOperationImage] = useState<Page | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [editPageNumber, setEditPageNumber] = useState<number>(1);
  const [editStudentId, setEditStudentId] = useState<string>('');
  const [editStudentName, setEditStudentName] = useState<string>('');
  const [isMarkingError, setIsMarkingError] = useState<boolean>(false);
  const [studentSearchResults, setStudentSearchResults] = useState<HomeworkStudentsWithStudentBaseInfo[]>([]);
  const [students, setStudents] = useState<HomeworkStudentsWithStudentBaseInfo[]>([]);
  const [currentOperationGroup, setCurrentOperationGroup] = useState<BatchImageList | null>(null);
  const [bindStudent, setBindStudent] = useState<HomeworkStudentsWithStudentBaseInfo | null>(null);

  // 获取批次详情
  useEffect(() => {
    if (!batchNo || !tenantName || !homework.id) return;
    fetchBatchDetail(page, pageSize);
    getStudentList();
  }, [batchNo, tenantName, homework.id]);

  const fetchBatchDetail = async (page: number, pageSize: number, status?: string) => {
    let params: any = {
      tenantName,
      exam_id: homework.id,
      page: page,
      page_size: pageSize,
    };
    if (batchNo !== 'all') {
      params.batch_number = batchNo;
    }
    if (status || imageStatusFilter !== 'all') {
      params.status = status || imageStatusFilter;
    }
    if (params.status && params.status === 'all') {
      params.status = undefined;
    }
    try {
      setLoading(true);
      const response = await scanApi.getBatchDetail(params);
      if (response.success && response.data) {
        const { data, pagination } = response;
        setBatchData(Array.isArray(data) ? data : []);
        setTotal(pagination.total);
        setPage(pagination.page);
        setPageSize(pagination.page_size);
      }
    } catch (error) {
      console.error('获取批次详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStudentList = async () => {
    const params: FindAllByHomeworkIdParams = {
      homework_id: homework.id,
      page_params: {
        page: 1,
        page_size: 9999,
      },
    };
    homeworkStudentsApi.findAllByHomeworkId(tenantId, tenantName, params).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setStudents(Array.isArray(data) ? data : []);
    });
  };

  // 过滤图片
  const filteredImages = batchImages.filter((group) => {
    const matchesSearch = searchQuery === '' || group.student_number.toLowerCase().includes(searchQuery.toLowerCase()) || group.student_id.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || group.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // 获取所有图片用于导航
  const allImages: Page[] = [];
  filteredImages.forEach((group) => {
    group.pages.forEach((page) => {
      allImages.push(page);
    });
  });

  // 分页处理
  const totalItems = filteredImages.length;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedImages = filteredImages.slice(startIndex, endIndex);

  // 处理图片点击
  const handleImageClick = (image: Page, group: BatchImageList) => {
    const imageIndex = allImages.findIndex((img) => img.id === image.id);
    navigate(`/scan-image-preview/${image.id}`, {
      state: {
        batchNo,
        imageIndex,
        allImages,
        group,
      },
    });
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal':
        return 'bg-green-50 border-green-200';
      case 'abnormal':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'abnormal':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  // 文件上传处理
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 保存操作
  const handleSaveOperation = () => {
    // 这里应该调用API保存操作
    if (operationType === 'edit_student' && bindStudent?.student_id) {
      let params: rebindStudentParams = {
        exam_id: homework.id as string,
        student_id: currentOperationGroup?.student.student_id || undefined,
        target_student_id: bindStudent?.student_id as string,
        page_ids: currentOperationGroup?.pages.map((page) => page.id) as string[],
      };
      scanApi.rebindStudentToLeaf(tenantName, params).then((res) => {
        const { success, message } = res;
        if (!success) {
          toast.error(message);
          console.error('报错:' + message);
          return;
        }
        toast.success('绑定成功');
        fetchBatchDetail(page, pageSize);
        setOperationDialogOpen(false);
        setOperationType(null);
        setCurrentOperationImage(null);
        setUploadFile(null);
        setUploadPreview(null);
      });
    }
  };

  // 打开图片预览
  const handleImagePreview = (group: BatchImageList) => {
    navigate(`/homework-scan/scan-image-preview/${group.id}`, {
      state: {
        entry: 'batchImage',
        student: group.student,
      },
    });
  };

  // 图片操作处理函数
  const handleImageOperation = (type: 'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add', image?: Page, group?: BatchImageList) => {
    if (type === 'delete') {
      setCurrentOperationImage(image || null);
      setDeleteConfirmOpen(true);
    } else {
      setOperationType(type);
      setCurrentOperationImage(image || null);
      if (type === 'edit_page' && image) {
        setEditPageNumber(image.page_num);
      }
      if (type === 'edit_student') {
        setEditStudentId(group?.student.student_number || '');
        setEditStudentName(group?.student.student_name || '');
        setCurrentOperationGroup(group || null);
      }
      setOperationDialogOpen(true);
    }
  };

  const paginationChange = async (page: number, pageSize: number) => {
    await new Promise((resolve) => {
      setPage(page);
      setPageSize(pageSize);
      setTimeout(resolve, 0);
    });
    fetchBatchDetail(page, pageSize);
  };

  const getStatus = (group: BatchImageList, image: Page) => {
    if (group.status === 'Unbound') {
      return {
        text: '未绑定学生信息',
        color: 'bg-blue-600/80',
      };
    }
    if (group.status === 'Done') {
      return {
        text: '已完成',
        color: 'bg-green-600/80',
      };
    }
    if (group.status === 'Duplicate' && image.is_duplicate) {
      return {
        text: '重复页',
        color: 'bg-yellow-600/80',
      };
    }
    if (group.status === 'Error' && image.is_abnormal) {
      return {
        text: '异常错误',
        color: 'bg-red-600/80',
      };
    } else {
      return {
        text: '已完成',
        color: 'bg-green-600/80',
      };
    }
  };

  const getStudentInfo = (group: BatchImageList) => {
    const { student } = group;

    const parts = [];

    if (student.student_number !== null) {
      parts.push(student.student_number);
    }
    if (student.class_name !== null) {
      parts.push(student.class_name);
    }
    if (student.student_name !== null) {
      parts.push(student.student_name);
    }
    if (group.status === 'Unbound' || !student.student_id) {
      parts.push('未绑定');
    }
    const resultText = parts.join(' ');
    const resultColor = student.student_id !== null ? 'text-green-700' : 'text-red-500';

    return {
      text: resultText,
      color: resultColor,
    };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
        <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate('/homework-management')}>
          作业管理
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate(`/homework-setting/${homework.id}`)}>
          {homework?.homework_name}
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate(-1)}>
          试卷扫描
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="text-foreground font-medium">批次详情 - {batchNo}</span>
      </nav>

      {/* 批次详情展开区域 */}
      {batchData && (
        <div className="mt-6 p-4 border rounded-lg bg-muted/20">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">批次详情：{batchNo}</h3>
              {/* <Button variant="outline" className="ml-3">
                重新同步
              </Button> */}
            </div>
          </div>
          {(() => {
            // 筛选图片
            const filteredImages = batchData.flatMap((item) => item.pages);

            return (
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
                  <div>
                    <span className="font-medium">总页数：</span>
                    <span>{batch.total} 页</span>
                  </div>
                  <div>
                    <span className="font-medium">扫描时间：</span>
                    <span>{new Date(batch.created_at).toLocaleString()}</span>
                  </div>
                </div>

                {/* 图片预览区域 */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-center mb-4">
                    <div className="flex items-center gap-4">
                      {/* 状态筛选 */}
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">状态:</span>
                        <Select
                          value={imageStatusFilter}
                          onValueChange={(v) => {
                            setImageStatusFilter(v);
                            fetchBatchDetail(1, pageSize, v);
                          }}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">全部</SelectItem>
                            {statusList.map((item) => (
                              <SelectItem key={item.value} value={item.value}>
                                {item.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* 每行显示组数配置 */}
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">每行:</span>
                        <Select value={imagesPerRow.toString()} onValueChange={(v) => setImagesPerRow(Number(v))}>
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1组</SelectItem>
                            <SelectItem value="2">2组</SelectItem>
                            <SelectItem value="3">3组</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* 批次分页 */}
                      <div>
                        <PaginationComponent
                          total={total}
                          current={page}
                          pageSize={pageSize}
                          onChange={(page, size) => {
                            paginationChange(page, size);
                          }}
                          showSizeChanger={true}
                          showTotal={true}
                        />
                      </div>
                    </div>
                  </div>

                  <div
                    className="grid gap-4"
                    style={{
                      gridTemplateColumns: `repeat(${imagesPerRow}, 1fr)`,
                    }}
                  >
                    {batchData?.map((group, groupIndex) => (
                      <Card key={groupIndex} className="p-4 shadow-md">
                        <div className="flex gap-2">
                          {group.pages.map((image: Page, imageIndex) => (
                            <div key={image.id} className="flex-1 max-w-[50%]">
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="relative h-full border rounded-lg overflow-hidden group cursor-pointer" onClick={() => handleImagePreview(group)}>
                                    <img src={image.url as string} alt={`页面 ${image.page_num}`} className="w-full h-full object-cover" />

                                    {/* 图片信息覆盖层 */}
                                    <div className={`absolute bottom-0 left-0 right-0 text-white p-2 text-xs ${getStatus(group, image).color}`}>
                                      <div>页码：{image.page_num}</div>
                                      <div>状态：{getStatus(group, image).text}</div>
                                    </div>
                                  </div>
                                </TooltipTrigger>
                                {image.abnormal_reason && <TooltipContent>{image.abnormal_reason}</TooltipContent>}
                              </Tooltip>

                              {/* 垂直分割线 */}
                              {imageIndex === 0 && <div className="absolute top-0 bottom-0 right-0 w-px bg-border" />}
                            </div>
                          ))}
                        </div>

                        {/* 学号信息和修改按钮 */}
                        <div className="mt-3 pt-3 border-t flex items-center justify-between">
                          <div className="text-sm">
                            <span className="text-muted-foreground">学号：</span>
                            <span className={`font-medium ${getStudentInfo(group).color}`}>{getStudentInfo(group).text}</span>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-7 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleImageOperation('edit_student', undefined, group);
                            }}
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            修改
                          </Button>
                        </div>
                      </Card>
                    ))}

                    {/* 添加新图片按钮 */}
                    <div
                      className="flex-shrink-0 w-48 h-64 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                      onClick={() => handleImageOperation('add')}
                    >
                      <div className="text-center text-muted-foreground">
                        <Plus className="h-8 w-8 mx-auto mb-2" />
                        <div className="text-sm">添加图片</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* 图片操作Dialog */}
      <ImageOperationDialog
        open={operationDialogOpen}
        onOpenChange={setOperationDialogOpen}
        operationType={operationType}
        currentImage={currentOperationImage}
        uploadFile={uploadFile}
        uploadPreview={uploadPreview}
        editPageNumber={editPageNumber}
        editStudentId={editStudentId}
        editStudentName={editStudentName}
        isMarkingError={isMarkingError}
        studentSearchResults={studentSearchResults}
        onFileUpload={handleFileUpload}
        onPageNumberChange={setEditPageNumber}
        onStudentIdChange={(value) => {
          setEditStudentId(value);
          // 这里应该调用API进行学生搜索
          if (value) {
            const studentList = students.filter((item) => {
              return item.student_base_info?.student_number.includes(value);
            });
            setStudentSearchResults(studentList);
          } else {
            setStudentSearchResults([]);
          }
        }}
        onStudentNameChange={setEditStudentName}
        onMarkingErrorChange={setIsMarkingError}
        onStudentSelect={(student) => {
          setBindStudent(student);
          setEditStudentId(student.student_base_info?.student_number as string);
          setEditStudentName(student.student_base_info?.student_name as string);
          setStudentSearchResults([]);
        }}
        onSave={handleSaveOperation}
        onCancel={() => setOperationDialogOpen(false)}
      />
    </div>
  );
};

export default BatchDetail;
