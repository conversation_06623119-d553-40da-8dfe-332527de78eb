import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Users, CheckCircle, Clock, ChevronRight, MessageSquare } from 'lucide-react';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { StudentListModal } from './components/StudentListModal';
import { AnswerStatisticsChart } from './components/AnswerStatisticsChart';
import { QuestionReview, StudentInfo as QuestionStudentInfo } from './components/QuestionDisplayCard';

// 扩展的学生信息接口，包含原始评分数据
interface ExtendedStudentInfo extends QuestionStudentInfo {
  scoreData?: ScoreVo; // 原始的评分数据，包含答题图片等信息
}
import MathRenderer from '../../components/math/MathRenderer';
import { homeworkReviewPageApi } from '@/services/homeworkReviewApi';
import { homeworkApi } from '@/services/homeworkApi';

// 后端API返回的数据结构
interface ClassAnalysisResponse {
  done_students: StudentInfo[];
  error_students: StudentInfo[];
  unsubmitted_students: StudentInfo[];
  content_answer_analysis_list: ContentAnswerAnalysis[];
}

// 学生信息接口
interface StudentInfo {
  student_id: string;
  student_name: string;
  student_number: string;
}

// 试题内容分析接口 - 匹配Rust enum结构
type ContentAnswerAnalysis = {
  Text: string;
} | {
  AnswerAnalysis: AnswerAnalysis;
};

// 答题分析接口 - 匹配Rust struct结构
interface AnswerAnalysis {
  criterion: ScoringCriterion;
  error_scored: ScoreVo[];
  pending_scored: ScoreVo[];
  avg_score: number | null;
  scored_map: Record<string, ScoreVo[]>;
}

// 评分标准接口 - 匹配Rust struct结构
interface ScoringCriterion {
  id: string;
  scoring_type: ScoringCriteriaTypeEnum;
  mode: string | null;
  criteriaName: string | null; // 对应 #[serde(rename = "criteriaName")] name
  answer: string | null;
  score: number;
  ocr_work_id: string | null;
  check_work_id: string | null;
  questionTips: string | null; // 对应 #[serde(rename = "questionTips")] question_tips
}

// 评分标准类型枚举 - 匹配Rust enum结构
type ScoringCriteriaTypeEnum = "Match" | "AI" | "Manual";

// 分数VO接口 - 匹配Rust struct结构
interface ScoreVo {
  id: string;
  criteria_id: string;
  score: number;
  status: ScoreStatus;
  blocks: ScoreBlock[];
  details: ScoreDetailVo[];
  student: StudentBase | null;
  answer: string;
}

// 分数状态枚举 - 匹配Rust enum结构
type ScoreStatus = 
  | "Undistributed"
  | "Distributed" 
  | "Excepted"
  | "Done"
  | "CheckedError"
  | "CheckedCorrect";

// 分数块接口
interface ScoreBlock {
  [key: string]: any;
}

// 分数详情VO接口
interface ScoreDetailVo {
  [key: string]: any;
}

// 学生基础信息接口
interface StudentBase {
  student_id: string;
  student_name: string;
  student_number: string;
}






const HomeworkReviewPage: React.FC = () => {
  const { homeworkId, classId } = useParams<{ homeworkId: string; classId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const className = location.state?.className || '未知班级';

  const [loading, setLoading] = useState(true);
  const [reviewData, setReviewData] = useState<ClassAnalysisResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [homeworkName, setHomeworkName] = useState<string>('作业分析'); // 从API获取的作业名称
  const [isUnsubmittedModalOpen, setIsUnsubmittedModalOpen] = useState(false);

  // 学生列表模态框状态
  const [isStudentListModalOpen, setIsStudentListModalOpen] = useState(false);
  const [modalStudents, setModalStudents] = useState<ExtendedStudentInfo[]>([]);
  const [modalTitle, setModalTitle] = useState('');
  const [modalQuestionId, setModalQuestionId] = useState('');
  const [modalCriterion, setModalCriterion] = useState<ScoringCriterion | undefined>(undefined);
  
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantName = identityInfo?.tenant_name || '';
  const tenantId = identityInfo?.tenant_id || '';
  const schemaName = identityInfo?.schema_name || '';

  // 处理文本中的换行符，将 \n 转换为 MathJax 能识别的换行
  const processTextForMathJax = (text: string | null | undefined): string => {
    if (!text) return '';
    return text.replace(/\\n/g, '<br />').replace(/\n/g, '<br />');
  };

  // 获取作业信息
  const fetchHomeworkInfo = async () => {
    if (!tenantId || !tenantName || !homeworkId) {
      return;
    }

    try {
      const response = await homeworkApi.getHomeworkById(tenantId, schemaName, homeworkId);

      if (response.success && response.data) {
        const homeworkNameValue = String(response.data.homework_name);
        setHomeworkName(homeworkNameValue);
      }
    } catch (error) {
      // 保持默认名称，不影响主要功能
    }
  };

  // 数据加载
  useEffect(() => {
    const loadData = async () => {
      await fetchHomeworkInfo();
      await loadReviewData();
    };
    loadData();
  }, [homeworkId, classId]);

  const loadReviewData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (!tenantName || !homeworkId || !classId) {
        throw new Error('缺少必要的参数');
      }

      // 调用新的班级分析接口
      const response = await homeworkReviewPageApi.getReviewData(schemaName, homeworkId, classId);
      
      if (response.success && response.data) {
        // 直接使用API返回的原始数据结构
        setReviewData(response.data);
      } else {
        throw new Error(response.message || '获取数据失败');
      }
    } catch (err: any) {
      setError(err.message || '加载作业讲评数据失败');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    navigate(-1);
  };



  // 获取评分类型标签
  const getScoringTypeLabel = (type: ScoringCriteriaTypeEnum) => {
    switch (type) {
      case 'Match': return '匹配评分';
      case 'AI': return 'AI评分';
      case 'Manual': return '人工评分';
      default: return '未知类型';
    }
  };

  // 获取评分类型样式
  const getScoringTypeVariant = (type: ScoringCriteriaTypeEnum) => {
    switch (type) {
      case 'Match': return 'default';
      case 'AI': return 'secondary';
      case 'Manual': return 'outline';
      default: return 'outline';
    }
  };

  // 判断是否为客观题（选择题）
  const isMultipleChoice = (criterion: ScoringCriterion, scoredMap: Record<string, ScoreVo[]>) => {
    // 如果是Match类型，且学生答案都是单个字母，则认为是客观题
    if (criterion.scoring_type === 'Match') {
      const allAnswers = Object.values(scoredMap).flat().map(score => score.answer?.trim() || '');
      const validAnswers = allAnswers.filter(answer => answer.length > 0);
      if (validAnswers.length === 0) return false;

      // 检查是否都是单个字母（A、B、C、D等）
      return validAnswers.every(answer => /^[A-Z]$/i.test(answer));
    }
    return false;
  };

  // 转换ScoreVo为ExtendedStudentInfo格式
  const convertScoreVoToStudentInfo = (scoreVo: ScoreVo, answerLabel?: string): ExtendedStudentInfo => {
    return {
      student_id: scoreVo.student?.student_id || scoreVo.id,
      student_name: scoreVo.student?.student_name || '未知学生',
      student_number: scoreVo.student?.student_number || '',
      answer_image_url: scoreVo.blocks?.[0]?.answer_block_url || '', // 从blocks中获取图片URL
      score: scoreVo.score,
      grading_details: answerLabel ? `选择了${answerLabel}，得分${scoreVo.score}分` : `得分${scoreVo.score}分`,
      scoreData: scoreVo // 保存原始评分数据
    };
  };

  // 将AnswerAnalysis转换为QuestionReview格式
  const convertAnswerAnalysisToQuestionReview = (analysis: AnswerAnalysis, index: number): QuestionReview => {
    const { criterion, scored_map } = analysis;
    const isChoice = isMultipleChoice(criterion, scored_map);

    if (isChoice) {
      // 客观题：统计选项分布
      const choiceMap = new Map<string, ScoreVo[]>();

      Object.values(scored_map).flat().forEach(scoreVo => {
        const choice = scoreVo.answer?.trim().toUpperCase() || '未填涂';
        if (!choiceMap.has(choice)) {
          choiceMap.set(choice, []);
        }
        choiceMap.get(choice)!.push(scoreVo);
      });

      const totalStudents = Object.values(scored_map).flat().length;
      const choices_distribution = Array.from(choiceMap.entries()).map(([choice, students]) => ({
        choice,
        count: students.length,
        percentage: totalStudents > 0 ? (students.length / totalStudents) * 100 : 0,
        students: students.map(scoreVo => convertScoreVoToStudentInfo(scoreVo, `选项 ${choice}`))
      }));

      return {
        question_id: criterion.id,
        question_number: index + 1,
        question_type: 'multiple_choice',
        question_content: processTextForMathJax(criterion.criteriaName) || `题目${index + 1}`,
        scoring_criteria: processTextForMathJax(criterion.answer) || '',
        max_score: criterion.score,
        statistics: {
          total_students: totalStudents,
          choices_distribution,
          score_distribution: []
        }
      };
    } else {
      // 主观题：使用分数分布
      const totalStudents = Object.values(scored_map).flat().length;
      const score_distribution = Object.entries(scored_map).map(([score, students]) => ({
        score: parseFloat(score),
        count: students.length,
        percentage: totalStudents > 0 ? (students.length / totalStudents) * 100 : 0,
        students: students.map(scoreVo => convertScoreVoToStudentInfo(scoreVo))
      })).sort((a, b) => b.score - a.score);

      return {
        question_id: criterion.id,
        question_number: index + 1,
        question_type: 'subjective',
        question_content: processTextForMathJax(criterion.criteriaName) || `题目${index + 1}`,
        scoring_criteria: processTextForMathJax(criterion.answer) || '',
        max_score: criterion.score,
        statistics: {
          total_students: totalStudents,
          choices_distribution: [],
          score_distribution
        }
      };
    }
  };

  // 处理图表点击事件
  const handleChartClick = (students: ExtendedStudentInfo[], label: string, questionId: string, criterion?: ScoringCriterion) => {
    setModalStudents(students);
    setModalTitle(`${label} - 学生列表`);
    setModalQuestionId(questionId);
    setModalCriterion(criterion);
    setIsStudentListModalOpen(true);
  };



  // 处理未交作业学生列表点击
  const handleUnsubmittedClick = () => {
    setIsUnsubmittedModalOpen(true);
  };

  // 将未交作业学生转换为StudentListModal需要的格式
  const getUnsubmittedStudentsForModal = () => {
    if (!reviewData?.unsubmitted_students) return [];
    return reviewData.unsubmitted_students.map(student => ({
      ...student,
      score: 0, // 未交作业设置为0分
      answer_image_url: '', // 未交作业没有答题图片
      grading_details: '未提交作业'
    }));
  };





  if (loading) {
    return (
      <div className="space-y-6">
        {/* 头部骨架 */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-4 w-48" />
            <Skeleton className="h-8 w-64" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        
        {/* 试题骨架 */}
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <div className="p-6 space-y-4">
              <div className="flex items-center gap-4">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-16" />
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-40 w-full" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-destructive text-lg font-medium mb-2">{error}</div>
          <Button onClick={loadReviewData} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部导航 */}
      <div className="flex items-center justify-between">
        <div>
          <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
            <Button 
              variant="ghost" 
              size="sm" 
              className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
              onClick={() => navigate('/homework-management')}
            >
              作业管理
            </Button>
            <ChevronRight className="h-4 w-4" />
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
              onClick={goBack}
            >
              作业详情
            </Button>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground font-medium">班级讲评</span>
          </nav>
          
          <div className="flex items-center gap-3">
            <MessageSquare className="h-6 w-6 text-primary" />
            <div>
              <h1 className="text-2xl font-bold">{homeworkName}</h1>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">班级：</span>
                  <Badge variant="outline" className="font-medium">
                    {className}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">共</span>
                  <Badge variant="secondary" className="font-medium">
                    {reviewData?.content_answer_analysis_list.length}
                  </Badge>
                  <span className="text-sm text-muted-foreground">项</span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">参与：</span>
                  <Badge variant="default" className="font-medium">
                    {(reviewData?.done_students.length || 0) + (reviewData?.error_students.length || 0) + (reviewData?.unsubmitted_students.length || 0)} 人
                  </Badge>
                </div>

                {reviewData?.done_students && reviewData.done_students.length > 0 && (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-muted-foreground">已完成：</span>
                    <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100 font-medium">
                      {reviewData.done_students.length} 人
                    </Badge>
                  </div>
                )}

                {reviewData?.unsubmitted_students && reviewData.unsubmitted_students.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-orange-600" />
                    <button 
                      onClick={handleUnsubmittedClick}
                      className="flex items-center gap-2 text-orange-600 hover:text-orange-700 transition-colors group"
                    >
                      <span className="text-sm">未交：</span>
                      <Badge variant="default" className="bg-orange-100 text-orange-800 hover:bg-orange-200 font-medium group-hover:bg-orange-200">
                        {reviewData.unsubmitted_students.length} 人
                      </Badge>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <Button onClick={goBack} variant="outline" className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>
      </div>

      {/* 统一内容分析卡片 */}
      <Card className="overflow-hidden">
        <CardHeader className="bg-muted/50">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              <Badge variant="outline">{homeworkName}</Badge>
              <span className="text-sm font-normal text-muted-foreground">
                共 {reviewData?.content_answer_analysis_list.length} 项内容
              </span>
            </CardTitle>
          </div>
        </CardHeader>
        
        <CardContent className="p-6">
          <div className={reviewData?.content_answer_analysis_list.every(item => 'Text' in item) ? "space-y-1" : "space-y-2"}>
            {reviewData?.content_answer_analysis_list.map((item, index) => {
                
              return (
                <div key={index} className={reviewData?.content_answer_analysis_list.every(item => 'Text' in item) ? "pb-1 pt-1 last:pb-0" : "pb-2 last:pb-0"}>
                  {/* 项目标题栏 */}
                  {'AnswerAnalysis' in item && (
                    <div className="flex items-center gap-3 mb-4">
                      <Badge variant={getScoringTypeVariant(item.AnswerAnalysis.criterion.scoring_type)}>
                        {getScoringTypeLabel(item.AnswerAnalysis.criterion.scoring_type)}
                      </Badge>
                      <Badge variant="outline">{item.AnswerAnalysis.criterion.score} 分</Badge>
                      <span className="text-sm text-muted-foreground">
                        平均分：{item.AnswerAnalysis.avg_score?.toFixed(1) || 0} 分
                      </span>
                    </div>
                  )}

                  {/* 项目内容 */}
                  <div className="space-y-1">
                    {/* Text 变体内容 */}
                    {'Text' in item && (
                      <div className="prose prose-sm max-w-none leading-relaxed">
                        <MathRenderer content={processTextForMathJax(item.Text)} />
                      </div>
                    )}

                    {/* AnswerAnalysis 变体内容 */}
                    {'AnswerAnalysis' in item && (
                      <div className="space-y-4">
                        {/* 答题统计 */}
                        <div>
                          <h3 className="font-medium mb-2 flex items-center gap-2">
                            <span className="w-1 h-4 bg-primary rounded"></span>
                            答题统计
                          </h3>

                          {/* 图表可视化 */}
                          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                            <AnswerStatisticsChart
                              question={convertAnswerAnalysisToQuestionReview(item.AnswerAnalysis, index)}
                              onChartClick={(students, label) => handleChartClick(students, label, item.AnswerAnalysis.criterion.id, item.AnswerAnalysis.criterion)}
                            />
                          </div>

                          {/* 详细分布列表 */}
                          <div className="space-y-1">
                            {(() => {
                              const questionData = convertAnswerAnalysisToQuestionReview(item.AnswerAnalysis, index);
                              const isChoice = questionData.question_type === 'multiple_choice';

                              if (isChoice) {
                                // 客观题：显示选项分布
                                return questionData.statistics.choices_distribution.map((choice) => (
                                  <div
                                    key={choice.choice}
                                    className="flex items-center justify-between p-2 bg-muted/30 rounded border hover:bg-muted/50 transition-colors cursor-pointer"
                                    onClick={() => handleChartClick(choice.students, `选项${choice.choice}`, questionData.question_id, item.AnswerAnalysis.criterion)}
                                  >
                                    <div className="flex items-center gap-2">
                                      <Badge variant="secondary" className="text-xs">选项{choice.choice}</Badge>
                                      <span className="text-xs text-muted-foreground">
                                        {choice.count}人 ({choice.percentage.toFixed(1)}%)
                                      </span>
                                    </div>
                                    <div className="flex -space-x-1">
                                      {choice.students.slice(0, 3).map((student, idx) => (
                                        <div key={idx} className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-xs font-medium border border-background">
                                          {student.student_name?.charAt(0) || '?'}
                                        </div>
                                      ))}
                                      {choice.students.length > 3 && (
                                        <div className="w-6 h-6 bg-muted-foreground rounded-full flex items-center justify-center text-background text-xs font-medium border border-background">
                                          +{choice.students.length - 3}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ));
                              } else {
                                // 主观题：显示分数分布
                                return questionData.statistics.score_distribution.map((score) => (
                                  <div
                                    key={score.score}
                                    className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                                    onClick={() => handleChartClick(score.students, `${score.score}分`, questionData.question_id, item.AnswerAnalysis.criterion)}
                                  >
                                    <div className="flex items-center gap-3">
                                      <Badge variant="secondary">{score.score}分</Badge>
                                      <span className="text-sm text-muted-foreground">
                                        {score.count}人 ({score.percentage.toFixed(1)}%)
                                      </span>
                                    </div>
                                    <div className="flex -space-x-1">
                                      {score.students.slice(0, 3).map((student, idx) => (
                                        <div key={idx} className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-xs font-medium border-2 border-background">
                                          {student.student_name?.charAt(0) || '?'}
                                        </div>
                                      ))}
                                      {score.students.length > 3 && (
                                        <div className="w-8 h-8 bg-muted-foreground rounded-full flex items-center justify-center text-background text-xs font-medium border-2 border-background">
                                          +{score.students.length - 3}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ));
                              }
                            })()}
                          </div>
                        </div>
                        
                        {/* 标准答案 */}
                        {item.AnswerAnalysis.criterion.answer && (
                          <div>
                            <h3 className="font-medium mb-3 flex items-center gap-2">
                              <span className="w-1 h-5 bg-green-500 rounded"></span>
                              标准答案
                            </h3>
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                              <div className="prose prose-sm max-w-none leading-relaxed [&>*]:mb-2 [&>p]:mb-1 [&>div]:mb-1">
                                <MathRenderer content={processTextForMathJax(item.AnswerAnalysis.criterion.answer)} />
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 未交作业学生列表模态框 */}
      <StudentListModal
        isOpen={isUnsubmittedModalOpen}
        onClose={() => setIsUnsubmittedModalOpen(false)}
        students={getUnsubmittedStudentsForModal()}
        title={`未交作业学生名单 - ${className}`}
        questionId="unsubmitted"
      />

      {/* 答题统计学生列表模态框 */}
      <StudentListModal
        isOpen={isStudentListModalOpen}
        onClose={() => setIsStudentListModalOpen(false)}
        students={modalStudents}
        title={modalTitle}
        questionId={modalQuestionId}
        criterion={modalCriterion}
      />
    </div>
  );
};

export default HomeworkReviewPage;