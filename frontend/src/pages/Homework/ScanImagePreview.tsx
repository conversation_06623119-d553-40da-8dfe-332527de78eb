import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, Eye, Edit, RotateCcw, Trash2, User, BookAlert, X, Check, Repeat1 } from 'lucide-react';
import { scanApi, BatchDetail, type PageImage } from '@/services/scanApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import MathRenderer from '@/components/math/MathRenderer';
import DetailsDrawer from './components/DetailsDrawer';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Scores, homeworkReviewApi } from '@/services/homeworkReviewApi';
import { toast } from 'sonner';
import RedoSorceDialog from './components/RedoSorceDialog';
import { useHomeworkStore } from '@/stores/useHomeworkStore';
import { usePageFullWidth } from '@/hooks/useLayoutWidth.tsx';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import ImageOperationDialog from './components/ImageOperationDialog';

interface ScanImagePreviewProps {}

const ScanImagePreview: React.FC<ScanImagePreviewProps> = () => {
  usePageFullWidth();
  const { pageId } = useParams<{ pageId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantName = identityInfo?.schema_name || '';
  //获取homeworkId
  const { homework } = useHomeworkStore();
  // 状态管理
  const [batchDetail, setBatchDetail] = useState<BatchDetail | null>(null);
  const [RedoSorceDialogOpen, setRedoSorceDialogOpen] = useState(false);
  //const [currentImageIndex, setCurrentImageIndex] = useState<number>(imageIndex);
  const [loading, setLoading] = useState(false);
  // 编辑分数状态
  const [editingScore, setEditingScore] = useState<{
    id: string;
    score: number;
  }>();
  const [zoomLevel] = useState(1);
  const [currentTab, setCurrentTab] = useState('');
  const [deleteImage, setDeleteImage] = useState<PageImage>();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [operationDialogOpen, setOperationDialogOpen] = useState(false);

  // 获取批次图片详情
  useEffect(() => {
    if (!pageId || !tenantName) return;
    fetchBatchDetail();
  }, [pageId, tenantName, location.state?.entry]);

  // 判断渲染的是原图还是处理图
  useEffect(() => {
    if (batchDetail?.pages.length === 0) return;

    const currentPage = batchDetail?.pages.find((page) => page.rectify_url && page.rectify_url !== '');
    if (currentPage) {
      setCurrentTab('rectify');
    } else {
      setCurrentTab('original');
    }
  }, [batchDetail?.pages]);

  const fetchBatchDetail = async () => {
    try {
      setLoading(true);
      let params;
      location.state?.entry === 'batchImage' ? (params = { sheet_id: pageId }) : (params = { student_id: pageId });
      const response = await scanApi.getBatchImageDetails(tenantName, params);
      if (response.success && response.data) {
        setBatchDetail(response.data);
      }
    } catch (error) {
      console.error('获取批次详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatus = (status: string) => {
    switch (status) {
      case 'Undistributed':
        return { label: '未分发', bgColor: 'bg-gray-600/80', color: 'text-gray-700' };
      case 'Distributed':
        return { label: '进行中', bgColor: 'bg-blue-600/80', color: 'text-blue-700' };
      case 'Excepted':
        return { label: '异常', bgColor: 'bg-red-600/80', color: 'text-red-700' };
      case 'Done':
        return { label: '已完成', bgColor: 'bg-green-600/80', color: 'text-green-700' };
      default:
        return { label: '未知', bgColor: 'bg-yellow-600/80', color: 'text-yellow-700' };
    }
  };

  const getScoringType = (scoring_type: string) => {
    switch (scoring_type) {
      case 'Match':
        return '题卡匹配';
      case 'AI':
        return '自动AI阅卷';
      case 'Manual':
        return '老师手阅(登分)';
      case 'Online':
        return '在线分发阅卷';
      case 'Check':
        return '核查';
      default:
        return '';
    }
  };

  const getPageStatus = (page: PageImage) => {
    return page.is_abnormal ? '异常' : page.is_blank ? '空白页' : '';
  };

  // 编辑分数
  const handleEditScore = (score: Scores) => {
    setEditingScore({ id: score.id, score: score.score });
  };

  // 处理其他操作
  const handlePageEdit = () => {
    console.log('修改页码');
  };

  const handleStudentEdit = () => {
    console.log('修改学号');
  };

  const handleRedoScore = (redo_ocr: boolean, include_done: boolean, include_checked: boolean, selected_criteria_ids: string[]) => {
    if (!batchDetail) return;
    //const criteria_ids = batchDetail.score_or_blocks.map((block) => block.Score.criteria.id);
    const student_ids = batchDetail.student_id ? [batchDetail.student_id] : [];
    const homework_id = homework.id.toString();

    scanApi
      .redoScore(tenantName, {
        criteria_ids: selected_criteria_ids,
        student_ids,
        homework_id,
        redo_ocr,
        include_done,
        include_checked,
      })
      .then((res) => {
        if (res.success) {
          toast.success('重新扫阅任务已提交');
          setRedoSorceDialogOpen(false);
          fetchBatchDetail();
        } else {
          toast.error('重新扫阅任务提交失败');
        }
      })
      .catch((err) => {
        console.error('重新扫阅任务提交失败:', err);
        toast.error('重新扫阅任务提交失败');
      });
  };

  const handleDeleteImage = (image: PageImage) => {
    setDeleteImage(image);
    setIsDeleteDialogOpen(true);
  };

  // 保存分数
  const saveScore = (score?: number) => {
    if (editingScore?.score || editingScore?.score === 0) {
      homeworkReviewApi
        .updateQuestionScores(tenantName, {
          id: editingScore.id,
          score: score !== undefined ? score : editingScore.score,
          // reason: undefined,
        })
        .then((res) => {
          if (res.success) {
            fetchBatchDetail();
          }
          setEditingScore({} as { id: string; score: number });
        });
    } else {
      toast.error('请输入分数');
    }
  };

  const handleDeleteByPageId = () => {
    const pageId = deleteImage?.page_id;
    let params = {
      exam_id: homework.id,
      page_ids: [pageId] as string[],
    };
    scanApi
      .deletePaperScanPage(tenantName, params)
      .then((res) => {
        if (res.success) {
          toast.success('删除成功');
          fetchBatchDetail();
        }
      })
      .catch((err) => {
        toast.error('删除失败');
        console.log('删除失败:', err);
      });
  };

  const getStudentInfo = (batchDetail: BatchDetail) => {
    const { student_number, student_id, student_name } = batchDetail.student || {};

    const parts = [];

    if (student_number !== null) {
      parts.push(student_number);
    }
    if (student_name !== null) {
      parts.push(student_name);
    }
    if (!student_id) {
      parts.push('未绑定');
    }
    const resultText = parts.join(' ');
    const resultColor = student_id && student_id !== null ? 'text-green-700' : 'text-red-500';

    return {
      text: resultText,
      color: resultColor,
    };
  };

  const handleRescan = (page: PageImage) => {
    if (!batchDetail?.pages.length) return;
    scanApi
      .rescanPage(tenantName, {
        exam_id: homework.id,
        batch_number: batchDetail.batch_no && batchDetail.batch_no !== 'all' ? batchDetail.batch_no : undefined,
        pages_ids: [page.page_id],
      })
      .then((res) => {
        if (res.success) {
          toast.success('已开始重新识别，请稍后刷新查看');
          fetchBatchDetail();
        } else {
          toast.error('重新识别失败');
        }
      })
      .catch((err) => {
        toast.error('重新识别失败');
        console.log('重新识别失败:', err);
      });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!batchDetail) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-500">未找到图片详情</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
        <ChevronLeft className="h-4 w-4" />
        <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate(-1)}>
          批次详情 - {batchDetail.batch_no}
        </Button>
      </nav>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-2 gap-6 h-[calc(100vh-200px)]">
        {/* 左侧：原图显示 */}
        <Card className="h-full">
          <div className="flex justify-center w-full">
            <Tabs defaultValue="original" className="h-[20px] mt-1" value={currentTab} onValueChange={(value) => setCurrentTab(value)}>
              <TabsList className="h-[26px]">
                <TabsTrigger value="original" className="h-[20px] px-10 data-[state=active]:bg-black data-[state=active]:text-white">
                  原图
                </TabsTrigger>
                <TabsTrigger value="rectify" className="h-[20px] px-10 data-[state=active]:bg-black data-[state=active]:text-white">
                  处理图
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <CardContent className="p-4 h-full">
            <div className="relative h-full overflow-auto">
              {batchDetail.pages.map((page, index) => (
                <div key={index} className="mb-4 cursor-pointer transition-all duration-200 hover:shadow-md relative group">
                  <div className="absolute top-2 left-2 flex gap-1 z-10">
                    <Tooltip>
                      <TooltipTrigger asChild>{getPageStatus(page) && <Badge variant="destructive">{getPageStatus(page)}</Badge>}</TooltipTrigger>
                      {getPageStatus(page) && <TooltipContent>{page.abnormal_reason}</TooltipContent>}
                    </Tooltip>
                    <div className="text-sm text-gray-500">页码：{page.page_num}</div>
                  </div>
                  <img
                    src={currentTab === 'original' ? page.file_url : page.rectify_url}
                    alt={`页面 ${index + 1}`}
                    className="w-full h-auto transition-transform hover:scale-105"
                    style={{ transform: `scale(${zoomLevel})` }}
                  ></img>
                  {/* 操作按钮 */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0 hover:bg-red-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRescan(page);
                          }}
                        >
                          <Repeat1 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>重新识别</p>
                      </TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteImage(page);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>删除图片</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 右侧：切块信息和操作 */}
        <div className="space-y-4 h-full overflow-auto">
          {/* 基础信息 */}
          <Card>
            <CardContent className="space-y-2 p-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="font-medium">学号:</span>
                <span className={getStudentInfo(batchDetail).color}>{getStudentInfo(batchDetail).text}</span>
              </div>
              <div className="grid grid-cols-5 gap-2">
                <Button size="sm" onClick={handleStudentEdit}>
                  <User className="h-4 w-4 mr-1" />
                  修改学号
                </Button>
                <Button size="sm" onClick={() => setRedoSorceDialogOpen(true)}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  重新评分
                </Button>
                <Button size="sm" onClick={() => fetchBatchDetail()}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  刷新
                </Button>
              </div>
            </CardContent>
          </Card>
          {/* 批量扫阅重新扫描会话 */}
          <RedoSorceDialog
            open={RedoSorceDialogOpen}
            onOpenChange={setRedoSorceDialogOpen}
            criteria_ids={batchDetail.score_or_blocks.map((block) => block.Score.criteria.id)}
            onConfirm={(redo_ocr, include_done, include_checked, selected_criteria_ids) => {
              handleRedoScore(redo_ocr, include_done, include_checked, selected_criteria_ids);
            }}
            onCancel={() => setRedoSorceDialogOpen(false)}
          />
          {/* 切块信息 */}
          <div className="space-y-4">
            {batchDetail.score_or_blocks.map((block, blockIndex) => (
              <Card key={blockIndex}>
                <CardContent className="space-y-4 p-3">
                  <div className="flex items-center gap-5">
                    <div>
                      <div className="text-sm">题号：{block.Score.criteria.criteriaName}</div>
                    </div>
                    <div className="text-sm">
                      状态：
                      <Badge className={getStatus(block.Score.score.status).bgColor}>{getStatus(block.Score.score.status).label}</Badge>
                    </div>
                    <div className="text-sm">总分：{block.Score.criteria.score}</div>
                    <div className="text-sm">评分方式：{getScoringType(block.Score.criteria.scoring_type)}</div>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="sm" className="text-blue-500">
                          <BookAlert className="h-4 w-4 mr-1" />
                          评分标准
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[650px] max-h-[400px] overflow-auto">
                        <div className="text-sm flex">
                          <MathRenderer content={block.Score.criteria.answer} />
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  {/* 显示Block图片 */}
                  {block.Score.score.blocks && block.Score.score.blocks.length > 0 && (
                    <div className="space-y-2">
                      {block.Score.score.blocks.map((blockImg: any, imgIndex: number) => (
                        <div key={imgIndex} className="relative w-full h-auto border rounded">
                          <img src={blockImg.answer_block_url || blockImg} alt={`图片缺失`} className="h-auto object-scale-down" />
                          {imgIndex === 0 && (
                            <div className="absolute top-2 left-2 z-39">
                              {editingScore?.id === block.Score.score.id ? (
                                <div className="bg-white/95 backdrop-blur-sm rounded p-2 shadow-xl border z-50">
                                  <Input
                                    type="number"
                                    value={editingScore?.score}
                                    min={0}
                                    onChange={(e) =>
                                      setEditingScore((prev) =>
                                        prev
                                          ? {
                                              ...prev,
                                              score: Number(e.target.value),
                                            }
                                          : undefined,
                                      )
                                    }
                                    className="w-20 h-8 text-center"
                                  />
                                  <div className="gap-1 mt-2">
                                    <div className="mb-2 gap-1 flex">
                                      <Button
                                        size="sm"
                                        onClick={() => {
                                          saveScore();
                                        }}
                                      >
                                        保存
                                      </Button>
                                      <Button size="sm" variant="outline" onClick={() => setEditingScore({} as { id: string; score: number })}>
                                        取消
                                      </Button>
                                    </div>
                                    <div className="mb-2 gap-1 flex">
                                      <Button
                                        size="sm"
                                        onClick={() => {
                                          saveScore(block.Score.criteria.score);
                                        }}
                                      >
                                        <Check className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          saveScore(0);
                                        }}
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="text-red-600 font-bold text-lg cursor-pointer hover:text-red-700 drop-shadow-lg"
                                  onClick={() => handleEditScore(block.Score.score)}
                                  style={{
                                    textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                                  }}
                                >
                                  {block.Score.score.score}分
                                </div>
                              )}
                            </div>
                          )}
                          {imgIndex === 0 && (
                            <div className="absolute top-2 right-2">
                              <DetailsDrawer
                                details={block.Score.score.details || []}
                                blocks={block.Score.score.blocks}
                                title="评分详情"
                                trigger={
                                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800">
                                    <Eye className={`h-4 w-4 ${getStatus(block.Score.score.status).color}`} />
                                  </Button>
                                }
                              />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>你确定要删除第 {deleteImage?.page_num} 页答题卡吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteByPageId}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 图片操作Dialog */}
      {/* <ImageOperationDialog
        open={operationDialogOpen}
        onOpenChange={setOperationDialogOpen}
        operationType={'edit_page'}
        editStudentId={editStudentId}
        editStudentName={editStudentName}
        studentSearchResults={studentSearchResults}
        onPageNumberChange={() => {}}
        onStudentIdChange={(value) => {
          setEditStudentId(value);
          // 这里应该调用API进行学生搜索
          if (value) {
            const studentList = students.filter((item) => {
              return item.student_base_info?.student_number.includes(value);
            });
            setStudentSearchResults(studentList);
          } else {
            setStudentSearchResults([]);
          }
        }}
        onStudentNameChange={() => {}}
        onMarkingErrorChange={() => {}}
        onStudentSelect={(student) => {
          setBindStudent(student);
          setEditStudentId(student.student_base_info?.student_number as string);
          setEditStudentName(student.student_base_info?.student_name as string);
          setStudentSearchResults([]);
        }}
        onSave={handleSaveOperation}
        onCancel={() => setOperationDialogOpen(false)}
      /> */}
    </div>
  );
};

export default ScanImagePreview;
