import React, { useState, useEffect } from 'react';
import { scan<PERSON><PERSON>, type BatchList, type BatchImageList, type BatchImage, type Page, type ScanStats, type Statistics } from '@/services/scanApi';
import ImageOperationDialog from './components/ImageOperationDialog';
import DeleteConfirmDialog from './components/DeleteConfirmDialog';
import StudentImageDialog from './components/StudentImageDialog';
import PaginationComponent from '@/components/Pagination';
import DraggableImage from '@/components/DraggableImage';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { HomeworkStudentsWithStudentBaseInfo, FindAllByHomeworkIdParams } from '@/types/homeworkStudents';
import { homeworkStudentsApi } from '@/services/homeworkStudentsApi';
import {
  RefreshCw,
  Image as ImageIcon,
  FileImage,
  AlertTriangle,
  Search,
  Upload,
  Edit,
  Trash2,
  Eye,
  Plus,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  CheckCircle,
  Cog,
  UserRoundX,
  CalendarSync,
} from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { useHomeworkStore } from '@/stores';
import { toast } from 'sonner';
import { usePageFullWidth } from '@/hooks/useLayoutWidth.tsx';

// mock scan stats
const mockScanStats: ScanStats = {
  total_scanned: 10,
  pending_processing: 2,
  processing_complete: 8,
  exception_count: 1,
};

// mock paper scans
type PaperScan = {
  id: string;
  student_id: string;
  scan_time: string;
  paper_images: string[];
  scan_quality: string;
  scanner_device: string;
  scan_status: string;
};

const mockStudentList = [
  {
    id: 'stu-001',
    name: '张三',
    class: '初一1班',
    student_no: '2024001',
    school: '第一中学',
    review_status: '已阅',
    exception_status: '正常',
    objective_score: 45,
    subjective_score: 40,
    total_score: 85,
  },
  {
    id: 'stu-002',
    name: '李四',
    class: '初一1班',
    student_no: '2024002',
    school: '第一中学',
    review_status: '未阅',
    exception_status: '异常',
    objective_score: 30,
    subjective_score: 20,
    total_score: 50,
  },
];

const getQualityBadge = (quality: string) => quality;
const getStatusBadge = (status: string) => status;

const HomeworkScan: React.FC = () => {
  const { id: homeworkId } = useParams() as { id: string };
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const tenantName = identityInfo?.schema_name || '';
  const navigate = useNavigate();
  const [scanStats] = useState<ScanStats>(mockScanStats);
  const [paperScans, setPaperScans] = useState<PaperScan[]>();
  const loadPaperScans = () => setPaperScans();
  const [studentScans, setStudentScans] = useState(mockStudentList);
  const [batches, setBatches] = useState<BatchList[]>([]);
  const [filters, setFilters] = useState({
    student_no: '',
    name: '',
    class: '',
    review_status: '',
    exception_status: '',
    is_exception: '',
  });
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);

  // 新增状态
  const [batchSearchQuery, setBatchSearchQuery] = useState('');
  const [batchStatusFilter, setBatchStatusFilter] = useState('all'); // 批次状态筛选
  const [selectedBatch, setSelectedBatch] = useState<BatchImageList[] | null>(null);
  const [currentBatch, setCurrentBatch] = useState<BatchList>();

  const [cardsPerRow, setCardsPerRow] = useState(4);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);

  // 图片操作相关状态
  const [operationDialogOpen, setOperationDialogOpen] = useState(false);
  const [operationType, setOperationType] = useState<'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add' | null>(null);
  const [currentOperationImage, setCurrentOperationImage] = useState<Page | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);

  // 表单状态
  const [editPageNumber, setEditPageNumber] = useState<number>(1);
  const [editStudentId, setEditStudentId] = useState<string>('');
  const [editStudentName, setEditStudentName] = useState<string>('');
  const [isMarkingError, setIsMarkingError] = useState<boolean>(false);
  const [studentSearchResults, setStudentSearchResults] = useState<HomeworkStudentsWithStudentBaseInfo[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

  // 批次操作相关状态
  const [batchDeleteConfirmOpen, setBatchDeleteConfirmOpen] = useState(false);
  const [currentBatchToDelete, setCurrentBatchToDelete] = useState<BatchList | null>(null);

  // 批次详情筛选和配置
  const [imageStatusFilter, setImageStatusFilter] = useState('all');
  const [imagesPerRow, setImagesPerRow] = useState(2);

  // 图片预览相关状态
  const [previewImageIndex, setPreviewImageIndex] = useState(0);
  const [previewImages, setPreviewImages] = useState<Page[]>([]);
  const [zoomLevel, setZoomLevel] = useState(1);

  // 学生图片详情弹窗状态
  const [studentImageDialogOpen, setStudentImageDialogOpen] = useState(false);
  const [currentStudentImages, setCurrentStudentImages] = useState<Page[]>([]);
  const [currentStudentInfo, setCurrentStudentInfo] = useState<{
    name: string;
    id: string;
    class: string;
  } | null>(null);

  // 分页状态
  const [batchPage, setBatchPage] = useState(1);
  const [batchPageSize, setBatchPageSize] = useState(10);
  const [studentPage, setStudentPage] = useState(1);
  const [studentPageSize, setStudentPageSize] = useState(20);
  const [students, setStudents] = useState<HomeworkStudentsWithStudentBaseInfo[]>([]);
  const { homework } = useHomeworkStore();
  const [error, setError] = useState<string | null>(null);
  const [statistics, setStatistics] = useState<Statistics>();

  useEffect(() => {
    // getStudentList();
    getBatchNumberList();
    getStatistics();
  }, [homeworkId]);

  const getStudentList = async () => {
    const params: FindAllByHomeworkIdParams = {
      homework_id: homeworkId as String,
      page_params: {
        page: 1,
        page_size: 9999,
      },
    };
    homeworkStudentsApi.findAllByHomeworkId(tenantId, tenantName, params).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setStudents(Array.isArray(data) ? data : []);
    });
  };

  const getBatchNumberList = async () => {
    scanApi.getBatchNumberList(tenantName, homeworkId).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setBatches(Array.isArray(data) ? data : []);
    });
  };

  const getStatistics = async () => {
    scanApi.getStatistics(tenantName, homeworkId).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setStatistics(data);
    });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ ...filters, [key]: value });
  };

  const filteredScans = studentScans.filter((scan) => {
    return (
      (filters.student_no === '' || scan.student_no.includes(filters.student_no)) &&
      (filters.name === '' || scan.name.includes(filters.name)) &&
      (filters.class === '' || scan.class === filters.class) &&
      (filters.review_status === 'all' || scan.review_status === filters.review_status) &&
      (filters.exception_status === 'all' || scan.exception_status === filters.exception_status) &&
      (filters.is_exception === 'all' || (filters.is_exception === '是' ? scan.exception_status === '异常' : scan.exception_status !== '异常'))
    );
  });

  // 学生分页数据
  const studentTotal = filteredScans.length;
  const studentStartIndex = (studentPage - 1) * studentPageSize;
  const studentEndIndex = studentStartIndex + studentPageSize;

  const handleShowImages = (images: string[]) => {
    setCurrentImages(images);
    setImageDialogOpen(true);
  };

  const handleRefreshBatches = () => {
    // setBatches([...mockBatches]);
    getBatchNumberList();
  };

  // 批次搜索过滤
  const filteredBatches = batches.filter((batch) => {
    const matchesSearch = batchSearchQuery === '' || batch.batch_no.toLowerCase().includes(batchSearchQuery.toLowerCase());
    // ||
    // batch.scanner.toLowerCase().includes(batchSearchQuery.toLowerCase());

    const matchesStatus = batchStatusFilter === 'all' || (batchStatusFilter === 'normal' && batch.status === 'normal') || (batchStatusFilter === 'abnormal' && batch.status === 'abnormal');

    return matchesSearch && matchesStatus;
  });

  // 批次分页数据
  const batchTotal = filteredBatches.length;
  const batchStartIndex = (batchPage - 1) * batchPageSize;
  const batchEndIndex = batchStartIndex + batchPageSize;
  const paginatedBatches = filteredBatches.slice(batchStartIndex, batchEndIndex);

  // 处理批次选中
  const handleBatchSelect = async (batch: BatchList) => {
    // getBatchDetail(batch.batch_no);
    // setSelectedBatch(selectedBatch === batchNo ? null : batchNo);
    setCurrentBatch(batch);
    navigate(`/homework-scan/batch-detail/${batch.batch_no === '全部' ? 'all' : batch.batch_no}`, {
      state: {
        batch,
      },
    });
  };

  // 图片操作处理函数
  const handleImageOperation = (type: 'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add', image?: Page, group?: BatchImageList) => {
    if (type === 'delete') {
      setCurrentOperationImage(image || null);
      setDeleteConfirmOpen(true);
    } else {
      setOperationType(type);
      setCurrentOperationImage(image || null);
      if (type === 'edit_page' && image) {
        setEditPageNumber(image.page_num);
      }
      if (type === 'edit_student' && image) {
        setEditStudentId(group?.student_id || '');
        setEditStudentName(group?.student_number || '');
      }
      setOperationDialogOpen(true);
    }
  };

  // 文件上传处理
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 保存操作
  const handleSaveOperation = () => {
    // 这里应该调用API保存操作
    console.log('保存操作:', operationType, currentOperationImage, uploadFile);
    setOperationDialogOpen(false);
    setOperationType(null);
    setCurrentOperationImage(null);
    setUploadFile(null);
    setUploadPreview(null);
  };

  // 批次删除确认
  const handleBatchDeleteConfirm = (batch: BatchList) => {
    setCurrentBatchToDelete(batch);
    setBatchDeleteConfirmOpen(true);
  };

  // 执行批次删除
  const handleBatchDelete = () => {
    if (currentBatchToDelete) {
      let params: any = {
        exam_id: homeworkId,
      };
      if (currentBatchToDelete.batch_no !== '全部' && currentBatchToDelete.exam_type === 'homework') {
        params.batch_number = currentBatchToDelete.batch_no;
      }
      scanApi.clearBatchPaperInfo(tenantName, params).then((res) => {
        const { success, message } = res;
        if (!success) {
          console.error('报错:' + message);
          return;
        }
        toast.success('删除成功');
        setBatchDeleteConfirmOpen(false);
        setCurrentBatchToDelete(null);
        getBatchNumberList();
      });
    }
  };

  // 打开图片预览
  const handleImagePreview = (image: Page, allImages: Page[]) => {
    const index = allImages.findIndex((img) => img.id === image.id);
    setPreviewImages(allImages);
    setPreviewImageIndex(index);
    setPreviewImageUrl(image.url);
    setZoomLevel(1);
  };

  // 切换到上一张图片
  const handlePrevImage = () => {
    if (previewImageIndex > 0) {
      const newIndex = previewImageIndex - 1;
      setPreviewImageIndex(newIndex);
      setPreviewImageUrl(previewImages[newIndex].url);
      setZoomLevel(1);
    }
  };

  // 切换到下一张图片
  const handleNextImage = () => {
    if (previewImageIndex < previewImages.length - 1) {
      const newIndex = previewImageIndex + 1;
      setPreviewImageIndex(newIndex);
      setPreviewImageUrl(previewImages[newIndex].url);
      setZoomLevel(1);
    }
  };

  // 处理滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    setZoomLevel((prev) => Math.max(0.5, Math.min(3, prev + delta)));
  };

  // 打开学生图片详情
  const handleShowStudentImages = (student: any) => {
    // Mock学生图片数据
    const mockStudentImages: BatchImage[] = [
      {
        id: 'student_img1',
        url: 'http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047_origin/Doc1750906124_19.jpg',
        page_number: 1,
        student_id: student.student_no,
        student_name: student.name,
        status: 'normal',
        created_at: '2024-07-21T10:00:00Z',
        updated_at: '2024-07-21T10:00:00Z',
      },
      {
        id: 'student_img2',
        url: 'http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047/Doc1750906124_-19.jpg',
        page_number: 2,
        student_id: student.student_no,
        student_name: student.name,
        status: 'ocr_error',
        created_at: '2024-07-21T10:00:00Z',
        updated_at: '2024-07-21T10:00:00Z',
      },
    ];

    setCurrentStudentImages(mockStudentImages);
    setCurrentStudentInfo({
      name: student.name,
      id: student.student_no,
      class: student.class,
    });
    setStudentImageDialogOpen(true);
  };

  // 处理学生查询
  const handleSearchStudents = () => {
    // 这里应该调用API进行学生查询
    console.log('查询学生:', filters);
    // 重置分页到第一页
    setStudentPage(1);
  };

  // 处理生成任务
  const handleGenerateTask = () => {
    // 这里应该调用API生成任务
    console.log('生成任务');
    alert('任务生成成功');
  };

  return (
    <>
      <div>
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
          <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate('/homework-management')}>
            作业管理
          </Button>
          <ChevronRight className="h-4 w-4" />
          <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate(`/homework-setting/${homework.id}`)}>
            {homework?.homework_name}
          </Button>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground font-medium">试卷扫描</span>
        </nav>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已扫描</CardTitle>
            <FileImage className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_scan_count || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理中</CardTitle>
            <Cog className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.processing_count || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理完成</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.completed_count || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">未绑定学号</CardTitle>
            <UserRoundX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_unbound_count || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">重复张数</CardTitle>
            <CalendarSync className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_duplicate_count || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">扫描异常</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{statistics?.error_scan_count || 0}</div>
          </CardContent>
        </Card>
      </div>
      <Tabs defaultValue="preview" className="w-full">
        {/* <TabsList className="grid w-96 grid-cols-2 mb-4">
          <TabsTrigger value="preview">扫描预览</TabsTrigger>
          <TabsTrigger value="student">学生列表</TabsTrigger>
        </TabsList> */}
        {/* 批次扫描预览 */}
        <TabsContent value="preview" className="space-y-4" style={{ marginTop: '10px' }}>
          {/* 搜索和刷新控制栏 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-center space-y-0 pb-0">
              <div className="flex items-center justify-center gap-4 mb-6">
                <span className="text-sm text-muted-foreground">状态:</span>
                <Select value={batchStatusFilter} onValueChange={setBatchStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="normal">正常</SelectItem>
                    <SelectItem value="abnormal">异常</SelectItem>
                  </SelectContent>
                </Select>
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索批次号" value={batchSearchQuery} onChange={(e) => setBatchSearchQuery(e.target.value)} className="w-64" />
                </div>
                <Button variant="outline" onClick={handleRefreshBatches}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div
                className="grid gap-4"
                style={{
                  gridTemplateColumns: `repeat(${cardsPerRow}, 1fr)`,
                }}
              >
                {batches.map((batch) => (
                  <Card
                    key={batch.batch_no}
                    className={`p-4 cursor-pointer transition-all duration-200 hover:shadow-md relative group ${currentBatch?.batch_no === batch.batch_no ? 'border-blue-500 border-2' : ''} ${
                      batch.status === 'normal' ? 'bg-green-50/50 hover:bg-green-50/70' : 'bg-orange-50/50 hover:bg-orange-50/70'
                    }`}
                    onClick={() => handleBatchSelect(batch)}
                  >
                    {/* 背景图标 */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none">
                      {batch.status === 'normal' ? <CheckCircle className="h-24 w-24" /> : <AlertTriangle className="h-24 w-24" />}
                    </div>
                    {/* 操作按钮 */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleBatchDeleteConfirm(batch);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>批量删除</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    <CardHeader className="p-0 pb-1">
                      <CardTitle className="text-base font-semibold">
                        批次号：
                        <span className="font-medium">{batch.batch_no}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0 space-y-1">
                      <div className="text-sm text-muted-foreground">总页数：{batch.total} 页</div>
                      {batch.batch_no !== '全部' && <div className="text-sm text-muted-foreground">扫描时间：{new Date(batch.created_at).toLocaleString()}</div>}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 批次详情展开区域 */}
          {selectedBatch && (
            <div className="mt-6 p-4 border rounded-lg bg-muted/20">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">批次详情：{currentBatch?.batch_no}</h3>
                  <Button variant="outline" className="ml-3">
                    重新同步
                  </Button>
                </div>

                <Button variant="ghost" size="sm" onClick={() => setSelectedBatch(null)}>
                  收起
                </Button>
              </div>
              {(() => {
                const batch = batches.find((b) => b.batch_no === currentBatch?.batch_no);
                if (!batch) return null;

                // 筛选图片
                const filteredImages = selectedBatch.flatMap((item) => item.pages);

                // 将图片两张一组
                // const imageGroups = [];
                // for (let i = 0; i < filteredImages.length; i += 2) {
                //   imageGroups.push(filteredImages.slice(i, i + 2));
                // }

                return (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
                      <div>
                        <span className="font-medium">总页数：</span>
                        <span>{batch.total} 页</span>
                      </div>
                      <div>
                        <span className="font-medium">扫描时间：</span>
                        <span>{new Date(batch.created_at).toLocaleString()}</span>
                      </div>
                      {/* <div>
                        <span className="font-medium">状态：</span>
                        <span>
                          {batch.status === "normal" ? "正常" : "异常"}
                        </span>
                        {batch.status === "abnormal" && batch.error_count && (
                          <span className="ml-2 text-orange-600 font-medium">
                            (待修改: {batch.error_count})
                          </span>
                        )}
                      </div> */}
                    </div>

                    {/* 图片预览区域 */}
                    <div className="border-t pt-4">
                      <div className="flex items-center justify-center mb-4">
                        <div className="flex items-center gap-4">
                          {/* 状态筛选 */}
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">状态:</span>
                            <Select value={imageStatusFilter} onValueChange={setImageStatusFilter}>
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">全部</SelectItem>
                                <SelectItem value="normal">正常</SelectItem>
                                <SelectItem value="ocr_error">OCR识别异常</SelectItem>
                                <SelectItem value="blank">空白页</SelectItem>
                                <SelectItem value="duplicate">重复页</SelectItem>
                                <SelectItem value="pending">待分发</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* 每行显示组数配置 */}
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">每行:</span>
                            <Select value={imagesPerRow.toString()} onValueChange={(v) => setImagesPerRow(Number(v))}>
                              <SelectTrigger className="w-20">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">1组</SelectItem>
                                <SelectItem value="2">2组</SelectItem>
                                <SelectItem value="3">3组</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* 批次分页 */}
                          <div>
                            <PaginationComponent
                              total={selectedBatch.length * 2}
                              current={batchPage}
                              pageSize={batchPageSize}
                              onChange={(page, size) => {
                                setBatchPage(page);
                                setBatchPageSize(size);
                              }}
                              showSizeChanger={true}
                              showTotal={true}
                            />
                          </div>
                        </div>
                      </div>

                      <div
                        className="grid gap-4"
                        style={{
                          gridTemplateColumns: `repeat(${imagesPerRow}, 1fr)`,
                        }}
                      >
                        {selectedBatch?.map((group, groupIndex) => (
                          <Card key={groupIndex} className="p-4 shadow-md">
                            <div className="flex gap-2">
                              {group.pages.map((image: Page, imageIndex) => (
                                <div key={image.id} className="flex-1">
                                  <div className="relative h-full border rounded-lg overflow-hidden group cursor-pointer" onClick={() => handleImagePreview(image, filteredImages)}>
                                    <img src={image.url as string} alt={`页面 ${image.page_num}`} className="w-full h-full object-cover" />

                                    {/* 图片信息覆盖层 */}
                                    {/* <div
                                      className={`absolute bottom-0 left-0 right-0 text-white p-2 text-xs ${
                                        group.status === 'normal'
                                          ? 'bg-green-600/80'
                                          : group.status === 'ocr_error'
                                          ? 'bg-red-600/80'
                                          : group.status === 'blank'
                                          ? 'bg-gray-600/80'
                                          : group.status === 'duplicate'
                                          ? 'bg-yellow-600/80'
                                          : group.status === 'pending'
                                          ? 'bg-blue-600/80'
                                          : 'bg-black/70'
                                      }`}
                                    >
                                      <div>页码：{image.page_num}</div>
                                      <div>
                                        状态：
                                        {group.status === 'normal'
                                          ? '正常'
                                          : group.status === 'ocr_error'
                                          ? 'OCR异常'
                                          : group.status === 'blank'
                                          ? '空白页'
                                          : group.status === 'duplicate'
                                          ? '重复页'
                                          : group.status === 'pending'
                                          ? '待分发'
                                          : '未知'}
                                      </div>
                                    </div> */}

                                    {/* hover操作按钮 */}
                                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1 flex-wrap p-2">
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation('replace', image, group);
                                        }}
                                      >
                                        <Upload className="h-3 w-3 mr-1" />
                                        替换
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation('edit_page', image);
                                        }}
                                      >
                                        <Edit className="h-3 w-3 mr-1" />
                                        页码
                                      </Button>
                                      {/* <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation(
                                            "edit_student",
                                            image
                                          );
                                        }}
                                      >
                                        <Edit className="h-3 w-3 mr-1" />
                                        学号
                                      </Button> */}
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        title="重置OCR"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          // 重置OCR功能
                                          console.log('重置OCR:', image.id);
                                          alert('OCR重置成功');
                                        }}
                                      >
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                        重置OCR
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="destructive"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation('delete', image);
                                        }}
                                      >
                                        <Trash2 className="h-3 w-3 mr-1" />
                                        删除
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImagePreview(image, filteredImages);
                                        }}
                                      >
                                        <Eye className="h-3 w-3 mr-1" />
                                        预览
                                      </Button>
                                    </div>
                                  </div>

                                  {/* 垂直分割线 */}
                                  {imageIndex === 0 && <div className="absolute top-0 bottom-0 right-0 w-px bg-border" />}
                                </div>
                              ))}
                            </div>

                            {/* 学号信息和修改按钮 */}
                            <div className="mt-3 pt-3 border-t flex items-center justify-between">
                              <div className="text-sm">
                                <span className="text-muted-foreground">学号：</span>
                                <span className="font-medium">{group?.student_id || '未识别'}</span>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-7 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // handleImageOperation(
                                  //   "edit_student",
                                  //   group
                                  // );
                                }}
                              >
                                <Edit className="h-3 w-3 mr-1" />
                                修改
                              </Button>
                            </div>
                          </Card>
                        ))}

                        {/* 添加新图片按钮 */}
                        <div
                          className="flex-shrink-0 w-48 h-64 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                          onClick={() => handleImageOperation('add')}
                        >
                          <div className="text-center text-muted-foreground">
                            <Plus className="h-8 w-8 mx-auto mb-2" />
                            <div className="text-sm">添加图片</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}
        </TabsContent>
        {/* 学生详情tab */}
        <TabsContent value="student" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>学生扫描详情</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-4">
                {/* 第一行筛选项 */}
                <div className="flex gap-4">
                  <Input placeholder="学号" value={filters.student_no} onChange={(e) => handleFilterChange('student_no', e.target.value)} className="w-32" />
                  <Input placeholder="姓名" value={filters.name} onChange={(e) => handleFilterChange('name', e.target.value)} className="w-32" />
                  <Input placeholder="班级" value={filters.class} onChange={(e) => handleFilterChange('class', e.target.value)} className="w-32" />
                  <Select value={filters.exception_status} onValueChange={(v) => handleFilterChange('exception_status', v)}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="异常状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="页码异常">页码异常</SelectItem>
                      <SelectItem value="少页">少页</SelectItem>
                      <SelectItem value="多页">多页</SelectItem>
                      <SelectItem value="存在相同页码">存在相同页码</SelectItem>
                      <SelectItem value="未上传答题卡">未上传答题卡</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filters.is_exception} onValueChange={(v) => handleFilterChange('is_exception', v)}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="是否异常" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="否">否</SelectItem>
                      <SelectItem value="是">是</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" onClick={handleSearchStudents}>
                    <Search className="h-4 w-4 mr-2" />
                    查询
                  </Button>
                  <Button onClick={handleGenerateTask}>生成任务</Button>
                </div>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>姓名</TableHead>
                    <TableHead>班级</TableHead>
                    <TableHead>学号</TableHead>
                    <TableHead>学校</TableHead>
                    <TableHead>异常状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentScans.map((stu) => (
                    <TableRow key={stu.id}>
                      <TableCell>{stu.name}</TableCell>
                      <TableCell>{stu.class}</TableCell>
                      <TableCell>{stu.student_no}</TableCell>
                      <TableCell>{stu.school}</TableCell>
                      <TableCell>{stu.exception_status}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline" onClick={() => handleShowStudentImages(stu)}>
                          <ImageIcon className="w-4 h-4 mr-1" />
                          图片详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 学生分页 */}
              <div className="mt-4 flex justify-end">
                <PaginationComponent
                  total={studentTotal}
                  current={studentPage}
                  pageSize={studentPageSize}
                  onChange={(page, size) => {
                    setStudentPage(page);
                    setStudentPageSize(size);
                  }}
                  showSizeChanger={true}
                  showTotal={true}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="确认删除"
        description="您确定要删除这张图片吗？此操作无法撤销。"
        onConfirm={() => {
          // 这里应该调用API删除图片
          console.log('删除图片:', currentOperationImage);
          setDeleteConfirmOpen(false);
          setCurrentOperationImage(null);
        }}
      />

      {/* 批次删除确认对话框 */}
      <DeleteConfirmDialog
        open={batchDeleteConfirmOpen}
        onOpenChange={setBatchDeleteConfirmOpen}
        title="确认删除批次"
        description={`您确定要删除批次 ${currentBatchToDelete?.batch_no} 的所有图片吗？此操作无法撤销。`}
        onConfirm={handleBatchDelete}
      />

      {/* 学生图片详情Dialog */}
      <Dialog open={imageDialogOpen} onOpenChange={setImageDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>图片详情</DialogTitle>
          </DialogHeader>
          <div className="flex flex-wrap gap-2">
            {currentImages.map((img, idx) => (
              <div key={idx} className="w-24 h-32 bg-gray-200 flex items-center justify-center text-xs">
                {img}
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* 学生图片详情弹窗 */}
      {currentStudentInfo && (
        <StudentImageDialog
          open={studentImageDialogOpen}
          onOpenChange={setStudentImageDialogOpen}
          studentName={currentStudentInfo.name}
          studentId={currentStudentInfo.id}
          studentClass={currentStudentInfo.class}
          images={currentStudentImages}
          onImageOperation={(type, image) => {
            // 复用现有的图片操作功能
            handleImageOperation(type, image);
          }}
        />
      )}
    </>
  );
};

export default HomeworkScan;
