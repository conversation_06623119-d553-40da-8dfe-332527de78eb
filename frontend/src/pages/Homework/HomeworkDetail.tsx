import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { homeworkApi } from '@/services/homeworkApi';
import { homeworkPapersApi } from '@/services/homeworkPapersApi';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { useHomeworkStore } from '@/stores';
import { TeachingAid, teachingAidApi } from '@/types';
import { HomeworkSummary } from '@/types/homework';
import { CreateHomeworkParams, Homework, HomeworkStatusEnum, UpdateHomeworkParams } from '@/types/homework.ts';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import { ChevronRight, Edit, FileText, RefreshCw, Scan, Trash2, UserPlus, Zap } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AddpapersByToHomework from './components/AddpapersByToHomework';
import EditHomework from './components/EditHomework';
import HomeworkClassInfo from './components/HomeworkClassInfo';
import TenantContext from './contexts/TenantContext';

const HomeworkDetail: React.FC = () => {
  const { id } = useParams() as { id: string };
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const schema_name = identityInfo?.schema_name || '';
  const navigate = useNavigate();
  const [homeworkSummary, setHomeworkSummary] = useState<HomeworkSummary>();
  const { homework } = useHomeworkStore();

  // State management
  const [loading, setLoading] = useState(false);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [homeworkForm, setHomeworkForm] = useState<CreateHomeworkParams>({
    homework_name: '',
    homework_status: HomeworkStatusEnum.Draft,
    subject_group_id: undefined,
    description: undefined,
  });
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddPapersDialogOpen, setIsAddPapersDialogOpen] = useState(false);
  const [textbookList, setTextbookList] = useState<TeachingAid[]>([]);

  useEffect(() => {
    loadHomeworkSummary();
    loadTextBookList().then( );
  }, [id, schema_name, tenantId]);

  const loadSubjectGroups = async () => {
    SubjectGroupsApi.findAll(schema_name).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error(message);
        return;
      }
      setSubjectGroups(Array.isArray(data) ? data : []);
    });
  };

  const loadTextBookList = async () => {
    teachingAidApi.getTeachingAids().then((res) => {
      setTextbookList(res);
    });
  };

  const openEditDialog = (homework: Homework) => {
    loadSubjectGroups();
    setHomeworkForm({
      homework_name: homework.homework_name,
      homework_status: homework.homework_status as HomeworkStatusEnum,
      subject_group_id: homework.subject_group_id,
      description: homework.description || '',
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteHomework = async () => {
    try {
      homeworkApi.deleteHomework(tenantId, schema_name, id).then((res) => {
        const { success, message } = res;
        if (!success) {
          console.error(message);
          return;
        }
        navigate('/homework-management');
      });
      setIsDeleteDialogOpen(false);
    } catch (err) {
      console.error('Failed to delete homework');
      console.error('Error deleting homework:', err);
    }
  };

  const handleUpdateHomework = async () => {
    try {
      await homeworkApi.updateHomework(tenantId, schema_name, {
        id: id,
        homework_name: homeworkForm.homework_name,
        homework_status: homeworkForm.homework_status,
        subject_group_id: homeworkForm.subject_group_id,
        description: homeworkForm.description,
      });
      setIsEditDialogOpen(false);
    } catch (err) {
      console.error('Failed to update homework');
      console.error('Error updating homework:', err);
    }
  };

  // 加载作业摘要数据
  const loadHomeworkSummary = () => {
    setLoading(true);
    homeworkApi
      .getHomeworkSummary(tenantId, schema_name, id)
      .then((res) => {
        const { success, data, message } = res;
        if (!success) {
          console.error('报错:' + message);
          return;
        }
        setHomeworkSummary(data);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  //绑定试卷
  const handleBindPapers = async (paperId: string[]) => {
    await homeworkPapersApi
      .bindPapersToHomework(tenantId, schema_name, {
        homework_id: id as string,
        //FIXME: 试卷只能单选
        paper_id: paperId[0],
      })
      .then((res) => {
        const { success, message } = res;
        if (!success) {
          console.error('报错:' + message);
          return;
        }
        // 添加成功，重新加载数据
        loadHomeworkSummary();
        // 关闭对话框
        setIsAddPapersDialogOpen(false);
      });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <TenantContext.Provider value={{ tenantId, tenantName: schema_name, homeworkId: id, homeworkName: homework?.homework_name as string }}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            {/* 面包屑导航 */}
            <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
              <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate('/homework-management')}>
                作业管理
              </Button>
              <ChevronRight className="h-4 w-4" />
              <span className="text-foreground font-medium">{homework?.homework_name}</span>
            </nav>
            <h1 className="text-3xl font-bold">{homework?.homework_name}</h1>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button variant="outline" size="sm" onClick={() => openEditDialog(homework as Homework)}>
              <Edit className="h-4 w-4" />
              编辑
            </Button>
            <Button variant="outline" size="sm" onClick={() => setIsAddPapersDialogOpen(true)}>
              <UserPlus className="h-4 w-4 mr-2" />
              添加试卷
            </Button>
            {homeworkSummary?.paper_id && (
              <Button variant="outline" size="sm" onClick={() => navigate(`/answerSheetEditing/${schema_name}/${homeworkSummary.paper_id}`)}>
                <Edit className="h-4 w-4 mr-2" />
                编辑题卡
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={() => setIsDeleteDialogOpen(true)}>
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105" onClick={() => navigate(`/homework-student/${id}`)}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">考生信息</CardTitle>
                <Scan className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{homeworkSummary?.student?.total_count || 0}</div>
                <div className="text-xs text-muted-foreground">
                  <span>异常: {homeworkSummary?.student?.error_count || 0}</span>
                  <span className="ml-2">缺考: {homeworkSummary?.student.absent_count || 0}</span>
                </div>
              </CardContent>
            </Card>
            {homeworkSummary?.paper_id ? (
              <>
                <Card
                  className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105"
                  onClick={homeworkSummary.paper_id ? () => navigate(`/answerSheetEditing/${schema_name}/${homeworkSummary.paper_id}`) : undefined}
                >
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      试卷题卡编辑
                      {!homeworkSummary.paper_id && <Badge className="ml-2">暂未绑定试卷</Badge>}
                    </CardTitle>
                    <Edit className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">总分：{homeworkSummary?.criteria.score || 0}分</div>
                    <div className="text-xs text-muted-foreground">
                      <span>共{homeworkSummary?.criteria.count || 0}题</span>
                    </div>
                  </CardContent>
                </Card>
                <Card className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105" onClick={() => navigate(`/homework-scan/${id}`)}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">扫描进度</CardTitle>
                    <Scan className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{homeworkSummary?.scan?.total_count || 0}张</div>
                    <div className="text-xs text-muted-foreground">
                      <span>异常: {homeworkSummary?.scan.error_count || 0}</span>
                      <span className="ml-2">重复: {homeworkSummary?.scan.duplication_count || 0}</span>
                      <span className="ml-2">未绑定: {homeworkSummary?.scan.unbounded_count || 0}</span>
                    </div>
                  </CardContent>
                </Card>
                <Card className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105" onClick={() => navigate(`/homework-grading/${id}`)}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">阅卷进度</CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">总任务：{homeworkSummary?.scoring?.total_count || 0}</div>
                    <div className="text-xs text-muted-foreground">
                      <span>已阅量: {homeworkSummary?.scoring.scored_count || 0}</span>
                      <span className="ml-2">平均分: {homeworkSummary?.scoring.avg_score.toFixed(1) || 0}</span>
                    </div>
                  </CardContent>
                </Card>

              </>
            ) : (
              <>
                <Card className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105" onClick={() => setIsAddPapersDialogOpen(true)}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">添加试卷</CardTitle>
                    <Zap className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-1xl font-bold">请绑定试卷，并配置好答题卡和评分标准</div>
                    <p className="text-sm text-muted-foreground mt-1">点击开始添加试卷流程</p>
                  </CardContent>
                </Card>
              </>
            )}
          </div>

          {/* 添加试卷对话框 */}
          <AddpapersByToHomework open={isAddPapersDialogOpen} onOpenChange={setIsAddPapersDialogOpen} onBind={handleBindPapers} textbookList={textbookList} />
        </div>
        <HomeworkClassInfo />

        {/* Delete Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>确认删除</DialogTitle>
              <DialogDescription>你确定要删除作业 {homework?.homework_name} 吗？此操作无法撤销。</DialogDescription>
            </DialogHeader>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                取消
              </Button>
              <Button variant="destructive" onClick={handleDeleteHomework}>
                删除
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Dialog */}
        <EditHomework
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          homeworkForm={homeworkForm as UpdateHomeworkParams}
          onHomeworkFormChange={setHomeworkForm}
          subjectGroups={subjectGroups}
          onSave={handleUpdateHomework}
          onCancel={() => setIsEditDialogOpen(false)}
        />
      </div>
    </TenantContext.Provider>
  );
};

export default HomeworkDetail;
