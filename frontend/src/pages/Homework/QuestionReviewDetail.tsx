import React, { useState, useContext, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import CustomPagination from '@/components/Pagination';
import { Eye, Check, X, ChevronDown, ChevronUp, Settings, RotateCcw, Search, Filter, CheckSquare, ChevronRight, FileText, Clock, TrendingUp } from 'lucide-react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import MathRenderer from '@/components/math/MathRenderer';
import { useHomeworkStore } from '@/stores';
import { Scores, homeworkReviewApi } from '@/services/homeworkReviewApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import DetailsDrawer from './components/DetailsDrawer';
import { scanApi, type Student } from '@/services/scanApi';

interface ReviewImage {
  id: string;
  studentId: string;
  studentNumber: string;
  className: string;
  imageUrl: string;
  aiScore: number;
  reviewStatus: 'not_reviewed' | 'review_correct' | 'review_error';
  recognitionResult: string;
  aiGradingBasis: string;
}

const mockReviewImages: ReviewImage[] = [
  {
    id: '1',
    studentId: 'stu001',
    studentNumber: '2024001',
    className: '高三(1)班',
    imageUrl: 'http://202.116.234.4:8173/static/image/1753692963655/score_area/2320511/68883b77eab0ec114ea9eff7.jpg',
    aiScore: 8.5,
    reviewStatus: 'not_reviewed',
    recognitionResult: '学生答案：根据题目要求...',
    aiGradingBasis: 'AI评分依据：答案完整性80%，准确性90%',
  },
  {
    id: '2',
    studentId: 'stu002',
    studentNumber: '2024002',
    className: '高三(1)班',
    imageUrl: 'http://202.116.234.4:8173/static/image/1753692963655/score_area/2320535/68883b76eab0ec114ea9efa2.jpg',
    aiScore: 6.0,
    reviewStatus: 'review_correct',
    recognitionResult: '学生答案：部分正确...',
    aiGradingBasis: 'AI评分依据：答案完整性60%，准确性70%',
  },
  {
    id: '3',
    studentId: 'stu003',
    studentNumber: '2024003',
    className: '高三(2)班',
    imageUrl: 'http://202.116.234.4:8173/static/image/1753692963655/score_area/2320535/68883b76eab0ec114ea9efa2.jpg',
    aiScore: 9.2,
    reviewStatus: 'review_error',
    recognitionResult: '学生答案：答案详细...',
    aiGradingBasis: 'AI评分依据：答案完整性95%，准确性90%',
  },
];

const QuestionReviewDetail: React.FC = () => {
  const navigate = useNavigate();
  const { homeworkId, question } = useLocation().state || {};
  const { homework } = useHomeworkStore();
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantName = identityInfo?.schema_name || '';

  const { scoreId } = useParams() as { scoreId: string };

  // 筛选和配置状态
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [questionInfoOpen, setQuestionInfoOpen] = useState(false);
  const [filters, setFilters] = useState({
    className: '',
    studentNumber: '',
    scoreRange: { min: '', max: '' },
    status: '',
  });
  const [imagesPerRow, setImagesPerRow] = useState(3);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [questionScores, setQuestionScores] = useState<Scores[]>([]);

  // 编辑分数状态
  const [editingScore, setEditingScore] = useState<{
    id: string;
    score: number;
  }>();

  // 重新批阅弹窗状态
  const [reGradingDialogOpen, setReGradingDialogOpen] = useState(false);
  const [reGradingOptions, setReGradingOptions] = useState({
    resetOCR: false,
    reGradeCompleted: false,
    reGradeReviewed: false,
  });
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    getScores();
  }, [homeworkId, scoreId]);

  const getScores = () => {
    let params = {
      homework_id: homeworkId,
      criteria_id: scoreId,
      page: currentPage,
      page_size: pageSize,
    };
    homeworkReviewApi.getQuestionScores(tenantName, params).then((res) => {
      if (res.success) {
        setQuestionScores(res.data as Scores[]);
      }
    });
  };

  // 处理筛选
  const handleFilter = () => {
    console.log('筛选条件:', filters);
    setCurrentPage(1);
  };

  // 重置筛选
  const resetFilters = () => {
    setFilters({
      className: '',
      studentNumber: '',
      scoreRange: { min: '', max: '' },
      status: '',
    });
    setCurrentPage(1);
  };

  // 编辑分数
  const handleEditScore = (stu: Scores) => {
    setEditingScore({ id: stu.id, score: stu.score });
  };

  // 保存分数
  const saveScore = (score?: number) => {
    if (editingScore?.score || editingScore?.score === 0) {
      console.log('保存分数:', editingScore);
      homeworkReviewApi
        .updateQuestionScores(tenantName, {
          id: editingScore.id,
          score: score !== undefined ? score : editingScore.score,
          // reason: undefined,
        })
        .then((res) => {
          if (res.success) {
            getScores();
          }
          setEditingScore({} as { id: string; score: number });
        });
    } else {
      alert('请输入分数');
    }
  };

  // 核查操作
  const handleReview = (imageId: string, isCorrect: boolean) => {
    console.log('核查操作:', imageId, isCorrect ? '正确' : '错误');
  };

  // 反馈识别结果
  const handleFeedback = (imageId: string, isCorrect: boolean) => {
    console.log('反馈识别结果:', imageId, isCorrect ? '正确' : '错误');
  };

  // 重阅操作
  const handleRegrade = (imageId: string) => {
    console.log('重阅:', imageId);
  };

  // 批量确认
  const handleBatchConfirm = async () => {
    const scoreIds = questionScores.map((stu) => stu.id);
    if (scoreIds.length === 0) {
      alert('请选择要确认的项');
      return null;
    }
    homeworkReviewApi
      .batchConfirm(tenantName, scoreIds)
      .then((res) => {
        if (res.success) {
          getScores();
          toast.success('批量确认成功');
        }
      })
      .catch(() => {
        toast.error('批量确认失败,请重试');
      });
  };

  // 处理全选/反选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setReGradingOptions({
      resetOCR: checked,
      reGradeCompleted: checked,
      reGradeReviewed: checked,
    });
  };

  // 处理单个选项变化
  const handleOptionChange = (option: keyof typeof reGradingOptions, checked: boolean) => {
    const newOptions = { ...reGradingOptions, [option]: checked };
    setReGradingOptions(newOptions);

    // 检查是否全选
    const allSelected = Object.values(newOptions).every((value) => value);
    const noneSelected = Object.values(newOptions).every((value) => !value);
    setSelectAll(allSelected ? true : noneSelected ? false : false);
  };

  // 重新批阅确认
  const handleReGradingConfirm = async () => {
    try {
      console.log('重新批阅:', reGradingOptions);
      // 调用API
      scanApi.redoScore(tenantName, {
        criteria_ids: [scoreId],
        student_ids: [],
        homework_id: homeworkId,
        redo_ocr: reGradingOptions.resetOCR,
        include_done: reGradingOptions.reGradeCompleted,
        include_checked: reGradingOptions.reGradeReviewed,
      });
      // const response = await gradingApi.reGrading({
      //   questionId: question.id,
      //   options: reGradingOptions
      // });
      setReGradingDialogOpen(false);
      alert('重新批阅任务已提交');
    } catch (error) {
      console.error('重新批阅失败:', error);
      alert('重新批阅失败');
    }
  };

  // 取消重新批阅
  const handleReGradingCancel = () => {
    setReGradingDialogOpen(false);
    setReGradingOptions({
      resetOCR: false,
      reGradeCompleted: false,
      reGradeReviewed: false,
    });
    setSelectAll(false);
  };

  const getStudentInfo = (student: Student | null) => {
    const { student_number, student_id, student_name } = student || {};

    const parts = [];

    if (student_number !== null) {
      parts.push(student_number);
    }
    if (student_name !== null) {
      parts.push(student_name);
    }
    if (!student_id) {
      parts.push('未绑定');
    }
    const resultText = parts.join(' ');
    const resultColor = student_id && student_id !== null ? 'text-green-700' : 'text-red-500';

    return {
      text: resultText,
      color: resultColor,
    };
  };

  const getScoringType = (scoring_type: string) => {
    switch (scoring_type) {
      case 'Match':
        return '题卡匹配';
      case 'AI':
        return '自动AI阅卷';
      case 'Manual':
        return '老师手阅(登分)';
      case 'Online':
        return '在线分发阅卷';
      case 'Check':
        return '核查';
      default:
        return '';
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-3 pb-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
          <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate('/homework-management')}>
            作业管理
          </Button>
          <ChevronRight className="h-4 w-4" />
          <Button onClick={() => navigate(`/homework-setting/${homework.id}`)} variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground">
            {homework.homework_name}
          </Button>
          <ChevronRight className="h-4 w-4" />
          <Button onClick={() => navigate(`/homework-grading/${homework.id}`)} variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground">
            阅卷核查
          </Button>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground font-medium">{question.name}核查</span>
        </nav>
        {/* 数据统计 */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="text-sm text-muted-foreground">阅卷进度</div>
                  <div className="text-2xl font-bold">
                    {question.success_count}/{question.total_count}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <CheckSquare className="h-8 w-8 text-green-600" />
                <div>
                  <div className="text-sm text-muted-foreground">平均分</div>
                  <div className="text-2xl font-bold">
                    {question.avg_score.toFixed(2)}/{question.score}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Clock className="h-8 w-8 text-orange-600" />
                <div>
                  <div className="text-sm text-muted-foreground">评分方式</div>
                  <div className="text-2xl font-bold">{getScoringType(question.scoring_type)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div>
                  <div className="text-sm text-muted-foreground">核查完成率</div>
                  <div className="text-2xl font-bold">0%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 评分标准 - 粘性定位 */}
        <div className="sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 bg-white">
          <Card className="border-b">
            <CardContent className="py-3">
              <div>
                <div className="text-sm font-medium text-muted-foreground mb-2">评分标准:</div>
                <MathRenderer content={question.answer} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 图片列表配置和批量操作 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button onClick={handleBatchConfirm}>
                <CheckSquare className="h-4 w-4 mr-2" />
                批量确认
              </Button>
              <Dialog open={reGradingDialogOpen} onOpenChange={setReGradingDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <RotateCcw className="h-4 w-4 mr-2" />
                    重新批阅
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>重新批阅</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">此操作将重置批阅结果和核查结果</div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="selectAll" checked={selectAll} onCheckedChange={handleSelectAll} />
                        <label htmlFor="selectAll" className="text-sm font-medium">
                          全选
                        </label>
                      </div>

                      <div className="ml-6 space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="resetOCR" checked={reGradingOptions.resetOCR} onCheckedChange={(checked) => handleOptionChange('resetOCR', checked as boolean)} />
                          <label htmlFor="resetOCR" className="text-sm">
                            重置OCR
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox id="reGradeCompleted" checked={reGradingOptions.reGradeCompleted} onCheckedChange={(checked) => handleOptionChange('reGradeCompleted', checked as boolean)} />
                          <label htmlFor="reGradeCompleted" className="text-sm">
                            重阅已阅试题
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox id="reGradeReviewed" checked={reGradingOptions.reGradeReviewed} onCheckedChange={(checked) => handleOptionChange('reGradeReviewed', checked as boolean)} />
                          <label htmlFor="reGradeReviewed" className="text-sm">
                            重阅已核查试题
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button onClick={handleReGradingConfirm} className="flex-1">
                        确认
                      </Button>
                      <Button onClick={handleReGradingCancel} variant="outline" className="flex-1">
                        取消
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              {/* 显示当前筛选条件 */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {filters.className && <span className="bg-muted px-2 py-1 rounded text-xs">班级: {filters.className}</span>}
                {filters.studentNumber && <span className="bg-muted px-2 py-1 rounded text-xs">学号: {filters.studentNumber}</span>}
                {(filters.scoreRange.min || filters.scoreRange.max) && (
                  <span className="bg-muted px-2 py-1 rounded text-xs">
                    分数: {filters.scoreRange.min || '0'}-{filters.scoreRange.max || '100'}
                  </span>
                )}
                {filters.status && <span className="bg-muted px-2 py-1 rounded text-xs">状态: {filters.status}</span>}
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* 每行显示配置 */}
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span className="text-sm">每行显示:</span>
                <Select value={imagesPerRow.toString()} onValueChange={(value) => setImagesPerRow(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1列</SelectItem>
                    <SelectItem value="2">2列</SelectItem>
                    <SelectItem value="3">3列</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 筛选条件 */}
              <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    筛选条件
                    {filtersOpen ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                  </Button>
                </CollapsibleTrigger>
              </Collapsible>
            </div>
          </div>

          {/* 筛选条件展开内容 */}
          <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
            <CollapsibleContent>
              <Card className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium">班级</label>
                    <Select value={filters.className} onValueChange={(value) => setFilters({ ...filters, className: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择班级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部班级</SelectItem>
                        <SelectItem value="高三(1)班">高三(1)班</SelectItem>
                        <SelectItem value="高三(2)班">高三(2)班</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">学号</label>
                    <Input
                      placeholder="输入学号"
                      value={filters.studentNumber}
                      onChange={(e) =>
                        setFilters({
                          ...filters,
                          studentNumber: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">分数范围</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="最低分"
                        value={filters.scoreRange.min}
                        onChange={(e) =>
                          setFilters({
                            ...filters,
                            scoreRange: {
                              ...filters.scoreRange,
                              min: e.target.value,
                            },
                          })
                        }
                      />
                      <span>-</span>
                      <Input
                        placeholder="最高分"
                        value={filters.scoreRange.max}
                        onChange={(e) =>
                          setFilters({
                            ...filters,
                            scoreRange: {
                              ...filters.scoreRange,
                              max: e.target.value,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">状态</label>
                    <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="pending">待批改</SelectItem>
                        <SelectItem value="not_reviewed">未核查</SelectItem>
                        <SelectItem value="review_correct">批阅正确</SelectItem>
                        <SelectItem value="review_error">批阅错误</SelectItem>
                        <SelectItem value="feedback_exception">反馈异常</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex gap-2 mt-4">
                  <Button onClick={handleFilter} size="sm">
                    <Search className="h-4 w-4 mr-2" />
                    筛选
                  </Button>
                  <Button variant="outline" onClick={resetFilters} size="sm">
                    重置
                  </Button>
                </div>
              </Card>
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* 图片列表 */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${imagesPerRow}, 1fr)` }}>
          {questionScores?.map((stu: Scores, index) => (
            <Card key={index} className="relative">
              {stu.blocks.map((image: any, imgIndex: number) => (
                <div key={image.id}>
                  <div className="relative w-full h-auto border rounded">
                    <img src={image.answer_block_url as string} alt={`学生${image.student_id}答题图片`} className="object-scale-down" />

                    <div className={`absolute bottom-2 left-2 z-39 text-sm ${getStudentInfo(stu.student).color}`}>{getStudentInfo(stu.student).text}</div>
                    {imgIndex === 0 && (
                      <div className="absolute top-2 left-2 z-39">
                        {editingScore?.id === stu.id ? (
                          <div className="bg-white/95 backdrop-blur-sm rounded p-2 shadow-xl border z-50">
                            <Input
                              type="number"
                              value={editingScore?.score}
                              min={0}
                              onChange={(e) =>
                                setEditingScore((prev) =>
                                  prev
                                    ? {
                                        ...prev,
                                        score: Number(e.target.value),
                                      }
                                    : undefined,
                                )
                              }
                              className="w-20 h-8 text-center"
                            />
                            <div className="gap-1 mt-2">
                              <div className="mb-2 gap-1 flex">
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    saveScore();
                                  }}
                                >
                                  保存
                                </Button>
                                <Button size="sm" variant="outline" onClick={() => setEditingScore({} as { id: string; score: number })}>
                                  取消
                                </Button>
                              </div>
                              <div className="mb-2 gap-1 flex">
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    // setEditingScore({ ...editingScore, score: question.score });
                                    saveScore(question.score);
                                  }}
                                >
                                  <Check className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    // setEditingScore({ ...editingScore, score: 0 });
                                    saveScore(0);
                                  }}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div
                            className="text-red-600 font-bold text-lg cursor-pointer hover:text-red-700 drop-shadow-lg"
                            onClick={() => handleEditScore(stu)}
                            style={{
                              textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                            }}
                          >
                            {stu.score}分
                          </div>
                        )}
                      </div>
                    )}

                    {imgIndex === 0 && (
                      <div className="absolute top-2 right-2">
                        <DetailsDrawer
                          details={stu.details || []}
                          blocks={stu.blocks}
                          title="评分详情"
                          trigger={
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800">
                              <Eye className={`h-4 w-4 ${stu.status === 'CheckedCorrect' ? 'text-green-700' : stu.status === 'CheckedError' ? 'text-red-500' : 'text-gray-500'}`} />
                            </Button>
                          }
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </Card>
          ))}
        </div>

        {/* 处理语文作文和英语作文 */}

        {/* 分页和批量操作 */}
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Button onClick={handleBatchConfirm}>
              <CheckSquare className="h-4 w-4 mr-2" />
              批量确认
            </Button>
            {/* <Button variant="outline" onClick={handlePageRegrade}>
              <RotateCcw className="h-4 w-4 mr-2" />
              此页重阅
            </Button> */}
          </div>
          <CustomPagination
            total={mockReviewImages.length}
            current={currentPage}
            pageSize={pageSize}
            onChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size);
            }}
            showSizeChanger={true}
            showTotal={true}
          />
        </div>
      </div>
    </TooltipProvider>
  );
};

export default QuestionReviewDetail;
