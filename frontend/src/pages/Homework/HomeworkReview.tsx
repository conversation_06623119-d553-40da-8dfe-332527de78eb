import React, { useState, useEffect, useContext } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, TrendingUp, Zap, CheckCircle2, Play, Square, Info, ChevronRight, RotateCcw } from 'lucide-react';
import { GradingStats, AIGradingStats } from '@/services/gradingApi';
import { useParams, useNavigate } from 'react-router-dom';
import { Question, homeworkReviewApi } from '@/services/homeworkReviewApi';
import MathRenderer from '@/components/math/MathRenderer';
import { useHomeworkStore } from '@/stores';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import RedoSorceDialog from './components/RedoSorceDialog';
import scanApi, { ReScoringParams } from '@/services/scanApi';

const HomeworkReview: React.FC = () => {
  const { homework } = useHomeworkStore();
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const tenantName = identityInfo?.schema_name || '';
  const navigate = useNavigate();
  const [gradingStats] = useState<GradingStats | null>(null);
  const [aiGradingStats] = useState<AIGradingStats | null>(null);

  // 页面状态
  const [currentView, setCurrentView] = useState<'overview' | 'question_review'>('overview');
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [expandedQuestion, setExpandedQuestion] = useState<string | null>(null);

  //重评
  const [redoScoreParam, setRedoScoreParam] = useState<ReScoringParams>(
    {
      criteria_ids: [],
      student_ids: [],
      homework_id: homework.id.toString() || '',
      redo_ocr: false,
      include_done: false,
      include_checked: false,
    }
  );
  const [redoSorceDialogOpen, setRedoSorceDialogOpen] = useState(false);

  const [questionTypeFilter, setQuestionTypeFilter] = useState<'all' | 'objective' | 'subjective'>('all');
  // const [classFilter, setClassFilter] = useState<string>("all");
  const [questions, setQuestions] = useState<Question[]>([]);

  useEffect(() => {
    if (tenantName && homework.id) {
      getQuestions(tenantName, homework.id);
    }
  }, [tenantName, homework.id]);

  const getQuestions = (tenantName: string, homeworkId: String) => {
    homeworkReviewApi
      .getQuestions(tenantName, homeworkId)
      .then((res) => {
        if (res.success) {
          setQuestions(res.data || []);
        }
        console.log(questions, 'questions');
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 处理题目卡片点击 - 所有题目都能跳转核查页面
  const handleQuestionClick = (question: Question) => {
    setSelectedQuestion(question);
    navigate(`/homework-review/${question.id}`, {
      state: {
        homeworkId: homework.id,
        tenantName: tenantName,
        homeworkName: homework.homework_name,
        question: question,
      },
    });
  };

  // 筛选题目
  const filteredQuestions = questions?.filter((question: Question) => {
    const matchesType =
      questionTypeFilter === 'all' ||
      (questionTypeFilter === 'objective' && question.scoring_type === 'match') ||
      (questionTypeFilter === 'subjective' && ['ai', 'manual'].includes(question.scoring_type));
    // const matchesClass = classFilter === "all";
    return matchesType;
  });
  // 处理重新评阅
  const handleRedoScore = (redoParam: ReScoringParams) => {
    scanApi.redoScore(tenantName, redoParam).then((res) => {
      if (res.success) {
        // 重新获取题目列表
        if (tenantName && homework.id) {
          getQuestions(tenantName, homework.id);
        }
      } else {
        console.log('重评失败:', res.message);
      }
    }).catch((err) => {
      console.log('重评出错:', err);
    });
  };

  return (
    <div className="space-y-6">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
        <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate('/homework-management')}>
          作业管理
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="ghost" size="sm" className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground" onClick={() => navigate(`/homework-setting/${homework.id}`)}>
          {homework.homework_name}
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="text-foreground font-medium">阅卷核查</span>
      </nav>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总试卷数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{gradingStats?.total_papers || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{gradingStats?.completed_papers || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(gradingStats?.average_score || 0).toFixed(1)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI成功率</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{((aiGradingStats?.success_rate || 0) * 100).toFixed(1)}%</div>
          </CardContent>
        </Card>
      </div>
      {/* 重评会话 */}
      <RedoSorceDialog
        open={redoSorceDialogOpen}
        onOpenChange={setRedoSorceDialogOpen}
        criteria_ids={questions?.map(q => q.id) || []}
        // criteria_names={questions?.map(q=> q.name) || []}
        onConfirm={(redoOcr, includeDone, includeChecked, selected_criteria_ids) => {
          const redoParams = {
            ...redoScoreParam,
            redo_ocr: redoOcr,
            include_done: includeDone,
            include_checked: includeChecked,
            criteria_ids: selected_criteria_ids
          };
          handleRedoScore(redoParams);
          // 在这里处理重评逻辑，例如调用API进行重评
        }}
        onCancel={() => {
          console.log('取消重评');
        }}
      />
      {/* 题目筛选 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">题目列表</CardTitle>
            <div className="flex gap-3">
              <Select value={questionTypeFilter} onValueChange={(value) => setQuestionTypeFilter(value as 'all' | 'objective' | 'subjective')}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="题目类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="objective">客观题</SelectItem>
                  <SelectItem value="subjective">主观题</SelectItem>
                </SelectContent>
              </Select>
              <Button size="sm" onClick={() => setRedoSorceDialogOpen(true)}>
                <RotateCcw className="h-4 w-4 mr-1" />
                重新评分
              </Button>
              {/* <Select value={classFilter} onValueChange={setClassFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="班级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部班级</SelectItem>
                  <SelectItem value="class1">高三(1)班</SelectItem>
                  <SelectItem value="class2">高三(2)班</SelectItem>
                  <SelectItem value="class3">高三(3)班</SelectItem>
                </SelectContent>
              </Select> */}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {filteredQuestions.map((question, index) => (
              <div key={index}>
                <Card
                  className={`cursor-pointer transition-all duration-200 hover:shadow-md border-l-4 ${question.scoring_type === 'match' ? 'border-l-blue-500 hover:bg-blue-50 hover:border-blue-200' : 'border-l-green-500 hover:bg-green-50 hover:border-green-200'
                    }`}
                  onClick={() => handleQuestionClick(question)}
                >
                  <CardContent className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span>{question.name}</span>
                          <Badge variant={question.scoring_type === 'match' ? 'secondary' : 'default'} className="text-xs">
                            {question.scoring_type === 'match' ? '客观题' : '主观题'}
                          </Badge>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              >
                                <Info className="h-4 w-4" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-120">
                              <div className="space-y-3">
                                <div>
                                  <div className="text-sm font-medium mb-2">答案(评分标准):</div>
                                  <div className="text-sm p-2 bg-muted rounded">
                                    <MathRenderer content={question.answer} />
                                  </div>
                                </div>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                        {/* <Badge
                          variant={
                            question.gradingStatus === "completed"
                              ? "default"
                              : question.gradingStatus === "in_progress"
                              ? "secondary"
                              : "outline"
                          }
                          className="text-xs"
                        >
                          {question.gradingStatus === "completed"
                            ? "阅卷完成"
                            : question.gradingStatus === "in_progress"
                            ? "进行中"
                            : "未开始"}
                        </Badge> */}
                      </div>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex justify-between">
                          <span>平均分:</span>
                          <span className="font-medium">
                            {question.avg_score.toFixed(1)}/{question.score.toFixed(1)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>阅卷进度:</span>
                          <span className="font-medium">
                            {question.success_count}/{question.total_count}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>核查比例:</span>
                          <span className="font-medium">0%</span>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        {/* <Button
                          size="sm"
                          variant="outline"
                          className="h-6 text-xs flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGradingAction(
                              question.gradingStatus === "not_started"
                                ? "start"
                                : "stop",
                              question
                            );
                          }}
                        >
                          {question.gradingStatus === "not_started" ? (
                            <>
                              <Play className="h-2 w-2 mr-1" />
                              开始阅卷
                            </>
                          ) : (
                            <>
                              <Square className="h-2 w-2 mr-1" />
                              停止阅卷
                            </>
                          )}
                        </Button> */}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomeworkReview;
