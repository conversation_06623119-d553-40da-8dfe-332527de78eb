import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  Users,
  UserCheck,
  UserX,
  TrendingUp,
  Download,
  Eye,
  BookOpen,
  ChevronRight,
} from "lucide-react";
import { useHomeworkStore } from "@/stores";
import { ClassSummaryResponse, homeworkReportApi } from "@/services/homeworkReportApi";
import { getTenantInfoFromLocalStorage } from "@/lib/apiUtils";
const HomeworkReport: React.FC = () => {
  const navigate = useNavigate();
  const { homework } = useHomeworkStore();
  //HomeworkReport
  const [homeworkReport, setHomeworkReport] = useState<ClassSummaryResponse>();

  // Get tenant ID from auth context (mock for now)
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || "";
  const tenantName = identityInfo?.schema_name || "";
  useEffect(() => {
    // 这里可以调用API获取数据
    homeworkReportApi.getHomeworkClassReport(tenantId, tenantName, homework.id.toString()).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error("获取教学报告数据失败:", message);
        return;
      }
      setHomeworkReport(data);
      console.log("获取教学报告数据成功:", data);
    })

    console.log("加载教学报告数据");
  }, []);

  // 导出整体报告
  const handleExportOverallReport = async () => {
    try {
      console.log("导出整体报告");
      // 调用API
      // const response = await reportApi.exportOverallReport();
      alert("整体报告导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      alert("导出失败");
    }
  };

  // 查看班级报告
  const handleViewClassReport = (classId: string, className: string) => {
    navigate(`/class-report/${classId}`, {
      state: { className },
    });
  };

  // 导出班级报告
  const handleExportClassReport = async (
    classId: string,
    className: string
  ) => {
    try {
      console.log("导出班级报告:", classId, className);
      // 调用API
      // const response = await reportApi.exportClassReport(classId);
      alert(`${className}报告导出成功`);
    } catch (error) {
      console.error("导出失败:", error);
      alert("导出失败");
    }
  };

  // 导出整体班级报告
  const handleExportAllClassReports = async () => {
    try {
      console.log("导出整体班级报告");
      // 调用API
      // const response = await reportApi.exportAllClassReports();
      alert("整体班级报告导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      alert("导出失败");
    }
  };
  const getClassStatus = (is_active: Boolean) => {
    return is_active ? (
      <Badge variant="default">启用</Badge>
    ) : (
      <Badge variant="secondary">禁用</Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* 班级详情 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>班级详情</CardTitle>
            <CardDescription>当前共有{homeworkReport?.details.length}个班级</CardDescription>
            <Button
              onClick={handleExportAllClassReports}
              variant="outline"
              className="gap-2"
            >
              <Download className="h-4 w-4" />
              导出整体报告
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>班级名称</TableHead>
                <TableHead>班级编号</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>已评分学生数</TableHead>
                <TableHead>缺考人数</TableHead>
                <TableHead>异常学生数</TableHead>
                <TableHead>平均分</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {homeworkReport?.details.map((classItem) => (
                <TableRow key={classItem.class.id}>
                  <TableCell className="font-medium">
                    {classItem.class.name}
                  </TableCell>
                  <TableCell>{classItem.class.code}</TableCell>
                  <TableCell>{getClassStatus(classItem.class.is_active)}</TableCell>
                  <TableCell>{classItem.scoring_student_count}</TableCell>
                  <TableCell>{classItem.absent_student_count}</TableCell>
                  <TableCell>{classItem.error_student_count}</TableCell>
                  <TableCell>{classItem.avg_score}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleViewClassReport(
                            classItem.class.id,
                            classItem.class.name
                          )
                        }
                        className="gap-1"
                      >
                        <Eye className="h-3 w-3" />
                        查看班级报告
                      </Button>
                      {/* <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleExportClassReport(
                            classItem.class.id,
                            classItem.class.name
                          )
                        }
                        className="gap-1"
                      >
                        <Download className="h-3 w-3" />
                        导出班级报告
                      </Button> */}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomeworkReport;
