import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface ScoreDistributionChartProps {
  scoredMap: Record<string, any[]>;
  onBarClick: (students: any[], score: string) => void;
}

// 预定义颜色方案 - 与AnswerStatisticsChart保持一致
const SCORE_COLORS = ['#059669', '#10B981', '#34D399', '#6EE7B7', '#A7F3D0', '#D1FAE5', '#F0FDF4'];

export const ScoreDistributionChart: React.FC<ScoreDistributionChartProps> = ({
  scoredMap,
  onBarClick
}) => {
  // 主观题数据处理 - 参考AnswerStatisticsChart的prepareScoreData方法
  const prepareScoreData = () => {
    const totalStudents = Object.values(scoredMap).flat().length;
    
    return Object.entries(scoredMap)
      .map(([score, students]) => ({
        score: parseFloat(score),
        name: `${score}分`,
        value: students.length, // 使用value字段保持与AnswerStatisticsChart一致
        percentage: (students.length / totalStudents) * 100,
        color: SCORE_COLORS[Object.keys(scoredMap).indexOf(score) % SCORE_COLORS.length],
        students: students
      }))
      .sort((a, b) => b.score - a.score); // 按分数降序排列
  };

  const data = prepareScoreData();

  // 柱状图点击处理 - 参考AnswerStatisticsChart的handleBarClick方法
  const handleBarClick = (data: any) => {
    onBarClick(data.students, `${data.score}分`);
  };

  // 自定义柱状图提示 - 参考AnswerStatisticsChart的renderBarTooltip方法
  const renderBarTooltip = (props: any) => {
    if (props.active && props.payload && props.payload.length > 0) {
      const data = props.payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">
            分数段: <span className="font-semibold">{data.name}</span>
          </p>
          <p className="text-sm text-gray-600">
            人数: <span className="font-medium text-blue-600">{data.value}</span>
          </p>
          <p className="text-sm text-gray-600">
            占比: <span className="font-medium text-gray-900">{data.percentage.toFixed(1)}%</span>
          </p>
          <p className="text-xs text-gray-500 mt-1">点击查看学生详情</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full">
      <h4 className="text-sm font-medium text-gray-700 mb-4 text-center">得分分布统计</h4>
      
      {/* 柱状图 - 完全参考AnswerStatisticsChart的样式 */}
      <ResponsiveContainer width="100%" height={350}>
        <BarChart 
          data={data} 
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          barCategoryGap="20%"
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="name"
            tick={{ fontSize: 12 }}
            stroke="#666"
            height={60}
            angle={-45}
          />
          <YAxis 
            label={{ value: '人数', angle: -90, position: 'insideLeft' }}
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <Tooltip content={renderBarTooltip} />
          <Bar 
            dataKey="value"
            onClick={handleBarClick}
            className="cursor-pointer"
            radius={[6, 6, 0, 0]}
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.color}
                stroke={entry.color}
                strokeWidth={1}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>

      {/* 统计信息 - 参考AnswerStatisticsChart的统计信息布局 */}
      <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-lg font-semibold text-gray-900">
            {data.reduce((acc, item) => Math.max(acc, item.value), 0)}
          </div>
          <div className="text-sm text-gray-600">最高人数分数段</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-lg font-semibold text-gray-900">
            {((data.reduce((acc, item) => acc + item.score * item.value, 0) / 
               data.reduce((acc, item) => acc + item.value, 0)) || 0).toFixed(1)}
          </div>
          <div className="text-sm text-gray-600">平均分</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-3 md:col-span-1 col-span-2">
          <div className="text-lg font-semibold text-gray-900">
            {Math.max(...data.map(item => item.score))}
          </div>
          <div className="text-sm text-gray-600">最高分</div>
        </div>
      </div>
    </div>
  );
};
