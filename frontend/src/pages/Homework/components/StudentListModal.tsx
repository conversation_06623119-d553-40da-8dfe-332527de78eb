import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { StudentAnswerDetailModal } from './StudentAnswerDetailModal';
import { StudentInfo } from './QuestionDisplayCard';

// 扩展的学生信息接口，包含原始评分数据
interface ExtendedStudentInfo extends StudentInfo {
  scoreData?: any; // 原始的评分数据，包含答题图片等信息
}
import { Eye, User, FileImage } from 'lucide-react';

// 评分标准接口
interface ScoringCriterion {
  id: string;
  scoring_type: 'Match' | 'AI' | 'Manual';
  mode: string | null;
  criteriaName: string | null;
  answer: string | null;
  score: number; // 满分
  ocr_work_id: string | null;
  check_work_id: string | null;
  questionTips: string | null;
}

interface StudentListModalProps {
  isOpen: boolean;
  onClose: () => void;
  students: (StudentInfo | ExtendedStudentInfo)[];
  title: string;
  questionId: string;
  criterion?: ScoringCriterion; // 评分标准
}

export const StudentListModal: React.FC<StudentListModalProps> = ({
  isOpen,
  onClose,
  students,
  title,
  questionId,
  criterion
}) => {
  const [selectedStudent, setSelectedStudent] = useState<StudentInfo | ExtendedStudentInfo | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const handleStudentClick = (student: StudentInfo | ExtendedStudentInfo) => {
    setSelectedStudent(student);
    setIsDetailModalOpen(true);
  };

  const handleDetailModalClose = () => {
    setIsDetailModalOpen(false);
    setSelectedStudent(null);
  };

  // 获取满分，优先使用criterion中的score，否则使用默认值10
  const maxScore = criterion?.score || 10;

  const getScoreBadgeVariant = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'default'; // 绿色，优秀
    if (percentage >= 80) return 'secondary'; // 蓝色，良好
    if (percentage >= 60) return 'outline'; // 灰色，及格
    return 'destructive'; // 红色，不及格
  };

  const getScoreText = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return '优秀';
    if (percentage >= 80) return '良好';
    if (percentage >= 60) return '及格';
    return '不及格';
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {title}
            </DialogTitle>
            <DialogDescription>
              共 {students.length} 名学生 · 点击学生查看答题详情
            </DialogDescription>
          </DialogHeader>

          <div className="mt-4">
            {students.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                暂无学生数据
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>学生信息</TableHead>
                    <TableHead>学号</TableHead>
                    <TableHead>得分</TableHead>
                    <TableHead>等级</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {students.map((student) => (
                    <TableRow 
                      key={student.student_id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleStudentClick(student)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="text-xs">
                              {student.student_name.slice(-2)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{student.student_name}</div>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {student.student_number}
                        </code>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium text-lg">
                          {student.score}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge variant={getScoreBadgeVariant(student.score, maxScore)}>
                          {getScoreText(student.score, maxScore)}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {student.answer_image_url ? (
                            <div className="flex items-center gap-1 text-green-600">
                              <FileImage className="h-4 w-4" />
                              <span className="text-sm">已提交</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-gray-500">
                              <FileImage className="h-4 w-4" />
                              <span className="text-sm">未提交</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStudentClick(student);
                          }}
                          className="gap-1"
                        >
                          <Eye className="h-3 w-3" />
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>

          {/* 统计信息 */}
          {students.length > 0 && (
            <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
              <div className="text-center">
                <div className="text-lg font-semibold">
                  {students.length}
                </div>
                <div className="text-sm text-muted-foreground">总人数</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold">
                  {(students.reduce((sum, s) => sum + s.score, 0) / students.length).toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">平均分</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold">
                  {Math.max(...students.map(s => s.score))}
                </div>
                <div className="text-sm text-muted-foreground">最高分</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold">
                  {Math.min(...students.map(s => s.score))}
                </div>
                <div className="text-sm text-muted-foreground">最低分</div>
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4">
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 学生答题详情弹窗 */}
      {selectedStudent && (
        <StudentAnswerDetailModal
          isOpen={isDetailModalOpen}
          onClose={handleDetailModalClose}
          student={selectedStudent}
          questionId={questionId}
          scoreData={'scoreData' in selectedStudent ? selectedStudent.scoreData : undefined}
          criterion={criterion}
        />
      )}
    </>
  );
};