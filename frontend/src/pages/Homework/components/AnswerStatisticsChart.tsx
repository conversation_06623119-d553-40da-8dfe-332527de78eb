import React from 'react';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { QuestionReview, ChoiceDistribution, ScoreDistribution, StudentInfo } from './QuestionDisplayCard';

interface AnswerStatisticsChartProps {
  question: QuestionReview;
  onChartClick: (students: StudentInfo[], label: string) => void;
}

// 预定义颜色方案
const CHOICE_COLORS = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#F97316'];
const SCORE_COLORS = ['#059669', '#10B981', '#34D399', '#6EE7B7', '#A7F3D0', '#D1FAE5', '#F0FDF4'];

export const AnswerStatisticsChart: React.FC<AnswerStatisticsChartProps> = ({
  question,
  onChartClick
}) => {
  // 客观题数据处理
  const prepareChoiceData = (distribution: ChoiceDistribution[]) => {
    return distribution.map((item, index) => ({
      name: `选项 ${item.choice}`,
      value: item.count,
      percentage: item.percentage,
      color: CHOICE_COLORS[index % CHOICE_COLORS.length],
      choice: item.choice,
      students: item.students
    }));
  };

  // 主观题数据处理
  const prepareScoreData = (distribution: ScoreDistribution[]) => {
    return distribution
      .sort((a, b) => b.score - a.score) // 按分数降序排列
      .map((item, index) => ({
        name: `${item.score}分`,
        value: item.count,
        percentage: item.percentage,
        score: item.score,
        color: SCORE_COLORS[index % SCORE_COLORS.length],
        students: item.students
      }));
  };

  // 饼图点击处理
  const handlePieClick = (data: any) => {
    onChartClick(data.students, `选项 ${data.choice}`);
  };

  // 柱状图点击处理
  const handleBarClick = (data: any) => {
    onChartClick(data.students, `${data.score}分`);
  };

  // 自定义饼图标签
  const renderCustomPieLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percentage }: any) => {
    if (percentage < 5) return null; // 小于5%不显示标签
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="500"
      >
        {`${percentage.toFixed(1)}%`}
      </text>
    );
  };

  // 自定义饼图提示
  const renderPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-gray-600">
            人数: <span className="font-medium text-gray-900">{data.value}</span>
          </p>
          <p className="text-sm text-gray-600">
            占比: <span className="font-medium text-gray-900">{data.percentage.toFixed(1)}%</span>
          </p>
          <p className="text-xs text-gray-500 mt-1">点击查看学生详情</p>
        </div>
      );
    }
    return null;
  };

  // 自定义柱状图提示
  const renderBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-gray-600">
            人数: <span className="font-medium text-gray-900">{data.value}</span>
          </p>
          <p className="text-sm text-gray-600">
            占比: <span className="font-medium text-gray-900">{data.percentage.toFixed(1)}%</span>
          </p>
          <p className="text-xs text-gray-500 mt-1">点击查看学生详情</p>
        </div>
      );
    }
    return null;
  };

  if (question.question_type === 'multiple_choice' && question.statistics.choices_distribution) {
    const data = prepareChoiceData(question.statistics.choices_distribution);
    
    return (
      <div className="w-full">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* 饼图 */}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">选项分布图</h4>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomPieLabel}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  onClick={handlePieClick}
                  className="cursor-pointer"
                >
                  {data.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.color}
                      stroke={entry.color}
                      strokeWidth={2}
                    />
                  ))}
                </Pie>
                <Tooltip content={renderPieTooltip} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  formatter={(value, entry: any) => (
                    <span style={{ color: entry.color }}>
                      {value} ({entry.payload.value}人)
                    </span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* 柱状图 */}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">选项人数统计</h4>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  stroke="#666"
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  stroke="#666"
                />
                <Tooltip content={renderBarTooltip} />
                <Bar 
                  dataKey="value" 
                  onClick={handleBarClick}
                  className="cursor-pointer"
                  radius={[4, 4, 0, 0]}
                >
                  {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  }

  if (question.question_type === 'subjective' && question.statistics.score_distribution) {
    const data = prepareScoreData(question.statistics.score_distribution);
    
    return (
      <div className="w-full">
        <h4 className="text-sm font-medium text-gray-700 mb-4 text-center">得分分布统计</h4>
        
        {/* 柱状图 */}
        <ResponsiveContainer width="100%" height={350}>
          <BarChart 
            data={data} 
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            barCategoryGap="20%"
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="name"
              tick={{ fontSize: 12, angle: -45, textAnchor: 'end' }}
              stroke="#666"
              height={60}
            />
            <YAxis 
              label={{ value: '人数', angle: -90, position: 'insideLeft' }}
              tick={{ fontSize: 12 }}
              stroke="#666"
            />
            <Tooltip content={renderBarTooltip} />
            <Bar 
              dataKey="value"
              onClick={handleBarClick}
              className="cursor-pointer"
              radius={[6, 6, 0, 0]}
            >
              {data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={entry.color}
                  stroke={entry.color}
                  strokeWidth={1}
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>

        {/* 统计信息 */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-lg font-semibold text-gray-900">
              {data.reduce((acc, item) => Math.max(acc, item.value), 0)}
            </div>
            <div className="text-sm text-gray-600">最高人数分数段</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-lg font-semibold text-gray-900">
              {((data.reduce((acc, item) => acc + item.score * item.value, 0) / 
                 data.reduce((acc, item) => acc + item.value, 0)) || 0).toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">平均分</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-3 md:col-span-1 col-span-2">
            <div className="text-lg font-semibold text-gray-900">
              {question.max_score}
            </div>
            <div className="text-sm text-gray-600">满分</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center h-48 text-gray-500">
      暂无统计数据
    </div>
  );
};