import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Plus } from "lucide-react";
import { HomeworkStatusEnum } from "@/types/homework";
import { SubjectGroupsDetail } from "@/types/subjectGroups";

interface CreateHomeworkDialogProps {
  subjectGroups: SubjectGroupsDetail[];
  onSubmit: (formData: {
    homework_name: string;
    homework_status: HomeworkStatusEnum;
    subject_group_id?: string;
    description?: string;
  }) => Promise<void>;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
}

export function CreateHomeworkDialog({
  subjectGroups,
  onSubmit,
  onOpenChange,
  children,
}: CreateHomeworkDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    homework_name: "",
    homework_status: HomeworkStatusEnum.Draft,
    subject_group_id: "",
    description: "",
  });

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (onOpenChange) {
      onOpenChange(open);
    }
    if (!open) {
      // Reset form when dialog is closed
      setFormData({
        homework_name: "",
        homework_status: HomeworkStatusEnum.Draft,
        subject_group_id: "",
        description: "",
      });
    }
  };

  const handleSubmit = async () => {
    if (!formData.homework_name) {
      console.error("请填写作业名称");
      return;
    }

    try {
      await onSubmit({
        homework_name: formData.homework_name,
        homework_status: formData.homework_status,
        subject_group_id: formData.subject_group_id || undefined,
        description: formData.description || undefined,
      });
      handleOpenChange(false);
    } catch (error) {
      console.error("创建作业失败:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children || (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            新建作业
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>新建作业</DialogTitle>
          <DialogDescription>请填写作业的基本信息</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">作业名称 *</Label>
              <Input
                id="name"
                value={formData.homework_name}
                onChange={(e) =>
                  setFormData({ ...formData, homework_name: e.target.value })
                }
                placeholder="请输入作业名称"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="homework_status">作业类型 *</Label>
              <Select
                value={formData.homework_status}
                onValueChange={(value) =>
                  setFormData({
                    ...formData,
                    homework_status: value as HomeworkStatusEnum,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择作业类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={HomeworkStatusEnum.Draft}>草稿</SelectItem>
                  <SelectItem value={HomeworkStatusEnum.Doing}>
                    处理中
                  </SelectItem>
                  <SelectItem value={HomeworkStatusEnum.Done}>
                    已完成
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subject_group_id">学科组</Label>
              <Select
                value={formData.subject_group_id}
                onValueChange={(value) =>
                  setFormData({
                    ...formData,
                    subject_group_id: value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择学科" />
                </SelectTrigger>
                <SelectContent>
                  {subjectGroups.map((subjectGroup) => (
                    <SelectItem
                      key={subjectGroup.id.toString()}
                      value={subjectGroup.id.toString()}
                    >
                      {subjectGroup.group_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">作业说明</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="请输入作业说明"
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
          >
            取消
          </Button>
          <Button onClick={handleSubmit}>创建作业</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
