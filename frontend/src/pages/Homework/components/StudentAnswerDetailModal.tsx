import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { StudentInfo } from './QuestionDisplayCard';
import MathRenderer from '../../../components/math/MathRenderer';

// 从后端API返回的数据结构
interface ScoreBlock {
  block_id: string;
  answer_block_url: string;
  answer_block_group_id: string;
  answer_content: string;
}

interface ScoreDetail {
  status: string;
  score: {
    source: string;
    parsedValue: number;
  };
  scoring_type: string;
  ocr: string;
  reason: string;
}

interface StudentBase {
  student_id: string;
  student_number: string;
  student_name: string;
}

interface ScoreVo {
  id: string;
  criteria_id: string;
  score: {
    source: string;
    parsedValue: number;
  };
  status: string;
  blocks: ScoreBlock[];
  details: ScoreDetail[];
  student: StudentBase;
  answer: string;
}
import {
  FileImage,
  Star,
  MessageSquare,
  ZoomIn,
  ZoomOut,
  RotateCw,
  X
} from 'lucide-react';

// 评分标准接口
interface ScoringCriterion {
  id: string;
  scoring_type: 'Match' | 'AI' | 'Manual';
  mode: string | null;
  criteriaName: string | null;
  answer: string | null;
  score: number; // 满分
  ocr_work_id: string | null;
  check_work_id: string | null;
  questionTips: string | null;
}

interface StudentAnswerDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  student: StudentInfo;
  questionId: string;
  scoreData?: ScoreVo; // 可选的评分数据，包含答题图片等详细信息
  criterion?: ScoringCriterion; // 评分标准，包含满分等信息
}

export const StudentAnswerDetailModal: React.FC<StudentAnswerDetailModalProps> = ({
  isOpen,
  onClose,
  student,
  questionId,
  scoreData,
  criterion
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [imageScale, setImageScale] = useState(1);
  const [imageRotation, setImageRotation] = useState(0);

  // 获取答题图片URL，优先使用scoreData中的blocks数据
  const getAnswerImageUrl = (): string => {
    if (scoreData?.blocks && scoreData.blocks.length > 0) {
      return scoreData.blocks[0].answer_block_url;
    }
    return student.answer_image_url || '';
  };

  const answerImageUrl = getAnswerImageUrl();

  // 获取满分，优先使用criterion中的score，否则使用默认值10
  const maxScore = criterion?.score || 10;

  // 处理文本中的换行符，将 \n 转换为 MathJax 能识别的换行
  // 同时处理 Rust ScoreReason 枚举结构
  const processTextForMathJax = (text: any): string => {
    // 处理各种可能的输入类型
    if (text === null || text === undefined) return '';

    let textStr: string;
    try {
      if (typeof text === 'string') {
        textStr = text;
      } else if (typeof text === 'number') {
        textStr = text.toString();
      } else if (typeof text === 'object') {
        // 处理 Rust ScoreReason 枚举结构
        if (text.Text && typeof text.Text === 'string') {
          // ScoreReason::Text(String) 情况
          textStr = text.Text;
        } else if (text === 'None' || text === 'Blank' || text === 'Composition') {
          // ScoreReason::None, Blank, Composition 情况
          return '';
        } else {
          // 其他对象，尝试转换为JSON字符串
          textStr = JSON.stringify(text);
        }
      } else {
        textStr = String(text);
      }
    } catch (error) {
      console.warn('processTextForMathJax: Failed to convert text to string:', text, error);
      return '';
    }

    return textStr.replace(/\\n/g, '<br />').replace(/\n/g, '<br />');
  };

  // 检查 ScoreReason 是否包含有效的文本内容
  const hasValidReasonText = (reason: any): boolean => {
    if (!reason) return false;

    // 如果是字符串且不是 'None'
    if (typeof reason === 'string') {
      return reason !== 'None' && reason !== 'Blank' && reason !== 'Composition' && reason.trim() !== '';
    }

    // 如果是对象且包含 Text 字段
    if (typeof reason === 'object' && reason.Text && typeof reason.Text === 'string') {
      return reason.Text.trim() !== '';
    }

    return false;
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const handleZoomIn = () => {
    setImageScale(prev => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setImageScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const handleRotate = () => {
    setImageRotation(prev => (prev + 90) % 360);
  };

  const resetImageTransform = () => {
    setImageScale(1);
    setImageRotation(0);
  };

  const getScoreBadgeVariant = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'default';
    if (percentage >= 80) return 'secondary';
    if (percentage >= 60) return 'outline';
    return 'destructive';
  };

  const getScoreText = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return '优秀';
    if (percentage >= 80) return '良好';
    if (percentage >= 60) return '及格';
    return '不及格';
  };

  // 获取评分详情，优先使用scoreData中的details
  const getGradingDetails = () => {
    if (scoreData?.details && scoreData.details.length > 0) {
      const detail = scoreData.details[0]; // 取第一个评分详情

      // 安全地提取分数
      let score: number | undefined;
      if (detail.score) {
        if (typeof detail.score === 'number') {
          score = detail.score;
        } else if (detail.score.parsedValue !== undefined) {
          score = detail.score.parsedValue;
        } else if (detail.score.source) {
          score = parseFloat(detail.score.source);
        }
      }

      return {
        scoring_type: detail.scoring_type,
        ocr_result: detail.ocr,
        reason: detail.reason,
        score: score,
        status: detail.status
      };
    }
    return null;
  };

  const gradingDetails = getGradingDetails();

  // 调试信息
  console.log('StudentAnswerDetailModal Debug:', {
    scoreData,
    gradingDetails,
    maxScore
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] w-full max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-sm">
                {student.student_name.slice(-2)}
              </AvatarFallback>
            </Avatar>
            <div>
              <span>{student.student_name} 的答题详情</span>
              <Badge variant={getScoreBadgeVariant(student.score, maxScore)} className="ml-2">
                {student.score}分 - {getScoreText(student.score, maxScore)}
              </Badge>
            </div>
          </DialogTitle>
          <DialogDescription>
            学号：{student.student_number}
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-6">
          {/* 左侧：答题原图 */}
          <div className="lg:col-span-3">
            <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileImage className="h-5 w-5" />
                  答题原图
                </div>
                
                {/* 图片操作按钮 */}
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleZoomOut}
                    disabled={imageScale <= 0.5}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleZoomIn}
                    disabled={imageScale >= 3}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleRotate}
                  >
                    <RotateCw className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={resetImageTransform}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="relative bg-gray-50 rounded-lg overflow-hidden" style={{ height: '600px' }}>
                {answerImageUrl ? (
                  <>
                    {imageLoading && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-32 w-full" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                      </div>
                    )}
                    
                    {imageError ? (
                      <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-500">
                        <FileImage className="h-12 w-12 mb-2" />
                        <p>图片加载失败</p>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="mt-2"
                          onClick={() => {
                            setImageError(false);
                            setImageLoading(true);
                          }}
                        >
                          重试
                        </Button>
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center overflow-hidden">
                        <img
                          src={answerImageUrl}
                          alt={`${student.student_name}的答题图片`}
                          className="max-w-full max-h-full object-contain transition-transform duration-200"
                          style={{
                            transform: `scale(${imageScale}) rotate(${imageRotation}deg)`,
                            transformOrigin: 'center center'
                          }}
                          onLoad={handleImageLoad}
                          onError={handleImageError}
                        />
                      </div>
                    )}
                  </>
                ) : (
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-500">
                    <FileImage className="h-16 w-16 mb-4" />
                    <p className="text-lg font-medium">暂无答题图片</p>
                    <p className="text-sm">该学生尚未提交答题内容</p>
                  </div>
                )}
              </div>
              
              {/* 图片信息 */}
              <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
                <span>缩放: {(imageScale * 100).toFixed(0)}%</span>
                <span>旋转: {imageRotation}°</span>
              </div>
            </CardContent>
          </Card>
          </div>

          {/* 右侧：评分详情 */}
          <div className="lg:col-span-2 space-y-4">
            {/* 评分标准 */}
            {criterion && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    评分标准
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 题目名称 */}
                  {criterion.criteriaName && (
                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-2">题目</h4>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="prose prose-sm max-w-none leading-relaxed">
                          <MathRenderer content={processTextForMathJax(criterion.criteriaName)} />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 标准答案 */}
                  {criterion.answer && (
                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-2">标准答案</h4>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="prose prose-sm max-w-none leading-relaxed">
                          <MathRenderer content={processTextForMathJax(criterion.answer)} />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 评分信息 */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="bg-blue-50 rounded-lg p-3 text-center">
                      <div className="font-medium text-blue-900">满分</div>
                      <div className="text-lg font-bold text-blue-600">{criterion.score}分</div>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-3 text-center">
                      <div className="font-medium text-purple-900">评分方式</div>
                      <div className="text-sm font-medium text-purple-600">
                        {criterion.scoring_type === 'Match' ? '匹配评分' :
                         criterion.scoring_type === 'AI' ? 'AI评分' : '人工评分'}
                      </div>
                    </div>
                  </div>

                  {/* 题目提示 */}
                  {criterion.questionTips && (
                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-2">题目提示</h4>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="prose prose-sm max-w-none leading-relaxed">
                          <MathRenderer content={processTextForMathJax(criterion.questionTips)} />
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* 总体评分 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  总体评分
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-4xl font-bold text-primary mb-2">
                    {student.score}
                  </div>
                  <Badge variant={getScoreBadgeVariant(student.score, maxScore)} className="text-sm px-3 py-1">
                    {getScoreText(student.score, maxScore)}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">评分方式</div>
                    <div className="text-gray-600">
                      {gradingDetails?.scoring_type === 'Match' ? '匹配评分' :
                       gradingDetails?.scoring_type === 'AI' ? 'AI评分' : '系统评分'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">评分状态</div>
                    <div className="text-gray-600">
                      {gradingDetails?.status === 'Done' ? '已完成' : '处理中'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 详细评分标准 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  详细评分
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {gradingDetails ? (
                    <>
                      {/* 评分方式 */}
                      <div className="bg-blue-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-blue-900">评分方式</span>
                          <Badge variant="secondary">
                            {gradingDetails.scoring_type === 'Match' ? '匹配评分' :
                             gradingDetails.scoring_type === 'AI' ? 'AI评分' : '人工评分'}
                          </Badge>
                        </div>
                        <div className="text-sm text-blue-700">
                          状态: {gradingDetails.status === 'Done' ? '已完成' : '处理中'}
                        </div>
                      </div>

                      {/* OCR识别结果 */}
                      {gradingDetails.ocr_result && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="font-medium text-gray-900 mb-2">识别结果</div>
                          <div className="text-sm text-gray-700 bg-white rounded border p-3">
                            <div className="prose prose-sm max-w-none leading-relaxed">
                              <MathRenderer content={processTextForMathJax(gradingDetails.ocr_result)} />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 评分原因/详情 */}
                      {hasValidReasonText(gradingDetails.reason) && (
                        <div className="bg-green-50 rounded-lg p-4">
                          <div className="font-medium text-green-900 mb-2">评分详情</div>
                          <div className="text-sm text-green-800">
                            <MathRenderer content={processTextForMathJax(gradingDetails.reason)} />
                          </div>
                        </div>
                      )}

                      {/* 得分信息 */}
                      <div className="bg-purple-50 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-purple-900">系统评分</span>
                          <div className="text-lg font-bold text-purple-600">
                            {gradingDetails.score !== undefined ? gradingDetails.score : '未知'} / {maxScore}
                          </div>
                        </div>
                        <div className="mt-2 w-full bg-purple-200 rounded-full h-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${gradingDetails.score !== undefined ? (gradingDetails.score / maxScore) * 100 : 0}%` }}
                          ></div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>暂无详细评分信息</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 总评 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">评分总结</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                  <div className="text-sm text-blue-900">
                    {hasValidReasonText(gradingDetails?.reason) ? (
                      <MathRenderer content={processTextForMathJax(gradingDetails?.reason)} />
                    ) : (
                      student.grading_details || '系统已完成自动评分，如需详细反馈请联系教师。'
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};