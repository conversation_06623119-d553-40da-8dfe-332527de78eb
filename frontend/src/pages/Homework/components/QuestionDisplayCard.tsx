import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AnswerStatisticsChart } from './AnswerStatisticsChart';
import { StudentListModal } from './StudentListModal';
import { ChevronDown, ChevronUp } from 'lucide-react';
import MathRenderer from '@/components/math/MathRenderer';

export interface QuestionReview {
  question_id: string;
  question_number: number;
  question_type: 'multiple_choice' | 'subjective';
  question_content: string;
  scoring_criteria: string;
  max_score: number;
  statistics: QuestionStatistics;
}

export interface QuestionStatistics {
  choices_distribution: ChoiceDistribution[];
  score_distribution: ScoreDistribution[];
  total_students: number;
}

export interface ChoiceDistribution {
  choice: string;
  count: number;
  percentage: number;
  students: StudentInfo[];
}

export interface ScoreDistribution {
  score: number;
  count: number;
  percentage: number;
  students: StudentInfo[];
}

export interface StudentInfo {
  student_id: string;
  student_name: string;
  student_number: string;
  answer_image_url?: string;
  score: number;
  grading_details: string;
}

interface QuestionDisplayCardProps {
  question: QuestionReview;
  index: number;
}

export const QuestionDisplayCard: React.FC<QuestionDisplayCardProps> = ({
  question,
  index
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [selectedStudents, setSelectedStudents] = useState<StudentInfo[]>([]);
  const [isStudentModalOpen, setIsStudentModalOpen] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState<string>('');

  // 预处理内容，支持\n换行
  const processContent = (content: string | undefined) => {
    if (!content) return '';
    return content.replace(/\\n/g, '<br />');
  };

  // 处理图表点击事件
  const handleChartClick = (students: StudentInfo[], label: string) => {
    setSelectedStudents(students);
    setSelectedLabel(label);
    setIsStudentModalOpen(true);
  };

  const getQuestionTypeLabel = (type: string) => {
    return type === 'multiple_choice' ? '选择题' : '主观题';
  };

  const getQuestionTypeVariant = (type: string) => {
    return type === 'multiple_choice' ? 'default' : 'secondary';
  };

  return (
    <>
      <Card className="overflow-hidden">
        <CardHeader className="bg-muted/50">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              <Badge variant="outline">第 {question.question_number} 题</Badge>
              <Badge variant={getQuestionTypeVariant(question.question_type)}>
                {getQuestionTypeLabel(question.question_type)}
              </Badge>
              <Badge variant="outline">{question.max_score} 分</Badge>
              <span className="text-sm font-normal text-muted-foreground">
                参与人数：{question.statistics.total_students} 人
              </span>
            </CardTitle>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="gap-2"
            >
              {isExpanded ? (
                <>收起 <ChevronUp className="h-4 w-4" /></>
              ) : (
                <>展开 <ChevronDown className="h-4 w-4" /></>
              )}
            </Button>
          </div>
        </CardHeader>
        
        {isExpanded && (
          <CardContent className="p-6 space-y-6">
            {/* 题目内容 */}
            <div>
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <span className="w-1 h-5 bg-primary rounded"></span>
                题目内容
              </h3>
              <div className="bg-background border rounded-lg p-4 shadow-sm">
                <div className="prose prose-sm max-w-none">
                  <MathRenderer content={processContent(question.question_content)} />
                </div>
              </div>
            </div>

            {/* 评分标准 */}
            <div>
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <span className="w-1 h-5 bg-blue-500 rounded"></span>
                评分标准
              </h3>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="text-sm text-blue-900">
                  <MathRenderer content={processContent(question.scoring_criteria)} />
                </div>
              </div>
            </div>

            {/* 答题统计 */}
            <div>
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <span className="w-1 h-5 bg-green-500 rounded"></span>
                答题统计
              </h3>
              
              <div className="bg-background border rounded-lg p-6">
                <AnswerStatisticsChart
                  question={question}
                  onChartClick={handleChartClick}
                />
              </div>
              
              {/* 统计摘要 */}
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3">
                {question.question_type === 'multiple_choice' 
                  ? question.statistics.choices_distribution?.map((choice) => (
                      <div 
                        key={choice.choice} 
                        className="text-center p-3 bg-background border rounded-lg hover:shadow-sm transition-shadow cursor-pointer"
                        onClick={() => handleChartClick(choice.students, `选项 ${choice.choice}`)}
                      >
                        <div className="font-medium text-lg">选项 {choice.choice}</div>
                        <div className="text-sm text-muted-foreground">
                          {choice.count} 人
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {choice.percentage.toFixed(1)}%
                        </div>
                      </div>
                    ))
                  : question.statistics.score_distribution?.slice(0, 6).map((score) => (
                      <div 
                        key={score.score} 
                        className="text-center p-3 bg-background border rounded-lg hover:shadow-sm transition-shadow cursor-pointer"
                        onClick={() => handleChartClick(score.students, `${score.score} 分`)}
                      >
                        <div className="font-medium text-lg">{score.score} 分</div>
                        <div className="text-sm text-muted-foreground">
                          {score.count} 人
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {score.percentage.toFixed(1)}%
                        </div>
                      </div>
                    ))
                }
              </div>

              {/* 操作提示 */}
              <div className="mt-4 text-center">
                <div className="inline-flex items-center gap-2 text-xs text-muted-foreground bg-muted px-3 py-2 rounded-full">
                  💡 点击图表或统计卡片查看对应学生信息
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* 学生列表弹窗 */}
      <StudentListModal
        isOpen={isStudentModalOpen}
        onClose={() => setIsStudentModalOpen(false)}
        students={selectedStudents}
        title={`${selectedLabel} - 学生列表`}
        questionId={question.question_id}
      />
    </>
  );
};