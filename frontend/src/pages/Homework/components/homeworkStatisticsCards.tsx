import React from 'react';
import { <PERSON>, <PERSON>Header, CardTitle, CardContent } from '@/components/ui/card';
import {  Users, Calendar, CheckCircle2, Clock } from 'lucide-react';

interface HomeworkStatisticsCardsProps {
  totalHomeworks: number;
  inProgressHomeworks: number;
  completedHomeworks: number;
  totalStudents: number;
}
const HomeworkStatisticsCards: React.FC<HomeworkStatisticsCardsProps> = ({
  totalHomeworks,
  inProgressHomeworks,
  completedHomeworks,
  totalStudents,
}) => {
    return(
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">总作业数</CardTitle>
        <Calendar className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{totalHomeworks || 0}</div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">进行中作业</CardTitle>
        <Clock className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{inProgressHomeworks || 0}</div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">已完成作业</CardTitle>
        <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{completedHomeworks || 0}</div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">总参与学生</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{totalStudents || 0}</div>
      </CardContent>
    </Card>
  </div>
  )
};

export default HomeworkStatisticsCards;