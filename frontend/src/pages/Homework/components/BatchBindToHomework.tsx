import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { TeachingClassesDetail } from '@/types/teachingClasses';
import { Student } from '@/types/student';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import React, { useState, useEffect, useRef } from 'react';
import { UserPlus } from 'lucide-react';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';

interface BatchBindToHomeworkProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  classList: TeachingClassesDetail[];
  tenantId: string;
  tenantName: string;
  onBind: (studentIds: string[],classId:string) => Promise<void>;
  title?: string;
  description?: string;
}

const BatchBindToHomework: React.FC<BatchBindToHomeworkProps> = ({
  open,
  onOpenChange,
  classList,
  tenantId,
  tenantName,
  onBind,
  title = '批量绑定学生到作业',
  description = '请选择班级并勾选要绑定的学生',
}) => {
  const [selectedClassId, setSelectedClassId] = useState<string>('');
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isBinding, setIsBinding] = useState(false);
  const selectAllRef = useRef<HTMLButtonElement>(null);

  // 选择班级后拉取学生
  useEffect(() => {
    if (!selectedClassId) {
      setStudents([]);
      setSelectedStudents([]);
      return;
    }
    setIsLoading(true);
    TeachingClassesApi.findAllStudentInClass(tenantId, tenantName, { class_id: selectedClassId })
      .then((res) => {
        if (res && Array.isArray(res.data)) {
          setStudents(res.data);
        } else if (res && res.data) {
          setStudents([res.data]);
        } else {
          setStudents([]);
        }
        setSelectedStudents([]);
      })
      .catch(() => {
        setStudents([]);
        setSelectedStudents([]);
      })
      .finally(() => setIsLoading(false));
  }, [selectedClassId, tenantId, tenantName]);

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(students.map((s) => s.id));
    } else {
      setSelectedStudents([]);
    }
  };

  // 单个勾选
  const handleStudentToggle = (studentId: string) => {
    setSelectedStudents((prev) =>
      prev.includes(studentId)
        ? prev.filter((id) => id !== studentId)
        : [...prev, studentId]
    );
  };

  // 添加
  const handleBind = async () => {
    if (selectedStudents.length === 0) return;
    setIsBinding(true);
    try {
      await onBind(selectedStudents,selectedClassId);
      setSelectedStudents([]);
      setStudents([]);
      setSelectedClassId('');
      onOpenChange(false);
    } catch (error) {
      // 错误处理可自定义
      console.error('绑定学生失败:', error);
    } finally {
      setIsBinding(false);
    }
  };

  // 取消
  const handleCancel = () => {
    setSelectedStudents([]);
    setStudents([]);
    setSelectedClassId('');
    onOpenChange(false);
  };

  // useEffect(() => {
  //   // 设置全选checkbox的indeterminate
  //   if (selectAllRef.current) {
  //     selectAllRef.current.indeterminate =
  //       selectedStudents.length > 0 && selectedStudents.length < students.length;
  //   }
  // }, [selectedStudents, students]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {/* 班级选择 */}
          <div>
            <Label htmlFor="class_select">选择班级</Label>
            <Select
              value={selectedClassId}
              onValueChange={(value) => setSelectedClassId(value)}
              disabled={isLoading || isBinding}
            >
              <SelectTrigger>
                <SelectValue placeholder="请选择班级" />
              </SelectTrigger>
              <SelectContent>
                {classList.map((cls) => (
                  <SelectItem key={cls.id as string} value={cls.id as string}>
                    {cls.class_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 学生列表 */}
          {selectedClassId && (
            <div className="space-y-2">
              <Label>学生列表 {isLoading ? '(加载中...)' : `(${students.length}人)`}</Label>
              <ScrollArea className="h-[300px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          ref={selectAllRef}
                          checked={students.length > 0 && selectedStudents.length === students.length}
                          onCheckedChange={(checked) => handleSelectAll(!!checked)}
                          disabled={students.length === 0}
                        />
                      </TableHead>
                      <TableHead>姓名</TableHead>
                      <TableHead>学号</TableHead>
                      <TableHead>性别</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {students.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedStudents.includes(student.id)}
                            onCheckedChange={() => handleStudentToggle(student.id)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{student.student_name}</TableCell>
                        <TableCell>{student.student_number}</TableCell>
                        <TableCell>{student.gender || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}

          {/* 选中学生数量提示 */}
          {selectedStudents.length > 0 && (
            <div className="text-sm text-muted-foreground">已选择 {selectedStudents.length} 名学生</div>
          )}
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={isBinding}>
            取消
          </Button>
          <Button
            onClick={handleBind}
            disabled={selectedStudents.length === 0 || isBinding}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            {isBinding ? '添加中...' : `添加 (${selectedStudents.length})`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BatchBindToHomework;
