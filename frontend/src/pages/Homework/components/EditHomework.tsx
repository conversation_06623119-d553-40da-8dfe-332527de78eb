import React from "react";
import { HomeworkStatusEnum, UpdateHomeworkParams } from "@/types/homework";
import { SubjectGroupsDetail } from "@/types/subjectGroups";
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { SelectItem } from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from "@/components/ui/textarea";

interface EditHomeworkProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    homeworkForm: UpdateHomeworkParams;
    onHomeworkFormChange: (form: UpdateHomeworkParams) => void;
    subjectGroups: SubjectGroupsDetail[];
    onSave: () => void;
    onCancel: () => void;
}
const EditHomework: React.FC<EditHomeworkProps> = ({
    open,
    onOpenChange,
    homeworkForm,
    onHomeworkFormChange,
    subjectGroups,
    onSave,
    onCancel
}) => {
    return (
    <Dialog open={open} onOpenChange={onOpenChange}>
    <DialogContent className="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>编辑作业</DialogTitle>
        <DialogDescription>
          修改作业信息
        </DialogDescription>
      </DialogHeader>
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="edit_name">作业名称</Label>
          <Input
            id="edit_name"
            value={homeworkForm.homework_name.toString()}
            onChange={(e) => onHomeworkFormChange({ ...homeworkForm, homework_name: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="homework_status">作业类型 *</Label>
          <Select value={homeworkForm.homework_status.toString()} onValueChange={(value) => onHomeworkFormChange({ ...homeworkForm, homework_status: value as HomeworkStatusEnum })}>
            <SelectTrigger>
              <SelectValue placeholder="选择作业类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={HomeworkStatusEnum.Draft}>草稿</SelectItem>
              <SelectItem value={HomeworkStatusEnum.Doing}>处理中</SelectItem>
              <SelectItem value={HomeworkStatusEnum.Done}>已完成</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit_subject_group_id">学科组</Label>
          <Select value={homeworkForm.subject_group_id?.toString()} onValueChange={(value) => onHomeworkFormChange({ ...homeworkForm, subject_group_id: value })}>
            <SelectTrigger>
              <SelectValue placeholder="选择学科组" />
            </SelectTrigger>
            <SelectContent>
              {subjectGroups.map(subjectGroup => (
                <SelectItem key={subjectGroup.id.toString()} value={subjectGroup.id.toString()}>{subjectGroup.group_name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit_description">作业说明</Label>
          <Textarea
            id="edit_description"
            value={homeworkForm.description?.toString()}
            onChange={(e) => onHomeworkFormChange({ ...homeworkForm, description: e.target.value })}
          />
        </div>
      </div>
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => onOpenChange(false)}>
          取消
        </Button>
        <Button onClick={onSave}>
          保存
        </Button>
      </div>
    </DialogContent>
  </Dialog>
  )
}
export default EditHomework;