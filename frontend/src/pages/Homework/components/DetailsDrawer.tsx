import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, Sheet<PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '@/components/ui/sheet';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import MathRenderer from '@/components/math/MathRenderer';
import { Eye } from 'lucide-react';

interface DetailsDrawerProps {
  details: any[];
  blocks?: any[];
  trigger?: React.ReactNode;
  title?: string;
}

interface SentenceDetail {
  原句: string;
  纠错: string;
  润色: string;
  修改说明: string;
  评级: '优' | '中' | '差';
  评级理由: string;
}

const DetailsDrawer: React.FC<DetailsDrawerProps> = ({ details, trigger, title = '详情', blocks }) => {
  const [open, setOpen] = useState(false);
  const [essayTab, setEssayTab] = useState('analysis');
  const [hoveredSentence, setHoveredSentence] = useState<SentenceDetail | null>(null);
  const [popoverPosition, setPopoverPosition] = useState({ x: 0, y: 0 });
  const contentRefs = useRef<(HTMLDivElement | null)[]>([]);

  // 计算Popover的最佳位置
  const calculatePopoverPosition = (targetRect: DOMRect) => {
    const popoverWidth = 320; // w-80 = 320px
    const popoverHeight = 400; // 估算高度
    const margin = 10;

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = targetRect.left + targetRect.width / 2;
    let y = targetRect.bottom + margin;

    // 水平位置调整
    if (x + popoverWidth / 2 > viewportWidth - margin) {
      x = viewportWidth - popoverWidth / 2 - margin;
    }
    if (x - popoverWidth / 2 < margin) {
      x = popoverWidth / 2 + margin;
    }

    // 垂直位置调整
    if (y + popoverHeight > viewportHeight - margin) {
      y = targetRect.top - popoverHeight - margin;
    }

    // 如果上方也放不下，则放在视口中央
    if (y < margin) {
      y = (viewportHeight - popoverHeight) / 2;
    }

    return { x, y };
  };

  const handleMouseEnter = useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (target.classList.contains('sentence-highlight')) {
      const sentenceData = target.getAttribute('data-sentence');
      if (sentenceData) {
        try {
          const sentence: SentenceDetail = JSON.parse(sentenceData);
          setHoveredSentence(sentence);
          const rect = target.getBoundingClientRect();
          const position = calculatePopoverPosition(rect);
          setPopoverPosition(position);
        } catch (error) {
          console.error('解析句子数据失败:', error);
        }
      }
    }
  }, []);

  const handleMouseLeave = useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (target.classList.contains('sentence-highlight')) {
      setHoveredSentence(null);
    }
  }, []);

  // 重新绑定事件监听器
  const rebindEventListeners = useCallback(() => {
    // 先清除旧的事件监听器
    const contents = contentRefs.current.filter(Boolean);
    contents.forEach((content) => {
      if (content) {
        content.removeEventListener('mouseenter', handleMouseEnter, true);
        content.removeEventListener('mouseleave', handleMouseLeave, true);
      }
    });

    // 添加新的事件监听器
    setTimeout(() => {
      const newContents = contentRefs.current.filter(Boolean);
      newContents.forEach((content) => {
        if (content) {
          content.addEventListener('mouseenter', handleMouseEnter, true);
          content.addEventListener('mouseleave', handleMouseLeave, true);
        }
      });
    }, 100); // 延迟100ms确保DOM已更新
  }, [handleMouseEnter, handleMouseLeave]);

  // 监听drawer打开状态和details变化
  useEffect(() => {
    if (open) {
      rebindEventListeners();
    }
  }, [open, details]);

  // 监听essayTab变化，重新绑定事件
  useEffect(() => {
    if (open && essayTab === 'analysis') {
      rebindEventListeners();
    }
  }, [essayTab, open]);

  // 获取评级对应的背景色
  const getGradeColor = (grade: string) => {
    switch (grade) {
      case '优':
        return 'bg-green-100 hover:bg-green-200 text-green-800';
      case '中':
        return 'bg-yellow-100 hover:bg-yellow-200 text-yellow-800';
      case '差':
        return 'bg-red-100 hover:bg-red-200 text-red-800';
      default:
        return 'bg-gray-100 hover:bg-gray-200 text-gray-800';
    }
  };

  const getScoringType = (scoring_type: string) => {
    switch (scoring_type) {
      case 'Match':
        return '题卡匹配';
      case 'AI':
        return '自动AI阅卷';
      case 'Manual':
        return '老师手阅(登分)';
      case 'Online':
        return '在线分发阅卷';
      case 'Check':
        return '核查';
      default:
        return '';
    }
  };

  // 渲染作文句子
  const renderEssaySentences = (sentenceData: Record<string, Record<string, SentenceDetail>>) => {
    const sentences: Array<{ sentence: SentenceDetail; key: string }> = [];

    // 收集所有句子
    Object.entries(sentenceData).forEach(([paragraphKey, paragraph]) => {
      Object.entries(paragraph).forEach(([sentenceKey, sentenceDetail]) => {
        sentences.push({ sentence: sentenceDetail, key: `${paragraphKey}-${sentenceKey}` });
      });
    });

    return (
      <div className="space-y-2">
        {sentences.map(({ sentence, key }) => (
          <span
            key={key}
            className={`sentence-highlight ${getGradeColor(sentence.评级)} cursor-pointer px-1 py-0.5 rounded transition-colors inline-block mr-1 mb-1`}
            data-sentence={JSON.stringify(sentence)}
          >
            {sentence.原句}
          </span>
        ))}
      </div>
    );
  };

  // 渲染润色结果
  const renderPolishedText = (sentenceData: Record<string, Record<string, SentenceDetail>>) => {
    const sentences: SentenceDetail[] = [];

    Object.values(sentenceData).forEach((paragraph) => {
      Object.values(paragraph).forEach((sentenceDetail) => {
        sentences.push(sentenceDetail);
      });
    });

    return (
      <div className="prose max-w-none">
        <div className="leading-relaxed">
          {sentences.map((sentence, index) => (
            <span key={index} className="inline">
              {sentence.润色}
              {index < sentences.length - 1 ? ' ' : ''}
            </span>
          ))}
        </div>
      </div>
    );
  };

  // 检查是否为作文
  const isEssay = (detail: any) => {
    if (!detail.reason?.Text) return false;
    try {
      const parsed = JSON.parse(detail.reason.Text);
      return !!parsed.句子批改;
    } catch {
      return false;
    }
  };

  // 渲染作文内容
  const renderEssayContent = (detail: any, detailIndex: number) => {
    let parsedReason;
    try {
      parsedReason = JSON.parse(detail.reason.Text);
    } catch (error) {
      console.error('解析作文数据失败:', error);
      return null;
    }

    const sentenceData = parsedReason.句子批改;
    if (!sentenceData) return null;

    return (
      <Tabs value={essayTab} onValueChange={setEssayTab} className="w-full">
        {/* <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analysis">点评分析</TabsTrigger>
          <TabsTrigger value="original">原图</TabsTrigger>
        </TabsList> */}

        <TabsContent value="analysis" className="space-y-4">
          {/* 学生作答 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">学生作答</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none" ref={(el) => (contentRefs.current[detailIndex] = el)}>
                {renderEssaySentences(sentenceData)}
              </div>
            </CardContent>
          </Card>

          {/* 润色结果 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">润色结果</CardTitle>
            </CardHeader>
            <CardContent>{renderPolishedText(sentenceData)}</CardContent>
          </Card>

          {/* 其他评估内容 */}
          <div className="space-y-4">
            {Object.entries(parsedReason).map(([key, value]) => {
              if (key === '句子批改') return null;

              return (
                <Card key={key}>
                  <CardHeader>
                    <CardTitle className="text-base">{key}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {typeof value === 'object' && value !== null ? (
                        Object.entries(value as Record<string, any>).map(([subKey, subValue]) => (
                          <div key={subKey} className="border-l-2 border-gray-200 pl-3">
                            <div className="font-medium text-sm text-gray-700">{subKey}</div>
                            <div className="text-sm text-gray-600 mt-1">{String(subValue)}</div>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-gray-600">{String(value)}</div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
    );
  };

  // 渲染普通内容
  const renderNormalContent = (details: any[]) => {
    return (
      <div className="space-y-4">
        {details.map((detail, index) => (
          <div key={index} className="space-y-0 pt-5">
            <div>
              <span className="text-sm font-medium">阅卷状态:</span>
              <Badge className="ml-2" variant={detail.status === 'Done' ? 'default' : 'destructive'}>
                {detail.status === 'Done' ? '阅卷完成' : detail.status === 'OcrFailed' ? 'OCR识别异常' : '阅卷失败'}
              </Badge>
            </div>
            <div>
              <span className="text-sm font-medium">分数:</span>
              <span className="ml-2 text-lg font-bold text-blue-600">{detail.score}</span>
            </div>
            <div>
              <span className="text-sm font-medium">评分类型:</span>
              <span className="ml-2">{getScoringType(detail.scoring_type)}</span>
            </div>
            {detail.ocr && (
              <div>
                <span className="text-sm font-medium">识别结果:</span>
                <div className="mt-2 p-3 bg-gray-50 rounded-md text-sm">{detail.ocr}</div>
              </div>
            )}
            {detail.reason && (
              <div>
                <span className="text-sm font-medium">评分理由:</span>
                <div className="mt-2">
                  <MathRenderer content={detail.reason.Text || detail.reason} />
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {trigger || (
          <button className="inline-flex items-center justify-center">
            <Eye className="h-4 w-4" />
          </button>
        )}
      </SheetTrigger>
      <SheetContent side="right" className="w-[800px] sm:max-w-[800px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>
        <div className="mt-6">
          <div className="space-y-6">
            {details.map((detail, index) => (
              <div key={index}>
                <Card>
                  {isEssay(detail) ? (
                    <>
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center justify-between">
                          详情 {index + 1}
                          <div className="flex items-center justify-end gap-4 text-sm">
                            <div>
                              <span className="font-medium">评分类型:</span>
                              <span className="ml-2">{getScoringType(detail.scoring_type)}</span>
                            </div>
                            <div className="w-[100px]">
                              <span className="font-medium">分数:</span>
                              <span className="ml-2 text-lg font-bold text-blue-600">{detail.score}</span>
                            </div>
                            <div className="flex items-center gap-4">
                              <Badge variant={detail.status === 'Done' ? 'default' : 'secondary'}>
                                {detail.status === 'Done' ? '阅卷完成' : detail.status === 'OcrFailed' ? 'OCR识别异常' : '阅卷失败'}
                              </Badge>
                            </div>
                          </div>
                        </CardTitle>
                      </CardHeader>
                    </>
                  ) : null}
                  <CardContent className="space-y-3">{isEssay(detail) ? renderEssayContent(detail, index) : <>{renderNormalContent([detail])}</>}</CardContent>
                </Card>
                {index < details.length - 1 && <Separator className="my-4" />}
              </div>
            ))}
          </div>
        </div>

        {/* 悬浮的句子详情popover */}
        {hoveredSentence && (
          <div
            className="fixed z-50 w-500 p-4 bg-white border border-gray-200 rounded-lg shadow-lg"
            style={{
              left: popoverPosition.x - 160,
              top: popoverPosition.y,
              transform: 'translateX(-50%)',
            }}
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">句子详情</h4>
                <Badge variant={hoveredSentence.评级 === '优' ? 'default' : hoveredSentence.评级 === '中' ? 'secondary' : 'destructive'}>{hoveredSentence.评级}</Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">原句:</span>
                  <p className="mt-1 text-gray-600">{hoveredSentence.原句}</p>
                </div>
                <div>
                  <span className="font-medium">纠错:</span>
                  <p className="mt-1 text-gray-600">{hoveredSentence.纠错}</p>
                </div>
                <div>
                  <span className="font-medium">润色:</span>
                  <p className="mt-1 text-gray-600">{hoveredSentence.润色}</p>
                </div>
                <div>
                  <span className="font-medium">修改说明:</span>
                  <p className="mt-1 text-gray-600">{hoveredSentence.修改说明}</p>
                </div>
                <div>
                  <span className="font-medium">评级理由:</span>
                  <p className="mt-1 text-gray-600">{hoveredSentence.评级理由}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};

export default DetailsDrawer;
