import React, { useState } from "react";
import { MultiSelect, Option } from "@/components/ui/multi-select";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { set } from "zod";
interface RedoSorceDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    criteria_ids?: string[]; // 题号ID集合
    criteria_names?: string[]; // 题目名称集合（可选，建议传入）
    onConfirm: (
        redo_ocr: boolean,
        include_done: boolean,
        include_checked: boolean,
        selected_criteria_ids: string[],
    ) => void;
    onCancel: () => void;
}

const RedoSorceDialog: React.FC<RedoSorceDialogProps> = ({ open, onOpenChange, onConfirm, onCancel, criteria_ids, criteria_names }) => {
    const [redoOcr, setRedoOcr] = useState(false);
    const [includeDone, setIncludeDone] = useState(false);
    const [includeChecked, setIncludeChecked] = useState(false);
    // 多选题号
    const [selectedOptions, setSelectedOptions] = useState<Option[]>([]);
    const [selectAll, setSelectAll] = useState(false);

    // 构造题号选项（如“第一题”、“第二题”），value为id，label为“第N题”或题目名
    const options: Option[] = criteria_ids ? criteria_ids.map((id, idx) => ({
        value: id,
        label: criteria_names?.[idx] ? criteria_names[idx] : `第${idx + 1}题`,
    })) : [];

    const handleConfirm = () => {
        const selected_criteria_ids = selectedOptions.map(opt => opt.value);
        onConfirm(redoOcr, includeDone, includeChecked, selected_criteria_ids);
        onOpenChange(false);
    };

    const handleCancel = () => {
        onCancel();
        onOpenChange(false);
    };
    const handleSelectAll = (checked: boolean) => {
        setSelectAll(checked);
        setRedoOcr(checked);
        setIncludeDone(checked);
        setIncludeChecked(checked);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[450px]">
                <DialogHeader>
                    <DialogTitle>重新批阅</DialogTitle>
                    <DialogDescription>此操作将重置批阅结果和核查结果</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                        <Checkbox id="selectAll" checked={selectAll} onCheckedChange={handleSelectAll} />
                        <label htmlFor="selectAll" className="text-sm font-medium">
                            全选
                        </label>
                    </div>
                    <div className="ml-6 flex items-center space-x-2">
                        <Checkbox checked={redoOcr} onCheckedChange={val => setRedoOcr(val === true)} id="redo-ocr" />
                        <Label htmlFor="redo-ocr">重新识别（OCR）</Label>
                    </div>
                    <div className="ml-6 flex items-center space-x-2">
                        <Checkbox checked={includeDone} onCheckedChange={val => setIncludeDone(val === true)} id="include-done" />
                        <Label htmlFor="include-done">包含已完成的试卷</Label>
                    </div>
                    <div className="ml-6 flex items-center space-x-2">
                        <Checkbox checked={includeChecked} onCheckedChange={val => setIncludeChecked(val === true)} id="include-checked" />
                        <Label htmlFor="include-checked">包含已复核的试卷</Label>
                    </div>
                    {criteria_ids &&
                        <div>
                            <MultiSelect
                                options={options}
                                value={selectedOptions}
                                onChange={setSelectedOptions}
                                placeholder="请选择需要重评题号"
                                maxSelected={options.length}
                            />
                        </div>
                    }
                </div>
                <div className="flex justify-end gap-2 pt-4">
                    <Button onClick={handleConfirm} className="flex-1">
                        确定
                    </Button>
                    <Button variant="outline" onClick={handleCancel} className="flex-1">
                        取消
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default RedoSorceDialog;