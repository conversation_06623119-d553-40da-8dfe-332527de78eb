import React, { useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Calendar,
  Users,
  FileText,
  Edit,
  Trash2,
  Eye,
  Clock,
  CheckCircle2,
  FileEdit,
  TrendingUp,
  AlertCircle,
  CircleAlert,
  TriangleAlert,
  BarChart2
} from 'lucide-react';
import { PageHomeworkResult, HomeworkStatusEnum, HomeworkSummary } from '@/types/homework';
import { homeworkApi } from '@/services/homeworkApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { useNavigate } from 'react-router-dom';
import { useHomeworkStore } from '@/stores/useHomeworkStore';

interface EnhancedHomeworkCardProps {
  homework: PageHomeworkResult;
  subjectGroupName: string;
  onEdit: (homework: PageHomeworkResult) => void;
  onDelete: (homework: PageHomeworkResult) => void;
  onView: (homework: PageHomeworkResult) => void;
  onViewDescription: (description: string) => void;
}

const EnhancedHomeworkCard: React.FC<EnhancedHomeworkCardProps> = ({
  homework,
  subjectGroupName,
  onEdit,
  onDelete,
  onView,
  onViewDescription,
}) => {
  const navigate = useNavigate();
  const { reset, setHomework } = useHomeworkStore();
  const { homework: hw, student_list } = homework;
  // Get tenant ID from auth context (mock for now)
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || "";
  const tenantName = identityInfo?.schema_name || "";
  //存储每个卡片的作业统计信息
  const [homeworkSummary, setHomeworkSummary] = React.useState<HomeworkSummary>();
  useEffect(() => {
    //获取统计信息
    homeworkApi.getHomeworkSummary(tenantId, tenantName, homework.homework.id.toString()).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.error("获取作业统计信息失败:", message);
        return;
      }
      setHomeworkSummary(data);
    });
  }, [homework]);
  const getStatusConfig = (status: HomeworkStatusEnum) => {
    switch (status) {
      case HomeworkStatusEnum.Draft:
        return {
          label: '草稿',
          variant: 'secondary' as const,
          icon: FileEdit,
          color: 'text-muted-foreground',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
      case HomeworkStatusEnum.Doing:
        return {
          label: '处理中',
          variant: 'default' as const,
          icon: Clock,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case HomeworkStatusEnum.Done:
        return {
          label: '已完成',
          variant: 'default' as const,
          icon: CheckCircle2,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      default:
        return {
          label: status,
          variant: 'outline' as const,
          icon: FileText,
          color: 'text-muted-foreground',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '昨天';
    } else if (diffDays === 2) {
      return '前天';
    } else if (diffDays <= 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const getProgressPercentage = () => {
    if (hw.homework_status === HomeworkStatusEnum.Done) return 100;
    if (hw.homework_status === HomeworkStatusEnum.Doing) return 60;
    return 0;
  };

  const statusConfig = getStatusConfig(hw.homework_status);
  const StatusIcon = statusConfig.icon;
  const progressPercentage = getProgressPercentage();

  return (
    <Card
      className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4"
      style={{
        borderLeftColor: hw.homework_status === HomeworkStatusEnum.Done ? '#10b981' :
          hw.homework_status === HomeworkStatusEnum.Doing ? '#3b82f6' : '#6b7280'
      }}
      onClick={() => {
        reset();
        setHomework(hw);
        navigate(`/homework-setting/${hw.id}`);
      }}
    >
      {/* 状态指示器 */}
      <div className={`absolute top-0 right-0 w-20 h-20 ${statusConfig.bgColor} rounded-bl-full opacity-10 group-hover:opacity-20 transition-opacity`} />

      <CardHeader className="pb-3 relative z-10">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-semibold leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors cursor-pointer">
              {hw.homework_name}
            </CardTitle>
            <div className="flex items-center gap-2 mt-2 flex-wrap">
              <Badge variant={statusConfig.variant} className="text-xs">
                <StatusIcon className="w-3 h-3 mr-1" />
                {statusConfig.label}
              </Badge>
              <Badge variant="outline" className="text-xs text-muted-foreground">
                {subjectGroupName}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 relative z-10">
        {/* 描述预览 */}
        {hw.description && (
          <div className="mb-3">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground text-left justify-start w-full"
                  onClick={() => onViewDescription(hw.description?.toString() || '')}
                >
                  <FileText className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span className="line-clamp-2">{hw.description.toString()}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <span>{hw.description.toString()}</span>
              </TooltipContent>
            </Tooltip>
          </div>
        )}

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>{student_list.length}人参与</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            <span>{formatDate(hw.created_at.toString())}</span>
          </div>
        </div>
        {/* 试卷绑定情况 */}
        {!homeworkSummary?.paper_id ? (
          <div className="mb-3 p-2 bg-red-50 rounded-md border border-red-200">
            <div className="flex items-center gap-2 text-xs text-red-700">
              <CircleAlert className="w-3 h-3" />
              <span>暂未绑定试卷，请先进行绑定</span>
            </div>
          </div>
        ) : (
          <div className="mb-3">
            {/* 扫描进度条 */}
            <div className="mb-2">
              <div className="flex items-center text-xs mb-1">
                <FileText className="w-3 h-3 mr-1 text-blue-500" />
                <span>扫描进度</span>
                <span className="ml-2 text-muted-foreground">
                  {homeworkSummary.scan.total_count > 0
                    ? `${homeworkSummary.scan.total_count - homeworkSummary.scan.error_count}/${homeworkSummary.scan.total_count}`
                    : '0/0'}
                </span>
                <TriangleAlert className="w-3 h-3 mr-1 text-red-500 ml-auto" />
                <span className="text-muted-foreground">异常：{homeworkSummary.scan.error_count}</span>
              </div>
              <div className="w-full h-2 bg-gray-200 rounded">
                <div
                  className="h-2 bg-blue-500 rounded"
                  style={{
                    width: `${homeworkSummary.scan.total_count > 0
                      ? ((homeworkSummary.scan.total_count - homeworkSummary.scan.error_count) / homeworkSummary.scan.total_count) * 100
                      : 0}%`
                  }}
                />
              </div>
            </div>
            {/* 阅卷进度条 */}
            <div>
              <div className="flex items-center text-xs mb-1">
                <TrendingUp className="w-3 h-3 mr-1 text-green-500" />
                <span>阅卷进度</span>
                <span className="ml-2 text-muted-foreground">
                  {homeworkSummary.scoring.scored_count}/{homeworkSummary.scoring.total_count}
                </span>
                <BarChart2  className="w-3 h-3 mr-1 text-blue-500 ml-auto" />
                <span className="text-muted-foreground">平均分：{homeworkSummary.scoring.avg_score.toFixed(2)}</span>
              </div>
              <div className="w-full h-2 bg-gray-200 rounded">
                <div
                  className="h-2 bg-green-500 rounded"
                  style={{
                    width: `${homeworkSummary.scoring.total_count > 0
                      ? (homeworkSummary.scoring.scored_count / homeworkSummary.scoring.total_count) * 100
                      : 0}%`
                  }}
                />
              </div>
            </div>
          </div>

        )
        }
        {/* 状态特定信息 */}
        {/* {hw.homework_status === HomeworkStatusEnum.Doing && (
          <div className="mb-3 p-2 bg-blue-50 rounded-md border border-blue-200">
            <div className="flex items-center gap-2 text-xs text-blue-700">
              <TrendingUp className="w-3 h-3" />
              <span>正在处理中，请稍候...</span>
            </div>
          </div>
        )} */}

        {hw.homework_status === HomeworkStatusEnum.Draft && (
          <div className="mb-3 p-2 bg-yellow-50 rounded-md border border-yellow-200">
            <div className="flex items-center gap-2 text-xs text-yellow-700">
              <AlertCircle className="w-3 h-3" />
              <span>草稿状态，学生不可见</span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {/* <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            className="flex-1 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-colors"
            onClick={() => onView(homework)}
          >
            <Eye className="w-4 h-4 mr-1" />
            查看
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="hover:bg-orange-50 hover:text-orange-700 hover:border-orange-300 transition-colors"
            onClick={() => onEdit(homework)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onDelete(homework)}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div> */}
      </CardContent>
    </Card>
  );
};

export default EnhancedHomeworkCard; 