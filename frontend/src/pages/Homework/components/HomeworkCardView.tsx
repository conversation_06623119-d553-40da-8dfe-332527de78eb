import React from 'react';
import { PageHomeworkResult } from '@/types/homework';
import EnhancedHomeworkCard from './EnhancedHomeworkCard';
import ResponsiveGrid from './ResponsiveGrid';

interface HomeworkCardViewProps {
  homeworks: PageHomeworkResult[];
  subjectGroupNameById: Record<string, string>;
  onEdit: (homework: PageHomeworkResult) => void;
  onDelete: (homework: PageHomeworkResult) => void;
  onView: (homework: PageHomeworkResult) => void;
  onViewDescription: (description: string) => void;
}

const HomeworkCardView: React.FC<HomeworkCardViewProps> = ({
  homeworks,
  subjectGroupNameById,
  onEdit,
  onDelete,
  onView,
  onViewDescription,
}) => {
  return (
    <ResponsiveGrid>
      {homeworks.map((pageHomeworkResult) => {
        const subjectGroupName = subjectGroupNameById[
          pageHomeworkResult.homework.subject_group_id?.toString() || ""
        ] || "未分类";

        return (
          <EnhancedHomeworkCard
            key={pageHomeworkResult.homework.id.toString()}
            homework={pageHomeworkResult}
            subjectGroupName={subjectGroupName}
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            onViewDescription={onViewDescription}
          />
        );
      })}
    </ResponsiveGrid>
  );
};

export default HomeworkCardView; 