import React, { useState, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Users,
  UserX,
  TrendingUp,
  Eye,
  ArrowUpDown,
  FileText,
  ChevronRight,
} from "lucide-react";
import { useHomeworkStore } from "@/stores";
import { homeworkReportApi, HomeworkReportSummary, StudentScoreMap } from "@/services/homeworkReportApi";
import { getTenantInfoFromLocalStorage } from "@/lib/apiUtils";
import { HomeworkStudentsStatusEnum } from "@/types/homeworkStudents";
const ClassReport: React.FC = () => {
  const { classId } = useParams<{ classId: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { homework } = useHomeworkStore();
  const className = location.state?.className || "班级报告";
  //统计信息
  const [statistics, setStatistics] = useState<HomeworkReportSummary | null>(null);
  //学生分数信息
  const [studentScoresInfo, setStudentScoresInfo] = useState<StudentScoreMap[]>([]);
  //存储题目信息
  const [questionTitles, setQuestionTitles] = useState<string[]>([]);

  // 确保classId存在
  if (!classId) {
    console.error("Class ID is not provided in the URL parameters.");
    return <div>班级ID未提供</div>;
  }

  // Get tenant ID from auth context (mock for now)
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || "";
  const tenantName = identityInfo?.schema_name || "";
  useEffect(() => {
    if (!classId) {
      console.error("Class ID is not provided.");
      return;
    }
    //加载班级报告数据
    loadInitialData()
    console.log("加载班级报告数据:", classId);
  }, [classId]);
  const loadInitialData = async () => {
    homeworkReportApi.getClassAnalysisDetail(tenantId, tenantName, homework.id.toString(), classId).then((res) => {
      const { success, data, message } = res;
      if (!success) {
        console.log("加载班级报告数据失败:", message);
        return;
      }
      ///加工数据，放在null.map报错
      const processedData = data?.student_scores.map(item => ({
        ...item,
        scores: item.scores ?? [] // 用空数组替代 null
      }));
      // setClassDetailReport(data ? data : null);
      setStatistics(data?.summary || null);
      setStudentScoresInfo(processedData || []);
      // 提取题目标题
      setQuestionTitles([]);
      data?.criteria_list.map(criteria => {
        const title = criteria.criteriaName;
        setQuestionTitles(prevTitles => [...prevTitles, title]);
      })
      console.log("加载班级报告数据成功:", data);
    });
  };


  return (
    <div className="space-y-6">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
          onClick={() => navigate("/homework-management")}
        >
          作业管理
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
          onClick={() => navigate(`/homework-setting/${homework.id}`)}
        >
          {homework?.homework_name}
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="text-foreground font-medium">{className}班级报告</span>
      </nav>

      {/* 数据看板 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">参与人数</div>
              <Users className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-2xl font-bold">
              {statistics?.scoring_student_count}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="text-sm text-muted-foreground">已完成评分项</div>
              <FileText className="h-4 w-4 text-purple-600" />
            </div>
            <div className="text-2xl font-bold">
              {statistics?.done_score_count}/{statistics?.total_score_count}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">班级平均分</div>
              <TrendingUp className="h-4 w-4 text-orange-600" />
            </div>
            <div className="text-2xl font-bold">
              {statistics?.avg_score}/{statistics?.total_score}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">缺考人数</div>
              <UserX className="h-4 w-4 text-red-600" />
            </div>
            <div className="text-2xl font-bold">
              {statistics?.absent_student_count}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 学生详情 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div>学生详情</div>
            <Button size="sm">导出班级报告</Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* <div className="overflow-x-auto"> */}
          <Table className="">
            <TableHeader className="sticky top-0 bg-white z-10">
              <TableRow>
                <TableHead className="sticky left-0 z-20 bg-white min-w-16">
                  序号
                </TableHead>
                <TableHead className="sticky left-16 z-20 bg-white min-w-[96px]">
                  <Button
                    variant="ghost"
                    size="sm"
                    //onClick={() => handleSort("studentNumber")}
                    className="h-auto p-0 font-medium"
                  >
                    学号
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead className="sticky left-40 z-10 bg-white min-w-20">学生姓名</TableHead>
                <TableHead className="sticky left-60 z-10 bg-white min-w-20">
                  <Button
                    variant="ghost"
                    size="sm"
                    //onClick={() => handleSort("status")}
                    className="h-auto p-0 font-medium"
                  >
                    状态
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </Button>
                </TableHead>
                <TableHead className="sticky left-80 z-10 bg-white min-w-20 text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    //onClick={() => handleSort("totalScore")}
                    className="h-auto p-0 font-medium text-xs"
                  >
                    总分
                    <ArrowUpDown className="ml-1 h-2 w-2" />
                  </Button>
                </TableHead>
                <TableHead className="p-0">
                  <div className="text-center">
                    <div>得分明细</div>
                    <div className="flex border-t mt-1 pt-1">
                      <div className="flex-1">
                        <div className="flex min-w-max pt-1">
                          {questionTitles && questionTitles.map((title, index) => (
                            <div
                              key={index}
                              className="min-w-12 text-xs text-center px-1"
                            >
                              {title}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </TableHead>
                <TableHead className="sticky right-0 z-10 bg-white min-w-20 text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {studentScoresInfo.map((student, index) => (
                <TableRow key={student.student.student_id}>
                  <TableCell className="sticky left-0 z-10 bg-white">{index + 1}</TableCell>
                  <TableCell className="sticky left-16 z-10 bg-white">{student.student.student_number}</TableCell>
                  <TableCell className="sticky left-40 z-10 bg-white">{student.student.student_name}</TableCell>
                  <TableCell className="sticky left-60 z-10 bg-white">
                    <Badge
                      variant={
                        student.student.status === "Done"
                          ? "default"
                          : student.student.status === "Unsubmitted" ? "secondary"
                            : "destructive"
                      }
                    >
                      {student.student.status === "Done"
                        ? "完成"
                        : student.student.status === "Unsubmitted"
                          ? "缺考"
                          : "异常"}
                    </Badge>
                  </TableCell>
                  <TableCell className="sticky left-80 z-10 bg-white min-w-20 font-medium text-center">
                    {student.scores?.reduce((sum, s) => sum + Number(s.score), 0) || 0}
                  </TableCell>
                  <TableCell className="min-w-[400px]  p-0">
                    <div className="flex">
                      <div className="flex-1">
                        <div className="flex min-w-max">
                          {student.scores?.map((score, qIndex) => (
                            <div
                              key={qIndex}
                              className="min-w-12 text-center px-1 text-sm"
                            >
                              {score.score}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="sticky right-0 z-10 bg-white min-w-20 text-center">
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 h-auto min-h-0 text-blue-600 font-bold"
                      onClick={() => {
                        if (student.student.status !== HomeworkStudentsStatusEnum.Unsubmitted) {
                          navigate(`/homework-scan/scan-image-preview/${student.student?.student_id}`, {
                            state: {
                              entry: 'student',
                            },
                          });
                        }
                      }}
                    >
                      <Eye className="h-3 w-3" />
                      查看原卷
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {/* </div> */}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassReport;
