import {<PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle} from "@/components/ui/card.tsx";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import React, {useEffect, useMemo, useState} from "react";
import {DEFAULT_PAPER_QUERY, paperApi, Paper, TenantPaperQueryParams} from "@/services/paperApi.ts";
import {toast} from "sonner";
import {Skeleton} from "@/components/ui/skeleton.tsx";
import Pagination from "@/components/Pagination.tsx";
import {useAuth} from "@/contexts/AuthContext.tsx";
import {createPaperDataStore, PaperData} from "@/components/question-card/store/paperDataStore.ts";
import {createComponentDataListStore} from "@/components/question-card/store/componentDataListStore.ts";
import {createDataCallbackStore} from "@/components/question-card/store/dataCallbackStore.ts";
import {createToolbarDataStore} from "@/components/question-card/store/toolbarDataStore.ts";
import {Dialog, DialogContent, DialogTitle} from "@/components/ui/dialog.tsx";
import {VisuallyHidden} from "@radix-ui/react-visually-hidden";
import {Button} from "@/components/ui/button.tsx";
import QuestionCard from "@/components/question-card";
import {Eye} from "lucide-react";
import PaperFilters from "@/pages/Paper/PaperFilters.tsx";
import {useParams} from "react-router-dom";

const Index = () => {
    const {id} = useParams<{ id?: string }>();
    const {tenant} = useAuth()
    const [loading, setLoading] = useState(false);
    const [papers, setPapers] = useState<Paper[]>([])
    const [paperQueryParams, setPapersQueryParams] = useState<TenantPaperQueryParams>({
        ...DEFAULT_PAPER_QUERY,
        search: id
    })
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });

    const loadPapers = async (params?: Partial<TenantPaperQueryParams>) => {
        try {
            setLoading(true)
            const finalParams = {...paperQueryParams, ...params, schema_name: tenant?.schema_name ?? ''};
            let response = await paperApi.getTenantPapers({...finalParams})
            if (response.success && response.data) {
                setPapers(response.data);
                setPagination({
                    current: response.pagination.page,
                    pageSize: response.pagination.page_size,
                    total: response.pagination.total,
                    totalPages: response.pagination.total_pages,
                });
            }
            setPapersQueryParams(finalParams)
        } catch (error) {
            console.error('Failed to load papers:', error);
            toast.error('加载试卷列表失败');
        } finally {
            setLoading(false);
        }
    }

    const handleClearFilters = () => {
        loadPapers({
            search: '',
        })
    }

    const handleFilterChange = (key: keyof TenantPaperQueryParams, value: any) => {
        const newParams = {...paperQueryParams, [key]: value, page: 1};
        loadPapers(newParams);
    };

    const handlePaginationChange = async (newPage: number, pageSize: number) => {
        await loadPapers({page: newPage, page_size: pageSize});
    };

    useEffect(() => {
        loadPapers()
    }, []);

    const [previewDialogVisible, setPreviewDialogVisible] = useState(false)
    const [paperData, setPaperData] = useState<PaperData | null>(null);
    const previewPaper = (paper: Paper) => {
        if (!paper.paper_content) {
            toast.info("试卷信息预览加载失败")
            return
        }
        let paperData: PaperData = {id: paper.id, paper_content: paper.paper_content, paper_name: paper.paper_name}
        setPaperData(paperData)
        setPreviewDialogVisible(true)
    }

    interface PaperPreviewProps {
        previewOpen: boolean;
        setPreviewOpen: (visible: boolean) => void;
    }

    const PaperPreviewDialog: React.FC<PaperPreviewProps> = ({previewOpen, setPreviewOpen}) => {
        const componentDataListStore = createComponentDataListStore();
        const paperDataStore = createPaperDataStore();
        const dataCallbackStore = useMemo(() => createDataCallbackStore(), [paperData]);
        const setPaperData = paperDataStore(state => state.setPaperData)

        //更新数据
        useEffect(() => {
            if (paperData) {
                setPaperData(paperData);
            }
        }, [setPaperData]);

        if (paperData === null) {
            return (<></>)
        }

        const toolbarDataStore = createToolbarDataStore();

        return (
            <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
                <DialogContent className="max-w-[90vw] h-[90vh] flex flex-col">
                    <VisuallyHidden>
                        <DialogTitle>试卷预览</DialogTitle>
                    </VisuallyHidden>

                    <QuestionCard
                        componentDataListStore={componentDataListStore}
                        toolbarDataStore={toolbarDataStore}
                        paperDataStore={paperDataStore}
                        dataCallbackStore={dataCallbackStore}
                        style={{height: "100%"}}
                    />
                </DialogContent>
            </Dialog>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">试卷管理</h1>
                    <p className="text-muted-foreground">试卷管理</p>
                </div>
            </div>

            {/* 筛选器和操作按钮 */}
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <PaperFilters
                        params={paperQueryParams}
                        onFilterChange={handleFilterChange}
                        clearFilters={handleClearFilters}
                    />
                </div>
            </div>


            <div className="transition-all duration-300 ease-in-out">
                <Card className="flex flex-col flex-grow">
                    <CardHeader className="flex flex-row items-center justify-between">
                        <div className="flex items-center gap-4">
                            <CardTitle>试卷列表</CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent className="flex-grow min-h-[200px]">

                        <div className="h-full">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>ID</TableHead>
                                        <TableHead>试卷名称</TableHead>
                                        <TableHead>操作</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {loading ?
                                        Array.from({length: 5}).map((_, i) => (
                                            <TableRow key={i}>
                                                <TableCell className="font-mono">
                                                    <Skeleton className="h-4 w-4"/>
                                                </TableCell>
                                                <TableCell>
                                                    <Skeleton className="h-4 flex-1"/>
                                                </TableCell>
                                                <TableCell>
                                                    <Skeleton className="h-4 w-4"/>
                                                </TableCell>
                                            </TableRow>
                                        )) :
                                        papers.map((paper) => (
                                            <TableRow key={paper.id}>
                                                <TableCell className="font-mono">{paper.id}</TableCell>
                                                <TableCell>{paper.paper_name}</TableCell>
                                                <TableCell>
                                                    <div className="flex gap-2">
                                                        {/* 编辑 */}
                                                        {/*  todo 暂时没有什么好编辑的，就没做*/}
                                                        {/*<Button*/}
                                                        {/*    size="sm"*/}
                                                        {/*    variant="outline"*/}
                                                        {/*    onClick={() => console.log("编辑:", paper)}*/}
                                                        {/*>*/}
                                                        {/*    <Pencil className="h-4 w-4 mr-1"/>*/}
                                                        {/*    编辑*/}
                                                        {/*</Button>*/}
                                                        {/* 预览答题卡 */}
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => previewPaper(paper)}
                                                        >
                                                            <Eye className="h-4 w-4 mr-1"/>
                                                            预览
                                                        </Button>
                                                        {/* 删除 */}
                                                        {/*  todo 要关联？担心删除来自其他引用的试卷*/}
                                                        {/*<Button*/}
                                                        {/*    size="sm"*/}
                                                        {/*    variant="destructive"*/}
                                                        {/*    onClick={() => console.log("删除:", paper)}*/}
                                                        {/*>*/}
                                                        {/*    <Trash2 className="h-4 w-4 mr-1"/>*/}
                                                        {/*    删除*/}
                                                        {/*</Button>*/}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    }
                                </TableBody>
                            </Table>
                            <div className="flex justify-center">
                                <Pagination
                                    total={pagination.total}
                                    current={pagination.current}
                                    pageSize={pagination.pageSize}
                                    onChange={handlePaginationChange}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
            <PaperPreviewDialog previewOpen={previewDialogVisible} setPreviewOpen={setPreviewDialogVisible}/>
        </div>
    )
}

export default Index
