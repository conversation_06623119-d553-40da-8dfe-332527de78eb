import {TenantPaperQueryParams} from "@/services/paperApi.ts";
import {FC, useState} from "react";
import {Filter, Search, X} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";
import {Label} from "@/components/ui/label.tsx";
import {Input} from "@/components/ui/input.tsx";

interface PaperFiltersProps {
    params: TenantPaperQueryParams;
    onFilterChange: (key: keyof TenantPaperQueryParams, value: any) => void;
    clearFilters: () => void;
}


const PaperFilters: FC<PaperFiltersProps> = ({params, onFilterChange, clearFilters}) => {

    const hasActiveFilters = params.search;
    const [searchInput, setSearchInput] = useState<string>(params.search??'')

    return (
        <div className="space-y-6">
            {/* 筛选器头部 */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg">
                        <Filter className="h-4 w-4 text-white"/>
                    </div>
                    <div>
                        <h3 className="font-semibold text-slate-900 dark:text-white">高级筛选</h3>
                        <p className="text-sm text-slate-600 dark:text-slate-400">精确筛选您需要的租户</p>
                    </div>
                </div>
                {hasActiveFilters && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={clearFilters}
                        className="h-9 px-4 bg-white/50 hover:bg-white border-slate-200 hover:border-slate-300"
                    >
                        <X className="h-4 w-4 mr-2"/>
                        清除筛选
                    </Button>
                )}
            </div>

            {/* 筛选器内容 */}
            <div className="grid gap-6 md:grid-cols-3">

                {/* 搜索框 */}
                <div className="space-y-1">
                    <Label
                        htmlFor="search"
                        className="text-sm font-medium text-slate-700 dark:text-slate-300"
                    >
                        关键词搜索
                    </Label>
                    <div className="relative flex">
                        <Search
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 group-focus-within:text-indigo-500 transition-colors"
                        />
                        <Input
                            id="search"
                            placeholder="搜索试卷名称、代码..."
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            className="pl-10 pr-20 bg-white/70 border-slate-200 focus:bg-white focus:border-indigo-300 focus:ring-indigo-200 transition-all flex-1"
                        />
                        <Button
                            onClick={() => onFilterChange("search", searchInput)}
                            className="absolute right-1 h-[calc(100%_-_4px)] px-3 mt-0.5"
                        >
                            搜索
                        </Button>
                    </div>
                </div>
            </div>

        </div>
    );
};

export default PaperFilters;
