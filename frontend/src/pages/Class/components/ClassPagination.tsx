// frontend/src/pages/Class/components/Pagination.tsx
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import React, { useEffect, useState } from 'react';

interface PaginationProps {
  pagination: {
    total: number;
    current: number;
    pageSize: number;
  };
  onChange: (page: number, pageSize: number) => void;
}

const ClassPagination: React.FC<PaginationProps> = ({ pagination, onChange }) => {
  const [pageInputValue, setPageInputValue] = useState(pagination.current.toString());
  useEffect(() => {
    setPageInputValue(pagination.current.toString());
  }, [pagination.current]);
  return (
    <div className="flex flex-wrap items-center gap-4 mt-2 ml-6 mb-4">
      <div className="text-sm text-gray-500 whitespace-nowrap">
        <span>共 {pagination.total} 条记录</span>
      </div>
      <div className="flex items-center space-x-4 text-sm text-gray-500">
        <div className="flex items-center space-x-2">
          <span>每页</span>
          <select
            className="border rounded px-2 py-1 text-sm"
            value={pagination.pageSize}
            onChange={e => onChange(1, Number(e.target.value))}
          >
            {[5, 10, 20, 50].map(size => (
              <option key={size} value={size}>{size} 条/页</option>
            ))}
          </select>
          <span>跳转到</span>
          <input
            type="number"
            min={1}
            max={Math.ceil(pagination.total / pagination.pageSize)}
            value={pageInputValue}
            onChange={e => {
              const value = e.target.value;
              setPageInputValue(value === '' ? '' : Number(value).toString()); // 处理空字符串
            }} // 更新状态
            onBlur={e => {
              let page = Number(e.target.value);
              if (page < 1) page = 1;
              const maxPage = Math.max(1, Math.ceil(pagination.total / pagination.pageSize));
              if (page > maxPage) {
                page = maxPage;
              }
              if (page !== pagination.current) {
                onChange(page, pagination.pageSize);
              } else {
                setPageInputValue(pagination.current.toString());
              }
            }}
            className="border rounded px-2 py-1 w-16 text-sm text-center"
          />
          <span>页</span>
        </div>
      </div>
      <div className="flex items-center">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => {
                  if (pagination.current > 1) {
                    onChange(pagination.current - 1, pagination.pageSize);
                  }
                }}
                className={pagination.current === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>
            {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }, (_, i) => i + 1).map((page) => (
              <PaginationItem key={page}>
                <PaginationLink
                  isActive={page === pagination.current}
                  onClick={() => onChange(page, pagination.pageSize)}
                  className="cursor-pointer"
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext
                onClick={() => {
                  if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                    onChange(pagination.current + 1, pagination.pageSize);
                  }
                }}
                className={
                  pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                    ? 'pointer-events-none opacity-50'
                    : 'cursor-pointer'
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default ClassPagination;