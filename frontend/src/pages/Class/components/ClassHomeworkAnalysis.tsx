import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate, useParams } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, ResponsiveContainer, Tooltip } from 'recharts';
import { StudentHomeworkAnalyse } from '@/types/administrativeClasses';
import { AdministrativeClassesApi } from '@/services/administrativeClassesApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { toast } from 'sonner';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { SubjectGroupsDetail } from '@/types/subjectGroups';

/**
 * 需求分析：
 * 0. 根据学科、时间段进行筛选
 * 1. 顶部暂时班级出勤、缺考、进步率统计
 * 2. 学生信息列表或卡片展示，姓名、学号、出勤率、缺考次数
 * 3. 每个学生可以展示名次动态表（名次指的是在所处教学班的名次）
 */

// 2. 选项数组保持不变
const timeRanges = [
    { label: '最近7天', value: "Week" },
    { label: '最近30天', value: "Month" },
    { label: '最近一年', value: "Year" },
];
const ClassHomeworkAnalysis: React.FC = () => {
    const { classId } = useParams<{ classId: string }>();
    const navigate = useNavigate();
    const [selectSubject, setSelectSubject] = useState('all');
    const [timeRange, setTimeRange] = useState('');
    // Get tenant ID from auth context (mock for now)
    const identityInfo = getTenantInfoFromLocalStorage();
    const tenantId = identityInfo?.tenant_id || "";
    const tenantName = identityInfo?.schema_name || "";

    const [studentInfo, setStudentInfo] = useState<StudentHomeworkAnalyse[]>([]);
    const [subjectSummaries, setSubjectSummaries] = useState<SubjectGroupsDetail[]>([]);

    useEffect(() => {
        loadSubjectsSummaries();
        loadStudentInfoList();
    }, [timeRange, selectSubject, classId]);
    //获取学科数据 
    const loadSubjectsSummaries = () => {
        SubjectGroupsApi.findAll(tenantName).then((res) => {
            const { success, data, message } = res;
            if (!success) {
                console.error("获取学科数据失败", message);
                return;
            }
            console.log("获取学科数据成功", data);
            setSubjectSummaries(data || []);
        })
    }
    //加载学生列表数据
    const loadStudentInfoList = () => {
        if (!classId){
            return;
        }
        AdministrativeClassesApi.getClassStudentHomeworkStats(tenantId, tenantName, {
            class_id: classId as string,
            subject_id: selectSubject === 'all' ? undefined : selectSubject,
            time_range: timeRange === '' ? undefined : timeRange,
        }).then((res) => {
            const { success, data, message } = res;
            if (!success) {
                console.error("获取学生列表失败", message);
                toast.error('加载学生列表失败');
                return;
            }
            setStudentInfo(data || []);
        })
    }
    //学生状态
    const getStatusBadge = (status: string) => {
        switch (status?.toLowerCase()) {
            case 'active':
                return <Badge variant="default">在校</Badge>;
            case 'inactive':
                return <Badge variant="secondary">休学</Badge>;
            case 'graduated':
                return <Badge variant="outline">毕业</Badge>;
            case 'transferred':
                return <Badge variant="destructive">转学</Badge>;
            default:
                return <Badge variant="secondary">未知</Badge>;
        }
    };

    return (
        <div className="space-y-6">
            <Button variant="outline" onClick={() => navigate('/administrative-classes')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
            </Button>
            <div className="flex flex-col gap-2">
                <h2 className="text-xl font-semibold tracking-tight">班级学习情况分析</h2>
                <p className="text-sm text-muted-foreground">查看学生作业出勤、缺考、班级名次动态，可筛选学科和时间段</p>
            </div>
            {/* 筛选栏 */}
            <div className="flex gap-4">
                <Select value={selectSubject} onValueChange={setSelectSubject}>
                    <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="选择学科" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">全部学科</SelectItem>
                        {subjectSummaries.map(s => (
                            <SelectItem key={s.id.toString()} value={s.id.toString()}>{s.group_name}</SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="选择时间段" />
                    </SelectTrigger>
                    <SelectContent>
                        {timeRanges.map(tr => (
                            <SelectItem key={tr.value} value={tr.value}>{tr.label}</SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
            {/* 列表展示学生数据 */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>学生列表</CardTitle>
                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>学生姓名</TableHead>
                                <TableHead>学号</TableHead>
                                <TableHead>状态</TableHead>
                                <TableHead>作业总数</TableHead>
                                <TableHead>缺考次数</TableHead>
                                <TableHead>作业出勤率</TableHead>
                                <TableHead>名次变化趋势</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {studentInfo.map((student) => (
                                <TableRow key={student.student_base_info.student_id}>
                                    <TableCell className="font-medium">{student.student_base_info.student_name}</TableCell>
                                    <TableCell>{student.student_base_info.student_number}</TableCell>
                                    <TableCell>{getStatusBadge(student.student_base_info.status)}</TableCell>
                                    <TableCell>
                                        {/* {student.trend && (
                                            <div className={`flex items-center text-ms mt-2 mb-2 ${student.trend.isPositive ? 'text-green-600' : 'text-red-600'
                                                }`}>
                                                {student.trend.isPositive ? (
                                                    <TrendingUp className="h-3 w-3 mr-1" />
                                                ) : (
                                                    <TrendingDown className="h-3 w-3 mr-1" />
                                                )}
                                                {student.trend.isPositive ? '+' : '-'}{student.trend.value}%
                                            </div>
                                        )} */}
                                        {student.total_homework}
                                    </TableCell>
                                    <TableCell>
                                        <div className="p-2 bg-yellow-50 rounded-md border border-yellow-200 w-8">
                                            <div className="text-center gap-2 text-xs text-yellow-700">
                                                <span>{student.absent}</span>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center space-x-2"> {/* 使用 flex 布局让进度条和文字并排 */}
                                            {/* 进度条容器 */}
                                            <div className="w-24 h-2 bg-gray-200 rounded">
                                                <div
                                                    className="h-2 rounded transition-all duration-300 ease-out" // 添加平滑过渡效果
                                                    style={{
                                                        width: `${student.total_homework > 0
                                                            ? ((student.total_homework - student.absent) / student.total_homework) * 100
                                                            : 0}%`,
                                                        // 根据出勤率水平动态改变颜色
                                                        backgroundColor: `${student.total_homework > 0
                                                            ? ((student.total_homework - student.absent) / student.total_homework) * 100 >= 80
                                                                ? '#10B981' 
                                                                : ((student.total_homework - student.absent) / student.total_homework) * 100 >= 60
                                                                    ? '#F59E0B' 
                                                                    : '#EF4444' 
                                                            : '#9CA3AF'}`, 
                                                    }}
                                                />
                                            </div>
                                            {/* 显示具体的出勤百分比 */}
                                            <span className="text-sm text-gray-600 min-w-[3rem]"> {/* 设置最小宽度防止数字抖动 */}
                                                {student.total_homework > 0
                                                    ? `${Math.round(((student.total_homework - student.absent) / student.total_homework) * 100)}%`
                                                    : 'N/A'}
                                            </span>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div style={{ width: 120, height: 60 }}>
                                            <ResponsiveContainer width="100%" height="100%">
                                                <LineChart data={student.homework_summary.map((h, idx) => ({ idx: idx + 1, rank: h.rank }))}>
                                                    <Line type="monotone" dataKey="rank" stroke="#3b82f6" dot={false} strokeWidth={2} />
                                                    <Tooltip
                                                        content={({ active, payload }) => {
                                                            if (active && payload && payload.length) {
                                                                return (
                                                                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                                                                        <div className="grid grid-cols-2 gap-2">
                                                                            <div className="flex flex-col">
                                                                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                                                                    排名
                                                                                </span>
                                                                                <span className="font-bold text-muted-foreground">
                                                                                    {payload[0].value}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        }}
                                                    />
                                                </LineChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>

                </CardContent>
            </Card>

        </div>
    );
};

export default ClassHomeworkAnalysis;
