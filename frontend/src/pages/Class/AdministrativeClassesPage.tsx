import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { AdministrativeClassesApi } from '@/services/administrativeClassesApi';
import gradeApi from '@/services/gradeApi';
import subjectApi from '@/services/subjectApi';
import { AdministrativeClassesDetail, CreateAdministrativeClassesParams, PageUserClassListParams } from '@/types/administrativeClasses';
import { GradeLevelSummary } from '@/types/grade';
import { Eye, RefreshCw, Search, Trash2, Upload } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ClassPagination from './components/ClassPagination';
import ClassStatisticsCards from './components/ClassStatisticsCards';
import CreateClassDialog from './components/CreateClassDialog';

const AdministrativeClassesPage: React.FC = () => {
  // State management
  const [classes, setClasses] = useState<AdministrativeClassesDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Statistics
  const [totalClasses, setTotalClasses] = useState(0);
  const [totalTeachers, setTotalTeachers] = useState(0);
  const [totalStudents, setTotalStudents] = useState(0);
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState<AdministrativeClassesDetail | null>(null);

  // Pagination and filtering
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 5,
    total: 0,
    totalPages: 0,
  });
  const [searchParams, setSearchParams] = useState<PageUserClassListParams>({
    name_like: undefined,
    is_active: undefined,
    class_code: undefined,
    page_params: {
      page: 1,
      page_size: 5
    }
  });

  // Form state
  const [subjects, setSubjects] = useState<any[]>([]);
  const [gradeLevels, setGradeLevels] = useState<GradeLevelSummary[]>([]);
  // const [editClassForm, setEditClassForm] = useState<UpdateAdministrativeClassesParams>({
  //   id: '',
  //   class_name: '',
  //   code: '',
  //   academic_year: new Date().getFullYear().toString(),
  //   grade_level_code: "",
  //   teacher_id: '',
  //   is_active: true
  // });


  // Get tenant ID from auth context (mock for now)
  //const tenantId = 'tenant_zhanghan';
  const identityInfo = getTenantInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const tenantName = identityInfo?.schema_name || '';

  useEffect(() => {
    loadInitialData();
  }, []);
  const navigate = useNavigate();

  const loadInitialData = async () => {
    setLoading(true);
    try {
      //获取行政班列表
      // AdministrativeClassesApi.getUserClassList(tenantId, tenantName).then(res => {
      //   const { success, data, message } = res
      //   if (!success) {
      //     return setError(message)
      //   }
      //   setClasses(data || []);
      // })
      loadClassList();
      //科目列表
      subjectApi.getSubjects().then(res => {
        const { success, data, message } = res
        if (!success) {
          return setError(message)
        }
        setSubjects(Array.isArray(data) ? data : []);
      })
      //年级列表
      gradeApi.getGradeSummaries().then(res => {
        const { success, data, message } = res
        if (!success) {
          return setError(message)
        }
        setGradeLevels(Array.isArray(data) ? data : []);
      })
      //获取统计数据
      AdministrativeClassesApi.getStatistics(tenantId, tenantName).then(stats => {
        const { success, data, message } = stats
        if (!success) {
          return setError(message)
        }
        setTotalClasses(Number(data?.total_classes) || 0);
        setTotalTeachers(Number(data?.total_teacher) || 0);
        setTotalStudents(Number(data?.total_students) || 0);
      })

      setError(null);
    } catch (err) {
      setError('加载数据失败');
      //setClasses([]);
    } finally {
      setLoading(false);
    }
  };
  const loadClassList = async (params?: PageUserClassListParams) => {
    try {
      const effectiveParams: PageUserClassListParams = params ?? searchParams;
      const res = await AdministrativeClassesApi.pageUserClassList(tenantId, tenantName, {
        ...effectiveParams,
      });
      const { success, data, message, pagination } = res
      if (!success) {
        return setError(message)
      }
      setClasses(data || []);
      setPagination({
        current: pagination.page,
        pageSize: pagination.page_size,
        total: pagination.total,
        totalPages: pagination.total_pages
      });
    } catch (err) {
      setError('加载班级列表失败');
    }
  }
  //创建班级
  const handleCreateClass = async (params: CreateAdministrativeClassesParams) => {
    try {
      await AdministrativeClassesApi.createClasses(tenantId, tenantName, params);
      setIsCreateDialogOpen(false);
      loadInitialData();
    } catch {
      setError('创建班级失败');
    }
  };

  // // 编辑班级
  // const handleUpdateClass = async () => {
  //   if (!selectedClass) return;
  //   if (!editClassForm.class_name) {
  //     setError('请填写班级名称');
  //     return;
  //   }
  //   try {
  //     const optionalKeys: (keyof UpdateAdministrativeClassesParams)[] = [
  //       'code',
  //       'academic_year',
  //       'grade_level_code',
  //       'teacher_id',
  //     ];
  //     // 清洗
  //     const cleanedForm = cleanOptionalFields(editClassForm,optionalKeys);
  //     await AdministrativeClassesApi.updateClasses(tenantId, tenantName, cleanedForm);
  //     setIsEditDialogOpen(false);
  //     setSelectedClass(null);
  //     resetForm();
  //     setSelectedSubjects([]);
  //     loadInitialData();
  //   } catch (err) {
  //     setError('编辑班级失败');
  //   }
  // };

  // 删除班级
  const handleDeleteClass = async () => {
    if (!selectedClass) return;
    try {
      await AdministrativeClassesApi.deleteClass(tenantId, tenantName, { class_id: String(selectedClass.id) });
      setIsDeleteDialogOpen(false);
      setSelectedClass(null);
      loadInitialData();
    } catch (err) {
      setError('删除班级失败');
    }
  };


  // 批量导入班级（需补全接口）
  const handleBatchImport = async () => {
    // TODO: 实现批量导入功能
    alert('批量导入功能待实现');
  };

  // const openEditDialog = (cls: AdministrativeClassesDetail) => {
  //   setSelectedClass(cls);
  //   setEditClassForm({
  //     id: String(cls.id),
  //     class_name: String(cls.class_name),
  //     code: String(cls.code),
  //     academic_year: String(cls.academic_year),
  //     grade_level_code: String(cls.grade_level_code),
  //     teacher_id: String(cls.teacher_id),
  //     is_active: cls.is_active
  //   });
  //   setIsEditDialogOpen(true);
  // };

  const openDeleteDialog = (cls: AdministrativeClassesDetail) => {
    setSelectedClass(cls);
    setIsDeleteDialogOpen(true);
  };

  const handleSearchSubmit = () => {
    const nextParams: PageUserClassListParams = {
      ...searchParams,
      page_params: {
        page: 1,
        page_size: searchParams.page_params?.page_size || pagination.pageSize,
      },
    };
    setSearchParams(nextParams);
    loadClassList(nextParams);
  };
  const handlePageChange = (page: number, pageSize: number) => {
    const nextParams: PageUserClassListParams = { ...searchParams, page_params: { page, page_size: pageSize } };
    setSearchParams(nextParams);
    loadClassList(nextParams);
  };
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }
  const getOperationButtons = (cls: AdministrativeClassesDetail) => {
    return (

      <div className="flex gap-1">
        <Button variant="ghost" size="sm" onClick={() => navigate(`/administrative-classes/${cls.id}`)}>
          <Eye className="h-4 w-4" />
        </Button>
        {/* <Button variant="ghost" size="sm" onClick={() => openEditDialog(cls)}>
          <Edit className="h-4 w-4" />
        </Button> */}
        <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(cls)}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }
  const getClassStatus = (is_active: Boolean) => {
    return is_active ? (
      <Badge variant="default">启用</Badge>
    ) : (
      <Badge variant="secondary">禁用</Badge>
    );
  }

  //------------------------------------------------------------------组件部分
  function RefreshButton() {
    return <Button variant="outline" onClick={() => {
      loadInitialData()
    }}>
      <RefreshCw className="h-4 w-4 mr-2" />
      刷新
    </Button>
  }
  function BatchImportButton() {
    return <Button variant="outline" onClick={handleBatchImport}>
      <Upload className="h-4 w-4 mr-2" />
      批量导入
    </Button>
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      {error && (
        <AlertDialog>
          <AlertDialogDescription>{error}</AlertDialogDescription>
        </AlertDialog>
      )}
      {/* 顶部按钮栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">班级管理</h1>
          <p className="text-muted-foreground">创建、管理和监控班级</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton />
        </div>
      </div>
      {/* 统计卡片 */}
      <ClassStatisticsCards
        totalClasses={totalClasses}
        totalTeachers={totalTeachers}
        totalStudents={totalStudents}
      />
      {/* 班级列表 */}
      <Card className="flex flex-col flex-grow">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>行政班列表</CardTitle>
          </div>
          <div className="flex gap-2">
            <CreateClassDialog
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
              gradeLevels={gradeLevels}
              onCreate={handleCreateClass}
            />
            <BatchImportButton />
          </div>
        </CardHeader>
        <CardContent className="flex-grow min-h-[200px]">
          <div className="flex gap-4 mb-4">
            <Input
              placeholder="搜索班级名称"
              value={searchParams.name_like?.toString() ?? ''}
              onChange={(e) => setSearchParams({ ...searchParams, name_like: e.target.value || undefined })}
              className="w-[200px]"
            />
            <Input placeholder="搜索班级编号"
              value={searchParams.class_code?.toString() ?? ''}
              onChange={(e) => setSearchParams({ ...searchParams, class_code: e.target.value || undefined })}
              className="w-[300px]"
            />
            <Select
              value={searchParams.is_active === undefined || searchParams.is_active === null ? 'all' : searchParams.is_active ? 'true' : 'false'}
              onValueChange={(value) => {
                let is_active: boolean | undefined;
                if (value === 'all') {
                  is_active = undefined;
                } else if (value === 'true') {
                  is_active = true;
                } else if (value === 'false') {
                  is_active = false;
                }
                setSearchParams({ ...searchParams, is_active });
              }}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="true">启用</SelectItem>
                <SelectItem value="false">禁用</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearchSubmit}>
              <Search className="h-4 w-4 mr-2" />
              查询
            </Button>
          </div>
          <Table className="w-full table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead>班级名称</TableHead>
                <TableHead>班级编号</TableHead>
                <TableHead>班主任</TableHead>
                <TableHead>班级人数</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
          </Table>
          {/* <ScrollArea className="h-[350px] border-none mt-4"> */}
          <Table className="w-full table-fixed ">
            <TableBody>
              {classes.map(cls => (
                <TableRow key={cls.id as string}>
                  <TableCell className="font-medium">{cls.class_name}</TableCell>
                  <TableCell>{cls.code}</TableCell>
                  <TableCell>{cls.teacher_name || '-'}</TableCell>
                  <TableCell>{cls.total_student.toString() || '-'}</TableCell>
                  <TableCell>{getClassStatus(cls.is_active)}</TableCell>
                  <TableCell>{getOperationButtons(cls)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {/* </ScrollArea> */}
        </CardContent>
        <ClassPagination
          pagination={pagination}
          onChange={(page, pageSize) => handlePageChange(page, pageSize)}
        />
      </Card>
      {/* <EditClassDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        classForm={editClassForm}
        onClassFormChange={setEditClassForm}
        gradeLevels={gradeLevels}
        onSave={handleUpdateClass}
        onCancel={() => setIsEditDialogOpen(false)}
      /> */}
      {/* 删除班级对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>你确定要删除班级 {selectedClass?.class_name} 吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
            <Button variant="destructive" onClick={handleDeleteClass}>删除</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdministrativeClassesPage;