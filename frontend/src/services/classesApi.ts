import { ApiResponse, IdStringParams, PageParams, PaginatedApiResponse } from '@/types';
import apiClient from './examApi';
import { ClassesDetail, ClassesStatistics, ClassType, ClassesSummary } from '@/types/classess';

/**
 * 作者：张瀚
 * 说明：班级管理相关的API
 */
export const ClassesApi = {
  /**
   * 统计班级管理信息
   * @param tenantName 
   * @returns 
   */
  getStatistics: async (tenantName: string): Promise<ApiResponse<ClassesStatistics>> => {
    return apiClient.get(`/api/v1/tenants/${tenantName}/classes/getStatistics`);
  },
  /**
   * 分页查询行政班列表
   * @param tenantName 
   * @returns 
   */
  pageAdministrativeClasses: async (tenantName: string, params: PageAdministrativeClassesParams): Promise<PaginatedApiResponse<ClassesDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/pageAdministrativeClasses`, params);
  },
  /**
   * 分页查询教学班列表
   * @param tenantName 
   * @returns 
   */
  pageTeachingClasses: async (tenantName: string, params: PageTeachingClassesParams): Promise<PaginatedApiResponse<ClassesDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/pageTeachingClasses`, params);
  },
  /**
   * 创建班级
   * @param tenantName 
   * @param params 
   * @returns 
   */
  createClasses: async (tenantName: string, params: CreateClassParams): Promise<ApiResponse<ClassesDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/createClasses`, params);
  },
  /**
   * 编辑班级
   * @param tenantName 
   * @param params 
   * @returns 
   */
  updateClasses: async (tenantName: string, params: UpdateClassParams): Promise<ApiResponse<ClassesDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/updateClasses`, params);
  },

  /**
   * 删除班级
   * @param tenantName 
   * @param params 
   * @returns 
   */
  deleteClasses: async (tenantName: string, params: IdStringParams): Promise<ApiResponse<null>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/deleteClasses`, params);
  },
  /**
   * 查询班级内部学生
   * @param tenantName 
   * @param params 
   * @returns 
   */
  pageClassesStudent: async (tenantName: string, params: PageClassesStudentParams): Promise<PaginatedApiResponse<ClassesDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/pageClassesStudent`, params);
  },

  /**
   * 添加班主任
   * @param tenantName 
   * @param params 
   * @returns 
   */
  addHeadTeacher: async (tenantName: string, params: AddTeacherParams): Promise<ApiResponse<null>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/addHeadTeacher`, params);
  },
  /**
   * 删除班主任
   * @param tenantName 
   * @param params 
   * @returns 
   */
  removeHeadTeacher: async (tenantName: string, params: RemoveTeacherParams): Promise<ApiResponse<null>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/classes/removeHeadTeacher`, params);
  },

  /**
   * 获取班级简要信息列表（用于下拉选择）
   * @param tenantName 租户名称
   * @param isActive
   * @returns 班级简要信息列表
   */
  getClassesSummaries: async (tenantName: string, isActive?:boolean): Promise<ApiResponse<ClassesSummary[]>> => {
    return apiClient.get(`/api/v1/tenants/${tenantName}/classes/summaries`,{
      params: {
        is_active: isActive?? true
      }
    });
  },
};

export interface CreateClassParams {
  name: String,
  code: String,
  grade_level_code: String,
  subject_code?: String,
  class_type: ClassType,
  school_year: Number,
}

export interface UpdateClassParams {
  id: String,
  name: String,
  code: String,
  grade_level_code: String,
  subject_code?: String,
  class_type: ClassType,
  school_year: Number,
}

export interface PageAdministrativeClassesParams {
  pagination: PageParams,
}
export interface PageTeachingClassesParams {
  pagination: PageParams,
}
export interface PageClassesStudentParams {
  pagination: PageParams,
}

export interface AddTeacherParams {
  classed_id: String,
  teacher_id: String,
  end_time?: String
}
export interface RemoveTeacherParams {
  classed_id: String,
  teacher_id: String,
}
