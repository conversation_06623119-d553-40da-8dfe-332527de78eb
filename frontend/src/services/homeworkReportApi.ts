import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import apiClient from './apiClient';


export const homeworkReportApi = {
    getHomeworkClassReport: async (tenant_id: string, schema_name: string, homework_id: string): Promise<ApiResponse<ClassSummaryResponse>> => {
        return apiClient.post(
            `/api/v1/tenants/${schema_name}/homework/analysis/classes/${homework_id}`,
            {},
            {
                headers: createApiHeaders(tenant_id),
            }
        );
    },
    getClassAnalysisDetail: async (tenant_id: string, schema_name: string, homework_id:string, class_id: string): Promise<ApiResponse<ClassStudentReport>> => {
        return apiClient.post(
            `/api/v1/tenants/${schema_name}/homework/analysis/class/detail`,
            {homework_id,class_id},
            {
                headers: createApiHeaders(tenant_id),
            }
        );
    },
}
//概览
export interface ClassSummaryResponse {
    summary: HomeworkReportSummary,
    details: ClassReport[],
}
export interface HomeworkReportSummary {
    scoring_student_count: number,
    absent_student_count: number,
    error_student_count: number,
    total_score_count: number,
    done_score_count: number,
    avg_score: number,
    total_score: number
}
export interface ClassReport {
    class: ClassBaseInfo,
    scoring_student_count: number,
    absent_student_count: number,
    error_student_count: number,
    total_score_count: number,
    done_score_count: number,
    avg_score: number,
    total_score: number
}
export interface ClassBaseInfo {
    id: string,
    name: string,
    code: string,
    is_active: boolean,
}

//班级详细信息
export interface ClassStudentReport{
    summary: HomeworkReportSummary,
    criteria_list: StudentCriteria[],
    student_scores: StudentScoreMap[], 
}
export interface StudentCriteria{
    id: string,
    scoring_type: string,
    mode: string,
    criteriaName: string,
    answer:string,
    score: number,
    ocr_work_id: string,
    check_work_id: string,
}
export interface StudentScoreMap{
    student: StudentBaseInfo,
    scores: Scores[] | null,
}
export interface StudentBaseInfo{
    student_id: string,
    student_number: string,
    student_name: string,
    status: string,
}

export interface Scores{
    id: string,
    criteria_id: string,
    student_id: string,
    score: string,
    score_status: string,
}