import apiClient from './apiClient';
import {GradeLevel, Subject} from "@/types";

// Types
export interface ExamCreateRequest {
  name: string;
  exam_type: string;
  grade_level: string;
  exam_nature: string;
  description?: string;
  start_time: string;
  end_time: string;
  expected_collection_time?: string;
  scan_start_time?: string;
  grading_mode: string;
  quality_control: string;
  ai_confidence_threshold?: number;
  manual_review_ratio?: number;
  subjects: ExamSubjectRequest[];
  classes: ExamClassRequest[];
  selected_students?: string[];
}

export interface ExamSubjectRequest {
  subject_id: string;
  paper_template_id: string;
  total_score: number;
  pass_score: number;
}

export interface ExamClassRequest {
  class_id: string;
  class_type: string;
}

export interface ExamResponse {
  id: string;
  name: string;
  exam_type: string;
  grade_level: string;
  exam_nature: string;
  description?: string;
  start_time: string;
  end_time: string;
  expected_collection_time?: string;
  scan_start_time?: string;
  grading_mode: string;
  quality_control: string;
  ai_confidence_threshold?: number;
  manual_review_ratio?: number;
  status: string;
  created_by: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  subjects: ExamSubject[];
  classes: ExamClass[];
  student_count: number;
  joint_exam_info?: JointExam[];
}

export interface ExamSubject {
  id: string;
  exam_id: string;
  subject_id: string;
  paper_template_id: string;
  total_score: number;
  pass_score: number;
  created_at: string;
}

export interface ExamClass {
  id: string;
  exam_id: string;
  class_id: string;
  class_type: string;
  created_at: string;
}

export interface JointExam {
  id: string;
  main_exam_id: string;
  organizer_tenant_id: string;
  participant_tenant_id: string;
  invitation_status: string;
  invitation_sent_at: string;
  responded_at?: string;
  sync_status: string;
  created_at: string;
}

export interface ExamQueryParams {
  page?: number;
  page_size?: number;
  name?: string;
  exam_type?: string;
  grade_level?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
}

export interface ExamListResponse {
  exams: ExamResponse[];
  total: number;
  page: number;
  page_size: number;
}

export interface ExamStatistics {
  total_exams: number;
  completed_exams: number;
  in_progress_exams: number;
  draft_exams: number;
  total_students: number;
  total_papers: number;
  grading_progress: number;
}

export interface UpdateExamRequest {
  name?: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  status?: string;
}

export interface JointExamInvitationRequest {
  exam_id: string;
  participant_tenant_ids: string[];
}

export interface JointExamResponseRequest {
  invitation_id: string;
  response: string; // 'accept' or 'reject'
}

// Exam Management API
export const examApi = {
  // Get exam statistics
  getStatistics: async (tenantId: string): Promise<ExamStatistics> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/exams/statistics`);
  },

  // Create exam
  createExam: async (tenantId: string, examData: ExamCreateRequest): Promise<ExamResponse> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/exams`, examData);
  },

  // Get exam list
  getExams: async (tenantId: string, params?: ExamQueryParams): Promise<ExamListResponse> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/exams`, { params });
  },

  // Get exam by ID
  getExam: async (tenantId: string, examId: string): Promise<ExamResponse> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/exams/${examId}`);
  },

  // Update exam
  updateExam: async (tenantId: string, examId: string, examData: UpdateExamRequest): Promise<ExamResponse> => {
    return apiClient.put(`/api/v1/tenant/${tenantId}/exams/${examId}`, examData);
  },

  // Delete exam
  deleteExam: async (tenantId: string, examId: string): Promise<boolean> => {
    return apiClient.delete(`/api/v1/tenant/${tenantId}/exams/${examId}`);
  },

  // Publish exam
  publishExam: async (tenantId: string, examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/exams/${examId}/publish`);
  },

  // Start exam
  startExam: async (tenantId: string, examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/exams/${examId}/start`);
  },

  // Complete exam
  completeExam: async (tenantId: string, examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/exams/${examId}/complete`);
  },

  // Joint exam invitation
  createJointExamInvitation: async (tenantId: string, invitationData: JointExamInvitationRequest): Promise<JointExam[]> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/exams/joint-invitation`, invitationData);
  },

  // Respond to joint exam invitation
  respondToJointExamInvitation: async (tenantId: string, responseData: JointExamResponseRequest): Promise<JointExam> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/exams/joint-response`, responseData);
  },

  // Get joint exam invitations
  getJointExamInvitations: async (tenantId: string): Promise<JointExam[]> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/exams/joint-invitations`);
  },
};


export const subjectsApi = {
    getSubjects: async (): Promise<Subject[]> => {
        return apiClient.get('/api/v1/subjects');
    },
};

export const gradeLevelsApi = {
    getGradeLevels: async (): Promise<GradeLevel[]> => {
        return apiClient.get('/api/v1/grade-levels');
    },
};

// Students API
export interface Student {
  id: string;
  student_id: string;
  name: string;
  administrative_class_id: string;
  grade_level_id: string;
  user_id?: string;
  profile_level?: string;
  profile_tags?: any;
  created_at: string;
  updated_at: string;
}

export const studentsApi = {
  getStudents: async (tenantId: string, classId?: string): Promise<Student[]> => {
    const params = classId ? { class_id: classId } : {};
    return apiClient.get(`/api/v1/tenant/${tenantId}/students`, { params });
  },
};

// Paper Templates API
export interface PaperTemplate {
  id: string;
  title: string;
  subject: string;
  grade_level: number;
  total_score: number;
  description?: string;
  structure: any;
  creator_id: string;
  version: number;
  status: string;
  created_at: string;
  updated_at: string;
  questions: any[];
  usage_count: number;
}

export const paperTemplatesApi = {
  getPaperTemplates: async (subject?: string, gradeLevel?: number): Promise<PaperTemplate[]> => {
    const params: any = {};
    if (subject) params.subject = subject;
    if (gradeLevel) params.grade_level = gradeLevel;
    return apiClient.get('/api/v1/paper-templates', { params });
  },
};

export default apiClient;