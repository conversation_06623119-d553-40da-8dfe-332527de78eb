import { PaperContentData } from '@/components/question-card/store/paperDataStore';
import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import { Paper } from '@/types/papers';
import apiClient from './apiClient';
const API_BASE_URL = '/api/v1';

/**
 * 作者：张瀚
 * 说明：租户内paper表的api
 */
export const tenantPaperApi = {
    /**
     * 作者：张瀚
     * 说明：通过试卷ID查询试卷信息
     */
    findById: async (tenant_id: string, schema_name: string, paper_id: string): Promise<ApiResponse<Paper>> => {
        return apiClient.post(`${API_BASE_URL}/tenants/${schema_name}/papers/findById`, { paper_id }, {
            headers: createApiHeaders(tenant_id)
        });
    },
    /**
     * 作者：张瀚
     * 说明：通过试卷ID更新试卷
     */
    updatePaperContent: async (tenant_id: string, schema_name: string, params: UpdatePaperContentParams): Promise<ApiResponse<Paper>> => {
        return apiClient.post(`${API_BASE_URL}/tenants/${schema_name}/papers/updatePaperContent`, params, {
            headers: createApiHeaders(tenant_id)
        });
    },
};

export interface UpdatePaperContentParams {
    paper_id: string,
    paper_content: PaperContentData,
}
