import apiClient from './apiClient';
import {
  SubjectGroupMember,
  SubjectGroupMemberQueryParams,
  CreateSubjectGroupMemberParams,
  UpdateSubjectGroupMemberParams,
  BatchAddMembersParams,
  SubjectGroupMemberStats,
  RecommendedTeacher,
  SubjectGroupMemberResponse,
  SubjectGroupMemberStatsResponse,
  RecommendedTeachersResponse
} from '@/types/subjectGroupMember';

export class SubjectGroupMemberApi {
  private static baseUrl = '/api/subject-group-members';

  // 获取学科组成员列表
  static async getMembers(
    tenantName: string, 
    params: SubjectGroupMemberQueryParams
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${tenantName}`, {
        params
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get subject group members:', error);
      return { success: false, message: '获取学科组成员失败' };
    }
  }

  // 添加学科组成员
  static async addMember(
    tenantName: string,
    params: CreateSubjectGroupMemberParams
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${tenantName}`, params);
      return response.data;
    } catch (error) {
      console.error('Failed to add subject group member:', error);
      return { success: false, message: '添加学科组成员失败' };
    }
  }

  // 批量添加学科组成员
  static async batchAddMembers(
    tenantName: string,
    params: BatchAddMembersParams
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${tenantName}/batch`, params);
      return response.data;
    } catch (error) {
      console.error('Failed to batch add subject group members:', error);
      return { success: false, message: '批量添加学科组成员失败' };
    }
  }

  // 更新学科组成员
  static async updateMember(
    tenantName: string,
    params: UpdateSubjectGroupMemberParams
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/${tenantName}/${params.id}`, params);
      return response.data;
    } catch (error) {
      console.error('Failed to update subject group member:', error);
      return { success: false, message: '更新学科组成员失败' };
    }
  }

  // 移除学科组成员
  static async removeMember(
    tenantName: string,
    memberId: string
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/${tenantName}/${memberId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to remove subject group member:', error);
      return { success: false, message: '移除学科组成员失败' };
    }
  }

  // 批量移除学科组成员
  static async batchRemoveMembers(
    tenantName: string,
    memberIds: string[]
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/${tenantName}/batch`, {
        data: { member_ids: memberIds }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to batch remove subject group members:', error);
      return { success: false, message: '批量移除学科组成员失败' };
    }
  }

  // 获取学科组成员统计
  static async getMemberStats(
    tenantName: string,
    subjectGroupId: string
  ): Promise<SubjectGroupMemberStatsResponse> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${tenantName}/${subjectGroupId}/stats`);
      return response.data;
    } catch (error) {
      console.error('Failed to get subject group member stats:', error);
      return { success: false, message: '获取学科组成员统计失败' };
    }
  }

  // 获取推荐教师列表
  static async getRecommendedTeachers(
    tenantName: string,
    subjectGroupId: string
  ): Promise<RecommendedTeachersResponse> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${tenantName}/${subjectGroupId}/recommended-teachers`);
      return response.data;
    } catch (error) {
      console.error('Failed to get recommended teachers:', error);
      return { success: false, message: '获取推荐教师失败' };
    }
  }

  // 设置组长（从现有成员中选择）
  static async setLeader(
    tenantName: string,
    subjectGroupId: string,
    memberId: string
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${tenantName}/${subjectGroupId}/set-leader`, {
        member_id: memberId
      });
      return response.data;
    } catch (error) {
      console.error('Failed to set subject group leader:', error);
      return { success: false, message: '设置学科组长失败' };
    }
  }

  // 转移组长职务
  static async transferLeadership(
    tenantName: string,
    subjectGroupId: string,
    fromMemberId: string,
    toMemberId: string
  ): Promise<SubjectGroupMemberResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${tenantName}/${subjectGroupId}/transfer-leadership`, {
        from_member_id: fromMemberId,
        to_member_id: toMemberId
      });
      return response.data;
    } catch (error) {
      console.error('Failed to transfer leadership:', error);
      return { success: false, message: '转移组长职务失败' };
    }
  }
}

export const subjectGroupMemberApi = SubjectGroupMemberApi;
