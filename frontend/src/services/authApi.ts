import apiClient from './apiClient';

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  verification_code?: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
  data?: {
    password_changed_at: string;
    requires_relogin: boolean;
  };
}

export interface SendVerificationCodeRequest {
  phone_number: string;
  code_type: string;
}

export interface SendVerificationCodeResponse {
  success: boolean;
  message: string;
  data: {
    expires_in: number;
    can_resend_after: number;
  };
}

export const changePassword = async (request: ChangePasswordRequest): Promise<ChangePasswordResponse> => {
  return await apiClient.post('/api/v1/auth/change-password', request);
};

export const sendVerificationCode = async (request: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse> => {
  return await apiClient.post('/api/v1/auth/send-verification-code', request);
};
