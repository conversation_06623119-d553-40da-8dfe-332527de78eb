import apiClient from "@/services/apiClient.ts";
import {BindIdentityRequest, CurrentUserData, IdentityInfo} from "@/types/identity.ts";



export async function getCurrentUser():Promise<CurrentUserData> {
    const response = await apiClient.get('/api/v1/auth/me');
    return response.data
}

export async function getIdentities():Promise<IdentityInfo[]> {
    const response = await apiClient.get('/api/v1/identity/list');
    return response.data;
}

export async function bindIdentities(request:BindIdentityRequest) {
    const response = await apiClient.post('/api/v1/identity/bind', request);
    return response.data;
}