/**
 * 菜单管理 API 服务
 * 提供菜单和权限模板相关的 API 调用接口
 */

import apiClient from './apiClient';
import {ApiResponse, PaginatedApiResponse} from '@/types';
import {
  MenuCreateRequest,
  MenuItem,
  MenuQueryParams,
  MenuUpdateRequest,
  PermissionTemplate,
  PermissionTemplateQueryParams,
} from '@/types/menu';

// 基础API路径
const MENUS_API = '/api/v1/menus';
const PERMISSION_TEMPLATES_API = '/api/v1/admin/permission-templates';

/**
 * 菜单管理 API
 */
export const menuApi = {
  /**
   * 获取菜单树结构
   */
  async getMenuTree(params?: MenuQueryParams): Promise<ApiResponse<MenuItem[]>> {
    return await apiClient.get(MENUS_API, {params}) as ApiResponse<MenuItem[]>;
  },

  /**
   * 创建新菜单
   */
  async createMenu(request: MenuCreateRequest): Promise<ApiResponse<MenuItem>> {
    return await apiClient.post(MENUS_API, request) as ApiResponse<MenuItem>;
  },

  /**
   * 更新菜单
   */
  async updateMenu(menuId: string, request: MenuUpdateRequest): Promise<ApiResponse<MenuItem>> {
    return await apiClient.put(`${MENUS_API}/${menuId}`, request) as ApiResponse<MenuItem>;
  },

  /**
   * 删除菜单
   */
  async deleteMenu(menuId: string, force: boolean = false): Promise<ApiResponse<string>> {
    const params = force ? { search: 'force' } : {};
    return await apiClient.delete(`${MENUS_API}/${menuId}`, {params}) as ApiResponse<string>;
  },
  /**
   * 复制菜单
   */
  async duplicateMenu(menuId: string, newMenuId: string, newName: string): Promise<ApiResponse<MenuItem>> {
    return await apiClient.post(`${MENUS_API}/${menuId}/duplicate`, {
      new_menu_id: newMenuId,
      new_name: newName
    }) as ApiResponse<MenuItem>;
  }
};

/**
 * 权限模板管理 API
 */
export const permissionTemplateApi = {
  /**
   * 获取权限模板列表
   */
  async getTemplates(params?: PermissionTemplateQueryParams): Promise<PaginatedApiResponse<PermissionTemplate>> {
    return await apiClient.get(PERMISSION_TEMPLATES_API, {params}) as PaginatedApiResponse<PermissionTemplate>;
  },
  /**
   * 创建权限模板
   */
  async createTemplate(data: Partial<PermissionTemplate>): Promise<ApiResponse<PermissionTemplate>> {
    return await apiClient.post(PERMISSION_TEMPLATES_API, data) as ApiResponse<PermissionTemplate>;
  },

  /**
   * 更新权限模板
   */
  async updateTemplate(id: string, data: Partial<PermissionTemplate>): Promise<ApiResponse<PermissionTemplate>> {
    return await apiClient.put(`${PERMISSION_TEMPLATES_API}/${id}`, data) as ApiResponse<PermissionTemplate>;
  },

  /**
   * 删除权限模板
   */
  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${PERMISSION_TEMPLATES_API}/${id}`) as ApiResponse<void>;
    return response;
  }
};
