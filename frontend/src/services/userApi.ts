import apiClient from './apiClient';
import {User} from '@/types/user';

export function updateUser(update: {user_id:string; password?: string; phone?: string; is_active?: boolean }) {
  return apiClient.put('/api/v1/users',update);
}

export function createUser(create: { username: string; phone: string; password: string }) {
  return apiClient.post('/api/v1/users', create);
}

export const getUsers = async (): Promise<User[]> => {
  const response = await apiClient.get('/api/v1/users');
  return response.data;
};