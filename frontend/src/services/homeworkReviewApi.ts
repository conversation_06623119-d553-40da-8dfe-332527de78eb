import { ApiResponse } from "@/types";
import apiClient from "./apiClient";
import { Student } from "@/services/scanApi";
import { QuestionReview } from '@/pages/Homework/components/QuestionDisplayCard';


// 题目类型
export interface Question {
  name: string;
  avg_score: number;
  id: string;
  score: number;
  scoring_type: "match" | "ai" | "manual";
  answer: string;
  total_count: number;
  success_count: number;
}

// 分数的请求参数
export interface ScoresRequest {
  homework_id: string;
  criteria_id: string;
  page: number;
  page_size: number;
}

// 分数的返回参数
export interface Scores {
  id: string;
  student: Student | null;
  criteria_id: string;
  score: number;
  status: 'Undistributed' | 'Distributed' | 'Excepted' | 'Done' | 'CheckedError' | 'CheckedCorrect';
  blocks: Block[];
  details?: Details[];
}
export interface StudentBase {
  student_number: string;
  student_name: string;
}
 
export interface Block {
  answer_block_group_id: string;
  answer_block_url: string;
}

export interface Details {
  ocr: string;
  status: 'OcrFailed' | 'GradingFailed' | 'Done';
  score: string;
  scoring_type: 'Match' | 'AI' | 'Manual' | 'Online' | 'Check'; 
  reason: Reason;
}

export interface Reason {
    Text: string;
    None: string | null;
    Blank: string | null;
    Composition: string | null;
}



// 阅卷核查API
export const homeworkReviewApi = {
  /**
   * 获取题目列表
   */
  getQuestions: async (
    tenantName: string,
    homework_id: String
  ): Promise<ApiResponse<Question[]>> => {
    return apiClient.post(
      `/api/v1/grading/${tenantName}/questions/${homework_id}/criteria`
    );
  },

  /**
   * 获取题目分数
   */
  getQuestionScores: async (
    tenantName: string,
    params: ScoresRequest
  ): Promise<ApiResponse<Scores[]>> => {
    return apiClient.post(
      `/api/v1/grading/${tenantName}/questions/scores`,
      params
    );
  },

  /**
   * 修改核查分数
   */
  updateQuestionScores: async (
    tenantName: string,
    params:{
      id: string,
      score: number,
      reason?: string | undefined
    }
  ): Promise<ApiResponse<any[]>> => {
    return apiClient.post(
      `/api/v1/grading/${tenantName}/checkById`,
      params
    );
  },

  /**
   * 批量确认
   */
  batchConfirm: async (
    tenantName: string,
    score_ids: string[]
  ): Promise<ApiResponse<any[]>> => {
    return apiClient.post(
      `/api/v1/grading/${tenantName}/checkCorrectWithBatchIds`,
      {score_ids}
    );
  },
};

export default homeworkReviewApi;

// ===================== 作业讲评相关接口 =====================

// 后端返回的班级分析数据接口
export interface ClassAnalysisResponse {
  done_students: StudentInfo[];
  error_students: StudentInfo[];
  unsubmitted_students: StudentInfo[];
  content_answer_analysis_list: ContentAnswerAnalysis[];
}

// 学生信息接口
export interface StudentInfo {
  student_id: string;
  student_name: string;
  student_number: string;
}

// 试题内容分析接口 - 匹配Rust enum结构
export type ContentAnswerAnalysis = {
  Text: string;
} | {
  AnswerAnalysis: AnswerAnalysis;
};

// 答题分析接口 - 匹配Rust struct结构
export interface AnswerAnalysis {
  criterion: ScoringCriterion;
  error_scored: ScoreVo[];
  pending_scored: ScoreVo[];
  avg_score: number | null;
  scored_map: Record<string, ScoreVo[]>;
}

// 评分标准接口 - 匹配Rust struct结构
export interface ScoringCriterion {
  id: string;
  scoring_type: ScoringCriteriaTypeEnum;
  mode: string | null;
  criteriaName: string | null; // 对应 #[serde(rename = "criteriaName")] name
  answer: string | null;
  score: number;
  ocr_work_id: string | null;
  check_work_id: string | null;
  questionTips: string | null; // 对应 #[serde(rename = "questionTips")] question_tips
}

// 评分标准类型枚举 - 匹配Rust enum结构
export type ScoringCriteriaTypeEnum = "Match" | "AI" | "Manual";

// 分数VO接口 - 匹配Rust struct结构
export interface ScoreVo {
  id: string;
  criteria_id: string;
  score: number;
  status: ScoreStatus;
  blocks: ScoreBlock[];
  details: ScoreDetailVo[];
  student: StudentBase | null;
  answer: string;
}

// 分数状态枚举
export type ScoreStatus = 
  | "Undistributed"
  | "Distributed" 
  | "Excepted"
  | "Done"
  | "CheckedError"
  | "CheckedCorrect";

// 分数块接口
export interface ScoreBlock {
  // 根据实际Rust struct定义调整
  [key: string]: any;
}

// 分数详情VO接口
export interface ScoreDetailVo {
  // 根据实际Rust struct定义调整
  [key: string]: any;
}

// 学生基础信息接口
export interface StudentBase {
  student_id: string;
  student_name: string;
  student_number: string;
}

// 作业讲评数据接口（前端使用）
export interface HomeworkReviewData {
  homework_id: string;
  class_id: string;
  class_name: string;
  homework_name: string;
  total_students: number;
  done_students: number;
  error_students: number;
  unsubmitted_students: number;
  questions: QuestionReview[];
}

// 学生答题详情接口
export interface StudentAnswerDetail {
  student_id: string;
  question_id: string;
  answer_image_url?: string;
  score: number;
  grading_details: string;
  grading_time: string;
  grader: string;
  grading_criteria: GradingCriteria[];
}

export interface GradingCriteria {
  item: string;
  score: number;
  maxScore: number;
  comment: string;
}

// 作业讲评专用API
export const homeworkReviewPageApi = {
  // 获取作业讲评数据 - 使用新的班级分析接口
  async getReviewData(
    tenantName: string,
    homeworkId: string,
    classId: string
  ): Promise<ApiResponse<ClassAnalysisResponse>> {
    return apiClient.post(
      `/api/v1/tenants/${tenantName}/homework/analysis/class/analysis`,
      {
        homework_id: homeworkId,
        class_id: classId
      }
    );
  },

  // 获取作业讲评数据 - 旧版本接口（保留兼容性）
  async getReviewDataLegacy(
    tenantId: string,
    schemaName: string,
    homeworkId: string,
    classId: string
  ): Promise<ApiResponse<HomeworkReviewData>> {
    return apiClient.get(
      `/api/v1/homework-review/${homeworkId}/${classId}`,
      {
        headers: {
          'X-Tenant-ID': tenantId,
          'X-Schema-Name': schemaName,
        },
      }
    );
  },

  // 获取学生答题详情
  async getStudentAnswerDetail(
    tenantId: string,
    schemaName: string,
    homeworkId: string,
    studentId: string,
    questionId: string
  ): Promise<ApiResponse<StudentAnswerDetail>> {
    return apiClient.get(
      `/api/v1/student-answer-detail/${homeworkId}/${studentId}/${questionId}`,
      {
        headers: {
          'X-Tenant-ID': tenantId,
          'X-Schema-Name': schemaName,
        },
      }
    );
  },

  // 导出学生评分详情
  async exportStudentGradingDetail(
    tenantId: string,
    schemaName: string,
    homeworkId: string,
    studentId: string,
    questionId: string
  ): Promise<Blob> {
    const response = await apiClient.get(
      `/api/v1/export/student-grading-detail/${homeworkId}/${studentId}/${questionId}`,
      {
        headers: {
          'X-Tenant-ID': tenantId,
          'X-Schema-Name': schemaName,
        },
        responseType: 'blob',
      }
    );
    return response.data;
  },

  // 导出作业讲评报告
  async exportHomeworkReviewReport(
    tenantId: string,
    schemaName: string,
    homeworkId: string,
    classId: string
  ): Promise<Blob> {
    const response = await apiClient.get(
      `/api/v1/export/homework-review-report/${homeworkId}/${classId}`,
      {
        headers: {
          'X-Tenant-ID': tenantId,
          'X-Schema-Name': schemaName,
        },
        responseType: 'blob',
      }
    );
    return response.data;
  },
};
