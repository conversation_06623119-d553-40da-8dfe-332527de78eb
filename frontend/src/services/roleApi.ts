/**
 * 角色管理 API 服务
 * 提供角色、权限相关的 API 调用接口
 */

import apiClient from './apiClient';
import { ApiResponse, PaginatedApiResponse } from '@/types';
import {
  Role,
  Permission,
  UserIdentity,
  CreateRoleRequest,
  UpdateRoleRequest,
  AssignRoleRequest,
  RoleQueryParams,
  PermissionQueryParams,
  RoleStatistics
} from '@/types/role';

// 基础API路径
const ROLES_API = '/api/v1/roles';
const PERMISSIONS_API = '/api/v1/permissions';
const USER_IDENTITIES_API = '/api/v1/user-identities';

/**
 * 角色管理 API
 */
export const roleApi = {
  /**
   * 获取角色列表
   */
  async getRoles(params?: RoleQueryParams): Promise<PaginatedApiResponse<Role>> {
    const response = await apiClient.get(ROLES_API, { params });
    return response;
  },

  /**
   * 获取单个角色详情
   */
  async getRole(id: string): Promise<ApiResponse<Role>> {
    const response = await apiClient.get(`${ROLES_API}/${id}`);
    return response;
  },

  /**
   * 创建角色
   */
  async createRole(data: CreateRoleRequest): Promise<ApiResponse<Role>> {
    const response = await apiClient.post(ROLES_API, data);
    return response;
  },

  /**
   * 更新角色
   */
  async updateRole(id: string, data: UpdateRoleRequest): Promise<ApiResponse<Role>> {
    const response = await apiClient.put(`${ROLES_API}/${id}`, data);
    return response;
  },

  /**
   * 删除角色
   */
  async deleteRole(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${ROLES_API}/${id}`);
    return response;
  },

  /**
   * 批量删除角色
   */
  async batchDeleteRoles(ids: string[]): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${ROLES_API}/batch`, { 
      data: { ids } 
    });
    return response;
  },

  /**
   * 启用/禁用角色
   */
  async toggleRoleStatus(id: string, isActive: boolean): Promise<ApiResponse<Role>> {
    const response = await apiClient.patch(`${ROLES_API}/${id}/status`, { 
      is_active: isActive 
    });
    return response;
  },

  /**
   * 复制角色
   */
  async cloneRole(id: string, name: string): Promise<ApiResponse<Role>> {
    const response = await apiClient.post(`${ROLES_API}/${id}/clone`, { 
      name 
    });
    return response;
  },

  /**
   * 获取角色统计信息
   */
  async getRoleStatistics(): Promise<ApiResponse<RoleStatistics>> {
    const response = await apiClient.get(`${ROLES_API}/statistics`);
    return response;
  }
};

/**
 * 权限管理 API
 */
export const permissionApi = {
  /**
   * 获取权限列表
   */
  async getPermissions(params?: PermissionQueryParams): Promise<PaginatedApiResponse<Permission>> {
    const response = await apiClient.get(PERMISSIONS_API, { params });
    return response;
  },

  /**
   * 获取权限树（按资源分组）
   */
  async getPermissionTree(): Promise<ApiResponse<Permission[]>> {
    const response = await apiClient.get(`${PERMISSIONS_API}/tree`);
    return response;
  },

  /**
   * 获取单个权限详情
   */
  async getPermission(id: string): Promise<ApiResponse<Permission>> {
    const response = await apiClient.get(`${PERMISSIONS_API}/${id}`);
    return response;
  },

  /**
   * 检查用户权限
   */
  async checkPermission(resource: string, action: string): Promise<ApiResponse<boolean>> {
    const response = await apiClient.get(`${PERMISSIONS_API}/check`, {
      params: { resource, action }
    });
    return response;
  }
};

/**
 * 用户身份管理 API
 */
export const userIdentityApi = {
  /**
   * 获取用户身份列表
   */
  async getUserIdentities(userId: string): Promise<ApiResponse<{identities: UserIdentity[], total: number, page: number, page_size: number}>> {
    const response = await apiClient.get(`${USER_IDENTITIES_API}?user_id=${userId}`);
    return response;
  },

  /**
   * 分配角色给用户（支持多角色）
   */
  async assignRole(data: AssignRoleRequest): Promise<ApiResponse<UserIdentity>> {
    // 转换为新的多角色格式
    const createRequest = {
      user_id: data.user_id,
      role_ids: [data.role_id], // 单角色转为数组
      target_type: data.target_type || 'school',
      target_id: data.target_id,
      subject: data.subject,
    };
    const response = await apiClient.post(USER_IDENTITIES_API, createRequest);
    return response;
  },

  /**
   * 批量分配角色给用户（支持多角色）
   */
  async assignMultipleRoles(data: {
    user_id: string;
    role_ids: string[];
    target_type?: string;
    target_id?: string;
    subject?: string;
  }): Promise<ApiResponse<UserIdentity>> {
    const createRequest = {
      user_id: data.user_id,
      role_ids: data.role_ids,
      target_type: data.target_type || 'school',
      target_id: data.target_id,
      subject: data.subject,
    };
    const response = await apiClient.post(USER_IDENTITIES_API, createRequest);
    return response;
  },

  /**
   * 移除用户角色
   */
  async removeUserRole(identityId: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${USER_IDENTITIES_API}/${identityId}`);
    return response;
  },

  /**
   * 切换用户身份
   */
  async switchIdentity(userId: string, identityId: string): Promise<ApiResponse<UserIdentity>> {
    const response = await apiClient.post(`${USER_IDENTITIES_API}/switch`, {
      user_id: userId,
      identity_id: identityId
    });
    return response;
  },

  /**
   * 获取用户身份历史
   */
  async getIdentityHistory(userId: string): Promise<ApiResponse<any[]>> {
    const response = await apiClient.get(`${USER_IDENTITIES_API}/history/${userId}`);
    return response;
  },

  /**
   * 验证用户身份
   */
  async verifyIdentity(identityId: string): Promise<ApiResponse<UserIdentity>> {
    const response = await apiClient.post(`${USER_IDENTITIES_API}/${identityId}/verify`);
    return response;
  }
};

/**
 * 权限矩阵 API
 */
export const permissionMatrixApi = {
  /**
   * 获取权限矩阵
   */
  async getPermissionMatrix(): Promise<ApiResponse<any>> {
    const response = await apiClient.get('/api/v1/permissions/matrix');
    return response;
  },

  /**
   * 计算用户数据访问范围
   */
  async calculateUserDataScope(userId: string, tenantId: string): Promise<ApiResponse<any>> {
    const response = await apiClient.post('/api/v1/permissions/calculate', {
      user_id: userId,
      tenant_id: tenantId
    });
    return response;
  },

  /**
   * 获取权限访问日志
   */
  async getPermissionLogs(params?: any): Promise<PaginatedApiResponse<any>> {
    const response = await apiClient.get('/api/v1/permissions/logs', { params });
    return response;
  }
};

// 导出默认角色API
export default roleApi;