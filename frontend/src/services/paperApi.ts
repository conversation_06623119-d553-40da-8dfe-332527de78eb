import apiClient from './apiClient';
const API_BASE_URL = '/api/v1';

// TypeScript Types
export interface Paper {
  id: string;
  paper_name: string;
  serial_number: number;
  paper_content?: any;
  created_at?: string;
  updated_at?: string;
}

export interface CreatePaperRequest {
  paper_name: string;
  paper_content?: any;
}

export interface UpdatePaperRequest extends Partial<CreatePaperRequest> {}

// API Service
export const paperApi = {
  async getPapers(params?: { page?: number; page_size?: number }): Promise<Paper[]> {
    const response = await apiClient.get(`${API_BASE_URL}/paper/paper/list`, { params });
    return response.data;
  },

  async getPaper(id: string): Promise<Paper> {
    const response = await apiClient.get(`${API_BASE_URL}/paper/${id}`);
    return response.data;
  },

  async createPaper(data: CreatePaperRequest): Promise<Paper> {
    const response = await apiClient.post(`${API_BASE_URL}/paper`, data);
    return response.data;
  },

  async updatePaper(id: string, data: UpdatePaperRequest): Promise<Paper> {
    const response = await apiClient.put(`${API_BASE_URL}/paper/${id}`, data);
    return response.data;
  },

  async deletePaper(id: string): Promise<void> {
    await apiClient.delete(`${API_BASE_URL}/paper/${id}`);
  },


};
