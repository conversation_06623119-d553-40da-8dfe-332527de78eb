import apiClient from './apiClient';
import {MenuPermission} from '@/contexts/MenuContext';
import {UserRole, DataScope} from '@/contexts/PermissionContext';

// API 响应接口
interface ApiResponse<T> {
    success: boolean;
    data: T;
    message?: string;
    code?: number;
}

// 菜单权限响应
interface MenuPermissionResponse {
    menus: MenuPermission[];
    total_count: number;
    filtered_count: number;
    user_identity: string;
    tenant_id: string;
}

// 菜单访问检查响应
interface MenuAccessCheckResponse {
    menu_id: string;
    accessible: boolean;
    reason?: string;
    required_permissions: string[];
    user_permissions: string[];
}

// 批量菜单访问检查响应
interface BatchMenuAccessResponse {
    results: MenuAccessCheckResponse[];
    summary: {
        total_checked: number;
        accessible_count: number;
        denied_count: number;
        check_duration_ms: number;
    };
}

// 用户角色响应
interface UserRolesResponse {
    roles: UserRole[];
    user_identity: string;
    tenant_id: string;
}

// 数据权限范围响应
interface DataScopesResponse {
    data_scopes: DataScope[];
    resource: string;
    user_identity: string;
    tenant_id: string;
}

// 权限配置响应
interface PermissionConfigResponse {
    permissions: string[];
    roles: string[];
    menu_configs: MenuPermission[];
}


// 权限API服务
export const permissionApi = {
    /**
     * 获取用户可访问的菜单列表
     */
    async getUserMenus(tenantId: string | null, menuType?: string): Promise<MenuPermissionResponse> {
        const params: any = {};
        if (tenantId) {
            params.tenant_id = tenantId;
        }
        if (menuType) {
            params.menu_type = menuType;
        }

        const response = await apiClient.get('/api/v1/permissions/menus', {
            params,
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 检查单个菜单的访问权限
     */
    async checkMenuAccess(tenantId: string, menuId: string): Promise<ApiResponse<MenuAccessCheckResponse>> {
        const response = await apiClient.get(`/api/v1/permissions/menus/${tenantId}/${menuId}/check`, {
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 批量检查菜单访问权限
     */
    async batchCheckMenuAccess(tenantId: string, menuIds: string[]): Promise<ApiResponse<BatchMenuAccessResponse>> {
        const response = await apiClient.post('/api/v1/permissions/menus/batch-check', {
            tenant_id: tenantId,
            menu_ids: menuIds
        }, {
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 获取用户角色信息
     */
    async getUserRoles(tenantId: string): Promise<UserRolesResponse> {
        const response = await apiClient.get('/api/v1/permissions/roles', {
            params: {tenant_id: tenantId},
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 获取用户数据权限范围
     */
    async getUserDataScopes(tenantId: string, resource: string): Promise<DataScopesResponse> {
        const response = await apiClient.get('/api/v1/permissions/data-scopes', {
            params: {
                tenant_id: tenantId,
                resource: resource
            },
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 检查单个权限
     */
    async checkPermission(tenantId: string, resource: string, action: string, scope?: string): Promise<ApiResponse<{
        allowed: boolean;
        reason?: string
    }>> {
        const response = await apiClient.post('/api/v1/permissions/check', {
            resource,
            action,
            scope,
            tenant_id: tenantId
        }, {
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 批量检查权限
     */
    async batchCheckPermissions(tenantId: string, permissions: Array<{
        resource: string,
        action: string,
        scope?: string
    }>): Promise<ApiResponse<{ results: boolean[] }>> {
        const response = await apiClient.post('/api/v1/permissions/batch-check', {
            permissions,
            tenant_id: tenantId
        }, {
            headers: {
                'X-Tenant-ID': tenantId
            }
        });
        return response.data;
    },

    /**
     * 获取权限配置（管理员功能）
     */
    async getPermissionConfig(): Promise<ApiResponse<PermissionConfigResponse>> {
        const response = await apiClient.get('/api/v1/permissions/config');
        return response.data;
    },

    /**
     * 更新菜单权限配置（管理员功能）
     */
    async updateMenuPermission(menuId: string, menuPermission: MenuPermission): Promise<ApiResponse<string>> {
        const response = await apiClient.put(`/api/v1/permissions/menus/${menuId}`, menuPermission);
        return response.data;
    },

    /**
     * 创建菜单权限配置（管理员功能）
     */
    async createMenuPermission(menuPermission: Omit<MenuPermission, 'menu_id'>): Promise<ApiResponse<MenuPermission>> {
        const response = await apiClient.post('/api/v1/permissions/menus', menuPermission);
        return response.data;
    },

    /**
     * 删除菜单权限配置（管理员功能）
     */
    async deleteMenuPermission(menuId: string): Promise<ApiResponse<string>> {
        const response = await apiClient.delete(`/api/v1/permissions/menus/${menuId}`);
        return response.data;
    },

    /**
     * 同步权限策略（管理员功能）
     */
    async syncPermissions(tenantId?: string): Promise<ApiResponse<{ message: string; synced_count: number }>> {
        const response = await apiClient.post('/api/v1/permissions/sync', {
            tenant_id: tenantId
        });
        return response.data;
    },

    /**
     * 清除租户权限缓存（管理员功能）
     */
    async clearPermissionCache(tenantId: string): Promise<ApiResponse<string>> {
        const response = await apiClient.delete(`/api/v1/permissions/cache/${tenantId}`);
        return response.data;
    },

    /**
     * 获取权限统计信息
     */
    async getPermissionStats(tenantId: string): Promise<ApiResponse<{
        total_policies: number;
        user_role_mappings: number;
        permission_policies: number;
        last_sync_at: string;
    }>> {
        const response = await apiClient.get(`/api/v1/permissions/stats/${tenantId}`);
        return response.data;
    },

    /**
     * 导出权限配置
     */
    async exportPermissionConfig(tenantId?: string): Promise<ApiResponse<{
        tenant_id?: string;
        menus: MenuPermission[];
        roles: UserRole[];
        policies: Array<{
            subject: string;
            domain: string;
            object: string;
            action: string;
            effect: string;
        }>;
        exported_at: string;
    }>> {
        const response = await apiClient.get('/api/v1/permissions/export', {
            params: tenantId ? {tenant_id: tenantId} : undefined
        });
        return response.data;
    },

    /**
     * 导入权限配置（管理员功能）
     */
    async importPermissionConfig(configData: any): Promise<ApiResponse<{
        imported_menus: number;
        imported_policies: number;
        message: string;
    }>> {
        const response = await apiClient.post('/api/v1/permissions/import', configData);
        return response.data;
    }
};