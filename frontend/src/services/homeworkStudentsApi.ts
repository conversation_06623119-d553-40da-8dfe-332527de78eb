import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse, PaginatedApiResponse } from '@/types';
import { BatchBindStudentsToHomeworkParams, BatchUnbindStudentsFromHomeworkParams, FindAllByHomeworkIdParams, HomeworkStudents, HomeworkStudentsWithStudentBaseInfo, PageStudentsByHomeworkIdParams } from '@/types/homeworkStudents';
import apiClient from './apiClient';
import {HomeworkClassSummary} from "@/types/teachingClasses.ts";

/**
 * 作者：张瀚
 * 说明：作业和学生关联的API
 */
export const homeworkStudentsApi = {
  /**
   * 作者：张瀚
   * 说明：批量绑定学生到作业中
   */
  batchBindStudentsToHomework: async (tenant_id: string, tenant_name: string, params: BatchBindStudentsToHomeworkParams): Promise<ApiResponse<HomeworkStudents[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/batchBindStudentsToHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：查询作业涉及的学生列表
   */
  findAllByHomeworkId: async (tenant_id: string, tenant_name: string, params: FindAllByHomeworkIdParams): Promise<ApiResponse<HomeworkStudentsWithStudentBaseInfo[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/findAllByHomeworkId`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：zyk
   * 说明：获取考试中的班级并给出学生数量
   */
  getHomeworkClassList: async (tenant_id: string, tenant_name: string, homework_id: string): Promise<ApiResponse<HomeworkClassSummary[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/${homework_id}/homework_classes`, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：批量解绑学生
   */
  batchUnbindStudentsFromHomework: async (tenant_id: string, tenant_name: string, params: BatchUnbindStudentsFromHomeworkParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/batchUnbindStudentsFromHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：朱若彪
   * 说明：分页查询作业绑定的学生列表
   */
  pageStudentsByHomeworkId: async (tenant_id: string, tenant_name: string, params: PageStudentsByHomeworkIdParams): Promise<PaginatedApiResponse<HomeworkStudentsWithStudentBaseInfo>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/pageStudentsByHomeworkId`, params, {
      headers: createApiHeaders(tenant_id)
    });
  }
};
