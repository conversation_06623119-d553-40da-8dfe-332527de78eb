import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import { BindPapersToHomeworkParams, HomeworkPapers } from '@/types/homeworkPapers';
import { Paper } from '@/types/papers';
import apiClient from './apiClient';

export const homeworkPapersApi = {

  /**
   * 作者：朱若彪
   * 说明：绑定作业和试卷的关联
   */
  bindPapersToHomework: async (tenant_id: string, schema_name: string, params: BindPapersToHomeworkParams): Promise<ApiResponse<HomeworkPapers[]>> => {
    return apiClient.post(`/api/v1/tenants/${schema_name}/homeworkPapers/bindPapersToHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：查询作业绑定的试卷列表
   */
  findPaperByHomeworkId: async (tenant_id: string, schema_name: string, homework_id: string): Promise<ApiResponse<Paper[]>> => {
    return apiClient.post(
      `/api/v1/tenants/${schema_name}/homeworkPapers/findPaperByHomeworkId`,
      { homework_id },
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },

};
