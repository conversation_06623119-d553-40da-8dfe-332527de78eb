import { createApiHeaders } from "@/lib/apiUtils";
import { ApiResponse, PaginatedApiResponse } from "@/types";
import {
  CreateHomeworkParams,
  Homework,
  HomeworkStatistics, HomeworkSummary,
  PageHomeworkParams,
  PageHomeworkResult,
  UpdateHomeworkParams,
} from "@/types/homework";
import apiClient from "./apiClient";
import { deleteHomework } from "./homeworkService";

/**
 * 作者：张瀚
 * 说明：作业API
 */
export const homeworkApi = {
  /**
   * 作者：张瀚
   * 说明：获取作业统计数据
   */
  getStatistics: async (
    tenant_id: string,
    tenant_name: string
  ): Promise<ApiResponse<HomeworkStatistics>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/getStatistics`,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：张瀚
   * 说明：创建作业
   */
  createHomework: async (
    tenant_id: string,
    tenant_name: string,
    params: CreateHomeworkParams
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/createHomework`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：张瀚
   * 说明：更新作业
   */
  updateHomework: async (
    tenant_id: string,
    tenant_name: string,
    params: UpdateHomeworkParams
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/updateHomework`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：朱若彪
   * 说明：分页查询作业
   */
  pageHomework: async (
    tenant_id: string,
    tenant_name: string,
    params: PageHomeworkParams
  ): Promise<PaginatedApiResponse<PageHomeworkResult>> => {
    return apiClient.post(
      `/api/v1/tenants/${tenant_name}/homework/pageHomework`,
      params,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  /**
   * 作者：朱若彪
   * 说明：根据id查询作业信息
   */
  getHomeworkById: async (
    tenant_id: string,
    tenant_name: string,
    id: string
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.get(
      `/api/v1/tenants/${tenant_name}/homework/getHomeworkById/${id}`,
      {
        headers: createApiHeaders(tenant_id),
      }
    );
  },
  getHomeworkSummary: async (
      tenant_id: string,
      tenant_name: string,
      id: string
  ): Promise<ApiResponse<HomeworkSummary>> => {
    return apiClient.post(
        `/api/v1/tenants/${tenant_name}/homework/summary/${id}`,
        {
          headers: createApiHeaders(tenant_id),
        }
    );
  },
  /**
    * 作者：朱若彪
    * 说明：删除作业
  */
  deleteHomework: async (
    tenant_id: string,
    tenant_name: string,
    id: string
  ): Promise<ApiResponse<Homework>> => {
    return apiClient.delete(`/api/v1/tenants/${tenant_name}/homework/delete/${id}`,
      {
        headers: createApiHeaders(tenant_id),
      });
  }
};
