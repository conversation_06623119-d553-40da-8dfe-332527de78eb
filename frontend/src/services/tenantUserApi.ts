import apiClient from './apiClient';
import { TenantUserAccess, CreateTenantUserAccessRequest, UpdateTenantUserAccessRequest } from '@/types/tenant.ts';
import {PaginatedApiResponse} from "@/types";

// 获取租户的用户访问列表
export const getTenantUsers = async (tenantId: string): Promise<PaginatedApiResponse<TenantUserAccess>> => {
  const response = await apiClient.get(`/api/v1/tenants/${tenantId}/users`);
  return response as PaginatedApiResponse<TenantUserAccess>;
};

// 添加用户到租户
export const addUserToTenant = async (
  tenantId: string, 
  request: CreateTenantUserAccessRequest
): Promise<TenantUserAccess> => {
  const response = await apiClient.post(`/api/v1/tenants/${tenantId}/users`, request);
  return response.data;
};

// 更新租户用户权限
export const updateTenantUserAccess = async (
  tenantId: string,
  userId: string,
  request: UpdateTenantUserAccessRequest
): Promise<TenantUserAccess> => {
  const response = await apiClient.put(`/api/v1/tenants/${tenantId}/users/${userId}`, request);
  return response.data;
};

// 从租户移除用户
export const removeUserFromTenant = async (tenantId: string, userId: string): Promise<void> => {
  await apiClient.delete(`/api/v1/tenants/${tenantId}/users/${userId}`);
};

// 批量添加用户到租户
export const batchAddUsersToTenant = async (
  tenantId: string,
  requests: CreateTenantUserAccessRequest[]
): Promise<TenantUserAccess[]> => {
  const response = await apiClient.post(`/api/v1/tenants/${tenantId}/users/batch`, { users: requests });
  return response.data;
};

// 获取租户用户统计信息
export const getTenantUserStats = async (tenantId: string) => {
  const response = await apiClient.get(`/api/v1/tenants/${tenantId}/users/stats`);
  return response.data;
};

// 搜索租户用户
export const searchTenantUsers = async (
  tenantId: string,
  searchTerm: string,
  accessType?: string
): Promise<TenantUserAccess[]> => {
  const params = new URLSearchParams();
  if (searchTerm) params.append('search', searchTerm);
  if (accessType) params.append('accessType', accessType);
  
  const response = await apiClient.get(`/api/v1/tenants/${tenantId}/users/search?${params.toString()}`);
  return response.data;
}; 