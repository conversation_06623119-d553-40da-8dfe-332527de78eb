/**
 * 教师管理 API 服务
 * 提供教师相关的 API 调用接口
 */

import { createApiHeaders } from '@/lib/apiUtils';
import { PaginatedApiResponse } from '@/types';
import {
  CreateTeacherParams,
  FindAllParams,
  PageAllTeacherParams,
  Teacher, 
  TeacherListVO,
  TeacherDetailVO,
  TeacherSearchParams,
  TeacherSummary,
} from '@/types/teacher';
import apiClient from './apiClient';

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  code: number;
  message?: string;
  data: T;
  meta?: any;
}

// Paginated response wrapper
export interface PaginatedResponse<T> {
  data: T[];
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

// 基础API路径
const Teacher_API = '/api/v1/tenants';


// Teacher API service
export const teachersApi = {
  /**
   * 获取教师简要信息列表（用于下拉选择）
   */
  async getTeacherSummaries(tenantName: string, isActive?: boolean): Promise<ApiResponse<TeacherSummary[]>> {
    return await apiClient.post(`${Teacher_API}/${tenantName}/teachers/Summaries`, isActive !== undefined ? { is_active: isActive } : {});
  },

  /**
   * 作者：张瀚
   * 说明：创建班级
   */
  findAll: async (tenantName: string, params: FindAllParams): Promise<ApiResponse<Teacher>> => {
    return apiClient.post(`${Teacher_API}/${tenantName}/teachers/findAll`, params);
  },
  /**
   * 作者：张瀚
   * 说明：分页查询所有学生
   */
  pageAllTeacher: async (tenant_id: string, schema_name: string, params: PageAllTeacherParams): Promise<PaginatedApiResponse<TeacherListVO>> => {
    return apiClient.post(`${Teacher_API}/${schema_name}/teachers/pageAllTeacher`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },

  /**
   * 作者：张瀚
   * 说明：新建老师
   */
  createTeacher: async (tenant_id: string, schema_name: string, params: CreateTeacherParams): Promise<ApiResponse<Teacher>> => {
    return apiClient.post(`${Teacher_API}/${schema_name}/teachers/createTeacher`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：新建老师
   */
  updateTeacher: async (tenant_id: string, schema_name: string, params: CreateTeacherParams): Promise<ApiResponse<Teacher>> => {
    return apiClient.post(`${Teacher_API}/${schema_name}/teachers/updateTeacher`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },


  // Get teacher detail with related information
  async getTeacherDetail(id: string): Promise<ApiResponse<TeacherDetailVO>> {
    const response = await apiClient.get<ApiResponse<TeacherDetailVO>>(`${Teacher_API}/${id}/detail`);
    return response.data;
  },

  // Delete a teacher
  async deleteTeacher(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<ApiResponse<void>>(`${Teacher_API}/${id}`);
    return response.data;
  },

  /**
   * 切换教师状态
   * @param tenantName 租户名称
   * @param id 教师ID
   * @param isActive 是否激活
   */
  async toggleTeacherStatus(tenantName: string, id: string, isActive: boolean): Promise<ApiResponse<Teacher>> {
    return apiClient.post(`${Teacher_API}/${tenantName}/teachers/${id}/toggle-status`, {
      is_active: isActive,
    });
  },

  // Get teachers count by employment status
  async getTeachersCountByStatus(): Promise<ApiResponse<Record<string, number>>> {
    const response = await apiClient.get<ApiResponse<Record<string, number>>>('${Teacher_API}/count/by-status');
    return response.data;
  },


  // Export teachers data
  async exportTeachers(params?: TeacherSearchParams): Promise<Blob> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 0) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const queryString = queryParams.toString();
    const url = queryString ? `${Teacher_API}/export?${queryString}` : '${Teacher_API}/export';
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    return response.data;
  },

  /**
   * 导入教师数据
   * @param tenantId 租户ID
   * @param schemaName 租户schema名称
   * @param file Excel文件
   */
  async importTeachers(tenantId: string, schemaName: string, file: File): Promise<ApiResponse<{
    success: number;
    failed: number;
    errors: Array<{
      row: number;
      error: string;
      data: any;
    }>;
  }>> {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post(`${Teacher_API}/${schemaName}/teachers/import`, formData, {
      headers: {
        ...createApiHeaders(tenantId),
        'Content-Type': 'multipart/form-data',
      },
    });
  },

};

// 导出默认教师API
export default teachersApi;

// 导出为 teacherApi 以保持一致性
export const teacherApi = teachersApi;