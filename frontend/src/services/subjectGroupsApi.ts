import { ApiResponse } from '@/types';
import { CreateSubjectGroupsParams, SubjectGroups, SubjectGroupsDetail, UpdateSubjectGroupsParams } from '@/types/subjectGroups';
import apiClient from './examApi';



// 基础API路径
const SUBJECTGRUOPS_API = '/api/v1/tenants';
/**
 * 作者：张瀚
 * 说明：学科组管理API
 */
export const SubjectGroupsApi = {
  /**
   * 作者：张瀚
   * 说明：创建学科组
   */
  createSubjectGroups: async (tenantName: string, params: CreateSubjectGroupsParams): Promise<ApiResponse<SubjectGroups>> => {
    console.log('createSubjectGroups', params)
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/createSubjectGroups`, params);
  },
  /**
   * 作者：张瀚
   * 说明：查询所有学科组信息
   */
  findAll: async (tenantName: string): Promise<ApiResponse<SubjectGroupsDetail[]>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/findAll`);
  },
  /**
   * 说明：删除学科组
   */
  deleteSubjectGroup: async (tenantName: string, id: string): Promise<ApiResponse<void>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/deleteSubjectGroup`, { id });
  },
  /**
   * 说明：更新学科组信息
   */
  updateSubjectGroups: async (tenantName: string, params: UpdateSubjectGroupsParams): Promise<ApiResponse<SubjectGroups>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/updateSubjectGroups`, params);
  },

  /**
   * 说明：添加学科组成员
   */
  addMember: async (tenantName: string, subjectGroupId: string, teacherId: string, role: string = 'member'): Promise<ApiResponse<any>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/${subjectGroupId}/members`, {
      teacher_id: teacherId,
      role: role
    });
  },

  /**
   * 说明：批量添加学科组成员
   */
  batchAddMembers: async (tenantName: string, subjectGroupId: string, members: Array<{teacher_id: string, role_code: string}>): Promise<ApiResponse<any>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/${subjectGroupId}/members/batch`, {
      members: members
    });
  },

  /**
   * 说明：获取学科组成员列表
   */
  getMembers: async (tenantName: string, subjectGroupId: string): Promise<ApiResponse<any[]>> => {
    return apiClient.get(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/${subjectGroupId}/members`);
  },

  /**
   * 说明：移除学科组成员
   */
  removeMember: async (tenantName: string, subjectGroupId: string, memberId: string): Promise<ApiResponse<any>> => {
    return apiClient.delete(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/${subjectGroupId}/members/${memberId}`);
  },

  /**
   * 说明：更新成员角色
   */
  updateMemberRole: async (tenantName: string, subjectGroupId: string, memberId: string, params: {role_code?: string, is_active?: boolean}): Promise<ApiResponse<any>> => {
    return apiClient.put(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/${subjectGroupId}/members/${memberId}`, params);
  },
};

