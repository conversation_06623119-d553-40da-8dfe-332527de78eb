import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse, PaginatedApiResponse } from '@/types';
import { Student } from '@/types/student';
import { CreateTeachingClassesParams, DeleteTeachingClassesParams, FindAllStudentInClassParams, MoveStudentToTeachingClassesParams, PageTeachingClassesParams, RemoveStudentFromTeachingClassesParams, TeachingClasses, TeachingClassesDetail, TeachingClassesStatistics, UpdateTeachingClassesParams } from '@/types/teachingClasses';
import apiClient from './examApi';
import { PageStudentInClassParams } from '@/types/administrativeClasses';

/**
 * 作者：张瀚
 * 说明：行政班级管理API
 */
export const TeachingClassesApi = {
  /**
   * 作者：张瀚
   * 说明：统计班级管理信息
   */
  getStatistics: async (tenant_id: string, tenant_name: string): Promise<ApiResponse<TeachingClassesStatistics>> => {
    return apiClient.get(`/api/v1/tenants/${tenant_name}/teachingClasses/getStatistics`, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：获取有权限的行政班列表
   */
  getUserClassList: async (tenant_id: string, tenant_name: string): Promise<ApiResponse<TeachingClassesDetail[]>> => {
    return apiClient.get(`/api/v1/tenants/${tenant_name}/teachingClasses/getUserClassList`, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：创建班级
   */
  createClasses: async (tenant_id: string, tenant_name: string, params: CreateTeachingClassesParams): Promise<ApiResponse<TeachingClasses>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/createClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：更新班级
   */
  updateClasses: async (tenant_id: string, tenant_name: string, params: UpdateTeachingClassesParams): Promise<ApiResponse<TeachingClasses>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/updateClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：删除班级
   */
  deleteClass: async (tenant_id: string, tenant_name: string, params: DeleteTeachingClassesParams): Promise<ApiResponse<TeachingClasses>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/deleteClass`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：查询班级内的学生
   */
  findAllStudentInClass: async (tenant_id: string, tenant_name: string, params: FindAllStudentInClassParams): Promise<ApiResponse<Student>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/findAllStudentInClass`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },

  /**
   * 作者：张瀚
   * 说明：移动学生到教学班内
   */
  moveStudentToTeachingClasses: async (tenant_id: string, tenant_name: string, params: MoveStudentToTeachingClassesParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/moveStudentToTeachingClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：从行政班内移除学生
   */
  removeStudentFromTeachingClasses: async (tenant_id: string, tenant_name: string, params: RemoveStudentFromTeachingClassesParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/removeStudentFromTeachingClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：朱若彪
   * 说明：分页查询教学班内的学生
   */
  pageStudentInClass: async (tenant_id: string, tenant_name: string, params: PageStudentInClassParams): Promise<PaginatedApiResponse<Student>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/pageStudentInClass`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：朱若彪
   * 说明：分页查询权限下的教学班列表
   */
  pageUserClassList: async (tenant_id: string, tenant_name: string, params: PageTeachingClassesParams): Promise<PaginatedApiResponse<TeachingClassesDetail>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/teachingClasses/pageUserClassList`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
};
