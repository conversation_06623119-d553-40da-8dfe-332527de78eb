import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import { Paper } from '@/types/papers';
import apiClient from './apiClient';
import { UpdatePaperContentParams } from './tenantPapersApi';
const API_BASE_URL = '/api/v1';

/**
 * 作者：张瀚
 * 说明：公共区域内paper表的api
 */
export const publicPaperApi = {
    /**
     * 作者：张瀚
     * 说明：更新试卷ID的同时把关联的数据一并更新
     */
    findByAnswerId: async (tenant_id: string, answer_card_id: string): Promise<ApiResponse<Paper>> => {
        return apiClient.post(`${API_BASE_URL}/tenants/public/papers/findByAnswerId`, { answer_card_id }, {
            headers: createApiHeaders(tenant_id)
        });
    },
    /**
     * 作者：张瀚
     * 说明：通过试卷ID更新试卷
     */
    updatePaperContent: async (tenant_id: string, params: UpdatePaperContentParams): Promise<ApiResponse<Paper>> => {
        return apiClient.post(`${API_BASE_URL}/tenants/public/papers/updatePaperContent`, params, {
            headers: createApiHeaders(tenant_id)
        });
    },
};
