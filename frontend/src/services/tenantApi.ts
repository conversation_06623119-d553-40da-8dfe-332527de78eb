import apiClient from './apiClient';
import {Tenant, TenantQueryParams, TenantStatsRes} from '@/types/tenant.ts';
import {ApiResponse, PaginatedApiResponse} from "@/types";

export const getTenants = async (params?: TenantQueryParams): Promise<PaginatedApiResponse<Tenant>> => {
  return apiClient.get('/api/v1/tenants', {params});
};

export const getTenantSummaries = async (): Promise<PaginatedApiResponse<Tenant>> => {
    return apiClient.get('/api/v1/tenants/summary');
};

export const getTenantStats = async (): Promise<ApiResponse<TenantStatsRes>> => {
  return apiClient.get('/api/v1/tenants/stats');
};

export const createTenant = async (tenantData: Omit<Tenant, 'id'>): Promise<Tenant> => {
  const response = await apiClient.post('/api/v1/tenants', tenantData);
  return response.data;
};

export const updateTenant = async (id: string, tenantData: Partial<Tenant>): Promise<Tenant> => {
  const response = await apiClient.put(`/api/v1/tenants/${id}`, tenantData);
  return response.data;
};

export const deleteTenant = async (id: string): Promise<void> => {
  await apiClient.delete(`/api/v1/tenants/${id}`);
};