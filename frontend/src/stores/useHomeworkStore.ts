import { create } from "zustand";
import { Homework } from "@/types/homework";
import { persist } from "zustand/middleware"; 

interface HomeworkState {
  homework: Homework;
  setHomework: (homework: Homework) => void,
  reset: () => void,
}

export const useHomeworkStore = create(
  persist<HomeworkState>( 
    (set) => ({
      homework: {} as Homework,
      setHomework: (homework) => set({ homework }),
      reset: () => set({ homework: {} as Homework }),
    }),
    {
      name: "homework-storage",
    }
  )
);