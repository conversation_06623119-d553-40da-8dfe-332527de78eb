import { TenantInfo, UserTenantInfo } from '@/types/identity';

/**
 * 租户缓存工具类
 * 提供租户信息的本地缓存管理功能
 */
export class TenantCache {
  private static readonly CACHE_KEY = 'tenant';

  /**
   * 获取缓存中的租户信息
   */
  static getCachedTenant(): TenantInfo | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) return null;
      return JSON.parse(cached);
    } catch (e) {
      console.warn('Failed to parse cached tenant:', e);
      return null;
    }
  }

  /**
   * 设置租户缓存
   */
  static setCachedTenant(tenant: TenantInfo | UserTenantInfo): void {
    try {
      const tenantToCache: TenantInfo = {
        tenant_id: tenant.tenant_id,
        tenant_name: tenant.tenant_name,
        schema_name: tenant.schema_name,
      };
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(tenantToCache));
    } catch (e) {
      console.warn('Failed to cache tenant:', e);
    }
  }

  /**
   * 清除租户缓存
   */
  static clearCachedTenant(): void {
    localStorage.removeItem(this.CACHE_KEY);
  }

  /**
   * 从租户列表中选择租户，优先使用缓存中的租户
   * @param tenants 可用的租户列表
   * @returns 选中的租户，如果没有可用租户则返回null
   */
  static selectTenantFromList(tenants: UserTenantInfo[]): UserTenantInfo | null {
    if (!tenants || tenants.length === 0) return null;

    // 优先使用缓存中的租户
    const cachedTenant = this.getCachedTenant();
    if (cachedTenant) {
      const matchingTenant = tenants.find(t => t.tenant_id === cachedTenant.tenant_id);
      if (matchingTenant) {
        console.log('🎯 使用缓存中的租户:', matchingTenant);
        return matchingTenant;
      }
    }

    // 如果缓存中没有有效的租户，则使用第一个租户
    console.log('🎯 使用第一个租户:', tenants[0]);
    return tenants[0];
  }
}
