import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Role, UserIdentity, AssignRoleRequest } from '@/types/role';
import { RoleAssignment, BatchRoleOperationRequest } from '@/types/user';
import { roleApi, userIdentityApi } from '@/services/roleApi';
import { useTenantContext } from './useTenantContext';

export const useRoleManagement = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getTenantId, hasTenantContext, canAssignRoles } = useTenantContext();

  // 获取角色列表
  const fetchRoles = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🔄 开始获取角色列表...');
      const response = await roleApi.getRoles({
        page: 1,
        page_size: 1000, // 获取所有角色
        is_active: true,
      });

      console.log('📦 API响应:', response);

      // 处理不同的响应格式
      let rolesData: Role[] = [];
      if (response) {
        // 根据实际的API响应结构处理数据
        if (Array.isArray(response.data)) {
          // 如果 response.data 直接是数组
          rolesData = response.data as Role[];
        } else if (response.data && typeof response.data === 'object') {
          // 如果 response.data 是对象，可能包含 items 或其他字段
          const responseData = response.data as any;
          if (responseData.items && Array.isArray(responseData.items)) {
            rolesData = responseData.items as Role[];
          } else if (Array.isArray(responseData)) {
            rolesData = responseData as Role[];
          }
        } else if (Array.isArray(response)) {
          // 如果整个 response 就是数组
          rolesData = response as Role[];
        }
      }
      console.log('✅ 解析后的角色数据:', rolesData);
      setRoles(rolesData);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || '获取角色列表失败';
      setError(errorMessage);
      console.error('获取角色列表失败:', err);
      setError(null); // 清除错误状态
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取用户身份列表
  const fetchUserIdentities = useCallback(async (userId: string): Promise<UserIdentity[]> => {
    try {
      const response = await userIdentityApi.getUserIdentities(userId);
      // 后端返回的是 UserIdentitiesListResponse 结构，需要访问 identities 字段
      return response.data?.identities || [];
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || '获取用户身份失败';
      console.error('获取用户身份失败:', err);
      toast.error(errorMessage);
      return [];
    }
  }, []);

  // 为用户分配角色
  const assignRolesToUser = useCallback(async (
    userId: string,
    assignments: RoleAssignment[]
  ): Promise<boolean> => {
    const tenantId = getTenantId();

    if (!tenantId) {
      toast.error('无法获取租户信息，请重新登录');
      return false;
    }

    if (!canAssignRoles) {
      toast.error('您没有权限分配角色');
      return false;
    }

    try {
      // 按目标类型和目标ID分组，支持多角色分配
      const groupedAssignments = assignments.reduce((groups, assignment) => {
        const key = `${assignment.target_type || 'school'}_${assignment.target_id || 'null'}_${assignment.subject || 'null'}`;
        if (!groups[key]) {
          groups[key] = {
            target_type: assignment.target_type || 'school',
            target_id: assignment.target_id,
            subject: assignment.subject,
            role_ids: []
          };
        }
        groups[key].role_ids.push(assignment.role_id);
        return groups;
      }, {} as Record<string, {
        target_type: string;
        target_id?: string;
        subject?: string;
        role_ids: string[];
      }>);

      // 为每个分组创建多角色分配请求
      for (const group of Object.values(groupedAssignments)) {
        await userIdentityApi.assignMultipleRoles({
          user_id: userId,
          role_ids: group.role_ids,
          target_type: group.target_type,
          target_id: group.target_id,
          subject: group.subject,
        });
      }

      toast.success(`成功为用户分配 ${assignments.length} 个角色`);
      return true;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || '角色分配失败';
      toast.error(errorMessage);
      return false;
    }
  }, [getTenantId, canAssignRoles]);

  // 移除用户角色
  const removeUserRole = useCallback(async (identityId: string): Promise<boolean> => {
    try {
      await userIdentityApi.removeUserRole(identityId);
      toast.success('角色移除成功');
      return true;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || '角色移除失败';
      toast.error(errorMessage);
      return false;
    }
  }, []);

  // 批量角色操作
  const batchRoleOperation = useCallback(async (
    operation: BatchRoleOperationRequest
  ): Promise<boolean> => {
    try {
      // 这里需要根据实际API实现批量操作
      // 目前先用循环实现
      const { user_ids, operation: op, role_assignments } = operation;
      
      let successCount = 0;
      for (const userId of user_ids) {
        try {
          if (op === 'assign') {
            await assignRolesToUser(userId, role_assignments);
            successCount++;
          } else if (op === 'remove') {
            // 移除操作需要先获取用户身份，然后移除
            const identities = await fetchUserIdentities(userId);
            for (const assignment of role_assignments) {
              // 查找包含指定角色的身份
              const identity = identities.find(id =>
                id.roles.some(role => role.id === assignment.role_id)
              );
              if (identity) {
                await removeUserRole(identity.id);
              }
            }
            successCount++;
          }
        } catch (err) {
          console.error(`用户 ${userId} 操作失败:`, err);
        }
      }

      if (successCount === user_ids.length) {
        toast.success(`批量操作成功，共处理 ${successCount} 个用户`);
      } else {
        toast.warning(`批量操作部分成功，成功处理 ${successCount}/${user_ids.length} 个用户`);
      }

      return successCount > 0;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || '批量操作失败';
      toast.error(errorMessage);
      return false;
    }
  }, [assignRolesToUser, removeUserRole, fetchUserIdentities]);

  // 获取角色信息
  const getRoleInfo = useCallback((roleId: string): Role | undefined => {
    return roles.find(role => role.id === roleId);
  }, [roles]);

  // 按分类获取角色
  const getRolesByCategory = useCallback(() => {
    return roles.reduce((acc, role) => {
      if (!acc[role.category]) {
        acc[role.category] = [];
      }
      acc[role.category].push(role);
      return acc;
    }, {} as Record<string, Role[]>);
  }, [roles]);

  // 获取活跃角色
  const getActiveRoles = useCallback(() => {
    return roles.filter(role => role.is_active);
  }, [roles]);

  // 搜索角色
  const searchRoles = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) return roles;
    
    const term = searchTerm.toLowerCase();
    return roles.filter(role => 
      role.name.toLowerCase().includes(term) ||
      role.code.toLowerCase().includes(term) ||
      role.description?.toLowerCase().includes(term)
    );
  }, [roles]);

  // 验证角色分配
  const validateRoleAssignments = useCallback((assignments: RoleAssignment[]): string[] => {
    const errors: string[] = [];
    
    // 检查重复角色
    const roleIds = assignments.map(a => a.role_id);
    const duplicates = roleIds.filter((id, index) => roleIds.indexOf(id) !== index);
    if (duplicates.length > 0) {
      errors.push('存在重复的角色分配');
    }

    // 检查角色是否存在且活跃
    for (const assignment of assignments) {
      const role = getRoleInfo(assignment.role_id);
      if (!role) {
        errors.push(`角色 ${assignment.role_id} 不存在`);
      } else if (!role.is_active) {
        errors.push(`角色 ${role.name} 已被禁用`);
      }
    }

    return errors;
  }, [getRoleInfo]);

  // 初始化时获取角色列表
  useEffect(() => {
    fetchRoles().then();
  }, [fetchRoles]);

  return {
    // 状态
    roles,
    loading,
    error,

    // 方法
    fetchRoles,
    fetchUserIdentities,
    assignRolesToUser,
    removeUserRole,
    batchRoleOperation,
    getRoleInfo,
    getRolesByCategory,
    getActiveRoles,
    searchRoles,
    validateRoleAssignments,
  };
};
