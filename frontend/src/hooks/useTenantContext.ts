import { useAuth } from '@/contexts/AuthContext';
import { useMemo } from 'react';

/**
 * 租户上下文 Hook
 * 提供当前租户信息和相关操作
 */
export const useTenantContext = () => {
  const { tenant } = useAuth();

  // 当前租户信息
  const currentTenant = useMemo(() => {
    if (!tenant) return null;

    return {
      tenantId: tenant.tenant_id,
      tenantName: tenant.tenant_name,
      identityType: 'admin',
      displayName: tenant.tenant_name, // 使用 name 作为显示名称
    };
  }, [tenant]);

  // 检查是否有租户上下文
  const hasTenantContext = useMemo(() => {
    return !!currentTenant?.tenantId;
  }, [currentTenant]);

  // 获取租户ID
  const getTenantId = () => {
    return currentTenant?.tenantId || null;
  };

  // 获取租户名称
  const getTenantName = () => {
    return currentTenant?.tenantName || null;
  };

  // 检查当前用户是否为租户管理员
  const isTenantAdmin = useMemo(() => {
    return currentTenant?.identityType === 'admin';
  }, [currentTenant]);

  // 检查当前用户是否为校长（基于 access_type，这里暂时用 admin 代替）
  const isPrincipal = useMemo(() => {
    // 注意：UserTenantInfo 的 access_type 中没有 'principal' 类型
    // 如果需要区分校长，可能需要从其他地方获取更详细的身份信息
    return currentTenant?.identityType === 'admin';
  }, [currentTenant]);

  // 检查当前用户是否有角色分配权限
  const canAssignRoles = useMemo(() => {
    console.log('currentTenant?.identityType:'+currentTenant?.identityType);
    // 基于当前的 access_type 类型，只有 admin 有权限分配角色
    return currentTenant?.identityType === 'admin';
  }, [currentTenant]);

  return {
    currentTenant,
    hasTenantContext,
    getTenantId,
    getTenantName,
    isTenantAdmin,
    isPrincipal,
    canAssignRoles,
  };
};

export default useTenantContext;
