import React, { createContext, useContext, useState, ReactNode } from 'react';

type LayoutWidth = 'default' | 'full';

interface LayoutWidthContextType {
  width: LayoutWidth;
  setWidth: (width: LayoutWidth) => void;
  resetWidth: () => void;
}

const LayoutWidthContext = createContext<LayoutWidthContextType | undefined>(undefined);

interface LayoutWidthProviderProps {
  children: ReactNode;
  defaultWidth?: LayoutWidth;
}

export function LayoutWidthProvider({ 
  children, 
  defaultWidth = 'default' 
}: LayoutWidthProviderProps) {
  const [width, setWidth] = useState<LayoutWidth>(defaultWidth);

  const resetWidth = () => setWidth(defaultWidth);

  return (
    <LayoutWidthContext.Provider value={{ width, setWidth, resetWidth }}>
      {children}
    </LayoutWidthContext.Provider>
  );
}

export function useLayoutWidth() {
  const context = useContext(LayoutWidthContext);
  if (context === undefined) {
    throw new Error('useLayoutWidth must be used within a LayoutWidthProvider');
  }
  return context;
}

// Hook for pages to set their preferred width
export function usePageWidth(pageWidth: LayoutWidth) {
  const { setWidth, resetWidth } = useLayoutWidth();
  
  // Set width when component mounts
  React.useEffect(() => {
    setWidth(pageWidth);
    
    // Reset to default when component unmounts
    return () => resetWidth();
  }, [pageWidth, setWidth, resetWidth]);
}

// Convenient hook for pages that need full width
export function usePageFullWidth() {
  usePageWidth('full');
}
