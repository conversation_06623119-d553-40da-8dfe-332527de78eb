import { TenantInfo } from "@/types/identity"

/**
 * 通用的创建api请求中的headers的方法
 */
export function createApiHeaders(tenant_id: string) {
  return {
    "X-Tenant-ID": tenant_id
  }
}

/**
 * 从本地缓存中获取当前身份信息
 */
export function getTenantInfoFromLocalStorage(): TenantInfo | undefined {
  const identity = localStorage.getItem('tenant')
  if (!identity || identity.trim().length === 0) {
    return undefined
  }
  return JSON.parse(identity)
}