import React, { useState, useEffect } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface PaginationProps {
  total: number;
  current: number;
  pageSize: number;
  onChange: (current: number, pageSize: number) => void;
  showSizeChanger?: boolean;
  showTotal?: boolean;
  pageSizeOptions?: number[];
  className?: string;
}

const CustomPagination: React.FC<PaginationProps> = ({
  total,
  current = 1,
  pageSize = 10,
  onChange,
  showSizeChanger = true,
  showTotal = true,
  pageSizeOptions = [5, 10, 20, 50, 100, 500],
  className = "",
}) => {
  const [jumpPage, setJumpPage] = useState<string>("");


  // 计算总页数
  const totalPages = Math.ceil(total / pageSize);

  // 处理页码跳转
  const handleJumpToPage = (value: string) => {
    const targetPage = parseInt(value);
    if (isNaN(targetPage)) {
      return;
    }

    let validPage = targetPage;
    if (targetPage < 1) {
      validPage = 1;
    } else if (targetPage > totalPages) {
      validPage = totalPages;
    }

    onChange(validPage, pageSize);
  };

  // 当页码变化时，清空跳转输入框
  useEffect(() => {
    setJumpPage("");
  }, [current]);
  
  if (total === 0) {
    return null;
  }

  // 生成页码数组，智能显示页码
  const generatePageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 7;

    if (totalPages <= maxVisiblePages) {
      // 如果总页数小于等于最大显示页数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 智能显示页码
      if (current <= 4) {
        // 当前页在前面
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (current >= totalPages - 3) {
        // 当前页在后面
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push(1);
        pages.push('...');
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  return (
    <div className={`flex items-center justify-between gap-4 ${className}`}>
      <div className="flex flex-wrap items-center gap-4">
        {/* 总数显示 */}
        {showTotal && (
          <div className="text-sm text-gray-500 whitespace-nowrap">
            共 {total} 条
          </div>
        )}

        {/* 每页条数选择器 */}
        {showSizeChanger && (
          <div className="flex items-center gap-2">
            <select
              className="border rounded px-1 py-1 text-sm min-w-[60px]"
              value={pageSize}
              onChange={e => onChange(current, Number(e.target.value))}
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>{size}条/页</option>
              ))}
            </select>
          </div>
        )}
      </div>

      <div className="flex items-center gap-4">
        {/* 跳转到指定页 */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500 flex-shrink-0">跳至</span>
          <input
            type="number"
            min={1}
            max={totalPages}
            value={jumpPage}
            onChange={(e) => setJumpPage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleJumpToPage(jumpPage);
                setJumpPage("");
              }
            }}
            onBlur={() => {
              if (jumpPage) {
                handleJumpToPage(jumpPage);
                setJumpPage("");
              }
            }}
            className="border rounded px-2 py-1 w-16 text-sm text-center"
            placeholder={current.toString()}
          />
          <span className="text-sm text-gray-500">页</span>
        </div>

        {/* 分页按钮 */}
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => {
                  if (current > 1) {
                    onChange(current - 1, pageSize);
                  }
                }}
                className={current === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {generatePageNumbers().map((page, index) => (
              <PaginationItem key={index}>
                {page === '...' ? (
                  <span className="px-3 py-2 text-sm text-gray-500">...</span>
                ) : (
                  <PaginationLink
                    isActive={page === current}
                    onClick={() => onChange(page as number, pageSize)}
                    className="cursor-pointer"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                onClick={() => {
                  if (current < totalPages) {
                    onChange(current + 1, pageSize);
                  }
                }}
                className={current === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default CustomPagination;

