'use client';

import { createComponentDataListStore } from '@/components/question-card/store/componentDataListStore';
import { createToolbarDataStore } from '@/components/question-card/store/toolbarDataStore';
import { useEffect, useMemo } from 'react';
import QuestionCard from '.';
import { createDataCallbackStore } from './store/dataCallbackStore';
import { AdmissionTicketNumberInfoQuestionItemConfig, AdmissionTicketQRCodeMsgBean, ChoiceQuestionInfoQuestionItemConfig, createPaperDataStore, FillInBlankQuestionItemConfig, PaperData, QuestionItemTypeEnum, ReadAndScanQuestionItemConfig, ScoringCriteriaModeEnum, ScoringCriteriaTypeEnum, StemQuestionItemConfig, TextareaQuestionItemConfig, TextQuestionItemConfig, TrueOrFalseQuestionItemConfig } from './store/paperDataStore';
import { QuestionCardUI } from './ui/question-card-components/global';
//--------------------------TODO 假数据，以后换成接口获取
let paperData: PaperData = {
  id: "00000000-0000-0000-0000-000000000000",
  paper_name: "测试试卷",
  paper_content: {
    question_data_list: [{
      id: "00000000-0000-0000-0001-000000000000",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000000",
        question_id: "00000000-0000-0000-0001-000000000000",
        item_type: QuestionItemTypeEnum.Text,
        config: new TextQuestionItemConfig('一、选择题')
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000001",
      original_question_id: "00000000-0000-0000-0002-000000000001",
      question_type: "选择题",
      question_content: [{
        id: "00000000-0000-0000-0003-000000000001",
        question_id: "00000000-0000-0000-0001-000000000001",
        item_type: QuestionItemTypeEnum.Stem,
        config: new StemQuestionItemConfig('在复平面内,复数 \\( \\frac{1}{2-\\mathrm{i}} \\) 对应的点在:', "1.")
      }, {
        id: "00000000-0000-0000-0003-000000000002",
        question_id: "00000000-0000-0000-0001-000000000001",
        item_type: QuestionItemTypeEnum.Choice,
        config: new ChoiceQuestionInfoQuestionItemConfig(['第一象限<br/>哈哈哈', '第二象限\\$10', '第三象限', '第四象限'], true),
        scoring_criteria: {
          id: '222',
          scoring_type: ScoringCriteriaTypeEnum.Match,
          mode: ScoringCriteriaModeEnum.Exact,
          answer: 'B',
          score: 4
        }
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000002",
      original_question_id: "00000000-0000-0000-0002-000000000001",
      question_type: "选择题",
      question_content: [{
        id: "00000000-0000-0000-0003-000000000003",
        question_id: "00000000-0000-0000-0001-000000000002",
        item_type: QuestionItemTypeEnum.Stem,
        config: new StemQuestionItemConfig('已知 \\( f(x)=(2-x)^{\\beta}=a_{0}+ \\) \\( a_{1} x+a_{2} x^{2}+\\cdots+a_{8} x^{8} \\), 则下列描述正确的是:', "2.")
      }, {
        id: "00000000-0000-0000-0003-000000000004",
        question_id: "00000000-0000-0000-0001-000000000002",
        item_type: QuestionItemTypeEnum.Choice,
        config: new ChoiceQuestionInfoQuestionItemConfig([
          ' \\( a_{1}+a_{2}+\\cdots+a_{8}\u003d1 \\)',
          ' \\( f(-1) \\) 除以 5 所得的余数是 1',
          ' \\( \\left|a_{1}\\right|+\\left|a_{2}\\right|+\\left|a_{3}\\right|+\\cdots+\\left|a_{8}\\right|\u003d3^{8}-2^{8} \\)',
          ' \\( 2 a_{2}+3 a_{3}+\\cdots+8 a_{8}\u003d-8 \\)',
        ], false)
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000003",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000005",
        question_id: "00000000-0000-0000-0001-000000000003",
        item_type: QuestionItemTypeEnum.Text,
        config: new TextQuestionItemConfig('二、判断题')
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000004",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000006",
        question_id: "00000000-0000-0000-0001-000000000004",
        item_type: QuestionItemTypeEnum.TrueOrFalse,
        config: new TrueOrFalseQuestionItemConfig('这个一个判断题，内容是阿斯顿发送到发送到发送到发送到发到发送到发送到发斯到发送到发送到发斯到发送到发送到发斯送到发送到发斯蒂芬哈哈', "1、")
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000006",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000009",
        question_id: "00000000-0000-0000-0001-000000000006",
        item_type: QuestionItemTypeEnum.Text,
        config: new TextQuestionItemConfig('三、填空题')
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000005",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000007",
        question_id: "00000000-0000-0000-0001-000000000005",
        item_type: QuestionItemTypeEnum.Stem,
        config: new StemQuestionItemConfig('    今年3月初，广东省农科院作物所玉米研究室种业专家联合海南海垦科学院技术员，在试验基地开展鲜食玉米组合筛选工作，与普通玉米相比，鲜食玉米具有甜、糯、嫩、香等特点。请结合图分析并回答问题。\n    <img src="http://192.168.4.9:900/question-acquisition-ees/book/thirdimport/202505301427143851487/images/2539936231576678409/image7.jpg" style="" />\n', "1、")
      }, {
        id: "00000000-0000-0000-0003-000000000008",
        question_id: "00000000-0000-0000-0001-000000000005",
        item_type: QuestionItemTypeEnum.FillInBlank,
        config: new FillInBlankQuestionItemConfig('（1）科研人员筛选籽粒饱满的鲜食玉米种子（如图甲）来播种，是因为种子的[⑥] \\(\\underline{\\quad\\quad\\quad\\quad}\\)内含有丰富的营养物质，能够保证种子萌发的需要。<br/>    （2）鲜食玉米“甜、糯”的口感与玉米植株通过\\(\\underline{\\quad\\quad\\quad\\quad}\\) 作用制造的有机物有关，该生理活动主要在图乙中[b] \\(\\underline{\\quad\\quad\\quad\\quad}\\) 细胞中进行。\n    （3）鲜食玉米果穗偶有缺粒现象，主要是由于\\(\\underline{\\quad\\quad\\quad\\quad}\\)  引起的，为了减少缺粒现象，科研人员用纸袋套住玉米雄蕊，收集图丙中B的\\(\\underline{\\quad\\quad\\quad\\quad}\\)  进行人工辅助授粉。\n    （4）合理密植有助于提高鲜食玉米产量，原因是 \\(\\underline{\\quad\\quad\\quad\\quad}\\) 。')
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000010",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000019",
        question_id: "00000000-0000-0000-0001-000000000010",
        item_type: QuestionItemTypeEnum.Text,
        config: new TextQuestionItemConfig('四、问答题')
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000011",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000020",
        question_id: "00000000-0000-0000-0001-000000000011",
        item_type: QuestionItemTypeEnum.Stem,
        config: new StemQuestionItemConfig('这是一个问答题，或者也叫简答题，你来回答一下：先有鸡还是先有蛋?', "1、")
      }, {
        id: "00000000-0000-0000-0003-000000000021",
        question_id: "00000000-0000-0000-0001-000000000011",
        item_type: QuestionItemTypeEnum.Textarea,
        config: new TextareaQuestionItemConfig('none', 7)
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000013",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000030",
        question_id: "00000000-0000-0000-0001-000000000013",
        item_type: QuestionItemTypeEnum.Stem,
        config: new StemQuestionItemConfig('这是一个问答题，或者也叫简答题，你来回答一下：先有鸡还是先有蛋?', "2、")
      }, {
        id: "00000000-0000-0000-0003-000000000031",
        question_id: "00000000-0000-0000-0001-000000000013",
        item_type: QuestionItemTypeEnum.Textarea,
        config: new TextareaQuestionItemConfig('underLine', 10)
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000012",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000080",
        question_id: "00000000-0000-0000-0001-000000000010",
        item_type: QuestionItemTypeEnum.Text,
        config: new TextQuestionItemConfig('五、作文题')
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000016",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000040",
        question_id: "00000000-0000-0000-0001-000000000016",
        item_type: QuestionItemTypeEnum.Stem,
        config: new StemQuestionItemConfig('这个一个命题作文', "1、")
      }, {
        id: "00000000-0000-0000-0003-000000000041",
        question_id: "00000000-0000-0000-0001-000000000016",
        item_type: QuestionItemTypeEnum.Textarea,
        config: new TextareaQuestionItemConfig('block', 50)
      }],
    }, {
      id: "00000000-0000-0000-0001-000000000017",
      original_question_id: undefined,
      question_type: undefined,
      question_content: [{
        id: "00000000-0000-0000-0003-000000000049",
        question_id: "00000000-0000-0000-0001-000000000016",
        item_type: QuestionItemTypeEnum.ReadAndScan,
        config: new ReadAndScanQuestionItemConfig('    今年3月初，广东省农科院作物所玉米研究室种业专家联合海南海垦科学院技术员，在试验基地开展鲜食玉米组合筛选工作，与普通玉米相比，鲜食玉米具有甜、糯、嫩、香等特点。请结合图分析并回答问题。\n    <img src="http://192.168.4.9:900/question-acquisition-ees/book/thirdimport/202505301427143851487/images/2539936231576678409/image7.jpg" style="" />\n', "1.")
      }],
    }],
    answer_card: {
      id: "00000000-0000-0001-0000-000000000005",
      paper_id: "00000000-0000-0000-0000-000000000000",
      dpi: 96,
      width: 794,
      height: 1123,
      x: 20,
      y: 40,
      block_group_list: [],
      question_item_block_group_list: [],
      answer_block_group_scoring_criteria_list: [],
      scoring_criteria_list: [],
      page_orientation_is_vertical: true,
      bucket_size: 1,
      show_page_index: true,
      show_pos_point: true,
      page_total: 0,
      pos_point_width: 20,
      pos_point_height: 10,
      admissionTicketNumberInfoQuestionItemConfig: new AdmissionTicketNumberInfoQuestionItemConfig(8, "这是题卡名字哈哈哈", JSON.stringify({
        paperId: '00000000-0000-0000-0002-000000000262',
      } satisfies AdmissionTicketQRCodeMsgBean)),
      right: 30,
      bottom: 10
    }
  }
}

export default function Home() {
  const componentDataListStore = createComponentDataListStore();
  const paperDataStore = createPaperDataStore();
  const dataCallbackStore = useMemo(() => createDataCallbackStore(), [paperData]);
  const setPaperData = paperDataStore(state => state.setPaperData)

  //更新数据
  setPaperData(paperData)
  //构建工具栏数据
  const toolbarDataStore = createToolbarDataStore();
  return (
    <QuestionCard
      componentDataListStore={componentDataListStore}
      toolbarDataStore={toolbarDataStore}
      paperDataStore={paperDataStore}
      dataCallbackStore={dataCallbackStore}
      style={{ width: '1600px', height: '740px', margin: 'auto', marginTop: '20px' }}
    />
  );

  // const [open, setOpen] = useState(false)
  // return <div style={{ width: '1500px', height: '740px', margin: 'auto', marginTop: '20px' }}>
  //   <QuestionCardUI.Button onClick={() => { setOpen(true) }}>显示弹窗</QuestionCardUI.Button>
  //   <QuestionCardDialog open={open} paperData={paperData} okCallback={(data) => {
  //     setOpen(false)
  //     console.log("保存！", data);
  //   }} onCancelCallback={() => { setOpen(false) }} />
  // </div>
}

export interface QuestionCardDialogProps {
  open: boolean,
  paperData: PaperData,
  okCallback: (paperData: PaperData) => void
  onCancelCallback: () => void
}
export function QuestionCardDialog({ open, paperData, okCallback, onCancelCallback }: QuestionCardDialogProps) {
  const componentDataListStore = useMemo(() => createComponentDataListStore(), [paperData]);
  const paperDataStore = useMemo(() => createPaperDataStore(), [paperData]);
  const toolbarDataStore = useMemo(() => createToolbarDataStore(), [paperData]);
  const dataCallbackStore = useMemo(() => createDataCallbackStore(), [paperData]);
  const setPaperData = paperDataStore(state => state.setPaperData)
  const getPaperData = paperDataStore(state => state.getPaperData)
  const isReadyToPrint = paperDataStore(state => state.isReadyToPrint)
  useEffect(() => {
    //更新数据
    setPaperData(paperData)
  }, [paperData])

  return <QuestionCardUI.Dialog
    title="题卡编辑"
    open={open}
    onClose={onCancelCallback}
    footer={[
      <QuestionCardUI.Button key="back" onClick={onCancelCallback}>
        关 闭
      </QuestionCardUI.Button>,
      <QuestionCardUI.Button key="submit" style={{ marginLeft: '10px' }} buttonType="primary" onClick={() => { okCallback(getPaperData()) }} disabled={!isReadyToPrint}>
        保 存
      </QuestionCardUI.Button>
    ]}
  >
    <QuestionCard
      componentDataListStore={componentDataListStore}
      toolbarDataStore={toolbarDataStore}
      paperDataStore={paperDataStore}
      dataCallbackStore={dataCallbackStore}
      style={{ width: '1800px', height: '700px', }}
    />
  </QuestionCardUI.Dialog>
}