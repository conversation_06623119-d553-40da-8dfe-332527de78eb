import { createContext, CSSProperties } from "react"
import { StoreApi } from "zustand"
import { CanvasVisibleLayer } from "./compontents/canvas.visible-layer"
import { ToolBar } from "./compontents/toolbar/toolbar"
import "./index.css"
import { ComponentDataListStoreProps, createComponentDataListStore } from "./store/componentDataListStore"
import { createDataCallbackStore, DataCallbackStoreProps } from "./store/dataCallbackStore"
import { PaperDataStoreContext, PaperDataStoreProps } from "./store/paperDataStore"
import { createToolbarDataStore, ToolbarDataStoreProps } from "./store/toolbarDataStore"
import "./ui/global.scss"
import { QuestionCardUI } from "./ui/question-card-components/global"

/**
 * 作者：张瀚
 * 说明：题卡组件主入口
 * 1、接收一个试卷数据（包含题目和试卷的）
 * 2、转成组件列表（也可以是直接传入的）
 * 3、渲染组件（只读），可以调整类型和位置（暂时不做新增删除等）
 * 
 * 需要的依赖项：
 * npm install qrcode.react
 * npm install radix-ui
 * npm install zustand
 * npm install uuid
 * npm install react-to-print
 */
export interface Props {
  /**
   * 组件数据
   */
  componentDataListStore: StoreApi<ComponentDataListStoreProps>
  /**
   * 工具栏数据
   */
  toolbarDataStore: StoreApi<ToolbarDataStoreProps>
  /**
   * 新数据
   */
  paperDataStore: StoreApi<PaperDataStoreProps>
  /**
   * 回调
   */
  dataCallbackStore: StoreApi<DataCallbackStoreProps>
  /**
   * 样式
   */
  style?: CSSProperties,

  className?: string
}

/**
 * 作者：张瀚
 * 说明：注入的组件数据对象
 */
export const ComponentDataListStoreContext = createContext<StoreApi<ComponentDataListStoreProps>>(createComponentDataListStore())
/**
 * 作者：张瀚
 * 说明：注入的工具栏数据
*/
export const ToolbarDataStoreContext = createContext<StoreApi<ToolbarDataStoreProps>>(createToolbarDataStore())
/**
 * 注入和回调
 */
export const DataCallbackStoreContext = createContext<StoreApi<DataCallbackStoreProps>>(createDataCallbackStore())

export default function QuestionCard({ componentDataListStore, toolbarDataStore, paperDataStore, dataCallbackStore, style, className }: Props) {
  return <>
    <ComponentDataListStoreContext.Provider value={componentDataListStore}>
      <ToolbarDataStoreContext.Provider value={toolbarDataStore}>
        <PaperDataStoreContext.Provider value={paperDataStore}>
          <DataCallbackStoreContext.Provider value={dataCallbackStore}>
            <QuestionCardUI.Flex style={style} justify="space-between" className={`question-card-main ${className ?? ''}`}>
              <ToolBar />
              <CanvasVisibleLayer />
            </QuestionCardUI.Flex>
          </DataCallbackStoreContext.Provider>
        </PaperDataStoreContext.Provider>
      </ToolbarDataStoreContext.Provider>
    </ComponentDataListStoreContext.Provider>
  </>
}