/**
 * 作者：张瀚
 * 标准：
 * 1、行高统一为32px（2倍16px行高），如果有border（1px）则改为30px以确保每一行的组件和文字等高，非文字类组件也可以通过padding一类控制到32px
 */
$backgroup-color: #fafafa;
$border-color: #d9d9d9;
$border-radius: 6px;
$border-radius-large: 8px;
$font-size: 14px;

.unset {
    padding: 0;
    margin: 0;
    outline: none;
    border: none;
    outline: none;
}

.line-default {
    font-size: $font-size;
    line-height: 26px;
    padding: 2px 8px;
}

.border-default {
    @extend .disabled;
    border: 1px solid $border-color;
    border-radius: $border-radius;
    line-height: 30px;

    &:focus-visible {
        outline: none;
    }
}

.hover-default {
    background-color: $backgroup-color;

    &:hover {
        border-color: #171717;
        color: #171717;
        background-color: #EBEEF5;
    }

    &:active {
        border-color: #000000;
        color: #000000;
        background-color: #E4E7ED;
    }

}

.disabled {

    &:disabled {

        &:hover,
        & {
            background-color: #E4E7ED !important;
            color: #171717 !important;
            border: 1px solid $border-color !important;
            cursor: not-allowed !important;
        }
    }
}

.flex-center {
    display: flex;
    align-content: center;
    align-items: center;
}

.flex-center-gap10 {
    @extend .flex-center;
    gap: 10px;
}

.flex-center-gap10-column {
    @extend .flex-center-gap10;
    flex-direction: column;
}

.flex-grow {
    flex-grow: 1;
}

//左滑进出动画
.translate-x-show {
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
    /* 初始状态 */
    transform: translateX(-100vw);
    opacity: 0;

    &.showing {
        transform: translateX(0);
        /* 向左滑出视窗 */
        opacity: 1;
    }
}

//渐隐消失动画
.opacity-show {
    transition: opacity 0.5s ease-in-out;
    /* 初始状态 */
    opacity: 0;

    &.showing {
        opacity: 1;
    }
}

//高亮色边框
.high-light-border {
    border: 1px solid rgb(57, 255, 20);
    box-shadow: 0 0 8px rgba(57, 255, 20, 0.7);
}