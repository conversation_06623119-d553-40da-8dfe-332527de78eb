import { Checkbox } from 'radix-ui';
import '../global.scss';
import './QuestionCardCheckbox.scss';
import { QuestionCardUI } from './global';
export interface Props extends Checkbox.CheckboxProps {}
export function QuestionCardCheckbox({ className, ...props }: Props) {
  return (
    <Checkbox.Root className={`${className ?? ''} checkbox-root`} {...props}>
      <Checkbox.Indicator className="checkbox-indicator">
        <QuestionCardUI.Icon icon="Check" className="checkbox-icon"></QuestionCardUI.Icon>
      </Checkbox.Indicator>
    </Checkbox.Root>
  );
}
