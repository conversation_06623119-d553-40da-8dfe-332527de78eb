import { Select } from 'radix-ui'
import '../global.scss'
import './QuestionCardSelect.scss'

export interface Props extends Select.SelectProps {
  placeholder?: string
  options?: {
    value: string
    label: React.JSX.Element
  }[]
  className?: string
}

export function QuestionCardSelect({ placeholder, options, className, ...props }: Props) {
  return (
    <Select.Root {...props}>
      <Select.Trigger className={`${className ?? ''} flex-grow select-trigger`}>
        <Select.Value placeholder={placeholder} />
        <Select.Icon />
      </Select.Trigger>

      <Select.Portal>
        <Select.Content className='select-content' position='popper'>
          <Select.ScrollUpButton />
          <Select.Viewport>
            {options?.map((item) => (
              <Select.Item key={item.value} value={item.value} className='select-item'>
                <Select.ItemText>{item.label}</Select.ItemText>
                <Select.ItemIndicator />
              </Select.Item>
            ))}
            <Select.Separator />
          </Select.Viewport>
          <Select.ScrollDownButton />
          <Select.Arrow />
        </Select.Content>
      </Select.Portal>
    </Select.Root>
  )
}
