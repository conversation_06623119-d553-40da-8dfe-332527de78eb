import { Toast } from 'radix-ui'
import { forwardRef, ReactNode, useImperativeHandle, useState } from 'react'
import './toast.component.scss'

export interface ToastMethod {
    openToast: (title: string | ReactNode | null, desc: string | ReactNode | null) => void
}

const QuestionCardToast = forwardRef<ToastMethod>((_, ref) => {
    const [open, setOpen] = useState(false)
    const [title, setTitle] = useState<string | ReactNode | null>()
    const [desc, setDesc] = useState<string | ReactNode | null>()

    const openToast = (title: string | ReactNode | null, desc: string | ReactNode | null) => {
        setTitle(title)
        setDesc(desc)
        setOpen(true)
    }
    useImperativeHandle(ref, () => ({
        openToast
    }))
    return <Toast.Provider swipeDirection="up">
        <Toast.Root className="toast-root" open={open} onOpenChange={setOpen}>
            <Toast.Title className="toast-title">{title}</Toast.Title>
            <Toast.Description asChild>
                {desc}
            </Toast.Description>
        </Toast.Root>
    </Toast.Provider>
})
QuestionCardToast.displayName = 'QuestionCardToast'
export default QuestionCardToast