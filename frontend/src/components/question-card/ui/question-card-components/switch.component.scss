@use '../global.scss' as base;

.switch-root {
    @extend .unset;
    @extend .border-default;

    cursor: pointer;
    width: 36px;
    height: 20px;
    border-radius: 99999px;
    position: relative;
    box-shadow: 0 2px 10px var(--black-a7);
    background-color: base.$backgroup-color;
    transition: all 200ms;
    margin: 5px 0;

    &[data-state="checked"] {
        background-color: #171717;
    }

    .switch-thumb {
        display: block;
        width: 14px;
        height: 14px;
        background-color: #E4E7ED;
        border-radius: 9999px;
        box-shadow: 0 2px 2px var(--black-a7);
        transition: transform 200ms;
        transform: translateX(2px);

        &[data-state="checked"] {
            transform: translateX(19px);
        }
    }
}