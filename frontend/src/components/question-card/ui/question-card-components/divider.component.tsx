import React from 'react'
import '../global.scss'
import './divider.component.scss'
export interface Props {
    className?: string
    title?: string,
    style?: React.CSSProperties
}
export default function QuestionCardDivider({ className, title, style }: Props) {
    return <div className={`${className ?? ''} divider-root`} style={style}>
        <span className='divider-title'>{title}</span>
    </div>
}