import React, { PropsWithChildren, useEffect, useState } from 'react'
import '../global.scss'
import './drawer.component.scss'
export interface Props extends PropsWithChildren {
    className?: string,
    title?: string
    open: boolean
    width: number
    onClose?: () => void,
    bodyStyle: React.CSSProperties
}
export default function QuestionCardDrawer({ className, title, open, width, onClose, bodyStyle = {}, children }: Props) {
    const [isClosed, setIsClosed] = useState(!open)
    const [isShowing, setIsShowing] = useState(open)
    useEffect(() => {
        if (!open) {
            setIsShowing(open)
            setTimeout(() => {
                setIsClosed(!open)
            }, 300);
        } else {
            setIsClosed(false)

        }
    }, [open])
    useEffect(() => {
        setTimeout(() => {
            setIsShowing(!isClosed)
        }, 10);
    }, [isClosed])

    return <div className={`${className ?? ''} drawer-root`} style={{ display: isClosed ? 'none' : 'block' }} >
        <div className={`drawer-mask ${isShowing ? 'showing' : ''}`} onClick={onClose} />
        <div className={`drawer-title ${isShowing ? 'showing' : ''}`} style={{ width }}>
            <div className='title'>{title}</div>
            <div className='drawer-close' onClick={onClose}>✖</div>
        </div>
        <div className={`drawer-body ${isShowing ? 'showing' : ''}`} style={{ ...bodyStyle, width }} >
            {children}
        </div>
    </div>
}