@use '../global.scss' as base;

.radio-group-root {
  .radio-group-item {
    @extend .unset;
    @extend .border-default;
    @extend .hover-default;
    background: base.$background-color;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    //中间选中的点
    .radio-group-indicator {
      @extend .border-default;
      background: base.$background-color;
      border-radius: 50%;
      display: inline-block;
    }

    .radio-group-indicator::after {
      content: '';
      display: block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: black;
    }

  }
}