@use '../global.scss' as base;

.toast-root {
    background-color: white;
    border-radius: 6px;
    box-shadow:
        hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
        hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
    padding: 15px;
    display: grid;
    grid-template-areas: "title action" "description action";
    grid-template-columns: auto max-content;
    column-gap: 15px;
    align-items: center;

    &[data-state="open"] {
        animation: slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
    }

    &[data-state="closed"] {
        animation: hide 100ms ease-in;
    }

    &[data-swipe="move"] {
        transform: translateX(var(--radix-toast-swipe-move-x));
    }

    &[data-swipe="cancel"] {
        transform: translateX(0);
        transition: transform 200ms ease-out;
    }

    &[data-swipe="end"] {
        animation: swipeOut 100ms ease-out;
    }

    .toast-title {
        grid-area: title;
        margin-bottom: 5px;
        font-weight: 500;
        color: var(--slate-12);
        font-size: 15px;
    }

}


@keyframes hide {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes slideIn {
    from {
        transform: translateX(calc(100% + var(--viewport-padding)));
    }

    to {
        transform: translateX(0);
    }
}

@keyframes swipeOut {
    from {
        transform: translateX(var(--radix-toast-swipe-end-x));
    }

    to {
        transform: translateX(calc(100% + var(--viewport-padding)));
    }
}