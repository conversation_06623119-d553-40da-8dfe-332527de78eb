@use '../global.scss' as base;

h2 {
    margin: 0;
    padding: 0;
}

.dialog-overlay {
    background-color: rgba(0, 0, 0, 0.3);
    position: fixed;
    inset: 0;
    animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.dialog-content {
    background-color: base.$backgroup-color;
    border-radius: 6px;
    position: fixed;
    top: 20px;
    left: 20px;
    width: calc(100% - 40px);
    height: fit-content;
    padding: 10px;
    animation: contentShow 300ms cubic-bezier(0.16, 1, 0.3, 1);
    z-index: 100;
    overflow: auto;

    .dialog-title {
        margin-bottom: 10px;
    }

    .dialog-description {
        display: flex;
        flex-direction: column;
        align-items: center;

        .dialog-footer {
            padding: 10px 20px 0 0;
            background-color: base.$backgroup-color;
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
    }

    &:focus {
        outline: none;
    }

    .dialog-close {
        @extend .hover-default;
        position: absolute;
        top: 10px;
        right: 10px;
        width: 32px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        cursor: pointer;
    }
}