import Question<PERSON>ardButton from "./button.component"
import Question<PERSON><PERSON><PERSON><PERSON>ckbox from "./checkbox.component"
import Question<PERSON>ardDialog from "./dialog.component"
import QuestionCardDivider from "./divider.component"
import QuestionCardDrawer from "./drawer.component"
import QuestionCardFlex from "./flex.component"
import QuestionCardInputNumber from "./input-number.component"
import QuestionCardInput from "./input.component"
import QuestionCardRadioGroup from "./radio-group.component"
import QuestionCardSelect from "./select.component"
import QuestionCardSwitch from "./switch.component"
import QuestionCardTextarea from "./textarea.component"
export const QuestionCardUI = {
    Input: QuestionCardInput,
    RadioGroup: QuestionCardRadioGroup,
    Select: QuestionCardSelect,
    Switch: QuestionCardSwitch,
    Textarea: QuestionCardTextarea,
    Checkbox: QuestionCardCheckbox,
    InputNumber: QuestionCardInputNumber,
    Divider: Question<PERSON>ardDiv<PERSON>,
    Drawer: Question<PERSON><PERSON><PERSON><PERSON><PERSON>,
    Flex: Question<PERSON><PERSON>Flex,
    Button: Question<PERSON>ard<PERSON>utt<PERSON>,
    Dialog: QuestionCardDialog,
}