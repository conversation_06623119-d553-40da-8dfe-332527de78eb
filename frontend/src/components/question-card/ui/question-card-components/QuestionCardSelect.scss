@use '../global.scss' as base;

.select-trigger {
  button {
    all: unset;
  }

  @extend .unset;
  @extend .hover-default;
  @extend .border-default;
  @extend .disabled;
  line-height: 30px;
  padding: 0 8px;
  display: flex;
  justify-content: space-between;
  background-color: base.$background-color;
  cursor: pointer;
}

.select-content {
  button {
    all: unset;
  }

  @extend .border-default;
  overflow: hidden;
  background-color: base.$background-color;
  box-shadow: 0px 10px 38px -10px rgba(22, 23, 24, 0.35),
  0px 10px 20px -15px rgba(22, 23, 24, 0.2);
  padding: 5px;
  z-index: 1000;
  min-width: 200px;

  .select-item {
    @extend .hover-default;
    cursor: pointer;

    &+.select-item {
      border-top: 1px dashed base.$border-color;
    }
  }
}