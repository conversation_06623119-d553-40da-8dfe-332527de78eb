import { Collapsible } from "radix-ui"
import React, { CSSProperties, PropsWithChildren, ReactNode, useState } from "react"

import './collapsible.component.scss'
import { QuestionCardUI } from "./global"
export interface Props extends PropsWithChildren {
    label?: ReactNode
    defaultOpen?: boolean,
    style?: CSSProperties
    onOpenChange?: (open: boolean) => void
}
const QuestionCardCollapsible: React.FC<Props> = ({ label, children, defaultOpen = false, style, onOpenChange }) => {
    const [open, setOpen] = useState(defaultOpen);
    return <Collapsible.Root className="collapsible-root"
        style={style}
        open={open}
        onOpenChange={(v) => {
            setOpen(v)
            onOpenChange && onOpenChange(v)
        }}>
        <Collapsible.Trigger className="collapsible-trigger">
            {label}
            <QuestionCardUI.Icon icon={open ? 'ArrowDownSvg' : 'ArrowUpSvg'} />
        </Collapsible.Trigger>
        <Collapsible.Content className="collapsible-content">
            {children}
        </Collapsible.Content>
    </Collapsible.Root>
}

export default QuestionCardCollapsible