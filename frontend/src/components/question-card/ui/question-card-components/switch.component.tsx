import { Switch } from "radix-ui";
import "../global.scss";
import './switch.component.scss';
export interface Props extends Switch.SwitchProps {
    checkedChildren?: React.ReactNode
    unCheckedChildren?: React.ReactNode

}
export default function QuestionCardSwitch({ checkedChildren, unCheckedChildren, ...props }: Props) {
    return <div className="flex-center-gap10">
        {unCheckedChildren}
        <Switch.Root {...props} className="switch-root">
            <Switch.Thumb className="switch-thumb" />
        </Switch.Root>
        {checkedChildren}
    </div>
}