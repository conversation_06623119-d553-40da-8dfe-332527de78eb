import React, { forwardRef, PropsWithChildren } from "react";
import "../global.scss";
import "./flex.component.scss";

export interface Props extends PropsWithChildren, React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
    className?: string
    vertical?: boolean;
    wrap?: boolean | React.CSSProperties['flexWrap'];
    justify?: React.CSSProperties['justifyContent'];
    align?: React.CSSProperties['alignItems'];
    gap?: React.CSSProperties['gap'];
}

const QuestionCardFlex = forwardRef<HTMLDivElement, Props>(({ className, children, justify = 'normal', vertical = false, wrap, gap, style, align, ...props }: Props, ref) => {
    let flexWrap: React.CSSProperties['flexWrap'] = 'nowrap'
    if (wrap === true) {
        flexWrap = 'wrap'
    } else if (wrap) {
        flexWrap = wrap
    }
    return <div ref={ref} className={`${className ?? ''} flex-root`} {...props} style={{
        ...style,
        justifyContent: justify,
        flexDirection: `${vertical ? 'column' : 'row'}`,
        flexWrap,
        gap,
        alignContent: align,
        alignItems: align
    }}>
        {children}
    </div>
})
export default QuestionCardFlex