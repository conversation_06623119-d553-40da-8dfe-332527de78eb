import React, { forwardRef, PropsWithChildren } from "react"
import './button.component.scss'
export interface Props extends Props<PERSON><PERSON><PERSON>hildren, React.DetailedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
    buttonType?: 'primary'
}

const QuestionCardButton = forwardRef<HTMLButtonElement, Props>(({ className, buttonType, children, ...props }: Props, ref) => {
    const buttonClassName = (() => {
        switch (buttonType) {
            case 'primary':
                return 'button-primary'
        }
        return 'button-default'
    })()
    return <button ref={ref} className={`${className ?? ''} ${buttonClassName}`} {...props}>{children}</button>
})
export default QuestionCardButton