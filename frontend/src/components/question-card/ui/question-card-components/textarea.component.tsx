import React, { useEffect, useRef } from 'react';
import '../global.scss';
import './textarea.component.scss';
export interface Props extends React.DetailedHTMLProps<React.TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement> {
    autoSize?: { minRows: number; maxRows: number; }
}
export default function QuestionCardTextarea({ autoSize, className, onChange, ...props }: Props) {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const [value, setValue] = React.useState('');
    const adjustTimerRef = useRef<NodeJS.Timeout | undefined>()
    // 高度调整函数
    const adjustHeight = () => {
        const textarea = textareaRef.current;
        if (!textarea) {
            return
        }
        //重置行高获取高度
        textarea.style.height = 'auto';
        const { scrollHeight } = textarea
        if (scrollHeight === 0) {
            clearTimeout(adjustTimerRef.current)
            adjustTimerRef.current = setTimeout(() => {
                adjustHeight()
            }, 100);
            return
        }
        const minHeight = Math.max(autoSize?.minRows ?? 1, 1) * 26 + 6
        const maxHeight = autoSize?.maxRows ? autoSize.maxRows * 26 + 6 : scrollHeight
        if (scrollHeight >= maxHeight) {
            textarea.style.height = `${maxHeight}px`;
        } else {
            textarea.style.height = `${Math.max(minHeight, scrollHeight)}px`;
        }
    };

    // 内容变化时调整高度
    useEffect(adjustHeight, [value]);

    return <textarea ref={textareaRef} rows={1} onChange={v => {
        setValue(v.target.value)
        onChange && onChange(v)
    }} {...props} className={`${className ?? ''} textarea-root`}></textarea>
}