@use '../global.scss' as base;

.drawer-root {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    user-select: none;

    .drawer-mask {
        @extend .opacity-show;
        width: 100%;
        height: 100%;
        z-index: -1;
        position: absolute;
        background: rgba($color: #000000, $alpha: 0.1);
    }

    .drawer-title {
        @extend .line-default;
        @extend .flex-center;
        @extend .translate-x-show;
        justify-content: space-between;
        background: base.$backgroup-color;
        border-bottom: 1px solid base.$border-color;
        padding: 10px 30px;

        .title {
            font-size: 20px;
            font-weight: bold;
        }
    }

    .drawer-close {
        @extend .hover-default;
        width: 26px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        cursor: pointer;
    }

    .drawer-body {
        @extend .translate-x-show;
        background: base.$backgroup-color;
        height: calc(100% - 47px);
        overflow-y: auto;
    }
}