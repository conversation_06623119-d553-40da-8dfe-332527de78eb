import { Dialog } from "radix-ui";
import React, { PropsWithChildren, ReactNode } from "react";
import './dialog.component.scss';

export interface Props extends PropsWithChildren {
    triggerNode?: ReactNode,
    title?: string | ReactNode
    open?: boolean
    footer?: ReactNode
    style?: React.CSSProperties
    onClose?: () => void
}

const QuestionCardDialog = ({ title, children, triggerNode, open, footer, style, onClose, ...props }: Props) => {
    return <Dialog.Root  {...props} open={open} >
        <Dialog.Trigger>
            {triggerNode}
        </Dialog.Trigger>
        <Dialog.Portal >
            <Dialog.Overlay className="dialog-overlay" />
            <Dialog.Content className="dialog-content" style={{ ...style }}>
                <Dialog.Title className="dialog-title">
                    {title}
                </Dialog.Title>
                <Dialog.Description className="dialog-description">
                    {children}
                    {footer && <div className="dialog-footer">
                        {footer}
                    </div>}
                </Dialog.Description>
                <Dialog.Close>
                    <div className='dialog-close' onClick={onClose}>✖</div>
                </Dialog.Close>
            </Dialog.Content>
        </Dialog.Portal>
    </Dialog.Root>
}
export default QuestionCardDialog