import '../global.scss'
import './input.component.scss'
export interface Props extends React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
    suffix?: string
}
export default function QuestionCardInput({ suffix, className, ...props }: Props) {
    return <div className={`${className ?? ''} flex-center input-main`}>
        <input className='input flex-grow' {...props} />
        {
            suffix && <div className='suffix'>{suffix}</div>
        }
    </div>
}