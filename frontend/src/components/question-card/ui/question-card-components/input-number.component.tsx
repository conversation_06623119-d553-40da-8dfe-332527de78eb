import '../global.scss';
import './input-number.component.scss';
export interface Props extends React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
    suffix?: string
    onValueChange?: (v: number | undefined) => void
}
export default function QuestionCardInputNumber({ suffix, className, type, min, max, onValueChange, onChange, ...props }: Props) {
    return <div className={`${className ?? ''} flex-center input-number-root`}>
        <input className='input flex-grow' type='number' {...props} min={min} max={max} onChange={v => {
            onChange && onChange(v)
            const value = v.target.value
            if (value === undefined) {
                onValueChange && onValueChange(undefined)
            } else {
                let num = parseFloat(value)
                if (isNaN(num)) {
                    onValueChange && onValueChange(undefined)
                } else {
                    if (min !== undefined) {
                        num = Math.max(parseFloat(String(min)), num)
                    }
                    if (max !== undefined) {
                        num = Math.min(parseFloat(String(max)), num)
                    }
                    onValueChange && onValueChange(num)
                }
            }
        }} />
        {
            suffix && <div className='suffix'>{suffix}</div>
        }
    </div>
}