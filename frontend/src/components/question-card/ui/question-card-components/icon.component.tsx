import React from "react";
import ArrowDownSvg from '../radix-icons/arrow-down.svg';
import ArrowUpSvg from '../radix-icons/arrow-up.svg';
import './icon.component.scss';

/**
 * 作者：张瀚
 * 说明：支持的svg图标
 */
const iconMap = {
    ArrowUpSvg,
    ArrowDownSvg
} as const;

export interface Props {
    icon: keyof typeof iconMap
    style?: React.CSSProperties
    className?: string
}

const QuestionCardIcon: React.FC<Props> = ({ icon, style, className }) => {
    return <img src={iconMap[icon]} alt={iconMap[icon]} style={style} className={`${className ?? ''} icon-root`} />
}
export default QuestionCardIcon