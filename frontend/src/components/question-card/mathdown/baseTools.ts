/**
 * 通用的元素判空方法
 * @param value 
 * @returns 
 */
export function isEmpty(value: any) {
  const type = typeof value;
  switch (type) {
    case 'string':
      return value.length === 0;
    case 'number':
      return value === null || value === undefined || value === 0;
    case 'object':
      if (value === null || value === undefined) return true;
      if (Array.isArray(value)) return value.length === 0;
      for (let key in value) {
        if (Object.hasOwnProperty.call(value, key)) return false;
      }
      return true;
    default:
      return value === null || value === undefined;
  }
}

/**
 * 格式化日期
 * @param {*} date
 * @param {*} format 日期格式
 */
export function formatDate(date: Date, format: String) {
  // 判断格式
  let i = 0
  let result = ''
  while (i < format.length) {
    switch (format[i]) {
      case 'y':
        if (format.substring(i, i + 4) === 'yyyy') {
          result += date.getFullYear()
          i += 4
          continue
        }
        break
      case 'M':
        if (format.substring(i, i + 2) === 'MM') {
          result += (date.getMonth() + 1).toString().padStart(2, "0")
          i += 2
          continue
        }
        break
      case 'd':
        if (format.substring(i, i + 2) === 'dd') {
          result += date.getDate().toString().padStart(2, "0")
          i += 2
          continue
        }
        break
      case 'H':
        if (format.substring(i, i + 2) === 'HH') {
          result += date.getHours().toString().padStart(2, "0")
          i += 2
          continue
        }
        break
      case 'm':
        if (format.substring(i, i + 2) === 'mm') {
          result += date.getMinutes().toString().padStart(2, "0")
          i += 2
          continue
        }
        break
      case 's':
        if (format.substring(i, i + 3) === 'sss') {
          result += date.getMilliseconds().toString().padStart(3, "0")
          i += 3
          continue
        }
        if (format.substring(i, i + 2) === 'ss') {
          result += date.getSeconds().toString().padStart(2, "0")
          i += 2
          continue
        }
        break
    }
    // 默认就是直接加进来
    result += format[i++]
  }
  return result
}
// 正则分割字符串
export function splitString(str: string, regex: RegExp): string[] {
  const result = []
  let lastIndex = 0
  let match
  const reg = new RegExp(`(${regex.source})`, regex.flags)
  while ((match = reg.exec(str)) !== null) {
    if (match.index != lastIndex) {
      result.push(str.substring(lastIndex, match.index))
    }
    result.push(match[0])
    lastIndex = match.index + match[0].length
  }
  if (lastIndex < str.length) {
    result.push(str.substring(lastIndex));
  }
  return result.filter(s => s !== '')
}
// 最小化公式
export function minimizeLatex(latex: string): string {
  const latexSymbol = new Set(['\\', '_', '^', '{', '}'])
  let minimized = latex.replace(/^\$+/, '\\(').replace(/\$+$/, '\\)');
  if (minimized.startsWith('\\(')) {
    const total = minimized.length - 2
    for (let i = 2; i < total; i++) {
      if (latexSymbol.has(minimized[i])) {
        minimized = minimized.substring(2, i) + '\\(' + minimized.substring(i);
        break
      }
    }
  }
  if (minimized.endsWith('\\)')) {
    const total = minimized.length - 2
    for (let i = total - 1; i > 2; i--) {
      if (latexSymbol.has(minimized[i])) {
        if (minimized[i] !== '}') {
          break
        }
        minimized = minimized.substring(0, i + 1) + '\\)' + minimized.substring(i + 1, total);
        break
      }
    }
  }
  return minimized
}

// 文本处理 将单一段落内的内容处理成细节点
export function splitText(text: string): string[] {
  const newlinePlaceholder = '__NEWLINE__';
  const sanitizedText = text.replaceAll(/\n/g, newlinePlaceholder);

  // 修正后的正则表达式，正确转义LaTeX公式
  const regex = /(.*?)(<img[^>]+?(?:\/img)?>|\$\$.*?\$\$|\$.*?\$|\\\(.*?\\\)|<table[^>]*>[\s\S]*?<\/table>)/s;
  const parts: string[] = [];
  let remainingText = sanitizedText;

  while (true) {
    const match = remainingText.match(regex);
    if (!match) break;

    const [full, before, element] = match;
    if (before.trim()) parts.push(before.trim().replaceAll(newlinePlaceholder, '\n'));
    if (element.trim()) parts.push(element.trim().replaceAll(newlinePlaceholder, '\n'));

    remainingText = remainingText.slice(full.length);
  }

  if (remainingText.trim()) {
    parts.push(remainingText.trim().replaceAll(newlinePlaceholder, '\n'));
  }

  return parts.length ? parts : [''];
}

// 文本处理 尝试修复latex中多个Dollar符号变为单个
interface SplitResult extends Array<[string, boolean]> {}

export function splitDollarLatex(text: string): SplitResult {
  text = text.replace(/^[$]+/, '$').replace(/[$]+$/, '$');
  const result: SplitResult = [];
  let inLatex = false;
  let buffer = '';
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    if (char === '$') {
      if (inLatex) {
        // 公式结束
        result.push([buffer + '\\)', true]);
        buffer = '';
      } else {
        // 公式开始
        if (buffer) {
          result.push([buffer, false]); // 存入普通文本
        }
        buffer = '\\('; // 公式前缀
      }
      inLatex = !inLatex;
    } else {
      buffer += char;
    }
  }

  // 处理剩余文本
  if (buffer) {
    result.push([buffer, false]); // 普通文本
  }

  return result;
}
export function latexDollarFix(text: string): string {
  return splitDollarLatex(text).map(([ct, isL]) => (isL ? `${minimizeLatex(ct)}` : ct)).join("")
}
export function fixTdLatex(text: string): string {
  let ct = text.replace("\\\\", "<br>")
  return `${minimizeLatex(ct)}`
}
export function latexTdFix(text: string): string {
  return splitDollarLatex(text).map(([ct, isL]) => (isL ? fixTdLatex(ct) : ct)).join("")
}
export function latexTableFix(text: string): string {
  const tdReg = /<td[^>]*?>([\s\S]*?)<\/td>/g;
  let tds = splitString(text, tdReg)
  for (let i = 0; i < tds.length; i++) {
    if (tds[i].startsWith('<td')) {
      tdReg.lastIndex = 0
      let m = tdReg.exec(tds[i])
      if (m !== null) {
        let tdContent = m[1]
        if (tdContent.replace("\\\$", "").includes("$")) {
          tds[i] = tds[i].replace(tdContent, latexTdFix(tdContent))
        }
      }
    }
  }
  return tds.join("");
}
