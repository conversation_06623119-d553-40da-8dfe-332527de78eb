import { MathJax } from "better-react-mathjax";
import { useEffect, useState } from "react";
import styles from './Mathdown.module.css';
import { latexDollarFix, latexTableFix, splitText } from "./baseTools";

const Mathdown = ({ content = '' }) => {
    const [mathContent, setMathContent] = useState('')
    useEffect(() => {
        const totalTextList = splitText(content)
        // const imgRegex = /<img[^>]*?src=["']([^"']+)["'][^>]*?>/g;
        const tableRegex = /<table[^>]*?>[\s\S]*?<\/table>/;
        let newContent = ""
        for (const paragraphText of totalTextList) {
            let content = "" + paragraphText
            //图片路径还是要拼接好了再传进来
            // while ((m = imgRegex.exec(paragraphText)) !== null) {
            //     content = content.replace(m[0], m[0].replace(m[1], '/static/' + m[1]))
            // }
            const tableMatch = content.match(tableRegex);
            if (tableMatch) {
                newContent += latexTableFix(content)
            } else {
                newContent += latexDollarFix(content)
            }
        }
        setMathContent(newContent)
    }, [content])

    return (
        <MathJax className={styles.mathdown}
            dynamic={true}
            dangerouslySetInnerHTML={{ __html: mathContent }}>
        </MathJax>
    );
};

export default Mathdown;
