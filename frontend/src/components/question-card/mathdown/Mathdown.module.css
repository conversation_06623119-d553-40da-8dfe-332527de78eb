.mathdown{
    white-space: 'pre-wrap';
    word-wrap: 'break-word';
}
.mathdown img {
    max-width: 100%;
    object-fit: contain; /* 或者使用 cover 根据需求 */
}

.mathdown table {
    border-collapse: collapse;
    border-spacing: 0;
    max-width: 100%;
    overflow-y: scroll;
    table-layout: fixed;
    width: 100%;
    position: relative;
    z-index: 1;
}

.mathdown th, .mathdown td {
    border: 1px solid black; /* 添加边框 */
    padding: 8px; /* 添加内边距 */
    text-align: left; /* 左对齐 */
}