import { ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useReactToPrint } from 'react-to-print'
import { v4 } from 'uuid'
import { useStore } from 'zustand'
import { ComponentDataListStoreContext, ToolbarDataStoreContext } from '..'
import { admissionTicketNumberInfoIdEx, pageIndexItemIdEx } from '../store/componentDataListStore'
import { AdmissionTicketNumberInfoQuestionItemConfig, AnswerBlockData, AnswerBlockGroupData, AnswerBlockGroupModeEnum, AnswerBlockGroupTypeEnum, getBucketNodeId, PaperDataStoreContext, QuestionItemData, QuestionItemTypeEnum, TextareaQuestionItemConfig } from '../store/paperDataStore'
import { BlockEditDrawer } from './base-component/block-edit-drawer/block-edit-drawer'
import { QuestionItemEditDrawer } from './base-component/question-item-edit-drawer/question-item-edit-drawer'
import { CanvasPageLayer } from './canvas.page-layer'
import './canvas.visible-layer.scss'
import { AdmissionTicketNumberComponent, ChoiceQuestionItemComponent, FillInBlankQuestionItemComponent, ReadAndScanQuestionItemComponent, StemQuestionItemComponent, TextareaQuestionItemComponent, TextQuestionItemComponent, TrueOrFalseQuestionItemComponent } from './question-item-component/question-item.component'

/**
 * 作者：张瀚
 * 说明：绘制题卡的画布区域，从上往下分多层
 * 1、可见层，控制可见区域并且提供滚动条
 * 2、预渲染层，按顺序从上往下渲染所有内容，用于提供尺寸信息等，尺寸会和桶一致
 * 3、页面层，根据预渲染层的结果，分析每个页面应该包含的内容而分页渲染出来的内容
 */
export function CanvasVisibleLayer() {
  //取出数据
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const bucketNodeIdToDomHeightMap = useStore(useComponentDataListStoreContext, state => state.bucketNodeIdToDomHeightMap)
  const bucketNodeIdToRealDomMap = useStore(useComponentDataListStoreContext, state => state.bucketNodeIdToRealDomMap)
  const refreshAllBucketNodeDomHeight = useStore(useComponentDataListStoreContext, state => state.refreshAllBucketNodeDomHeight)
  const bucketHeight = useStore(useComponentDataListStoreContext, state => state.bucketHeight)
  const setBucketHeight = useStore(useComponentDataListStoreContext, state => state.setBucketHeight)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  const setNowEditingNode = useStore(useComponentDataListStoreContext, state => state.setNowEditingNode)
  const [lastEditingNode, setLastEditingNode] = useState<BucketNode>()
  //工具栏数据
  const useToolbarDataStoreContext = useContext(ToolbarDataStoreContext)
  const setPrintFunc = useStore(useToolbarDataStoreContext, state => state.setPrintFunc)
  const showAnswerBox = useStore(useToolbarDataStoreContext, state => state.showAnswerBox)
  const setAnswerBoxVersion = useStore(useToolbarDataStoreContext, state => state.setAnswerBoxVersion)
  //配置信息
  const usePaperDataStoreContext = useContext(PaperDataStoreContext)
  const pageDataList = useStore(usePaperDataStoreContext, state => state.pageDataList)
  const setPageDataList = useStore(usePaperDataStoreContext, state => state.setPageDataList)
  const paperData = useStore(usePaperDataStoreContext, state => state.paperData)
  const [lastPaperId, setLastPaperId] = useState(paperData.id)
  const answerCard = useStore(usePaperDataStoreContext, state => state.answerCard)
  const { bucket_size } = answerCard
  const questionItemIdList = useStore(usePaperDataStoreContext, state => state.questionItemIdList)
  const questionItemIdToQuestionItemMap = useStore(usePaperDataStoreContext, state => state.questionItemIdToQuestionItemMap)
  const bucketNodeIdToBlockIdListMap = useStore(usePaperDataStoreContext, state => state.bucketNodeIdToBlockIdListMap)
  const blockIdToBlockMap = useStore(usePaperDataStoreContext, state => state.blockIdToBlockMap)
  const bucketNodeIdToBucketNodeMap = useStore(usePaperDataStoreContext, state => state.bucketNodeIdToBucketNodeMap)
  const questionItemIdToBucketNodeIdListMap = useStore(usePaperDataStoreContext, state => state.questionItemIdToBucketNodeIdListMap)
  const questionItemIdToBlockGroupIdListMap = useStore(usePaperDataStoreContext, state => state.questionItemIdToBlockGroupIdListMap)
  const blockGroupIdToBlockGroupMap = useStore(usePaperDataStoreContext, state => state.blockGroupIdToBlockGroupMap)
  const addAdmissionNumberBlockGroup = useStore(usePaperDataStoreContext, state => state.addAdmissionNumberBlockGroup)
  const setIsReadyToPrint = useStore(usePaperDataStoreContext, state => state.setIsReadyToPrint)
  const isReadyToPrint = useStore(usePaperDataStoreContext, state => state.isReadyToPrint)

  //打印对象
  const printRef = useRef(null);
  const nowPrintFunc = useReactToPrint({
    contentRef: printRef,
    documentTitle: '',
  })
  useEffect(() => {
    setPrintFunc(nowPrintFunc)
  }, [nowPrintFunc, setPrintFunc])

  useEffect(() => {
    setLastPaperId(paperData.id)
    //创建一个初始化数据，所有组件都加载在第一个桶内
    addAdmissionNumberBlockGroup(1)
    let newPageDataList: PageData[] = [{
      pageIndex: 1,
      bucketList: [{
        nodeIdList: [`${admissionTicketNumberInfoIdEx}_${1}_${0}`, ...questionItemIdList.map(questionItemId => {
          return questionItemIdToBucketNodeIdListMap.get(questionItemId) ?? []
        }).flat()]
      }]
    }]
    setPageDataList(newPageDataList)
  }, [addAdmissionNumberBlockGroup, paperData.id, questionItemIdList, questionItemIdToBucketNodeIdListMap, setPageDataList])

  /**
   * 作者：张瀚
   * 说明：根据组件高度重新分桶
   */
  const calculatePage = useCallback(() => {
    if (bucketHeight <= 0) {
      refreshAllBucketNodeDomHeight()
      return
    }
    //准备新的分页数据
    const newPageDataList: PageData[] = []
    let nowPage: PageData = {
      pageIndex: 1,
      bucketList: []
    }
    newPageDataList.push(nowPage)
    let nowBucketList: BucketData[] = []
    nowPage.bucketList = nowBucketList
    let nowBucket: BucketData = {
      nodeIdList: []
    }
    nowBucketList.push(nowBucket)
    let nowNodeIdList: string[] = []
    nowBucket.nodeIdList = nowNodeIdList
    let nowHeight = 0
    //按题块检查组件和分桶
    for (let questionItemIndex = 0; questionItemIndex < questionItemIdList.length; questionItemIndex++) {
      const questionItemId = questionItemIdList[questionItemIndex];
      //检查高度是否足以容纳下一个组件
      const bucketNodeIdList = questionItemIdToBucketNodeIdListMap.get(questionItemId)
      if (!bucketNodeIdList) {
        continue
      }
      for (let bucketNodeIdIndex = 0; bucketNodeIdIndex < bucketNodeIdList.length; bucketNodeIdIndex++) {
        if (nowNodeIdList.length === 0) {
          //每个桶的开始，检查是不是要插入二维码
          if (nowPage.pageIndex % 2 === 1 && nowPage.bucketList.length === 1) {
            //奇数页要插入二维码
            addAdmissionNumberBlockGroup(nowPage.pageIndex)
            const admissionTicketNumberItemId = `${admissionTicketNumberInfoIdEx}_${nowPage.pageIndex}`
            const admissionTicketNumberNodeId = `${admissionTicketNumberItemId}_0`
            nowNodeIdList.push(admissionTicketNumberNodeId)
            const domHeight = bucketNodeIdToDomHeightMap.get(admissionTicketNumberNodeId)
            if (domHeight) {
              nowHeight += domHeight
            }
          }
        }
        const bucketNodeId = bucketNodeIdList[bucketNodeIdIndex];
        const domHeight = bucketNodeIdToDomHeightMap.get(bucketNodeId)
        if (!domHeight || domHeight <= 0) {
          //不知道啊，就加进去先
          nowNodeIdList.push(bucketNodeId)
          continue
        }
        if (nowHeight + domHeight >= bucketHeight) {
          //放不下了，开新桶
          nowNodeIdList = []
          nowBucket = { nodeIdList: nowNodeIdList }
          //看看本页是不是满了
          if (nowBucketList.length >= bucket_size) {
            //满了开新页面
            nowBucketList = [nowBucket]
            nowPage = {
              pageIndex: nowPage.pageIndex + 1,
              bucketList: nowBucketList
            } satisfies PageData
            newPageDataList.push(nowPage)
          } else {
            nowBucketList.push(nowBucket)
          }
          if (domHeight > bucketHeight) {
            //牛逼，单页比整桶高，直接都给你了
            nowNodeIdList.push(bucketNodeId)
            nowHeight = 0
            continue
          }
          //正常开好以后回滚重新来
          bucketNodeIdIndex -= 1
          nowHeight = 0
          continue
        }
        nowNodeIdList.push(bucketNodeId)
        nowHeight += domHeight
      }
    }
    if (JSON.stringify(newPageDataList) !== JSON.stringify(pageDataList)) {
      setPageDataList(newPageDataList)
      setAnswerBoxVersion(Date.now().toString())
    }
  }, [addAdmissionNumberBlockGroup, bucketHeight, bucketNodeIdToDomHeightMap, bucket_size, pageDataList, questionItemIdList, questionItemIdToBucketNodeIdListMap, refreshAllBucketNodeDomHeight, setAnswerBoxVersion, setPageDataList])

  /**
 * 作者：张瀚
 * 说明：刷新所有位置块位置信息
 */
  const refreshBox = useCallback(() => {
    let isReady = true
    let needRefreshVersion = false
    //遍历所有显示出来的组件ID
    pageDataList.forEach(page => {
      //根据页面第一个桶内元素位置计算页面左上角起始位置
      let firstNodeId = page.bucketList[0]?.nodeIdList[0]
      if (!firstNodeId) {
        return
      }
      let firstDom = bucketNodeIdToRealDomMap.get(firstNodeId)
      if (!firstDom) {
        return
      }
      const { x: fx, y: fy } = firstDom.getBoundingClientRect()
      //然后需要根据页面的边距和定位点等信息调整
      const pageX = fx - answerCard.x
      const pageY = fy - answerCard.y - (answerCard.show_pos_point ? answerCard.pos_point_height : 0)
      page.bucketList.forEach(bucket => {
        bucket.nodeIdList.forEach(nodeId => {
          const nodeDom = bucketNodeIdToRealDomMap.get(nodeId)
          if (!nodeDom) {
            console.error("找不到dom", nodeId);
            return
          }
          const node = bucketNodeIdToBucketNodeMap.get(nodeId)
          if (!node) {
            console.error("找不到组件", nodeId);
            return
          }
          const questionItemId = node.itemId
          const questionItem = questionItemIdToQuestionItemMap.get(questionItemId)
          if (!questionItem) {
            console.error("找不到题块", questionItemId);
            return
          }
          //对应的块ID列表
          const blockIdList = bucketNodeIdToBlockIdListMap.get(nodeId)
          if (!blockIdList) {
            return
          }
          //如果有丢了的就忽略跳过，后面的排上来，这里可能有问题但是先这样
          const blockList = blockIdList.map(blockId => blockIdToBlockMap.get(blockId)).filter(block => block !== undefined)
          //按类型查询需要定位的位置块
          const { item_type } = questionItem
          let targetNodeDomList: Element[] = []
          switch (item_type) {
            case QuestionItemTypeEnum.Choice:
              targetNodeDomList = Array.from(nodeDom.querySelectorAll('.option-item'))
              break
            case QuestionItemTypeEnum.TrueOrFalse:
              targetNodeDomList = Array.from(nodeDom.querySelectorAll('.answer-area'))
              break
            case QuestionItemTypeEnum.FillInBlank:
              targetNodeDomList = Array.from(nodeDom.querySelectorAll('span>mjx-container>mjx-math>mjx-munder>mjx-row>mjx-under>mjx-mo>mjx-box>mjx-stretchy-h>mjx-ext'))
              break
            case QuestionItemTypeEnum.Textarea:
            case QuestionItemTypeEnum.ReadAndScan:
              targetNodeDomList = [nodeDom]
              break
            case QuestionItemTypeEnum.AdmissionTicketNumberInfo:
              targetNodeDomList = Array.from(nodeDom.querySelectorAll('.qrcode'))
              targetNodeDomList.push(...Array.from(nodeDom.querySelectorAll('.student-number-fill-answer-area')))
              break
          }
          for (let i = 0; i < targetNodeDomList.length; i++) {
            const targetNodeDom = targetNodeDomList[i];
            const { x, y, width, height } = targetNodeDom.getBoundingClientRect()
            let realHeight = height
            let realWidth = width
            let realY = y - pageY
            let realX = x - pageX
            //一些特殊调整
            switch (item_type) {
              case QuestionItemTypeEnum.FillInBlank: {
                //填空题的框是线，要根据字体尺寸调整范围
                let fontSizeNumber = parseInt(window.getComputedStyle(targetNodeDom).fontSize)
                realHeight += fontSizeNumber * 1.5
                realY -= fontSizeNumber * 1.5 + 2
                break
              }
            }
            //四舍五入
            realX = Math.round(realX)
            realY = Math.round(realY)
            realWidth = Math.round(realWidth)
            realHeight = Math.round(realHeight)
            //更新对应位置的值
            if (blockList[i] && blockList[i].isLock !== true) {
              if (blockList[i].x !== realX || blockList[i].y !== realY || blockList[i].width !== realWidth || blockList[i].height !== realHeight || blockList[i].page_number !== page.pageIndex) {
                needRefreshVersion = true
              }
              blockList[i].x = realX
              blockList[i].y = realY
              blockList[i].width = realWidth
              blockList[i].height = realHeight
              blockList[i].page_number = page.pageIndex
              if (realWidth === 0 || realHeight === 0) {
                //有不正常的尺寸
                isReady = false
              }
            }
          }
        })
      })
    })
    //额外计算页码的位置
    //根据页面第一个桶内元素位置计算页面左上角起始位置
    let firstNodeId = pageDataList[0]?.bucketList[0]?.nodeIdList[0]
    if (!firstNodeId) {
      return
    }
    let firstDom = bucketNodeIdToRealDomMap.get(firstNodeId)
    if (!firstDom) {
      return
    }
    const { x: fx, y: fy } = firstDom.getBoundingClientRect()
    //然后需要根据页面的边距和定位点等信息调整
    const pageX = fx - answerCard.x
    const pageY = fy - answerCard.y - (answerCard.show_pos_point ? answerCard.pos_point_height : 0)
    //页码数据
    const pageIndexItemId = `${pageIndexItemIdEx}_1`
    const pageIndexNodeId = `${pageIndexItemId}_0`
    let pageIndexBlockGroupIdList = questionItemIdToBlockGroupIdListMap.get(pageIndexItemId)
    if (!pageIndexBlockGroupIdList) {
      isReady = false
      //需要新建页码的位置组，每页都有但是只放第一页，所以是1组件1组,然后根据页码长度有多个块，动态创建
      const pageIndexBlockGroup: AnswerBlockGroupData = {
        id: v4(),
        card_id: answerCard.id,
        serial_number: 0,
        block_list: [],
        group_type: AnswerBlockGroupTypeEnum.Page,
        mode: AnswerBlockGroupModeEnum.FillRandom,
      };
      blockGroupIdToBlockGroupMap.set(pageIndexBlockGroup.id, pageIndexBlockGroup);
      questionItemIdToBlockGroupIdListMap.set(`${pageIndexItemId}`, [pageIndexBlockGroup.id]);
      pageIndexBlockGroupIdList = [pageIndexBlockGroup.id]
    }
    const pageIndexDom = bucketNodeIdToRealDomMap.get(pageIndexNodeId)
    if (pageIndexDom) {
      const blockDomList = Array.from(pageIndexDom.querySelectorAll(".page-index-block"))
      const blockIdList = bucketNodeIdToBlockIdListMap.get(pageIndexNodeId)?.slice(0, blockDomList.length) ?? []
      //数量应该一致
      for (let i = 0; i < blockDomList.length; i++) {
        const dom = blockDomList[i];
        let blockId = blockIdList[i]
        if (!blockId) {
          //新建
          const newBlock: AnswerBlockData = {
            id: v4(),
            group_id: pageIndexBlockGroupIdList[0],
            page_number: 1,
            width: 0,
            height: 0,
            x: 0,
            y: 0,
            serial_number: i
          }
          blockId = newBlock.id
          blockIdList[i] = newBlock.id
          blockIdToBlockMap.set(newBlock.id, newBlock)
        }
        let block = blockIdToBlockMap.get(blockId)
        if (block) {
          //更新数据
          const { x, y, width, height } = dom.getBoundingClientRect()
          let realHeight = height
          let realWidth = width
          let realY = y - pageY
          let realX = x - pageX
          //四舍五入
          realX = Math.round(realX)
          realY = Math.round(realY)
          realWidth = Math.round(realWidth)
          realHeight = Math.round(realHeight)
          if (block.x !== realX || block.y !== realY || block.width !== realWidth || block.height !== realHeight) {
            needRefreshVersion = true
          }
          block.x = realX
          block.y = realY
          block.width = realWidth
          block.height = realHeight
          block.page_number = 1
          if (realWidth === 0 || realHeight === 0) {
            isReady = false
          }
        }
      }
      bucketNodeIdToBlockIdListMap.set(pageIndexNodeId, blockIdList)
    } else if (answerCard.show_page_index) {
      //有显示页码但是没数据
      isReady = false
    }
    if (isReady && isReady !== isReadyToPrint) {
      setTimeout(() => {
        setIsReadyToPrint(isReady)
      }, 3000);
    } else {
      setIsReadyToPrint(isReady)
    }
    if (needRefreshVersion && showAnswerBox) {
      setAnswerBoxVersion(Date.now().toString())
    }
  }, [answerCard.id, answerCard.pos_point_height, answerCard.show_page_index, answerCard.show_pos_point, answerCard.x, answerCard.y, blockGroupIdToBlockGroupMap, blockIdToBlockMap, bucketNodeIdToBlockIdListMap, bucketNodeIdToBucketNodeMap, bucketNodeIdToRealDomMap, isReadyToPrint, pageDataList, questionItemIdToBlockGroupIdListMap, questionItemIdToQuestionItemMap, setAnswerBoxVersion, setIsReadyToPrint, showAnswerBox])

  useEffect(() => {
    const calculatePageTimer = setInterval(() => {
      calculatePage()
      refreshAllBucketNodeDomHeight()
    }, 1000);
    const refreshBoxTimer = setInterval(() => {
      refreshBox()
    }, 1000);
    return () => {
      clearInterval(calculatePageTimer)
      clearInterval(refreshBoxTimer)
    }
  }, [calculatePage, refreshAllBucketNodeDomHeight, refreshBox])

  /**
   * 作者：张瀚
   * 说明：如果选中的节点变动则滚动到该区域
   */
  useEffect(() => {
    if (nowEditingNode !== lastEditingNode) {
      setLastEditingNode(nowEditingNode)
      //定位是在什么位置
      if (nowEditingNode) {
        const dom = bucketNodeIdToRealDomMap.get(getBucketNodeId(nowEditingNode.itemId, nowEditingNode.serial_number))
        if (dom) {
          const pageDom = dom.offsetParent
          const visibleDom = pageDom?.parentElement?.parentElement
          if (pageDom && visibleDom) {
            const pageDomTop = pageDom.getBoundingClientRect().top
            const visibleTop = visibleDom.getBoundingClientRect().top + 10
            visibleDom.scrollTo({ top: pageDomTop + visibleDom.scrollTop - visibleTop + dom.offsetTop - 100 })
          }
        }
      }
    }
  }, [nowEditingNode, lastEditingNode, bucketNodeIdToRealDomMap])

  return <>
    <div className="canvas-visible-layer" key={lastPaperId} onClick={() => setNowEditingNode()} >
      <div ref={printRef}>
        {
          pageDataList.map((page) => {
            return <CanvasPageLayer key={page.pageIndex} pageIndex={page.pageIndex} pageTotals={pageDataList.length} bucketNodeIdList={page.bucketList.map(b => b.nodeIdList)} getBucketHeight={(height) => setBucketHeight(height)}></CanvasPageLayer>
          })
        }
      </div>
    </div>
    <QuestionItemEditDrawer />
    <BlockEditDrawer />
  </>
}

/**
 * 子组件返回的最小颗粒度组件的对象
 */
export interface ComponentNode {
  /**
   * 组件唯一ID，用来关联dom
   */
  id: string
  /**
   * 对应的数据ID
   */
  infoId: string
  /**
   * 在子组件内的序号
   */
  index: number
  /**
   * 渲染的对象
   */
  node: ReactNode
}

/**
 * 作者：张瀚
 * 说明：页面数据
 */
export interface PageData {
  /**
   * 页码，从1开始
   */
  pageIndex: number,
  bucketList: BucketData[]
}

/**
 * 作者：张瀚
 * 说明：每个桶的数据
 */
interface BucketData {
  /**
   * 桶内是node的ID，一般是questionItemId_序号，也有一些特别的例如二维码什么的
   */
  nodeIdList: string[]
}

export interface BucketNode {
  itemId: string,
  /**
   * 在同一个题块下的序号
   */
  serial_number: number,
  node: ReactNode
}
/**
 * 作者：张瀚
 * 说明：根据题块渲染对应的内容
 */
export function getNodeListByQuestionItem(item: QuestionItemData): BucketNode[] {
  let { item_type, id, config } = item
  switch (item_type) {
    case QuestionItemTypeEnum.Text:
      return [{
        itemId: id,
        serial_number: 0,
        node: <TextQuestionItemComponent key={`${item_type}-${id}`} data={item} ></TextQuestionItemComponent>
      }]
    case QuestionItemTypeEnum.Stem:
      return [{
        itemId: id,
        serial_number: 0,
        node: <StemQuestionItemComponent key={`${item_type}-${id}`} data={item} ></StemQuestionItemComponent>
      }]
    case QuestionItemTypeEnum.ReadAndScan:
      return [{
        itemId: id,
        serial_number: 0,
        node: <ReadAndScanQuestionItemComponent key={`${item_type}-${id}`} data={item} ></ReadAndScanQuestionItemComponent>
      }]
    case QuestionItemTypeEnum.Choice:
      return [{
        itemId: id,
        serial_number: 0,
        node: <ChoiceQuestionItemComponent key={`${item_type}-${id}`} data={item} ></ChoiceQuestionItemComponent>
      }]
    case QuestionItemTypeEnum.TrueOrFalse:
      return [{
        itemId: id,
        serial_number: 0,
        node: <TrueOrFalseQuestionItemComponent key={`${item_type}-${id}`} data={item} ></TrueOrFalseQuestionItemComponent>
      }]
    case QuestionItemTypeEnum.FillInBlank:
      return [{
        itemId: id,
        serial_number: 0,
        node: <FillInBlankQuestionItemComponent key={`${item_type}-${id}`} data={item} ></FillInBlankQuestionItemComponent>
      }]
    case QuestionItemTypeEnum.Textarea: {
      const nowConfig = config as TextareaQuestionItemConfig
      return Array(nowConfig.lineTotal).fill(null).map((_, i) => {
        return {
          itemId: id,
          serial_number: i,
          node: <TextareaQuestionItemComponent key={`${item_type}-${id}-${i}`} data={item} lineIndex={i}></TextareaQuestionItemComponent>
        }
      })
    }
    default:
      return [{
        itemId: id,
        serial_number: 0,
        node: <div key={`${item_type}-${id}`}>{JSON.stringify(item)}</div>
      }]
  }
}

export function getAdmissionTicketNumberComponentNode(itemId: string, admissionTicketNumberInfoQuestionItemConfig: AdmissionTicketNumberInfoQuestionItemConfig): BucketNode {
  return {
    itemId,
    serial_number: 0,
    node: <AdmissionTicketNumberComponent key={`${itemId}_0`} data={admissionTicketNumberInfoQuestionItemConfig} itemId={itemId} ></AdmissionTicketNumberComponent>
  }
}