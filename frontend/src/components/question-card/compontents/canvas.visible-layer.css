.canvas-visible-layer{
  flex-grow: 1;
  height: 100%;
  overflow: auto;
  position: relative;
}
.attribute-edit-title{
  font-weight: bold;
  min-width: 90px;
  text-align: right;
  margin: 0;
}

.attribute-edit-input{
  width:0;
  flex-grow: 1;
}

.canvas-page-layer{
  background-color: white;
  margin: auto;
  overflow: hidden;
  margin-bottom: 10px;
  margin-top: 10px;  
}

.normal-component{
  cursor: pointer;
  position: relative;
}

.flex-gap-h + .flex-gap-h{
  border-top: 1px solid black;
}
.flex-gap-v + .flex-gap-v{
  border-left: 1px solid black;
}

.edit-drawer-item{
  width: 100%;
}

.answer-area-block{
  width: 28px;
  height: 28px;
  line-height: 28px;
  border: 1px solid black;
  text-align:center;
  margin-bottom: 8px;
}

.answer-area-box{
  pointer-events: none;
  position:absolute;
  z-index: 50;
}
