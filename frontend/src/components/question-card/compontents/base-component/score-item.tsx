import { QuestionCardUI } from "../../ui/question-card-components/global";

export interface Props {
  /**
   * 分数，
   */
  score: number;
  /**
   * 是否垂直显示
   */
  vertical: boolean;
}
/**
 * 作者：张瀚
 * 说明：评分组件,0.5分一档
 */
export function ScoreItem({ score, vertical }: Props) {
  return <QuestionCardUI.Flex vertical={vertical} className="score-area" align='center' style={{ border: '1px solid black' }} >
    {
      Array(Math.ceil(score * 2)).fill(undefined).map((_, index) => {
        return <QuestionCardUI.Flex align='center' key={`index_${index}`} style={{
          width: vertical ? '100%' : 0, height: vertical ? 0 : '100%', flexGrow: 1, minHeight: '30px', minWidth: '30px', fontSize: '14px',
          borderTop: !vertical || index === 0 ? 'none' : '1px solid black',
          borderLeft: vertical || index === 0 ? 'none' : '1px solid black'
        }}>
          <div style={{ textAlign: 'center', width: '100%' }}>{(index + 1) * 0.5}</div>
        </QuestionCardUI.Flex>
      })
    }
  </QuestionCardUI.Flex>
}