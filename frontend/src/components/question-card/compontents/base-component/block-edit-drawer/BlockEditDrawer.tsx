import { PropsWithChildren, useContext } from 'react';
import { useStore } from 'zustand';
import { ComponentDataListStoreContext, ToolbarDataStoreContext } from '../../..';
import { QuestionCardUI } from '../../../ui/question-card-components/global';
import { PropsDrawerItem } from '../question-item-edit-drawer/question-item-edit-drawer';

export interface PropsDrawerItemProps extends PropsWithChildren {
  title: string;
}
/**
 * 作者：张瀚
 * 说明：基础的属性编辑弹窗中的组件，用来修改一些基础内容
 */
export function BlockEditDrawer() {
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext);
  const nowEditingBlock = useStore(useComponentDataListStoreContext, (state) => state.nowEditingBlock);
  const setNowEditingBlock = useStore(useComponentDataListStoreContext, (state) => state.setNowEditingBlock);
  const useToolbarDataStoreContext = useContext(ToolbarDataStoreContext);
  const setAnswerBoxVersion = useStore(useToolbarDataStoreContext, (state) => state.setAnswerBoxVersion);
  return (
    <>
      <QuestionCardUI.Drawer title="位置框编辑" open={nowEditingBlock !== undefined} width={500} onClose={() => setNowEditingBlock()} bodyStyle={{ padding: '0 24px 24px 24px' }}>
        {nowEditingBlock && (
          <div className="flex-center-gap10-column">
            <PropsDrawerItem title="锁定">
              <QuestionCardUI.Checkbox
                defaultChecked={nowEditingBlock.isLock == true}
                onCheckedChange={(value) => {
                  nowEditingBlock.isLock = value == true;
                }}
              />
            </PropsDrawerItem>
            <PropsDrawerItem title="宽度">
              <QuestionCardUI.InputNumber
                className="flex-grow"
                defaultValue={nowEditingBlock.width}
                suffix="像素"
                onValueChange={(value) => {
                  nowEditingBlock.width = Math.round(value ?? 10);
                  setAnswerBoxVersion(Date.now().toString());
                }}
              />
            </PropsDrawerItem>
            <PropsDrawerItem title="高度">
              <QuestionCardUI.InputNumber
                className="flex-grow"
                defaultValue={nowEditingBlock.height}
                suffix="像素"
                onValueChange={(value) => {
                  nowEditingBlock.height = Math.round(value ?? 10);
                  setAnswerBoxVersion(Date.now().toString());
                }}
              />
            </PropsDrawerItem>
            <PropsDrawerItem title="上边距">
              <QuestionCardUI.InputNumber
                className="flex-grow"
                defaultValue={nowEditingBlock.y}
                suffix="像素"
                onValueChange={(value) => {
                  nowEditingBlock.y = Math.round(value ?? 0);
                  setAnswerBoxVersion(Date.now().toString());
                }}
              />
            </PropsDrawerItem>
            <PropsDrawerItem title="左边距">
              <QuestionCardUI.InputNumber
                className="flex-grow"
                defaultValue={nowEditingBlock.x}
                suffix="像素"
                onValueChange={(value) => {
                  nowEditingBlock.x = Math.round(value ?? 0);
                  setAnswerBoxVersion(Date.now().toString());
                }}
              />
            </PropsDrawerItem>
          </div>
        )}
      </QuestionCardUI.Drawer>
    </>
  );
}
