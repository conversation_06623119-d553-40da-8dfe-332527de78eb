import { ComponentDataListStoreContext } from '@/components/question-card';
import { AnswerBlockData } from '@/components/question-card/store/paperDataStore';
import React, { useContext, useEffect, useState } from 'react';
import { useStore } from 'zustand';
import './BlockArea.scss';
export interface Props {
  block: AnswerBlockData;
  id: string;
}
interface Point {
  x: number;
  y: number;
}
/**
 * 作者：张瀚
 * 说明：作答块的框，拖拽开启锁定，双击打开编辑框
 */
const BlockArea: React.FC<Props> = ({ id, block }) => {
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext);
  const setNowEditingBlock = useStore(useComponentDataListStoreContext, (state) => state.setNowEditingBlock);
  const nowEditingBlock = useStore(useComponentDataListStoreContext, (state) => state.nowEditingBlock);
  const { x, y, height, width } = block;
  const [startPoint, setStartPoint] = useState<Point>();
  const [nowX, setNowX] = useState(x);
  const [nowY, setNowY] = useState(y);
  const onMouseDown: React.MouseEventHandler<HTMLDivElement> = (e) => {
    setStartPoint({ x: e.clientX + window.scrollX, y: e.clientY + window.scrollY });
  };
  const onMouseMove: React.MouseEventHandler<HTMLDivElement> = (e) => {
    if (!startPoint) {
      return;
    }
    block.isLock = true;
    //偏移量
    const dx = e.clientX + window.scrollX - startPoint.x;
    const dy = e.clientY + window.scrollY - startPoint.y;
    setNowX(x + dx);
    setNowY(y + dy);
  };
  useEffect(() => {
    const onMouseUp = (e: MouseEvent) => {
      if (!startPoint) {
        return;
      }
      setStartPoint(undefined);
      block.x = nowX;
      block.y = nowY;
    };
    window.addEventListener('mouseup', onMouseUp);
    return () => {
      window.removeEventListener('mouseup', onMouseUp);
    };
  });
  return (
    <div
      className={`answer-area-box ${nowEditingBlock === block || startPoint !== undefined ? 'high-light-border-red' : 'high-light-border-green'}`}
      key={`${id}`}
      id={id}
      title="左键拖拽锁定区域，双击打开编辑区域功能"
      style={{ left: `${nowX}px`, top: `${nowY}px`, height: `${height}px`, width: `${width}px`, cursor: startPoint ? 'grabbing' : 'grab', zIndex: startPoint ? 51 : 50 }}
      onMouseDown={onMouseDown}
      onMouseMove={onMouseMove}
      onDoubleClick={() => {
        setNowEditingBlock(block);
      }}
    />
  );
};
export default BlockArea;
