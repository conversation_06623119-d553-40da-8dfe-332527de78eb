import { ComponentDataListStoreContext, DataCallbackStoreContext } from "@/components/question-card"
import { Workflow } from "@/components/question-card/store/dataCallbackStore"
import { PaperDataStoreContext, ScoringCriteriaModeEnum, ScoringCriteriaTypeEnum } from "@/components/question-card/store/paperDataStore"
import { QuestionCardUI } from "@/components/question-card/ui/question-card-components/global"
import React, { useContext, useEffect, useState } from "react"
import { useStore } from "zustand"
import { PropsDrawerItem } from "./question-item-edit-drawer"
export interface Props {
    title: string
    blockGroupId: string
}

const ScoreEditing: React.FC<Props> = ({ title, blockGroupId }) => {
    const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
    const nowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.nowEditingQuestionItemId)
    const useDataCallbackStoreContext = useContext(DataCallbackStoreContext)
    const getCheckWorkflowList = useStore(useDataCallbackStoreContext, state => state.getCheckWorkflowList)
    const getOcrWorkflowList = useStore(useDataCallbackStoreContext, state => state.getOcrWorkflowList)
    const userPaperDataStoreContext = useContext(PaperDataStoreContext)
    const questionItemIdToQuestionItemMap = useStore(userPaperDataStoreContext, state => state.questionItemIdToQuestionItemMap)
    const blockGroupIdToScoringCriteriaIdMap = useStore(userPaperDataStoreContext, state => state.blockGroupIdToScoringCriteriaIdMap)
    const scoringCriteriaIdToScoringCriteriaMap = useStore(userPaperDataStoreContext, state => state.scoringCriteriaIdToScoringCriteriaMap)
    const updateScoringCriteria = useStore(userPaperDataStoreContext, state => state.updateScoringCriteria)
    const scoringCriteriaId = blockGroupIdToScoringCriteriaIdMap.get(blockGroupId)
    //获取对应的题块数据
    const questionItem = questionItemIdToQuestionItemMap.get(nowEditingQuestionItemId ?? '')
    //当前工作流
    const [ocrWorkflowList, setOcrWorkflowList] = useState<Workflow[]>([])
    const [checkWorkflowList, setCheckWorkflowList] = useState<Workflow[]>([])
    useEffect(() => {
        if (questionItem) {
            getCheckWorkflowList(questionItem).then(setCheckWorkflowList)
            getOcrWorkflowList(questionItem).then(setOcrWorkflowList)
        }
    }, [getCheckWorkflowList, getOcrWorkflowList, questionItem])
    if (!scoringCriteriaId) {
        return
    }
    const scoringCriteria = scoringCriteriaIdToScoringCriteriaMap.get(scoringCriteriaId)
    if (!scoringCriteria) {
        return <></>
    }
    const { scoring_type, mode, answer, score, criteriaName, ocr_work_id, check_work_id } = scoringCriteria
    return <React.Fragment key={title}>
        <QuestionCardUI.Divider style={{ borderColor: '#1677ff' }} title={title} />
        <PropsDrawerItem title="名称/题号" >
            <QuestionCardUI.Input type="text" className='flex-grow' defaultValue={criteriaName} onChange={v => {
                updateScoringCriteria({
                    ...scoringCriteria, criteriaName: v.target.value
                })
            }} />
        </PropsDrawerItem>
        <PropsDrawerItem title="评分类型" >
            <QuestionCardUI.Select
                defaultValue={scoring_type.toString()}
                className='flex-grow'
                options={
                    [{
                        value: ScoringCriteriaTypeEnum.Match.toString(),
                        label: <span>题卡匹配</span>
                    },
                    {
                        value: ScoringCriteriaTypeEnum.AI.toString(),
                        label: <span>AI评阅</span>
                    }, {
                        value: ScoringCriteriaTypeEnum.Manual.toString(),
                        label: <span>手动批阅</span>
                    }]
                }
                onValueChange={(value) => {
                    updateScoringCriteria({
                        ...scoringCriteria, scoring_type: value as ScoringCriteriaTypeEnum, mode: (() => {
                            switch (value) {
                                case ScoringCriteriaTypeEnum.AI:
                                    return ScoringCriteriaModeEnum.None
                                case ScoringCriteriaTypeEnum.Match:
                                    return ScoringCriteriaModeEnum.Exact
                                case ScoringCriteriaTypeEnum.Manual:
                                    return ScoringCriteriaModeEnum.None
                            }
                        })()
                    })
                }}
            />
        </PropsDrawerItem>
        <PropsDrawerItem title="评分模式">
            <QuestionCardUI.Select
                key={mode}
                defaultValue={mode}
                className='flex-grow'
                disabled={[ScoringCriteriaTypeEnum.AI, ScoringCriteriaTypeEnum.Manual].includes(scoring_type)}
                options={
                    (() => {
                        switch (scoring_type) {
                            case ScoringCriteriaTypeEnum.Match:
                                return [{
                                    value: ScoringCriteriaModeEnum.Exact,
                                    label: <span>完全匹配</span>
                                }, {
                                    value: ScoringCriteriaModeEnum.Partial,
                                    label: <span>部分匹配</span>
                                }, {
                                    value: ScoringCriteriaModeEnum.Count,
                                    label: <span>计数匹配</span>
                                }]
                            default:
                                return [{
                                    value: ScoringCriteriaModeEnum.None,
                                    label: <span>无</span>
                                }]
                        }
                    })()
                }
                onValueChange={(value) => {
                    if (value === ScoringCriteriaModeEnum.None) {
                        scoringCriteria.mode = undefined
                    } else {
                        scoringCriteria.mode = value as ScoringCriteriaModeEnum
                    }
                    updateScoringCriteria({ ...scoringCriteria })
                }}
            />
        </PropsDrawerItem>
        <PropsDrawerItem title="分值">
            <QuestionCardUI.InputNumber
                defaultValue={score}
                className="attribute-edit-input"
                onValueChange={v => {
                    scoringCriteria.score = v
                    updateScoringCriteria({ ...scoringCriteria })
                }} />
        </PropsDrawerItem>
        {
            scoringCriteria.scoring_type === ScoringCriteriaTypeEnum.AI && <PropsDrawerItem title="问题描述">
                <QuestionCardUI.Textarea className="attribute-edit-input" defaultValue={scoringCriteria.questionTips} autoSize={{ minRows: 1, maxRows: 10 }} onChange={v => {
                    scoringCriteria.questionTips = v.target.value
                    updateScoringCriteria({ ...scoringCriteria })
                }} />
            </PropsDrawerItem>
        }
        <PropsDrawerItem title="评分参考">
            <QuestionCardUI.Textarea className="attribute-edit-input" defaultValue={answer} autoSize={{ minRows: 1, maxRows: 10 }} onChange={v => {
                scoringCriteria.answer = v.target.value
                updateScoringCriteria({ ...scoringCriteria })
            }} />
        </PropsDrawerItem>
        {
            scoringCriteria.scoring_type === ScoringCriteriaTypeEnum.AI && <>
                <PropsDrawerItem title="OCR工作流">
                    <QuestionCardUI.Select
                        defaultValue={ocr_work_id}
                        className='flex-grow'
                        options={
                            ocrWorkflowList.map(item => ({
                                label: <span>{item.workflow_name}</span>,
                                value: item.workflow_id
                            }))
                        }
                        onValueChange={(value) => {
                            updateScoringCriteria({
                                ...scoringCriteria, ocr_work_id: value,
                            })
                        }}
                    />
                </PropsDrawerItem>
                <PropsDrawerItem title="评阅工作流">
                    <QuestionCardUI.Select
                        defaultValue={check_work_id}
                        className='flex-grow'
                        options={
                            checkWorkflowList.map(item => ({
                                label: <span>{item.workflow_name}</span>,
                                value: item.workflow_id
                            }))
                        }
                        onValueChange={(value) => {
                            updateScoringCriteria({
                                ...scoringCriteria, check_work_id: value,
                            })
                        }}
                    />
                </PropsDrawerItem>
            </>
        }
    </React.Fragment>
}

export default ScoreEditing;