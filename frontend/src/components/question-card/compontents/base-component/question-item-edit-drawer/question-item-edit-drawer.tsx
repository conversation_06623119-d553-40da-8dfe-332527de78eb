import React, { PropsWithChildren, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { useStore } from "zustand";
import { ComponentDataListStoreContext, DataCallbackStoreContext } from "../../..";
import { QuestionType } from "../../../store/dataCallbackStore";
import { AdmissionTicketNumberInfoQuestionItemConfig, AdmissionTicketQRCodeMsgBean, AnswerBlockGroupData, BaseQuestionItemConfig, ChoiceQuestionInfoQuestionItemConfig, FillInBlankQuestionItemConfig, PaperDataStoreContext, QuestionItemTypeEnum, ReadAndScanQuestionItemConfig, TextareaQuestionItemConfig } from "../../../store/paperDataStore";
import { QuestionCardUI } from "../../../ui/question-card-components/global";
import ScoreEditing from "./score-editing";

export interface PropsDrawerItemProps extends PropsWithChildren {
  title: string
}
/**
 * 作者：张瀚
 * 说明：基础的属性编辑弹窗中的组件，用来修改一些基础内容
 */
export function QuestionItemEditDrawer() {
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const nowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.nowEditingQuestionItemId)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const useDataCallbackStoreContext = useContext(DataCallbackStoreContext)
  const getCheckWorkflowList = useStore(useDataCallbackStoreContext, state => state.getCheckWorkflowList)
  const getOcrWorkflowList = useStore(useDataCallbackStoreContext, state => state.getOcrWorkflowList)
  const getQuestionTypeList = useStore(useDataCallbackStoreContext, state => state.getQuestionTypeList)
  const userPaperDataStoreContext = useContext(PaperDataStoreContext)
  const questionItemIdToQuestionItemMap = useStore(userPaperDataStoreContext, state => state.questionItemIdToQuestionItemMap)
  const questionItemIdToBlockGroupIdListMap = useStore(userPaperDataStoreContext, state => state.questionItemIdToBlockGroupIdListMap)
  const blockGroupIdToBlockGroupMap = useStore(userPaperDataStoreContext, state => state.blockGroupIdToBlockGroupMap)
  const updateQuestionItemNode = useStore(userPaperDataStoreContext, state => state.updateQuestionItemNode)
  const changeQuestionItemType = useStore(userPaperDataStoreContext, state => state.changeQuestionItemType)
  const questionIdToQuestionMap = useStore(userPaperDataStoreContext, state => state.questionIdToQuestionMap)

  //获取对应的题块数据
  const questionItem = questionItemIdToQuestionItemMap.get(nowEditingQuestionItemId ?? '')
  //位置组数据
  const blockGroupIdList = questionItemIdToBlockGroupIdListMap.get(nowEditingQuestionItemId ?? '') ?? []
  const nowBlockGroupList = blockGroupIdList.map(blockGroupId => blockGroupIdToBlockGroupMap.get(blockGroupId)).filter(it => it !== undefined)
  const { questionNo = undefined, text = undefined, style = {}, questionItemType } = questionItem?.config ?? {}
  const question = questionIdToQuestionMap.get(questionItem?.question_id ?? '')
  const { question_type = undefined } = question ?? {}
  const isQuestion = useMemo(() => {
    switch (questionItemType) {
      case QuestionItemTypeEnum.Choice:
      case QuestionItemTypeEnum.TrueOrFalse:
      case QuestionItemTypeEnum.Textarea:
      case QuestionItemTypeEnum.FillInBlank:
      case QuestionItemTypeEnum.ReadAndScan:
      case QuestionItemTypeEnum.Text:
      case QuestionItemTypeEnum.Stem:
        return true
    }
    return false
  }, [questionItemType])
  //当前题型列表
  const [questionTypeList, setQuestionTypeList] = useState<QuestionType[]>([])
  useEffect(() => {
    if (questionItem) {
      getQuestionTypeList(questionItem).then(setQuestionTypeList)
    }
  }, [getCheckWorkflowList, getOcrWorkflowList, getQuestionTypeList, questionItem])
  /**
   * 更新的方法
   */
  const updateQuestionItem = useCallback((config: Object) => {
    if (!questionItem) {
      return
    }
    Object.assign(questionItem.config, config)
    updateQuestionItemNode(questionItem)
  }, [questionItem, updateQuestionItemNode])

  const TextareaDrawer = useCallback((data: TextareaQuestionItemConfig) => {
    const { inputMode, answerAreaBlockNumber, lineTotal } = data
    return <>
      <PropsDrawerItem title="输入框模式">
        <QuestionCardUI.RadioGroup
          onValueChange={v => {
            updateQuestionItem({ inputMode: v })
          }}
          defaultValue={inputMode}
          options={[
            {
              value: 'none',
              label: <div>隐藏</div>,
            },
            {
              value: 'underLine',
              label: <div>下划线</div>,
            },
            {
              value: 'block',
              label: <div>方格</div>,
            }
          ]}
        />
      </PropsDrawerItem>
      {
        <PropsDrawerItem title="总行数">
          <QuestionCardUI.InputNumber min={1}
            defaultValue={lineTotal}
            step={1}
            className="attribute-edit-input"
            onValueChange={v => {
              updateQuestionItem({ lineTotal: parseInt(String(v ?? 1)) })
            }} />
        </PropsDrawerItem>
      }
      {
        ['block'].includes(inputMode) && <PropsDrawerItem title="每行字数">
          <QuestionCardUI.InputNumber min={1}
            defaultValue={answerAreaBlockNumber}
            step={1}
            className="attribute-edit-input"
            onValueChange={v => {
              updateQuestionItem({ answerAreaBlockNumber: parseInt(String(v ?? 1)) })
            }} />
        </PropsDrawerItem>
      }
      {
        nowBlockGroupList[0] && <ScoreEditing title="评分标准" blockGroupId={nowBlockGroupList[0].id} />
      }
    </>
  }, [nowBlockGroupList, updateQuestionItem])

  const ChoiceDrawer = useCallback((config: ChoiceQuestionInfoQuestionItemConfig, nowBlockGroupList: AnswerBlockGroupData[]) => {
    const { isHorizontal, choices } = config
    return <>
      <PropsDrawerItem title="选项方向">
        <QuestionCardUI.RadioGroup
          name="radiogroup"
          defaultValue={String(isHorizontal)}
          options={[
            { value: 'true', label: '横向显示' },
            { value: 'false', label: '竖向显示' },
          ]}
          onValueChange={v => {
            config.isHorizontal = v === 'true'
            updateQuestionItem({ isHorizontal: v === 'true' })
          }}
        />
      </PropsDrawerItem>
      {
        choices.map((c, i) => {
          return <PropsDrawerItem title={`选项${String.fromCharCode(65 + i)}`} key={`${i}`} >
            <QuestionCardUI.Textarea defaultValue={c} placeholder="题干不能为空" autoSize={{ minRows: 1, maxRows: 5 }} onChange={v => {
              choices[i] = v.target.value
              updateQuestionItem({ choices })
            }} />
          </PropsDrawerItem>
        })
      }
      {
        nowBlockGroupList[0] && <ScoreEditing title="评分标准" blockGroupId={nowBlockGroupList[0].id} />
      }
    </>
  }, [updateQuestionItem])

  const FillInBlankDrawer = useCallback((_config: FillInBlankQuestionItemConfig, nowBlockGroupList: AnswerBlockGroupData[]) => {
    return <React.Fragment >
      {
        nowBlockGroupList.map((blockGroup, i) => {
          return <ScoreEditing key={i} title={`第${i + 1}空评分标准`} blockGroupId={blockGroup.id} />
        })
      }
    </React.Fragment>
  }, [])

  const ReadAndScanDrawer = useCallback((config: ReadAndScanQuestionItemConfig, nowBlockGroupList: AnswerBlockGroupData[]) => {
    if (nowBlockGroupList.length === 0) {
      return
    }
    return <ScoreEditing title={`${config.questionNo ?? ''}评分标准`} blockGroupId={nowBlockGroupList[0].id} />
  }, [])

  const AdmissionTicketNumberDrawer = useCallback((config: AdmissionTicketNumberInfoQuestionItemConfig) => {
    const { fillText, size, showQRCode, needPaintCard, isHorizontal, examName, qrcodeMsg } = config
    const qrcodeBean: AdmissionTicketQRCodeMsgBean = (() => {
      try {
        return JSON.parse(qrcodeMsg ?? '') as AdmissionTicketQRCodeMsgBean
      } catch {
        return {}
      }
    })()
    return <>
      <PropsDrawerItem title="标题">
        <QuestionCardUI.Input type="text"
          defaultValue={examName}
          className="attribute-edit-input"
          onChange={v => {
            updateQuestionItem({ examName: v.target.value })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="涂卡区描述">
        <QuestionCardUI.Input type="text"
          defaultValue={fillText}
          className="attribute-edit-input"
          onChange={v => {
            updateQuestionItem({ fillText: v.target.value })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="学号长度">
        <QuestionCardUI.InputNumber min={0}
          defaultValue={size}
          className="attribute-edit-input"
          onValueChange={v => {
            updateQuestionItem({ size: parseInt(String(v ?? size)) })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="需要涂卡">
        <QuestionCardUI.Checkbox defaultChecked={needPaintCard} onCheckedChange={v => {
          updateQuestionItem({ needPaintCard: v })
        }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="横向显示">
        <QuestionCardUI.Checkbox defaultChecked={isHorizontal} onChange={v => {
          updateQuestionItem({ isHorizontal: v })
        }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="显示二维码">
        <QuestionCardUI.Checkbox defaultChecked={showQRCode} onChange={v => {
          updateQuestionItem({ showQRCode: v })
        }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="试卷ID">
        <QuestionCardUI.Input type="text"
          defaultValue={qrcodeBean.paperId}
          className="attribute-edit-input"
          onChange={v => {
            qrcodeBean.paperId = v.target.value
            updateQuestionItem({ qrcodeMsg: JSON.stringify(qrcodeBean) })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="题卡ID">
        <QuestionCardUI.Input type="text"
          defaultValue={qrcodeBean.cardId}
          className="attribute-edit-input"
          onChange={v => {
            qrcodeBean.cardId = v.target.value
            updateQuestionItem({ qrcodeMsg: JSON.stringify(qrcodeBean) })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="学号">
        <QuestionCardUI.InputNumber
          defaultValue={qrcodeBean.studentNumber}
          placeholder="请输入学生号数字"
          className="attribute-edit-input"
          onValueChange={v => {
            qrcodeBean.studentNumber = v ? parseInt(String(v)) : undefined
            updateQuestionItem({ qrcodeMsg: JSON.stringify(qrcodeBean) })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="姓名">
        <QuestionCardUI.Input type="text"
          defaultValue={qrcodeBean.studentName}
          className="attribute-edit-input"
          onChange={v => {
            qrcodeBean.studentName = v.target.value
            updateQuestionItem({ qrcodeMsg: JSON.stringify(qrcodeBean) })
          }} />
      </PropsDrawerItem>
      <PropsDrawerItem title="班级">
        <QuestionCardUI.Input type="text"
          defaultValue={qrcodeBean.studentClassName}
          className="attribute-edit-input"
          onChange={v => {
            qrcodeBean.studentClassName = v.target.value
            updateQuestionItem({ qrcodeMsg: JSON.stringify(qrcodeBean) })
          }} />
      </PropsDrawerItem>
    </>
  }, [updateQuestionItem])

  /**
   * 根据类型获取独特的组件
   */
  const getComponentConfig = useCallback((config: BaseQuestionItemConfig, nowBlockGroupList: AnswerBlockGroupData[]) => {
    const { questionItemType: type } = config
    const titleDivider = <QuestionCardUI.Divider style={{ borderColor: '#7cb305' }} title="模块操作" />
    switch (type) {
      case QuestionItemTypeEnum.Choice:
        return <>
          {titleDivider}
          {ChoiceDrawer(config as ChoiceQuestionInfoQuestionItemConfig, nowBlockGroupList)}
        </>
      case QuestionItemTypeEnum.AdmissionTicketNumberInfo:
        return <>
          {titleDivider}
          {AdmissionTicketNumberDrawer(config as AdmissionTicketNumberInfoQuestionItemConfig)}
        </>
      case QuestionItemTypeEnum.TrueOrFalse:
        return <>
          {titleDivider}
          {
            nowBlockGroupList[0] && <ScoreEditing title="评分标准" blockGroupId={nowBlockGroupList[0].id} />
          }
        </>
      case QuestionItemTypeEnum.FillInBlank:
        return <>
          {titleDivider}
          {FillInBlankDrawer(config as FillInBlankQuestionItemConfig, nowBlockGroupList)}
        </>
      case QuestionItemTypeEnum.Textarea:
        return <>
          {titleDivider}
          {TextareaDrawer(config as TextareaQuestionItemConfig)}
        </>
      case QuestionItemTypeEnum.ReadAndScan:
        return <>
          {titleDivider}
          {ReadAndScanDrawer(config as ReadAndScanQuestionItemConfig, nowBlockGroupList)}
        </>
    }
    return <></>
  }, [AdmissionTicketNumberDrawer, ChoiceDrawer, FillInBlankDrawer, ReadAndScanDrawer, TextareaDrawer])

  return <>
    <QuestionCardUI.Drawer
      title="编辑属性"
      open={nowEditingQuestionItemId !== undefined}
      width={500}
      onClose={() => setNowEditingQuestionItemId()}
      bodyStyle={{ padding: '0 24px 24px 24px' }}
    >
      <div className="flex-center-gap10-column" >
        {isQuestion && <><QuestionCardUI.Divider style={{ borderColor: '#7cb305' }} title="题目属性" />
          <div className="edit-drawer-item flex-center-gap10">
            <p className="attribute-edit-title">题号</p>
            <QuestionCardUI.Input type="text"
              defaultValue={questionNo}
              className="attribute-edit-input"
              onChange={v => {
                updateQuestionItem({ questionNo: v.target.value ?? undefined })
              }} />
          </div>
          <PropsDrawerItem title="题型">
            <QuestionCardUI.Select
              defaultValue={question_type}
              className='flex-grow'
              disabled={!question}
              options={
                questionTypeList.map(item => ({
                  label: <span>{item.type_name}</span>,
                  value: item.code
                }))
              }
              onValueChange={(value) => {
                if (question) {
                  question.question_type = value
                }
              }}
            />
          </PropsDrawerItem>
          <PropsDrawerItem title="题干">
            <QuestionCardUI.Textarea className="attribute-edit-input" defaultValue={text} autoSize={{ minRows: 2, maxRows: 10 }} onChange={v => {
              updateQuestionItem({ text: v.target.value })
            }} />
          </PropsDrawerItem>
          <PropsDrawerItem title="题块类型">
            <QuestionCardUI.Select
              defaultValue={questionItem?.item_type}
              className='flex-grow'
              disabled={!question}
              options={
                [['二维码', QuestionItemTypeEnum.AdmissionTicketNumberInfo],
                ['纯文本', QuestionItemTypeEnum.Text],
                ['Latex文本', QuestionItemTypeEnum.Stem],
                ['选项', QuestionItemTypeEnum.Choice],
                ['填空题', QuestionItemTypeEnum.FillInBlank],
                ['扫阅区域', QuestionItemTypeEnum.ReadAndScan],
                ['判断题', QuestionItemTypeEnum.TrueOrFalse],
                ['作答区', QuestionItemTypeEnum.Textarea]].filter(arr => {
                  if (questionItem?.config.questionItemType?.toString() === arr[1]) {
                    return true
                  }
                  switch (questionItem?.config.questionItemType) {
                    case QuestionItemTypeEnum.ReadAndScan:
                      return ['Text', 'Stem'].includes(arr[1])
                    case QuestionItemTypeEnum.Text:
                      return ['Stem'].includes(arr[1])
                    case QuestionItemTypeEnum.Stem:
                      return ['Text'].includes(arr[1])
                  }
                  return false
                }).map(arr => ({
                  label: <span>{arr[0]}</span>,
                  value: arr[1]
                }))
              }
              onValueChange={(newItemType) => {
                //修改类型时，config需要做对应转换，为了避免不知名问题，先只允许部分类型的转换
                const oldItemType = questionItem?.item_type
                switch (oldItemType) {
                  case QuestionItemTypeEnum.ReadAndScan:
                    switch (newItemType) {
                      case QuestionItemTypeEnum.Text:
                      case QuestionItemTypeEnum.Stem:
                        changeQuestionItemType(questionItem, newItemType)
                        break
                      default:
                        toast.warning('功能限制提示', { description: "目前只支持转为文本或者Latex文本" })
                        break
                    }
                    break
                  case QuestionItemTypeEnum.Text:
                    switch (newItemType) {
                      case QuestionItemTypeEnum.Stem:
                        changeQuestionItemType(questionItem, newItemType)
                        break
                      default:
                        toast.warning('功能限制提示', { description: "目前只支持转为Latex文本" })
                        break
                    }
                    break
                  case QuestionItemTypeEnum.Stem:
                    switch (newItemType) {
                      case QuestionItemTypeEnum.Text:
                        changeQuestionItemType(questionItem, newItemType)
                        break
                      default:
                        toast.warning('功能限制提示', { description: "目前只支持转为纯文本" })
                        break
                    }
                    break
                  default:
                    toast.warning('功能限制提示', { description: "目前只支持部分类型的修改！" })
                    break
                }
              }}
            />
          </PropsDrawerItem>
        </>}
        {
          questionItem && getComponentConfig(questionItem?.config, nowBlockGroupList)
        }
        <QuestionCardUI.Divider style={{ borderColor: '#7cb305' }} title="样式属性" />
        {
          [
            { title: '上边距', key: 'paddingTop', value: parseInt(style.paddingTop?.toString() ?? '0') ?? 0 },
            { title: '下边距', key: 'paddingBottom', value: parseInt(style.paddingBottom?.toString() ?? '0') ?? 0 },
            { title: '左边距', key: 'paddingLeft', value: parseInt(style.paddingLeft?.toString() ?? '0') ?? 0 },
            { title: '右边距', key: 'paddingRight', value: parseInt(style.paddingRight?.toString() ?? '0') ?? 0 },
            { title: '字体大小', key: 'fontSize', value: parseInt(style.fontSize?.toString() ?? '14') ?? 0 },
          ].map(item => {
            return <PropsDrawerItem key={item.title} title={item.title} >
              <QuestionCardUI.InputNumber min={0}
                value={item.value}
                className="attribute-edit-input"
                suffix="像素"
                onValueChange={v => {
                  updateQuestionItem({ style: { ...style, [item.key]: `${parseInt(String(v ?? 0))}px` } })
                }} />
            </PropsDrawerItem>
          })
        }
      </div>
    </QuestionCardUI.Drawer>
  </>
}


/**
 * 作者：张瀚
 * 说明：通用的默认的属性编辑弹窗中使用的一行数据
 */
export function PropsDrawerItem({ title, children }: PropsDrawerItemProps) {
  return <div className="edit-drawer-item flex-center-gap10">
    <p className="attribute-edit-title">{title}</p>
    {children}
  </div>
}