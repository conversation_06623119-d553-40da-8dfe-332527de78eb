import { QRCodeSVG } from 'qrcode.react'
import { useContext, useMemo } from "react"
import { useStore } from "zustand"
import { ComponentDataListStoreContext } from "../.."
import Mathdown from "../../mathdown/Mathdown"
import { AdmissionTicketNumberInfoQuestionItemConfig, AdmissionTicketQRCodeMsgBean, ChoiceQuestionInfoQuestionItemConfig, FillInBlankQuestionItemConfig, getBucketNodeId, QuestionItemData, ReadAndScanQuestionItemConfig, StemQuestionItemConfig, TextareaQuestionItemConfig, TextQuestionItemConfig, TrueOrFalseQuestionItemConfig } from "../../store/paperDataStore"
import { QuestionCardUI } from "../../ui/question-card-components/global"
import { OptionItem } from "../base-component/option-item"

export interface BaseQuestionItemProps {
  /**
   * 对应的组件对象
   */
  data: QuestionItemData
}

export interface TextareaQuestionItemProps extends BaseQuestionItemProps {
  lineIndex: number
}

/**
 * 作者：张瀚
 * 说明：纯文字组件
 */
export function TextQuestionItemComponent({ data }: BaseQuestionItemProps) {
  const config = data.config as TextQuestionItemConfig
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <div style={config.style}
    id={getBucketNodeId(data.id, 0)}
    ref={el => setDom(data.id, 0, updateBucketNodeDom, el)}
    className={`normal-component ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === 0 ? 'high-light-border-red' : ''}`}
    onClick={() => setNowEditingQuestionItemId(data.id)} >
    {config.text}
  </div>
}

/**
 * 作者：张瀚
 * 说明：题目文本组件，支持latex渲染和自动加上题号
 */
export function StemQuestionItemComponent({ data }: BaseQuestionItemProps) {
  const config = data.config as StemQuestionItemConfig
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <div style={config.style}
    id={getBucketNodeId(data.id, 0)}
    ref={el => {
      setDom(data.id, 0, updateBucketNodeDom, el)
    }}
    className={`normal-component ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === 0 ? 'high-light-border-red' : ''}`}
    onClick={() => setNowEditingQuestionItemId(data.id)} >
    <Mathdown content={`${config.questionNo ?? ''}${config.text}`} />
  </div>
}

/**
 * 作者：张瀚
 * 说明：题目文本组件，支持latex渲染和自动加上题号
 */
export function ReadAndScanQuestionItemComponent({ data }: BaseQuestionItemProps) {
  const config = data.config as ReadAndScanQuestionItemConfig
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <div style={config.style}
    id={getBucketNodeId(data.id, 0)}
    ref={el => {
      setDom(data.id, 0, updateBucketNodeDom, el)
    }}
    className={`normal-component ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === 0 ? 'high-light-border-red' : ''}`}
    onClick={() => setNowEditingQuestionItemId(data.id)} >
    <Mathdown content={`${config.questionNo ?? ''}${config.text}`} />
  </div>
}

/**
 * 作者：张瀚
 * 说明：选择题组件
 */
export function ChoiceQuestionItemComponent({ data }: BaseQuestionItemProps) {
  const config = data.config as ChoiceQuestionInfoQuestionItemConfig
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <div style={config.style}
    id={getBucketNodeId(data.id, 0)}
    ref={el => setDom(data.id, 0, updateBucketNodeDom, el)}
    className={`normal-component ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === 0 ? 'high-light-border-red' : ''}`}
    onClick={() => setNowEditingQuestionItemId(data.id)} >
    {
      config.choices.length > 0 && <QuestionCardUI.Flex justify='space-between' vertical={!config.isHorizontal} wrap>
        {config.choices.map((item, index) => {
          return <div style={{ display: 'flex', alignItems: 'flex-start', gap: '10px' }} key={`ChoiceQuestionComponent-${data.id}-${index}`} >
            <OptionItem index={index} />
            <Mathdown content={item} />
          </div>
        })}
      </QuestionCardUI.Flex>
    }
  </div>
}

/**
 * 作者：张瀚
 * 说明：判断题组件
 */
export function TrueOrFalseQuestionItemComponent({ data }: BaseQuestionItemProps) {
  const config = data.config as TrueOrFalseQuestionItemConfig
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <div id={getBucketNodeId(data.id, 0)} ref={el => setDom(data.id, 0, updateBucketNodeDom, el)} style={config.style}
    className={`normal-component ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === 0 ? 'high-light-border-red' : ''}`}
    onClick={() => setNowEditingQuestionItemId(data.id)} >
    <QuestionCardUI.Flex gap={10} >
      <Mathdown content={`${config.questionNo}.${config.text}`} />
      <QuestionCardUI.Flex style={{ fontSize: '14px', textWrap: 'nowrap' }} >
        <span className="answer-area" style={{ textAlign: 'center', height: 'fit-content', lineHeight: 1, width: '20px', border: '1px solid black' }}>√</span>
        <span className="answer-area" style={{ textAlign: 'center', height: 'fit-content', lineHeight: 1, width: '20px', marginLeft: '10px', border: '1px solid black' }}>×</span>
      </QuestionCardUI.Flex>
    </QuestionCardUI.Flex>
  </div>
}

/**
 * 作者：张瀚
 * 说明：填空题组件
 */
export function FillInBlankQuestionItemComponent({ data }: BaseQuestionItemProps) {
  const config = data.config as FillInBlankQuestionItemConfig
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <div id={getBucketNodeId(data.id, 0)} ref={el => setDom(data.id, 0, updateBucketNodeDom, el)} style={config.style}
    className={`normal-component ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === 0 ? 'high-light-border-red' : ''}`}
    onClick={() => setNowEditingQuestionItemId(data.id)} >
    <Mathdown content={`${config.questionNo ?? ''}${config.text}`} />
  </div>
}

/**
 * 作者：张瀚
 * 说明：简答题回答部分，以行为单位划分，如果是格子类型的，则先在预渲染区域的题干区域生成，换行后计算一行的数量再实际生成
 */
export function TextareaQuestionItemComponent({ data, lineIndex }: TextareaQuestionItemProps) {
  const config = data.config as TextareaQuestionItemConfig
  const { inputMode, answerAreaBlockNumber, style } = config
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const nowEditingNode = useStore(useComponentDataListStoreContext, state => state.nowEditingNode)
  return <>
    {
      inputMode === 'none' && <div id={getBucketNodeId(data.id, lineIndex)} ref={el => setDom(data.id, lineIndex, updateBucketNodeDom, el)} className={`normal-component answer-area ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === lineIndex ? 'high-light-border-red' : ''}`} style={{ ...style, width: '100%' }} key={lineIndex} onClick={() => { setNowEditingQuestionItemId(data.id) }}><br /></div>
    }
    {
      inputMode === 'underLine' && <div id={getBucketNodeId(data.id, lineIndex)} ref={el => setDom(data.id, lineIndex, updateBucketNodeDom, el)} className={`normal-component answer-area ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === lineIndex ? 'high-light-border-red' : ''}`} style={{ ...style, borderBottom: '1px solid black', width: '100%' }} key={lineIndex} onClick={() => { setNowEditingQuestionItemId(data.id) }}><br /></div>
    }
    {
      inputMode === 'block' && <QuestionCardUI.Flex id={getBucketNodeId(data.id, lineIndex)} ref={el => setDom(data.id, lineIndex, updateBucketNodeDom, el)} className={`normal-component answer-area ${data.id === nowEditingNode?.itemId && nowEditingNode.serial_number === lineIndex ? 'high-light-border-red' : ''}`} style={{ ...style, lineHeight: 2 }} gap={2} wrap='wrap' align='center' onClick={() => { setNowEditingQuestionItemId(data.id) }} >
        {
          Array(answerAreaBlockNumber).fill(undefined).map((_, i) => {
            //当前的字数
            const nowIndex = lineIndex * answerAreaBlockNumber + i
            if (nowIndex > 0 && (nowIndex + 1) % 100 === 0) {
              return <QuestionCardUI.Flex vertical={true} align='center' justify='center' key={i}>
                <div className="answer-area-block" style={{ marginBottom: 0 }} />
                <div style={{ fontSize: '8px', lineHeight: '8px', height: '8px', textAlign: 'center' }}>▲{nowIndex + 1}</div>
              </QuestionCardUI.Flex>
            }
            return <div className="answer-area-block" key={`${i}`} />
          })
        }
      </QuestionCardUI.Flex>
    }
  </>
}

export interface AdmissionTicketNumberQuestionItemProps {
  itemId: string,
  data: AdmissionTicketNumberInfoQuestionItemConfig
}

/**
 * 作者：张瀚
 * 说明：试卷名组件
 */
export function AdmissionTicketNumberComponent({ data, itemId }: AdmissionTicketNumberQuestionItemProps) {
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowEditingQuestionItemId = useStore(useComponentDataListStoreContext, state => state.setNowEditingQuestionItemId)
  const updateBucketNodeDom = useStore(useComponentDataListStoreContext, state => state.updateBucketNodeDom)
  const { examName, showQRCode, qrcodeMsg, style } = data
  const qrcodeBean = new AdmissionTicketQRCodeMsgBean(qrcodeMsg)

  //学生信息填写区域
  const studentInfoComponent = useMemo(() => <QuestionCardUI.Flex gap={10} wrap>
    <QuestionCardUI.Flex gap={10}>
      <span style={{ minWidth: 40 }}>姓名:</span>
      <span className="name-answer-area" style={{ borderBottom: '1px solid black', width: '120px' }}>{qrcodeBean.studentName}</span>
    </QuestionCardUI.Flex>
    <QuestionCardUI.Flex gap={10}>
      <span style={{ minWidth: 40 }}>班级:</span>
      <span className="class-answer-area" style={{ borderBottom: '1px solid black', width: '120px' }}>{qrcodeBean.studentClassName}</span>
    </QuestionCardUI.Flex>
  </QuestionCardUI.Flex>, [qrcodeBean.studentClassName, qrcodeBean.studentName])
  const qrCodeContent = [qrcodeBean.paperId ?? '', qrcodeBean.studentNumber ?? ''].join(";")
  return <>
    <QuestionCardUI.Flex id={getBucketNodeId(itemId, 0)} ref={el => setDom(`${itemId}`, 0, updateBucketNodeDom, el)} wrap gap={10} justify='space-between' style={{ ...style, display: 'flex', paddingBottom: '10px' }} className={`normal-component`} onClick={() => setNowEditingQuestionItemId(`${itemId}`)}>
      {
        qrcodeMsg && showQRCode && <QRCodeSVG className='qrcode' value={qrCodeContent} size={90} style={{ marginLeft: "20px", padding: '12px', border: '1px solid #e1e1e1', borderRadius: '8px' }} />
      }
      <QuestionCardUI.Flex gap={10} vertical={true} style={{ flexGrow: 1, width: 0 }} align='flex-start' justify='space-between'>
        <div style={{ fontSize: '20px', fontWeight: 'bold' }}>{examName}</div>
        {studentInfoComponent}
        {StudentNumberComponent(data, qrcodeBean)}
      </QuestionCardUI.Flex>
    </QuestionCardUI.Flex>
  </>
}

//学号填涂区域
function StudentNumberComponent(config: AdmissionTicketNumberInfoQuestionItemConfig, qrcodeBean: AdmissionTicketQRCodeMsgBean) {
  const { isHorizontal, fillText, needPaintCard, size } = config
  const flexGapClassName = isHorizontal ? 'flex-gap-h' : 'flex-gap-v'
  const { studentNumber } = qrcodeBean
  const studentNumberText = studentNumber?.toString() ?? ''
  return <QuestionCardUI.Flex style={{ border: '1px solid black', height: 'min-content', width: 'min-content' }} className="student-number-fill-answer-area" vertical={!isHorizontal}>
    <p
      style={{
        textAlign: 'center',
        borderBottom: isHorizontal ? 'none' : '1px solid black',
        borderRight: isHorizontal ? '1px solid black' : 'none',
        fontSize: '14px',
        fontWeight: 'bold',
        margin: 0,
        writingMode: isHorizontal ? 'vertical-lr' : 'horizontal-tb'
      }}>{fillText}</p>
    <QuestionCardUI.Flex vertical={isHorizontal} >
      {
        Array(size).fill(null).map((_, index) => {
          return <div key={`blank_${index}`} className={flexGapClassName} style={{
            textAlign: 'center', height: '30px', width: '30px', lineHeight: '30px', fontSize: '24px',
            borderBottom: needPaintCard && !isHorizontal ? '1px solid black' : 'none',
            borderRight: needPaintCard && isHorizontal ? '1px solid black' : 'none'
          }}>
            {studentNumberText[index]}
          </div>
        })
      }
    </QuestionCardUI.Flex>
    {
      needPaintCard && <>
        {
          Array(10).fill(null).map((_, i) => {
            return <QuestionCardUI.Flex vertical={isHorizontal} key={`f_${i}`}>
              {
                Array(size).fill(null).map((_, index) => {
                  return <div key={`paint_${i}_${index}`} className={flexGapClassName} style={{ textAlign: 'center', height: '30px', width: '30px', lineHeight: '30px', fontSize: '12px', fontWeight: 'bold', background: (parseInt(studentNumberText[index]) ?? -1) == i ? 'black' : "none" }} >[ {i} ]</div>
                })
              }
            </QuestionCardUI.Flex>

          })
        }
      </>
    }
  </QuestionCardUI.Flex>
}

function setDom(itemId: string, serial_number: number, updateBucketNodeDom: (bucketNodeId: string, dom: HTMLElement) => void, el: HTMLElement | null) {
  if (el) {
    updateBucketNodeDom(getBucketNodeId(itemId, serial_number), el)
  }
}