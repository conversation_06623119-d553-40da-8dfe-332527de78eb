
export interface PageSize {
  /**
   * 唯一ID
   */
  value: string,
  /**
   * 显示用的名称
   */
  label: string,
  /**
   * 像素宽度
   */
  width: number,
  /**
   * 像素高度
   */
  height: number
}

/**
 * 作者：张瀚
 * 说明：支持的各种画布的尺寸设定，单位是像素（96DPI下），
 */
export const PageSizeList: Array<PageSize> = [
  { value: 'a4-96dpi', label: "A4（210mm x 297mm）", width: 794, height: 1123 },
  { value: 'a3-96dpi', label: "A3（297mm x 420mm）", width: 1123, height: 1587 },
]