import { createContext, CSSProperties } from 'react';
import { v4 } from 'uuid';
import { create, StoreApi } from 'zustand';
import { BucketNode, getAdmissionTicketNumberComponentNode, getNodeListByQuestionItem, PageData } from '../compontents/canvas.visible-layer';
import { admissionTicketNumberInfoIdEx, pageIndexItemIdEx } from './componentDataListStore';

const fillInBlankRegex = /\\underline{(\\quad)+}/g;

export interface PaperData {
  id: string;
  paper_name: string;
  paper_content: PaperContentData;
}

export interface PaperContentData {
  //题目部分
  question_data_list: QuestionData[];
  //答题卡部分
  answer_card: AnswerCardData;
}

export interface QuestionData {
  id: string;
  original_question_id?: string;
  //无题型的是非题目类型，类似标题一类的
  question_type?: string;
  question_content: QuestionItemData[];
}

export interface QuestionItemData {
  id: string;
  question_id: string;
  item_type: QuestionItemTypeEnum;
  config: BaseQuestionItemConfig;
  //可选项，有答案的题块才有
  scoring_criteria?: ScoringCriteria;
}

export interface AnswerCardData {
  id: string;
  paper_id: string;
  dpi: number;
  width: number;
  height: number;
  //左边距
  x: number;
  //上边距
  y: number;
  //右边距
  right: number;
  //下边距
  bottom: number;
  //是否纵向
  page_orientation_is_vertical: boolean;
  //分栏数量
  bucket_size: number;
  //是否显示页码
  show_page_index: boolean;
  //是否显示定位点,定位点位于页面内部四个顶点，上面两个是横向，下面两个竖向
  show_pos_point: boolean;
  //定位点宽度，指左上角那个
  pos_point_width: number;
  //定位点高度度，指左上角那个
  pos_point_height: number;
  //题块组列表
  block_group_list: AnswerBlockGroupData[];
  //题块关联题块区域
  question_item_block_group_list: QuestionItemBlockGroupData[];
  //题块区域关联评分标准
  answer_block_group_scoring_criteria_list: AnswerBlockScoringCriteria[];
  //评分标准
  scoring_criteria_list: ScoringCriteria[];
  //总页码
  page_total: number;
  //答题卡信息
  admissionTicketNumberInfoQuestionItemConfig: AdmissionTicketNumberInfoQuestionItemConfig;
}
export interface AnswerBlockGroupData {
  id: string;
  card_id: string;
  serial_number: number;
  block_list: AnswerBlockData[];
  group_type: AnswerBlockGroupTypeEnum;
  mode: AnswerBlockGroupModeEnum;
}
export enum AnswerBlockGroupTypeEnum {
  /**
   * 选择题用的
   */
  FillChoice = 'fill_choice',
  FillTrueOrFalse = 'fill_true_or_false',
  HandwriteQuestion = 'handwrite_question',
  CardNo = 'card_no',
  Number = 'number',
  Page = 'page',
  Location = 'location',
}
export enum AnswerBlockGroupModeEnum {
  /**
   * 随机匹配
   */
  FillRandom = 'fill_random',
  AnswerArea = 'answer_area',
  Qrcode = 'qrcode',
  Ocr = 'ocr',
  Block = 'block',
}
export interface AnswerBlockData {
  id: string;
  group_id: string;
  page_number: number;
  width: number;
  height: number;
  x: number;
  y: number;
  serial_number: number;
  //是否已经锁定尺寸和位置
  isLock?: boolean
}

export interface QuestionItemBlockGroupData {
  id: string;
  question_item_id: string;
  block_group_id: string;
}

export interface AnswerBlockScoringCriteria {
  id: string;
  scoring_criteria_id: string;
  block_group_id: string;
}

export interface ScoringCriteria {
  id: string;
  criteriaName?: string;
  scoring_type: ScoringCriteriaTypeEnum;
  mode?: ScoringCriteriaModeEnum;
  answer?: string;
  questionTips?: string,
  score?: number;
  ocr_work_id?: string;
  check_work_id?: string;
}

export enum ScoringCriteriaTypeEnum {
  /**
   * 未被初始化
   */
  None = 'None',
  /**
   * 选择题判断题
   */
  Match = 'Match',
  /**
   * AI评阅，填空题作文一类
   */
  AI = 'AI',
  /**
   * 批阅轨迹
   */
  Manual = 'Manual',
}

export enum ScoringCriteriaModeEnum {
  /**
   * match-完全匹配
   */
  Exact = 'Exact',
  /**
   * match-部分匹配
   */
  Partial = 'Partial',
  /**
   * match-计数匹配
   */
  Count = 'Count',
  /**
   * 会给undefined仅用于选项下拉框等明确标识
   */
  None = 'None',
}

export enum QuestionItemTypeEnum {
  Text = 'Text',
  Stem = 'Stem',
  Choice = 'Choice',
  TrueOrFalse = 'TrueOrFalse',
  FillInBlank = 'FillInBlank',
  Textarea = 'Textarea',
  /**
   * 二维码和学生信息
   */
  AdmissionTicketNumberInfo = 'AdmissionTicketNumberInfo',
  /**
   * 先阅后扫，整个题块作为评分区，包括内容，一般评分模式也是ReadAndScan
   */
  ReadAndScan = 'ReadAndScan',
}

/**
 * 作者：张瀚
 * 说明：各个题块类型的名字
 */
export function getQuestionItemTypeEnumName(itemType: QuestionItemTypeEnum): string {
  switch (itemType) {
    case QuestionItemTypeEnum.Text:
      return "纯文本";
    case QuestionItemTypeEnum.Stem:
      return "Latex文本";
    case QuestionItemTypeEnum.Choice:
      return "选项";
    case QuestionItemTypeEnum.TrueOrFalse:
      return "判断题";
    case QuestionItemTypeEnum.FillInBlank:
      return "填空题";
    case QuestionItemTypeEnum.Textarea:
      return "简答题";
    case QuestionItemTypeEnum.ReadAndScan:
      return "手阅";
    default:
      return itemType
  }
}

/**
 * 作者：张瀚
 * 说明：基础配置对象
 */
export abstract class BaseQuestionItemConfig {
  questionItemType: QuestionItemTypeEnum;
  /**
   * 组件额外样式
   */
  style: CSSProperties;
  /**
   * 题号，可选项
   */
  questionNo?: string;
  /**
   * 题干或者文本内容，可选
   */
  text?: string;

  constructor() {
    this.questionItemType = QuestionItemTypeEnum.Text;
    this.style = {};
  }
}

/**
 * 作者：张瀚
 * 说明：无意义文本
 */
export class TextQuestionItemConfig extends BaseQuestionItemConfig {
  constructor(text: string) {
    super();
    this.questionItemType = QuestionItemTypeEnum.Text;
    this.text = text;
  }
}

/**
 * 作者：张瀚
 * 说明：题目用的文本
 */
export class StemQuestionItemConfig extends BaseQuestionItemConfig {
  constructor(text: string, questionNo?: string) {
    super();
    this.questionItemType = QuestionItemTypeEnum.Stem;
    this.text = text;
    this.questionNo = questionNo;
  }
}

export class ChoiceQuestionInfoQuestionItemConfig extends BaseQuestionItemConfig {
  /**
   * 选项列表
   */
  choices: Array<string>;
  /**
   * 选项是否横向显示
   */
  isHorizontal: boolean;

  constructor(choices: Array<string>, isHorizontal: boolean) {
    super();
    this.choices = choices;
    this.questionItemType = QuestionItemTypeEnum.Choice;
    this.isHorizontal = isHorizontal;
  }
}

/**
 * 作者：张瀚
 * 说明：判断题模块
 */
export class TrueOrFalseQuestionItemConfig extends BaseQuestionItemConfig {
  constructor(text: string, questionNo?: string) {
    super();
    this.questionItemType = QuestionItemTypeEnum.TrueOrFalse;
    this.text = text;
    this.questionNo = questionNo;
  }
}

/**
 * 作者：张瀚
 * 说明：填空题模块，里面包含了下划线
 */
export class FillInBlankQuestionItemConfig extends BaseQuestionItemConfig {
  //第一部分题块才有题号
  constructor(text: string, questionNo?: string) {
    super();
    this.questionItemType = QuestionItemTypeEnum.FillInBlank;
    this.text = text;
    this.questionNo = questionNo;
  }
}

/**
 * 作者：张瀚
 * 说明：先阅后扫，整体渲染出来不做处理但是整体做一个答题区
 */
export class ReadAndScanQuestionItemConfig extends BaseQuestionItemConfig {
  constructor(text: string, questionNo?: string) {
    super();
    this.questionItemType = QuestionItemTypeEnum.ReadAndScan;
    this.text = text;
    this.questionNo = questionNo;
  }
}

/**
 * 作者：张瀚
 * 说明：问答题模块，大片空白作答区
 */
export class TextareaQuestionItemConfig extends BaseQuestionItemConfig {
  /**
   * 空白区域输入方式，空白，下划线，方框
   */
  inputMode: 'none' | 'underLine' | 'block';
  /**
   * 作答区域每行多少字
   */
  answerAreaBlockNumber: number;
  /**
   * 总行数
   */
  lineTotal: number;

  constructor(inputMode: 'none' | 'underLine' | 'block', lineTotal: number, answerAreaBlockNumber: number = 20) {
    super();
    this.questionItemType = QuestionItemTypeEnum.Textarea;
    this.inputMode = inputMode;
    this.lineTotal = lineTotal;
    this.answerAreaBlockNumber = answerAreaBlockNumber;
  }
}

export class AdmissionTicketQRCodeMsgBean {
  paperId?: string;
  cardId?: string;
  studentNumber?: number;
  studentName?: string;
  studentClassName?: string;
  constructor(qrcodeMsg?: string) {
    try {
      Object.assign(this, JSON.parse(qrcodeMsg ?? ''))
    } catch { /* empty */ }
  }

}

export class AdmissionTicketNumberInfoQuestionItemConfig extends BaseQuestionItemConfig {
  /**
   * 考试名称
   */
  examName: string;
  /**
   * 模板名称
   */
  modelName: string;
  /**
   * 长度
   */
  size: number;
  /**
   * 二维码内容,undefined时不显示,内容是json字符串,AdmissionTicketQRCodeMsg格式
   */
  qrcodeMsg?: string;
  /**
   * 是否显示二维码
   */
  showQRCode: boolean;
  /**
   * 准考证号是否需要涂卡
   */
  needPaintCard: boolean;
  /**
   * 学号处文本信息
   */
  fillText: string;
  /**
   * 涂卡区域和准考证是不是横向显示
   */
  isHorizontal: boolean;

  constructor(size = 8, examName: string, qrcodeMsg?: string, modelName: string = '默认', moreStyle: CSSProperties = {}) {
    super();
    this.examName = examName;
    this.questionItemType = QuestionItemTypeEnum.AdmissionTicketNumberInfo;
    this.size = size;
    this.qrcodeMsg = qrcodeMsg;
    this.showQRCode = true;
    this.needPaintCard = false;
    this.fillText = '学号';
    this.isHorizontal = false;
    this.style = { ...this.style, display: 'inline-block', ...moreStyle };
    this.modelName = modelName;
  }

}

export interface PaperDataStoreProps {
  /**
   * 试卷数据,外部需要提供的，只用来初始化，后续处理都是修改副本
   */
  paperData: PaperData;
  setPaperData: (paperData: PaperData) => void;
  /**
   * 更新某个题块的渲染组件
   */
  updateQuestionItemNode: (data: QuestionItemData) => void;
  /**
   * 构建输出结果,从题块列表出发，然后只把用到的部分取出来
   */
  getPaperData: () => PaperData;
  //--------------------------从试卷数据中初始化的数据的副本
  answerCard: AnswerCardData;
  setAnswerCard: (data: AnswerCardData) => void;
  /**
   * 页面渲染的数据列表
   */
  pageDataList: PageData[];
  setPageDataList: (data: PageData[]) => void;
  //------------------------- 题目映射
  /**
  * 题目ID映射题块
  */
  questionIdToQuestionMap: Map<string, QuestionData>;
  //------------------------- 题块映射
  /**
   * 题块ID列表
   */
  questionItemIdList: Array<string>;
  /**
   * 题块ID映射题块
   */
  questionItemIdToQuestionItemMap: Map<string, QuestionItemData>;
  /**
   * 题块ID映射位置组ID列表
   */
  questionItemIdToBlockGroupIdListMap: Map<string, string[]>;
  /**
   * 题块ID映射渲染的组件模块ID列表
   */
  questionItemIdToBucketNodeIdListMap: Map<string, string[]>;
  /**
   * 临时性的题块ID映射评分标准ID，会在没有位置组映射评分标准的时候作为初始数据
   */
  questionItemIdToScoringCriteriaIdMap: Map<string, string>;
  //------------------------- 渲染组件映射
  /**
   * 渲染组件ID映射组件，渲染组件ID由题块ID和组件序号构成，中间用下划线隔开
   */
  bucketNodeIdToBucketNodeMap: Map<string, BucketNode>;
  /**
   * 渲染组件ID映射位置块ID列表
   */
  bucketNodeIdToBlockIdListMap: Map<string, string[]>;
  //------------------------- 位置组映射
  /**
   * 位置组ID映射位置组
   */
  blockGroupIdToBlockGroupMap: Map<string, AnswerBlockGroupData>;
  /**
   * 位置组ID映射评分标准ID
   */
  blockGroupIdToScoringCriteriaIdMap: Map<string, string>;
  /**
   * 位置组ID映射位置快ID列表
   */
  blockGroupIdToBlockIdListMap: Map<string, string[]>;
  //------------------------- 位置块映射
  /**
   * 位置块ID映射位置块
   */
  blockIdToBlockMap: Map<string, AnswerBlockData>;
  //------------------------- 评分标准映射
  /**
   * 评分标准ID映射评分标准
   */
  scoringCriteriaIdToScoringCriteriaMap: Map<string, ScoringCriteria>;
  //------------------------- 旧关联表ID
  /**
   * 旧的题块ID+位置组ID映射旧关联表ID
   */
  questionItemIdBlockGroupIdToLinkIdMap: Map<string, string>;
  /**
   * 旧的评分标准ID+位置组ID映射旧关联表ID
   */
  scoringCriteriaIdBlockGroupIdToLInkIdMap: Map<string, string>;
  /**
   * 增加一个第几页的题号区域
   */
  addAdmissionNumberBlockGroup: (pageNumber: number) => void;
  /**
   * 防呆是否已经渲染完并且计算好区域位置，所有的block都不为0才可以
   */
  isReadyToPrint: boolean;
  setIsReadyToPrint: (data: boolean) => void;
  /**
   * 更新评分标准并且触发刷新
   */
  updateScoringCriteria: (data: ScoringCriteria) => void;
  /**
   * 修改题块类型
   */
  changeQuestionItemType: (questionItem: QuestionItemData | undefined, newItemType: QuestionItemTypeEnum) => void
  //------------------------- 加载方法，按顺序自动联动来初始化数据,加载数据后会确保关联准确，移除多余的
  /**
   * 重置所有数据
   */
  reset: () => void
  /**
   * 开始加载
   */
  startLoad: (paperData: PaperData) => void
  /**
   * 加载题目，需要提供所有数据，会同步所有的关联关系
   */
  loadQuestionItem: (questionItem: QuestionItemData, blockGroupList: AnswerBlockGroupData[]) => void
  /**
   * 加载区域组
   */
  loadBlockGroup: (data: AnswerBlockGroupData[]) => void
  /**
   * 加载评分标准
   */
  loadScoringCriteria: (criteriaList: ScoringCriteria[], answer_block_group_scoring_criteria_list: AnswerBlockScoringCriteria[]) => void
}

/**
 * 作者：张瀚
 * 说明：纪录题卡组件使用的试卷、题目数据的缓存
 */
export function createPaperDataStore() {
  return create<PaperDataStoreProps>((set, get) => ({
    updateScoringCriteria(data) {
      const { scoringCriteriaIdToScoringCriteriaMap } = get();
      const newMap = new Map(scoringCriteriaIdToScoringCriteriaMap);
      if (data.scoring_type !== ScoringCriteriaTypeEnum.AI) {
        data.questionTips = undefined
      }
      newMap.set(data.id, data);
      set({ scoringCriteriaIdToScoringCriteriaMap: newMap });
    },
    updateQuestionItemNode(data) {
      const { loadQuestionItem, bucketNodeIdToBucketNodeMap, questionItemIdToBucketNodeIdListMap, questionItemIdToBlockGroupIdListMap, blockGroupIdToBlockGroupMap } = get();
      if (data.id.startsWith(admissionTicketNumberInfoIdEx)) {
        //二维码，每一个都要更新
        bucketNodeIdToBucketNodeMap.forEach((_, nodeId) => {
          const ids = nodeId.split('_');
          if (ids[0] === admissionTicketNumberInfoIdEx) {
            bucketNodeIdToBucketNodeMap.set(nodeId, getAdmissionTicketNumberComponentNode(`${ids[0]}_${ids[1]}`, data.config as AdmissionTicketNumberInfoQuestionItemConfig));
          }
        });
      } else {
        loadQuestionItem(data, questionItemIdToBlockGroupIdListMap.get(data.id)?.map(groupId => blockGroupIdToBlockGroupMap.get(groupId)).filter(it => it !== undefined) ?? [])
        // const nodeList = getNodeListByQuestionItem(data);
        // const newNodeIdList: string[] = [];
        // nodeList.forEach((node) => {
        //   const nodeId = `${data.id}_${node.serial_number}`;
        //   bucketNodeIdToBucketNodeMap.set(nodeId, node);
        //   newNodeIdList.push(nodeId);
        // });
        // questionItemIdToBucketNodeIdListMap.set(data.id, newNodeIdList);
      }
      const newMap = new Map(bucketNodeIdToBucketNodeMap);
      set({ bucketNodeIdToBucketNodeMap: newMap });
    },
    paperData: createDefaultPaperData(),
    reset() {
      set(() => ({
        isReadyToPrint: false,
        paperData: createDefaultPaperData(),
        answerCard: createDefaultAnswerCard(),
        questionItemIdList: [],
        questionItemIdToQuestionItemMap: new Map(),
        questionItemIdToBlockGroupIdListMap: new Map(),
        questionItemIdToBucketNodeIdListMap: new Map(),
        questionItemIdBlockGroupIdToLinkIdMap: new Map(),
        bucketNodeIdToBucketNodeMap: new Map(),
        bucketNodeIdToBlockIdListMap: new Map(),
        blockGroupIdToBlockGroupMap: new Map(),
        blockGroupIdToBlockIdListMap: new Map(),
        questionIdToQuestionMap: new Map(),
        blockIdToBlockMap: new Map(),
        blockGroupIdToScoringCriteriaIdMap: new Map(),
        scoringCriteriaIdBlockGroupIdToLInkIdMap: new Map(),
        scoringCriteriaIdToScoringCriteriaMap: new Map(),
      }));
    },
    startLoad(paperData) {
      const { questionItemIdBlockGroupIdToLinkIdMap, questionItemIdToBlockGroupIdListMap, questionIdToQuestionMap, blockGroupIdToBlockGroupMap, loadQuestionItem, loadBlockGroup, loadScoringCriteria } = get()
      const newPaperData: PaperData = JSON.parse(JSON.stringify(paperData));
      const { answer_card, question_data_list } = newPaperData.paper_content;
      const { question_item_block_group_list, block_group_list, scoring_criteria_list, answer_block_group_scoring_criteria_list } = answer_card;
      set({
        paperData: newPaperData,
        answerCard: answer_card
      })
      //--------从初始数据中加载（后续可能会覆盖或者解开关联）
      loadBlockGroup(block_group_list)
      loadScoringCriteria(scoring_criteria_list, answer_block_group_scoring_criteria_list)
      question_item_block_group_list.forEach(link => {
        const list = questionItemIdToBlockGroupIdListMap.get(link.question_item_id) ?? []
        if (!list.includes(link.block_group_id)) {
          list.push(link.block_group_id)
          questionItemIdToBlockGroupIdListMap.set(link.question_item_id, list)
        }
        questionItemIdBlockGroupIdToLinkIdMap.set(`${link.question_item_id}_${link.block_group_id}`, link.id)
      })
      //题块相关
      question_data_list.forEach(question => {
        questionIdToQuestionMap.set(question.id, question)
        question.question_content.forEach((questionItem) => {
          const { id } = questionItem
          const blockGroupList = questionItemIdToBlockGroupIdListMap.get(id)?.map(blockGroupId => {
            return blockGroupIdToBlockGroupMap.get(blockGroupId)
          }).filter(it => it != undefined) ?? []
          loadQuestionItem(questionItem, blockGroupList)
        });
      })
      //页码的位置组，每页都有但是只放第一页，所以是1组件1组,然后根据页码长度有多个块，动态创建
      const pageBlockGroup: AnswerBlockGroupData = block_group_list.find((it) => it.group_type === AnswerBlockGroupTypeEnum.Page) ?? {
        id: v4(),
        card_id: answer_card.id,
        serial_number: 0,
        block_list: [],
        group_type: AnswerBlockGroupTypeEnum.Page,
        mode: AnswerBlockGroupModeEnum.FillRandom,
      };
      blockGroupIdToBlockGroupMap.set(pageBlockGroup.id, pageBlockGroup);
      questionItemIdToBlockGroupIdListMap.set(`${pageIndexItemIdEx}_1`, [pageBlockGroup.id]);
    },
    loadQuestionItem(questionItem, blockGroupList) {
      const { answerCard, questionItemIdList, blockGroupIdToScoringCriteriaIdMap, bucketNodeIdToBlockIdListMap, questionItemIdToQuestionItemMap, blockGroupIdToBlockGroupMap, blockGroupIdToBlockIdListMap, blockIdToBlockMap, questionItemIdToBucketNodeIdListMap, bucketNodeIdToBucketNodeMap, questionItemIdToBlockGroupIdListMap, questionItemIdToScoringCriteriaIdMap, scoringCriteriaIdToScoringCriteriaMap } = get()
      const { id: questionItemId, config, scoring_criteria } = questionItem
      if (!questionItemIdList.includes(questionItemId)) {
        questionItemIdList.push(questionItemId)
      }
      questionItemIdToQuestionItemMap.set(questionItemId, questionItem)
      //初始评分标准
      if (scoring_criteria) {
        const { id, criteriaName } = scoring_criteria
        if (!criteriaName) {
          scoring_criteria.criteriaName = config.questionNo
        }
        scoringCriteriaIdToScoringCriteriaMap.set(id, scoring_criteria)
        questionItemIdToScoringCriteriaIdMap.set(questionItemId, id)
      }
      //位置组
      const groupIdList: string[] = []
      blockGroupList = checkBlockGroupList(questionItem, blockGroupList, answerCard.id)
      blockGroupList.forEach(blockGroup => {
        const { id: blockGroupId, block_list } = blockGroup
        blockGroupIdToBlockGroupMap.set(blockGroupId, blockGroup)
        groupIdList.push(blockGroupId)
        //位置块关联
        block_list.forEach(block => {
          const { id: blockId } = block
          blockIdToBlockMap.set(blockId, block)
        })
        blockGroupIdToBlockIdListMap.set(blockGroupId, block_list.map(it => it.id))
        //评分标准关联
        const criteriaId = blockGroupIdToScoringCriteriaIdMap.get(blockGroupId)
        if (!criteriaId) {
          //没关联评分标准的给新建一下,仅针对有需要的
          const newCriteria = createCriteria(questionItem)
          if (newCriteria) {
            const { id: criteriaId } = newCriteria
            scoringCriteriaIdToScoringCriteriaMap.set(criteriaId, newCriteria)
            blockGroupIdToScoringCriteriaIdMap.set(blockGroupId, criteriaId)
          }
        }
      })
      questionItemIdToBlockGroupIdListMap.set(questionItemId, groupIdList)
      //组件
      const bucketNodeList = getNodeListByQuestionItem(questionItem);
      const newBucketNodeIdList: string[] = []
      bucketNodeList.forEach((bucketNode) => {
        const { serial_number } = bucketNode;
        const bucketNodeId = getBucketNodeId(questionItemId, serial_number);
        bucketNodeIdToBucketNodeMap.set(bucketNodeId, bucketNode);
        newBucketNodeIdList.push(bucketNodeId);
      });
      questionItemIdToBucketNodeIdListMap.set(questionItemId, newBucketNodeIdList);
      //绑定组件和位置
      bindBlockAndBucketNode(questionItem, blockGroupList, bucketNodeIdToBlockIdListMap)
    },
    loadBlockGroup(blockGroupList) {
      const { blockGroupIdToBlockGroupMap, blockGroupIdToBlockIdListMap, blockIdToBlockMap } = get()
      blockGroupList.forEach(blockGroup => {
        const { id: blockGroupId, block_list } = blockGroup
        blockGroupIdToBlockGroupMap.set(blockGroupId, blockGroup)
        const blockIdList = blockGroupIdToBlockIdListMap.get(blockGroupId) ?? []
        block_list.forEach(block => {
          const { id: blockId } = block
          blockIdToBlockMap.set(blockId, block)
          if (!blockIdList.includes(blockId)) {
            blockIdList.push(blockId)
          }
        })
        blockGroupIdToBlockIdListMap.set(blockGroupId, blockIdList)
      })
    },
    loadScoringCriteria(criteriaList, links) {
      const { scoringCriteriaIdToScoringCriteriaMap, blockGroupIdToScoringCriteriaIdMap, scoringCriteriaIdBlockGroupIdToLInkIdMap } = get()
      criteriaList.forEach(criteria => {
        const { id } = criteria
        scoringCriteriaIdToScoringCriteriaMap.set(id, criteria)
      })
      links.forEach(link => {
        const { id, scoring_criteria_id, block_group_id } = link
        blockGroupIdToScoringCriteriaIdMap.set(block_group_id, scoring_criteria_id)
        scoringCriteriaIdBlockGroupIdToLInkIdMap.set(`${scoring_criteria_id}_${block_group_id}`, id)
      })
    },
    setPaperData(data) {
      const { reset, startLoad } = get()
      //先重置数据
      reset()
      startLoad(data)
    },
    getPaperData() {
      const {
        paperData,
        answerCard,
        questionItemIdToQuestionItemMap,
        questionItemIdToBlockGroupIdListMap,
        blockGroupIdToBlockGroupMap,
        blockGroupIdToScoringCriteriaIdMap,
        scoringCriteriaIdToScoringCriteriaMap,
        blockGroupIdToBlockIdListMap,
        blockIdToBlockMap,
        bucketNodeIdToBlockIdListMap,
        questionIdToQuestionMap
      } = get();
      const newPaperData: PaperData = JSON.parse(JSON.stringify(paperData));
      const newAnswerCard: AnswerCardData = JSON.parse(JSON.stringify(answerCard));
      const { id: answerCardId, pos_point_height, pos_point_width, show_pos_point, show_page_index, x, y } = newAnswerCard;
      const { paper_content } = newPaperData;
      paper_content.answer_card = newAnswerCard;
      //-----------新数据
      let pageTotal = 0;
      const newBlockGroupList: AnswerBlockGroupData[] = [];
      const newQuestionItemBlockGroupDataList: QuestionItemBlockGroupData[] = [];
      const newAnswerBlockGroupScoringCriteriaList: AnswerBlockScoringCriteria[] = [];
      const newScoringCriteriaList: ScoringCriteria[] = [];
      paper_content.question_data_list.forEach((question) => {
        //更新题目数据
        Object.assign(question, questionIdToQuestionMap.get(question.id) ?? {})
        question.question_content.forEach((questionItem) => {
          //更新题块数据
          Object.assign(questionItem, questionItemIdToQuestionItemMap.get(questionItem.id) ?? {});
          const { id, item_type, config } = questionItem;
          const blockGroupIdList = questionItemIdToBlockGroupIdListMap.get(id);
          if (!blockGroupIdList) {
            return;
          }
          const blockGroupList = blockGroupIdList.map((blockGroupId) => blockGroupIdToBlockGroupMap.get(blockGroupId)).filter((it) => it !== undefined);
          if (!blockGroupList) {
            return;
          }
          //如果是解答题，要合并同桶同页的块为单独的块
          switch (item_type) {
            case QuestionItemTypeEnum.Textarea: {
              blockGroupList.forEach((blockGroup) => {
                const { id: blockGroupId } = blockGroup;
                const blockIdList = blockGroupIdToBlockIdListMap.get(blockGroupId);
                if (!blockIdList) {
                  return;
                }
                const blockList = blockIdList.map((blockId) => blockIdToBlockMap.get(blockId)).filter((it) => it != undefined);
                if (blockList.length > 0) {
                  let newBlockList: AnswerBlockData[] = [];
                  let firstBlock: AnswerBlockData = blockList[0];
                  let lastBlock: AnswerBlockData = blockList[0];
                  //根据当前行数只要前一部分而不是所有
                  let lineTotal = Math.min((config as TextareaQuestionItemConfig).lineTotal, blockList.length);
                  for (let i = 1; i < lineTotal; i++) {
                    const block = blockList[i];
                    const { page_number, x, width } = block;
                    const { page_number: fp, x: fx, y: fy } = firstBlock;
                    const { y: ly, height: lh } = lastBlock;
                    //更新页码的最大值
                    pageTotal = Math.max(page_number, pageTotal);
                    //如果x变了，或者页码变了，就合并第一个到上一个
                    if (fp !== page_number || fx != x) {
                      newBlockList.push({
                        id: v4(),
                        group_id: blockGroup.id,
                        page_number: fp,
                        width,
                        height: ly + lh - fy,
                        x: fx,
                        y: fy,
                        serial_number: newBlockList.length,
                      });
                      firstBlock = block;
                    }
                    lastBlock = block;
                  }
                  //收尾
                  const { page_number: fp, x: fx, y: fy, width: fw } = firstBlock;
                  const { y: ly, height: lh } = lastBlock;
                  newBlockList.push({
                    id: v4(),
                    group_id: blockGroup.id,
                    page_number: fp,
                    width: fw,
                    height: ly + lh - fy,
                    x: fx,
                    y: fy,
                    serial_number: newBlockList.length,
                  });
                  blockGroup.block_list = newBlockList;
                }
              });
              break
            }
            default:
              blockGroupList.forEach((blockGroup) => {
                const { id: blockGroupId } = blockGroup;
                const blockIdList = blockGroupIdToBlockIdListMap.get(blockGroupId);
                if (!blockIdList) {
                  return;
                }
                blockGroup.block_list = blockIdList.map(id => blockIdToBlockMap.get(id)).filter(it => it != undefined)
              })
              break
          }
          //加入到结果中
          blockGroupList.forEach((blockGroup) => {
            const blockList = blockGroup.block_list;
            blockList.forEach((b) => {
              //更新页码的最大值
              pageTotal = Math.max(b.page_number, pageTotal);
            });
            const tempBlockGroupList: AnswerBlockGroupData[] = [];
            switch (item_type) {
              //把解答题的组拆分为1块1组但是评分标准相同
              case QuestionItemTypeEnum.Textarea:
                blockList.forEach((block) => {
                  const newBlockGroup: AnswerBlockGroupData = {
                    id: v4(),
                    card_id: answerCard.id,
                    serial_number: 0,
                    block_list: [block],
                    group_type: AnswerBlockGroupTypeEnum.HandwriteQuestion,
                    mode: AnswerBlockGroupModeEnum.AnswerArea,
                  };
                  block.group_id = newBlockGroup.id;
                  tempBlockGroupList.push(newBlockGroup);
                });
                break;
              default:
                tempBlockGroupList.push(blockGroup);
                break;
            }
            //创建关联关系
            tempBlockGroupList.forEach((newBlockGroup) => {
              //关联关系
              newQuestionItemBlockGroupDataList.push({
                id: v4(),
                question_item_id: id,
                block_group_id: newBlockGroup.id,
              });
              //评分标准，复用旧的
              const scoringCriteriaId = blockGroupIdToScoringCriteriaIdMap.get(blockGroup.id);
              const scoringCriteria = scoringCriteriaIdToScoringCriteriaMap.get(scoringCriteriaId ?? '');
              if (scoringCriteria) {
                newAnswerBlockGroupScoringCriteriaList.push({
                  id: v4(),
                  scoring_criteria_id: scoringCriteria.id,
                  block_group_id: newBlockGroup.id,
                });
                newScoringCriteriaList.push(scoringCriteria);
              }
            });
            newBlockGroupList.push(...tempBlockGroupList);
          });
        });
      });
      //奇数页的题号组件的定位点组
      for (let i = 1; i <= pageTotal; i += 2) {
        const itemId = `${admissionTicketNumberInfoIdEx}_${i}`;
        //二维码和学号分开两个，里面各有1块
        const blockGroupIdList = questionItemIdToBlockGroupIdListMap.get(itemId) ?? [];
        blockGroupIdList.forEach(blockGroupId => {
          const blockGroup = blockGroupIdToBlockGroupMap.get(blockGroupId)
          if (!blockGroup) {
            return
          }
          const { group_type } = blockGroup
          switch (group_type) {
            case AnswerBlockGroupTypeEnum.CardNo:
              {
                const blockList = bucketNodeIdToBlockIdListMap.get(`${itemId}_0`)
                  ?.map((it) => blockIdToBlockMap.get(it))
                  .filter((it) => it !== undefined) ?? []
                //过滤掉无效的，然后取第一个有效数据
                blockGroup.block_list = blockList.filter((item) => item.width > 0 && item.height > 0).slice(0, 1);
                newBlockGroupList.unshift(blockGroup);
              }
              break
            case AnswerBlockGroupTypeEnum.Number:
              {
                const blockList = bucketNodeIdToBlockIdListMap.get(`${itemId}_1`)
                  ?.map((it) => blockIdToBlockMap.get(it))
                  .filter((it) => it !== undefined) ?? []
                //过滤掉无效的，然后取第一个有效数据
                blockGroup.block_list = blockList.filter(item => item.width > 0 && item.height > 0).slice(0, 1);
                newBlockGroupList.unshift(blockGroup);
              }
              break
          }
        })
      }
      //页码
      if (show_page_index) {
        const blockGroupIdList = questionItemIdToBlockGroupIdListMap.get(`${pageIndexItemIdEx}_1`) ?? [];
        const blockGroupList = blockGroupIdList.map((blockGroupId) => blockGroupIdToBlockGroupMap.get(blockGroupId)).filter((it) => it !== undefined);
        if (blockGroupList && blockGroupList.length > 0) {
          newBlockGroupList.unshift(blockGroupList[0]);
          blockGroupList.forEach((blockGroup) => {
            blockGroup.block_list =
              bucketNodeIdToBlockIdListMap
                .get(`${pageIndexItemIdEx}_1_0`)
                ?.map((it) => blockIdToBlockMap.get(it))
                .filter((it) => it !== undefined) ?? [];
          });
        }
      }
      //增加唯一的定位点组
      if (show_pos_point) {
        const locationBlockGroup: AnswerBlockGroupData = {
          id: v4(),
          card_id: answerCardId,
          serial_number: 0,
          block_list: [],
          group_type: AnswerBlockGroupTypeEnum.Location,
          mode: AnswerBlockGroupModeEnum.Block,
        };
        locationBlockGroup.block_list.push({
          id: v4(),
          group_id: locationBlockGroup.id,
          page_number: 1,
          width: pos_point_width,
          height: pos_point_height,
          x: x,
          y: y,
          serial_number: 0,
        });
        newBlockGroupList.unshift(locationBlockGroup);
      }
      //修正排序
      newBlockGroupList.forEach((blockGroup, i) => {
        blockGroup.serial_number = i;
        //联查块
        if (blockGroup.block_list.length === 0) {
          blockGroup.block_list =
            blockGroupIdToBlockIdListMap
              .get(blockGroup.id)
              ?.map((it) => blockIdToBlockMap.get(it))
              .filter((it) => it !== undefined) ?? [];
        }
      });
      //更新值
      newAnswerCard.page_total = pageTotal;
      newAnswerCard.block_group_list = newBlockGroupList;
      newAnswerCard.question_item_block_group_list = newQuestionItemBlockGroupDataList;
      newAnswerCard.answer_block_group_scoring_criteria_list = newAnswerBlockGroupScoringCriteriaList;
      newAnswerCard.scoring_criteria_list = newScoringCriteriaList;
      return newPaperData;
    },
    answerCard: createDefaultAnswerCard(),
    setAnswerCard(data) {
      set(() => ({ answerCard: data }));
    },
    pageDataList: [],
    setPageDataList(data) {
      set(() => ({ pageDataList: data }));
    },
    questionItemIdList: [],
    questionItemIdToQuestionItemMap: new Map(),
    questionItemIdToBlockGroupIdListMap: new Map(),
    questionItemIdToBucketNodeIdListMap: new Map(),
    bucketNodeIdToBucketNodeMap: new Map(),
    blockGroupIdToBlockGroupMap: new Map(),
    scoringCriteriaIdToScoringCriteriaMap: new Map(),
    questionItemIdToBlockGroupListMap: new Map(),
    scoringCriteriaIdBlockGroupIdToLInkIdMap: new Map(),
    blockGroupIdToScoringCriteriaMap: new Map(),
    questionIdToQuestionMap: new Map(),
    questionItemIdBlockGroupIdToLinkIdMap: new Map(),
    blockGroupIdToScoringCriteriaIdMap: new Map(),
    questionItemIdToScoringCriteriaMap: new Map(),
    bucketNodeIdToBlockIdListMap: new Map(),
    blockGroupIdToBlockIdListMap: new Map(),
    questionItemIdToScoringCriteriaIdMap: new Map(),
    blockIdToBlockMap: new Map(),
    bucketNodeIdToBlockListMap: new Map(),
    addAdmissionNumberBlockGroup(pageNumber) {
      const {
        answerCard,
        bucketNodeIdToBucketNodeMap,
        questionItemIdToBucketNodeIdListMap,
        questionItemIdToBlockGroupIdListMap,
        blockGroupIdToBlockGroupMap,
        blockIdToBlockMap,
        blockGroupIdToBlockIdListMap,
        bucketNodeIdToBlockIdListMap,
        questionItemIdToQuestionItemMap,
      } = get();
      const { admissionTicketNumberInfoQuestionItemConfig } = answerCard;
      //一个组件，2个位置组（二维码、学号），每个组内一个块
      const itemId = `${admissionTicketNumberInfoIdEx}_${pageNumber}`;
      questionItemIdToQuestionItemMap.set(itemId, {
        id: itemId,
        question_id: v4(),
        item_type: QuestionItemTypeEnum.AdmissionTicketNumberInfo,
        config: admissionTicketNumberInfoQuestionItemConfig,
      });
      const nodeId = `${itemId}_0`;
      //创建组件
      const newNode = bucketNodeIdToBucketNodeMap.get(nodeId) ?? getAdmissionTicketNumberComponentNode(`${itemId}`, admissionTicketNumberInfoQuestionItemConfig);
      bucketNodeIdToBucketNodeMap.set(nodeId, newNode);
      questionItemIdToBucketNodeIdListMap.set(itemId, [nodeId]);
      const blockGroupIdList = questionItemIdToBlockGroupIdListMap.get(itemId) ?? [];
      //用到的位置组
      const blockIdList: string[] = [];
      //二维码的位置组
      const qrCodeId = blockGroupIdList[0];
      let qrCodeBlockGroup = blockGroupIdToBlockGroupMap.get(qrCodeId);
      if (!qrCodeBlockGroup) {
        //新建
        qrCodeBlockGroup = {
          id: v4(),
          card_id: answerCard.id,
          serial_number: 0,
          block_list: [],
          group_type: AnswerBlockGroupTypeEnum.CardNo,
          mode: AnswerBlockGroupModeEnum.Qrcode,
        };
        blockGroupIdList[0] = qrCodeBlockGroup.id;
        blockGroupIdToBlockGroupMap.set(qrCodeBlockGroup.id, qrCodeBlockGroup);
      }
      //检查二维码的位置块是不是存在，不在就新建
      const qrcodeBlockIdList = blockGroupIdToBlockIdListMap.get(qrCodeBlockGroup.id)
      if (!qrcodeBlockIdList || qrcodeBlockIdList.length !== 1) {
        //新建位置块
        const newBlock: AnswerBlockData = {
          id: v4(),
          group_id: qrCodeBlockGroup.id,
          page_number: pageNumber,
          width: 0,
          height: 0,
          x: 0,
          y: 0,
          serial_number: 0,
        };
        blockIdToBlockMap.set(newBlock.id, newBlock);
        blockGroupIdToBlockIdListMap.set(qrCodeBlockGroup.id, [newBlock.id]);
        blockIdList.push(newBlock.id);
      } else {
        //更新块的ID列表
        blockIdList.push(qrcodeBlockIdList[0]);
      }
      //学号位置组
      const studentNumberId = blockGroupIdList[1];
      let studentNumberBlockGroup = blockGroupIdToBlockGroupMap.get(studentNumberId);
      if (!studentNumberBlockGroup) {
        //新建
        studentNumberBlockGroup = {
          id: v4(),
          card_id: answerCard.id,
          serial_number: 0,
          block_list: [],
          group_type: AnswerBlockGroupTypeEnum.Number,
          mode: AnswerBlockGroupModeEnum.Ocr,
        };
        blockGroupIdList[1] = studentNumberBlockGroup.id;
        blockGroupIdToBlockGroupMap.set(studentNumberBlockGroup.id, studentNumberBlockGroup);
      }
      //检查学号的位置块是不是存在，不在就新建
      const studentNumberBlockIdList = blockGroupIdToBlockIdListMap.get(studentNumberBlockGroup.id)
      if (!studentNumberBlockIdList || studentNumberBlockIdList.length !== 1) {
        //新建位置块
        const newBlock: AnswerBlockData = {
          id: v4(),
          group_id: studentNumberBlockGroup.id,
          page_number: pageNumber,
          width: 0,
          height: 0,
          x: 0,
          y: 0,
          serial_number: 0,
        };
        blockIdToBlockMap.set(newBlock.id, newBlock);
        blockGroupIdToBlockIdListMap.set(studentNumberBlockGroup.id, [newBlock.id]);
        blockIdList.push(newBlock.id);
      } else {
        //更新块的ID列表
        blockIdList.push(studentNumberBlockIdList[0]);
      }
      questionItemIdToBlockGroupIdListMap.set(itemId, blockGroupIdList.slice(0, 2));
      bucketNodeIdToBlockIdListMap.set(nodeId, blockIdList);
    },
    isReadyToPrint: false,
    setIsReadyToPrint(data) {
      if (data !== get().isReadyToPrint) {
        set({ isReadyToPrint: data });
      }
    },
    changeQuestionItemType(questionItem, newItemType) {
      if (!questionItem) {
        return
      }
      //更新题目
      const { questionItemIdToQuestionItemMap, questionItemIdToBlockGroupIdListMap, questionItemIdToBucketNodeIdListMap, bucketNodeIdToBucketNodeMap } = get()
      const { id, config } = questionItem
      //新建config，如果不允许修改则用旧的
      const newConfig = (() => {
        //用config的item避免错误，先取出有用的部分
        switch (config.questionItemType) {
          case QuestionItemTypeEnum.ReadAndScan: {
            const nowConfig = config as ReadAndScanQuestionItemConfig
            switch (newItemType) {
              case QuestionItemTypeEnum.Text:
                //删除关联
                questionItemIdToBlockGroupIdListMap.delete(id)
                return {
                  ...nowConfig,
                  questionItemType: QuestionItemTypeEnum.Text,
                } satisfies TextQuestionItemConfig
              case QuestionItemTypeEnum.Stem:
                //删除关联
                questionItemIdToBlockGroupIdListMap.delete(id)
                return {
                  ...nowConfig,
                  questionItemType: QuestionItemTypeEnum.Stem,
                } satisfies StemQuestionItemConfig
              default:
                return config
            }
          }
          case QuestionItemTypeEnum.Text: {
            const nowConfig = config as TextQuestionItemConfig
            switch (newItemType) {
              case QuestionItemTypeEnum.Stem:
                //删除关联
                questionItemIdToBlockGroupIdListMap.delete(id)
                return {
                  ...nowConfig,
                  questionItemType: QuestionItemTypeEnum.Stem,
                } satisfies StemQuestionItemConfig
              default:
                return config
            }
          }
          case QuestionItemTypeEnum.Stem: {
            const nowConfig = config as StemQuestionItemConfig
            switch (newItemType) {
              case QuestionItemTypeEnum.Text:
                //删除关联
                questionItemIdToBlockGroupIdListMap.delete(id)
                return {
                  ...nowConfig,
                  questionItemType: QuestionItemTypeEnum.Text,
                } satisfies TextQuestionItemConfig
              default:
                return config
            }
          }
          default:
            return config
        }
      })()
      const newQuestionItem = { ...questionItem, config: newConfig, item_type: newConfig?.questionItemType }
      questionItemIdToQuestionItemMap.set(id, newQuestionItem)
      const newMap = new Map(questionItemIdToQuestionItemMap);
      //更新生成节点
      const nodeList = getNodeListByQuestionItem(newQuestionItem)
      questionItemIdToBucketNodeIdListMap.set(id, nodeList.map(node => getBucketNodeId(node.itemId, node.serial_number)))
      nodeList.forEach(node => {
        bucketNodeIdToBucketNodeMap.set(getBucketNodeId(node.itemId, node.serial_number), node)
      })
      set({ questionItemIdToQuestionItemMap: newMap })
    }
  }));
}

function createDefaultPaperData(): PaperData {
  return {
    id: '',
    paper_name: '无数据',
    paper_content: {
      question_data_list: [],
      answer_card: createDefaultAnswerCard(),
    },
  };
}

function createDefaultAnswerCard(): AnswerCardData {
  return {
    id: v4(),
    paper_id: v4(),
    dpi: 96,
    width: 792,
    height: 1123,
    x: 20,
    y: 40,
    page_orientation_is_vertical: true,
    bucket_size: 1,
    show_page_index: true,
    show_pos_point: true,
    block_group_list: [],
    question_item_block_group_list: [],
    answer_block_group_scoring_criteria_list: [],
    scoring_criteria_list: [],
    page_total: 0,
    pos_point_width: 20,
    pos_point_height: 10,
    admissionTicketNumberInfoQuestionItemConfig: new AdmissionTicketNumberInfoQuestionItemConfig(10, '默认答题卡'),
    right: 20,
    bottom: 40,
  };
}

export const PaperDataStoreContext = createContext<StoreApi<PaperDataStoreProps>>(createPaperDataStore());

/**
 * 作者：张瀚
 * 说明：对于渲染出来的节点，id由所属题块ID和在题块的渲染对象内的序号
 */
export const getBucketNodeId = (itemId?: string, serial_number?: number) => {
  return `${itemId ?? ''}_${serial_number ?? ''}`
}

/**
 * 作者：张瀚
 * 说明：检查并且修正指定类型的题块应该有的位置组
 */
function checkBlockGroupList(questionItem: QuestionItemData, blockGroupList: AnswerBlockGroupData[], answerCardId: string): AnswerBlockGroupData[] {
  const targetBlockGroupLength = getBlockGroupLength(questionItem)
  const targetBlockLength = getBlockLength(questionItem)
  while (blockGroupList.length < targetBlockGroupLength) {
    blockGroupList.push(createNewBlockGroup(questionItem, answerCardId))
  }
  blockGroupList = blockGroupList.slice(0, targetBlockGroupLength)
  blockGroupList.forEach((blockGroup, blockGroupIndex) => {
    blockGroup.serial_number = blockGroupIndex
    const { id, block_list } = blockGroup
    while (block_list.length < targetBlockLength) {
      block_list.push({
        id: v4(),
        group_id: id,
        page_number: 0,
        width: 0,
        height: 0,
        x: 0,
        y: 0,
        serial_number: 0
      })
    }
    blockGroup.block_list = block_list.slice(0, targetBlockLength)
    blockGroup.block_list.forEach((block, blockIndex) => {
      block.serial_number = blockIndex
    })
  })
  return blockGroupList
}

/**
 * 作者：张瀚
 * 说明：每个类型的题型应该有多少个位置组
 */
function getBlockGroupLength(questionItem: QuestionItemData) {
  const { item_type, config } = questionItem
  switch (item_type) {
    case QuestionItemTypeEnum.Choice:
    case QuestionItemTypeEnum.TrueOrFalse:
    case QuestionItemTypeEnum.ReadAndScan:
    case QuestionItemTypeEnum.Textarea:
      return 1
    case QuestionItemTypeEnum.FillInBlank: {
      //填空题:1组件，有多少个空就有多少个位置组，每个位置组内是一个位置块
      const nowConfig = config as FillInBlankQuestionItemConfig;
      const { text } = nowConfig;
      return text?.match(fillInBlankRegex)?.length ?? 0;
    }
    default:
      return 0
  }
}
/**
 * 作者：张瀚
 * 说明：每个类型的题型的组内应该有多少个块
 */
function getBlockLength(questionItem: QuestionItemData) {
  const { item_type, config } = questionItem
  switch (item_type) {
    case QuestionItemTypeEnum.Choice: {
      //选项：1组件，1位置组，选项数量的位置块
      const nowConfig = config as ChoiceQuestionInfoQuestionItemConfig;
      const { choices } = nowConfig;
      return choices.length;
    }
    case QuestionItemTypeEnum.TrueOrFalse:
      return 2
    case QuestionItemTypeEnum.ReadAndScan:
    case QuestionItemTypeEnum.FillInBlank:
      return 1
    case QuestionItemTypeEnum.Textarea: {
      const nowConfig = config as TextareaQuestionItemConfig;
      const { lineTotal } = nowConfig;
      return lineTotal
    }
    default:
      return 0
  }
}

/**
 * 作者：张瀚
 * 说明：创建新的组
 */
function createNewBlockGroup(questionItem: QuestionItemData, answerCardId: string): AnswerBlockGroupData {
  const { item_type } = questionItem
  const blockGroup: AnswerBlockGroupData = {
    id: v4(),
    card_id: answerCardId,
    serial_number: 0,
    block_list: [],
    group_type: AnswerBlockGroupTypeEnum.FillChoice,
    mode: AnswerBlockGroupModeEnum.FillRandom,
  };
  switch (item_type) {
    case QuestionItemTypeEnum.Choice:
      blockGroup.group_type = AnswerBlockGroupTypeEnum.FillChoice;
      blockGroup.mode = AnswerBlockGroupModeEnum.FillRandom;
      break
    case QuestionItemTypeEnum.TrueOrFalse:
      blockGroup.group_type = AnswerBlockGroupTypeEnum.FillTrueOrFalse;
      blockGroup.mode = AnswerBlockGroupModeEnum.FillRandom;
      break
    case QuestionItemTypeEnum.FillInBlank:
    case QuestionItemTypeEnum.Textarea:
    case QuestionItemTypeEnum.ReadAndScan:
      blockGroup.group_type = AnswerBlockGroupTypeEnum.HandwriteQuestion;
      blockGroup.mode = AnswerBlockGroupModeEnum.AnswerArea;
      break
  }
  return blockGroup
}

/**
 * 作者：张瀚
 * 说明：创建新评分标准
 */
function createCriteria(questionItem: QuestionItemData): ScoringCriteria | undefined {
  const initScoringCriteria: ScoringCriteria = {
    id: v4(),
    scoring_type: ScoringCriteriaTypeEnum.None
  }
  switch (questionItem.item_type) {
    case QuestionItemTypeEnum.Choice:
      initScoringCriteria.scoring_type = ScoringCriteriaTypeEnum.Match;
      initScoringCriteria.mode = ScoringCriteriaModeEnum.Exact;
      break;
    case QuestionItemTypeEnum.TrueOrFalse:
      initScoringCriteria.scoring_type = ScoringCriteriaTypeEnum.Match;
      initScoringCriteria.mode = ScoringCriteriaModeEnum.Exact;
      break
    case QuestionItemTypeEnum.FillInBlank:
      initScoringCriteria.scoring_type = ScoringCriteriaTypeEnum.AI;
      initScoringCriteria.mode = ScoringCriteriaModeEnum.None;
      break
    case QuestionItemTypeEnum.Textarea:
      initScoringCriteria.scoring_type = ScoringCriteriaTypeEnum.AI;
      initScoringCriteria.mode = ScoringCriteriaModeEnum.None;
      break
    case QuestionItemTypeEnum.ReadAndScan:
      initScoringCriteria.scoring_type = ScoringCriteriaTypeEnum.Manual;
      initScoringCriteria.mode = ScoringCriteriaModeEnum.None;
      break
    default:
      return undefined
  }
}

/**
 * 作者：张瀚
 * 说明：绑定位置和组件（组件数量都是已知的）
 */
function bindBlockAndBucketNode(questionItem: QuestionItemData, blockGroupList: AnswerBlockGroupData[], bucketNodeIdToBlockIdListMap: Map<string, string[]>) {
  const { item_type, id: questionItemId } = questionItem
  const blockIdList = blockGroupList.map(blockGroup => blockGroup.block_list.map(block => block.id)).flat()
  switch (item_type) {
    case QuestionItemTypeEnum.Choice:
    case QuestionItemTypeEnum.TrueOrFalse:
    case QuestionItemTypeEnum.ReadAndScan:
      //1组件1组1至多块的，组件和组一样映射
      bucketNodeIdToBlockIdListMap.set(getBucketNodeId(questionItemId, 0), blockIdList);
      break;
    case QuestionItemTypeEnum.FillInBlank:
      //1组件多组，组内1块，全关联到组件
      bucketNodeIdToBlockIdListMap.set(getBucketNodeId(questionItemId, 0), blockIdList);
      break;
    case QuestionItemTypeEnum.Textarea:
      //只有1组，多组件，组内多块的，按顺序关联组件和块
      blockIdList.forEach((blockId, i) => {
        bucketNodeIdToBlockIdListMap.set(getBucketNodeId(questionItemId, i), [blockId]);
      });
      break;
  }
}