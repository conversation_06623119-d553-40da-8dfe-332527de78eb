import { create } from 'zustand';
import { QuestionItemData } from './paperDataStore';

export interface Workflow { workflow_name: string, workflow_id: string, description: string, workflow_type: string }
export interface QuestionType { code: string, type_name: string }
export interface DataCallbackStoreProps {
    /**
     * 获取题块能选择的ocr工作流列表
     */
    getOcrWorkflowList: (item: QuestionItemData) => Promise<Workflow[]>;
    /**
     * 获取题块能选择的评阅工作流列表
     */
    getCheckWorkflowList: (item: QuestionItemData) => Promise<Workflow[]>;
    /**
     * 获取题块能使用的题型列表
     */
    getQuestionTypeList: (item: QuestionItemData) => Promise<QuestionType[]>;
    /**
     * 设置所有回调
     */
    setAllCallback: (data: {
        getOcrWorkflowList: (item: QuestionItemData) => Promise<Workflow[]>,
        getCheckWorkflowList: (item: QuestionItemData) => Promise<Workflow[]>,
        getQuestionTypeList: (item: QuestionItemData) => Promise<QuestionType[]>
    }) => void
}

/**
 * 作者：张瀚
 * 说明：组件内需要数据时调用的回调
 */
export function createDataCallbackStore() {
    return create<DataCallbackStoreProps>((set) => ({
        getOcrWorkflowList() {
            return new Promise<Workflow[]>((resolve) => {
                resolve([])
            })
        },
        getCheckWorkflowList() {
            return new Promise<Workflow[]>((resolve) => {
                resolve([])
            })
        },
        getQuestionTypeList() {
            return new Promise<QuestionType[]>((resolve) => {
                resolve([])
            })
        },
        setAllCallback(data) {
            set({ ...data })
        }
    }));
}
