import { create } from 'zustand';
import { BucketNode } from '../compontents/canvas.visible-layer';
import { AnswerBlockData } from './paperDataStore';

export interface ComponentDataListStoreProps {
  /**
   * 当前正在编辑中的组件所属questionItem
   */
  nowEditingQuestionItemId?: string;
  setNowEditingQuestionItemId: (data?: string) => void;
  /**
   * 当前编辑中的位置区域
   */
  nowEditingBlock?: AnswerBlockData
  setNowEditingBlock: (data?: AnswerBlockData) => void
  /**
   * 当前编辑中的节点
   */
  nowEditingNode?: BucketNode
  setNowEditingNode: (data?: BucketNode) => void
  /**
   * 桶的高度
   */
  bucketHeight: number;
  /**
   * 更新桶高度
   */
  setBucketHeight: (height: number) => void;
  /**
   * 组件ID和它渲染的dom的映射
   */
  bucketNodeIdToRealDomMap: Map<string, HTMLElement>;
  /**
   * 组件ID映射高度
   */
  bucketNodeIdToDomHeightMap: Map<string, number>;
  /**
   * 更新组件对应的dom元素
   */
  updateBucketNodeDom: (bucketNodeId: string, dom: HTMLElement) => void;
  /**
   * 更新所有组件对应的dom的高度
   */
  refreshAllBucketNodeDomHeight: () => void;
}

export const admissionTicketNumberInfoIdEx = 'admissionTicketNumberInfo';
export const pageIndexItemIdEx = 'pageIndexItemId';

/**
 * 作者：张瀚
 * 说明：当前渲染用的页面内数据
 */
export function createComponentDataListStore() {
  return create<ComponentDataListStoreProps>((set, get) => ({
    setNowEditingQuestionItemId(data) {
      return set(() => ({ nowEditingQuestionItemId: data }));
    },
    bucketHeight: 0,
    setBucketHeight(height) {
      set(() => ({ bucketHeight: height }));
    },
    setNowEditingBlock(data) {
      set(() => ({ nowEditingBlock: data }))
    },
    setNowEditingNode(data) {
      set(() => ({ nowEditingNode: data }))
    },
    bucketNodeIdToRealDomMap: new Map(),
    bucketNodeIdToDomHeightMap: new Map(),
    updateBucketNodeDom(bucketNodeId, dom) {
      if (!dom) {
        return;
      }
      const { bucketNodeIdToRealDomMap } = get();
      bucketNodeIdToRealDomMap.set(bucketNodeId, dom);
    },
    refreshAllBucketNodeDomHeight() {
      const { bucketNodeIdToRealDomMap, bucketNodeIdToDomHeightMap } = get();
      bucketNodeIdToRealDomMap.forEach((el, bucketNodeId) => {
        const { height } = el.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(el);
        const { marginTop, marginBottom } = computedStyle;
        const realHeight = height + parseFloat(marginTop) + parseFloat(marginBottom);
        if (realHeight > 0) {
          bucketNodeIdToDomHeightMap.set(bucketNodeId, realHeight);
        }
      });
    },
  }));
}
