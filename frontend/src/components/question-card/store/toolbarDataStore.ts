import { create } from 'zustand';

export interface ToolbarDataStoreProps {
  /**
   * 是否显示扫阅区域
   */
  showAnswerBox: boolean;
  /**
   * 设置是否显示扫阅区域
   */
  setShowAnswerBox: (value: boolean) => void;
  /**
   * 作答区域的版本号，用于触发刷新
   */
  answerBoxVersion: string;
  setAnswerBoxVersion: (data: string) => void;
  /**
   * 调用打印页面的方法
   */
  printFunc: () => void;
  setPrintFunc: (func: () => void) => void;
  /**
   * 为所有页面设置的背景图，临时用 FIXME:
   */
  pageBackground?: string
  setPageBackground: (data?: string) => void
  showPageBackground: boolean,
  setShowPageBackground: (data: boolean) => void
}

/**
 * 作者：张瀚
 * 说明：创建一个和工具箱有关的数据对象
 */
export function createToolbarDataStore() {
  return create<ToolbarDataStoreProps>((set, get) => ({
    showAnswerBox: false,
    setShowAnswerBox(value = false) {
      set(() => ({ showAnswerBox: value }));
    },
    printFunc: () => { },
    setPrintFunc: (func: () => void) => set(() => ({ printFunc: func })),
    answerBoxVersion: '',
    setAnswerBoxVersion(data) {
      set({ answerBoxVersion: data });
    },
    setPageBackground(data) {
      if (data && data.length === 0) {
        data = undefined
      }
      if (data == get().pageBackground) {
        return
      }
      set({ pageBackground: data })
    },
    showPageBackground: false,
    setShowPageBackground(data) {
      set({ showPageBackground: data })
    },
  }));
}
