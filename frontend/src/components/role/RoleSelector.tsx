import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Check,
  X,
  Search,
  Users,
} from 'lucide-react';
import { cn } from "@/lib/utils";
import { Role } from '@/types/role';
import { RoleAssignment } from '@/types/user';

interface RoleSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableRoles: Role[];
  selectedAssignments: RoleAssignment[];
  onAssignmentsChange: (assignments: RoleAssignment[]) => void;
  assignedRoleIds?: string[]; // 已分配的角色ID列表
  title?: string;
  description?: string;
  onConfirm?: (assignments: RoleAssignment[]) => void;
  onCancel?: () => void;
}

const RoleSelector: React.FC<RoleSelectorProps> = ({
  open,
  onOpenChange,
  availableRoles,
  selectedAssignments,
  onAssignmentsChange,
  assignedRoleIds = [],
  title = "选择角色",
  description = "为用户分配角色和权限",
  onConfirm,
  onCancel,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // 过滤角色
  const filteredRoles = useMemo(() => {
    return availableRoles.filter(role => {
      const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           role.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           role.description?.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch && role.is_active;
    });
  }, [availableRoles, searchTerm]);

  // 获取角色级别颜色
  const getRoleLevelColor = (level: number) => {
    const colors: Record<number, string> = {
      1: 'bg-red-100 text-red-800',
      2: 'bg-orange-100 text-orange-800',
      3: 'bg-purple-100 text-purple-800',
      4: 'bg-blue-100 text-blue-800',
      5: 'bg-indigo-100 text-indigo-800',
      6: 'bg-green-100 text-green-800',
      7: 'bg-yellow-100 text-yellow-800',
      8: 'bg-cyan-100 text-cyan-800',
      9: 'bg-gray-100 text-gray-800',
      10: 'bg-pink-100 text-pink-800',
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  // 检查角色是否已选择（包括新选择的和已分配的）
  const isRoleSelected = (roleId: string) => {
    return selectedAssignments.some(assignment => assignment.role_id === roleId) ||
           assignedRoleIds.includes(roleId);
  };

  // 检查角色是否为已分配的角色
  const isRoleAssigned = (roleId: string) => {
    return assignedRoleIds.includes(roleId);
  };

  // 添加角色分配
  const addRoleAssignment = (role: Role) => {
    if (isRoleSelected(role.id)) {
      return;
    }

    const newAssignment: RoleAssignment = {
      role_id: role.id,
      display_name: role.name,
    };

    onAssignmentsChange([...selectedAssignments, newAssignment]);
  };

  // 移除角色分配
  const removeRoleAssignment = (roleId: string) => {
    onAssignmentsChange(selectedAssignments.filter(assignment => assignment.role_id !== roleId));
  };

  // 处理确认
  const handleConfirm = () => {
    onConfirm?.(selectedAssignments);
    onOpenChange(false);
  };

  // 处理取消
  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索角色..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>

          {/* 角色列表 */}
          <div className="h-[400px] overflow-y-auto border rounded-md p-2">
            <div className="space-y-2">
                {filteredRoles?.map((role) => {
                  const isSelected = isRoleSelected(role.id);
                  const isAssigned = isRoleAssigned(role.id);

                  return (
                    <div
                      key={role.id}
                      className={cn(
                        "p-3 border rounded-lg transition-colors",
                        isSelected
                          ? isAssigned
                            ? "border-orange-300 bg-orange-50 opacity-70 cursor-not-allowed"
                            : "border-primary bg-primary/5 opacity-60 cursor-not-allowed"
                          : "cursor-pointer hover:border-primary/50"
                      )}
                      onClick={() => !isSelected && addRoleAssignment(role)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className={cn(
                              "font-medium",
                              isSelected && (isAssigned ? "text-orange-700" : "text-muted-foreground")
                            )}>
                              {role.name}
                            </h5>
                            <Badge variant="outline" className={getRoleLevelColor(role.level)}>
                              级别 {role.level}
                            </Badge>
                            {isSelected && (
                              <Check className={cn(
                                "h-4 w-4",
                                isAssigned ? "text-orange-600" : "text-primary"
                              )} />
                            )}
                          </div>
                          <p className={cn("text-sm text-muted-foreground", isSelected && "opacity-70")}>
                            {role.description}
                          </p>
                          <p className={cn("text-xs text-muted-foreground mt-1", isSelected && "opacity-70")}>
                            代码: {role.code}
                          </p>
                          {isSelected && (
                            <p className={cn(
                              "text-xs mt-1 font-medium",
                              isAssigned ? "text-orange-600" : "text-primary"
                            )}>
                              {isAssigned ? "✓ 已分配" : "✓ 已选择"}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}

                {filteredRoles.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">没有找到匹配的角色</p>
                  </div>
                )}
              </div>
            </div>

          {/* 已选择的角色 */}
          {selectedAssignments.length > 0 && (
            <div className="mt-4 p-3 bg-muted/30 rounded-lg">
              <h4 className="font-medium mb-2 text-sm">
                已选择角色 ({selectedAssignments.length})
              </h4>
              <div className="flex flex-wrap gap-2">
                {selectedAssignments.map((assignment) => {
                  const role = availableRoles.find(r => r.id === assignment.role_id);
                  if (!role) return null;

                  return (
                    <Badge
                      key={assignment.role_id}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {role.name}
                      <X
                        className="h-3 w-3 cursor-pointer hover:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeRoleAssignment(assignment.role_id);
                        }}
                      />
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleConfirm} disabled={selectedAssignments.length === 0}>
            确认分配 ({selectedAssignments.length})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RoleSelector;
