import React, { useState, useMemo } from 'react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Check,
  ChevronsUpDown,
  X,
  Shield,
  GraduationCap,
  BookOpen,
  Building,
  UserCheck,
  Users,
} from 'lucide-react';
import { cn } from "@/lib/utils";
import { Role, RoleCategory, RoleLevel } from '@/types/role';
import { RoleAssignment } from '@/types/user';

interface RoleSelectProps {
  // 数据
  availableRoles: Role[];
  selectedAssignments: RoleAssignment[];
  onAssignmentsChange: (assignments: RoleAssignment[]) => void;
  
  // 配置
  multiple?: boolean;
  placeholder?: string;
  disabled?: boolean;
  
  // 样式
  className?: string;
}

const RoleSelect: React.FC<RoleSelectProps> = ({
  availableRoles,
  selectedAssignments,
  onAssignmentsChange,
  multiple = true,
  placeholder = "选择角色...",
  disabled = false,
  className,
}) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 过滤可用角色
  const filteredRoles = useMemo(() => {
    return availableRoles.filter(role => {
      const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           role.code.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch && role.is_active;
    });
  }, [availableRoles, searchTerm]);

  // 按分类分组角色
  const rolesByCategory = useMemo(() => {
    return filteredRoles.reduce((acc, role) => {
      if (!acc[role.category]) {
        acc[role.category] = [];
      }
      acc[role.category].push(role);
      return acc;
    }, {} as Record<RoleCategory, Role[]>);
  }, [filteredRoles]);

  // 获取分类显示名称
  const getCategoryName = (category: RoleCategory) => {
    const names: Record<RoleCategory, string> = {
      system: '系统级',
      tenant: '租户级',
      school: '学校级',
      business: '业务级',
      class_grade: '班级/年级',
      end_user: '终端用户',
    };
    return names[category] || category;
  };

  // 获取分类图标
  const getCategoryIcon = (category: RoleCategory) => {
    const icons: Record<RoleCategory, React.ReactNode> = {
      system: <Shield className="h-4 w-4" />,
      tenant: <Building className="h-4 w-4" />,
      school: <GraduationCap className="h-4 w-4" />,
      business: <Users className="h-4 w-4" />,
      class_grade: <BookOpen className="h-4 w-4" />,
      end_user: <UserCheck className="h-4 w-4" />,
    };
    return icons[category];
  };

  // 获取角色级别颜色
  const getRoleLevelColor = (level: RoleLevel) => {
    const colors: Record<RoleLevel, string> = {
      [RoleLevel.SuperAdmin]: 'bg-red-100 text-red-800',
      [RoleLevel.TenantAdmin]: 'bg-orange-100 text-orange-800',
      [RoleLevel.Principal]: 'bg-purple-100 text-purple-800',
      [RoleLevel.AcademicDirector]: 'bg-blue-100 text-blue-800',
      [RoleLevel.SubjectLeader]: 'bg-indigo-100 text-indigo-800',
      [RoleLevel.GradeLeader]: 'bg-green-100 text-green-800',
      [RoleLevel.ClassTeacher]: 'bg-yellow-100 text-yellow-800',
      [RoleLevel.Teacher]: 'bg-cyan-100 text-cyan-800',
      [RoleLevel.Student]: 'bg-gray-100 text-gray-800',
      [RoleLevel.Parent]: 'bg-pink-100 text-pink-800',
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  // 检查角色是否已选择
  const isRoleSelected = (roleId: string) => {
    return selectedAssignments.some(assignment => assignment.role_id === roleId);
  };

  // 获取角色信息
  const getRoleInfo = (roleId: string) => {
    return availableRoles.find(role => role.id === roleId);
  };

  // 切换角色选择
  const toggleRole = (role: Role) => {
    if (isRoleSelected(role.id)) {
      // 移除角色
      onAssignmentsChange(selectedAssignments.filter(assignment => assignment.role_id !== role.id));
    } else {
      // 添加角色
      if (!multiple && selectedAssignments.length > 0) {
        onAssignmentsChange([{
          role_id: role.id,
          display_name: role.name,
        }]);
      } else {
        onAssignmentsChange([...selectedAssignments, {
          role_id: role.id,
          display_name: role.name,
        }]);
      }
    }
  };

  // 移除角色
  const removeRole = (roleId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    onAssignmentsChange(selectedAssignments.filter(assignment => assignment.role_id !== roleId));
  };

  // 获取显示文本
  const getDisplayText = () => {
    if (selectedAssignments.length === 0) {
      return placeholder;
    }
    
    if (selectedAssignments.length === 1) {
      const role = getRoleInfo(selectedAssignments[0].role_id);
      return role?.name || '未知角色';
    }
    
    return `已选择 ${selectedAssignments.length} 个角色`;
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
          disabled={disabled}
        >
          <div className="flex items-center gap-1 flex-1 min-w-0">
            {selectedAssignments.length > 0 ? (
              <div className="flex items-center gap-1 flex-wrap">
                {selectedAssignments.slice(0, 2).map((assignment) => {
                  const role = getRoleInfo(assignment.role_id);
                  if (!role) return null;
                  
                  return (
                    <Badge
                      key={assignment.role_id}
                      variant="secondary"
                      className="text-xs max-w-24 truncate"
                    >
                      {role.name}
                      {multiple && (
                        <X
                          className="h-3 w-3 ml-1 hover:bg-muted-foreground/20 rounded-sm"
                          onClick={(e) => removeRole(assignment.role_id, e)}
                        />
                      )}
                    </Badge>
                  );
                })}
                {selectedAssignments.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{selectedAssignments.length - 2}
                  </Badge>
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <Command>
          <CommandInput
            placeholder="搜索角色..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandEmpty>未找到角色</CommandEmpty>
          <CommandList>
            <ScrollArea className="h-72">
              {Object.entries(rolesByCategory).map(([category, roles]) => (
                <CommandGroup key={category} heading={
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(category as RoleCategory)}
                    <span>{getCategoryName(category as RoleCategory)}</span>
                  </div>
                }>
                  {roles?.map((role) => (
                    <CommandItem
                      key={role.id}
                      value={role.id}
                      onSelect={() => toggleRole(role)}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{role.name}</span>
                            <Badge variant="outline" className={cn("text-xs", getRoleLevelColor(role.level))}>
                              {role.level}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground truncate">{role.description}</p>
                        </div>
                        {isRoleSelected(role.id) && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))}
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default RoleSelect;
