import React, { useState, useRef } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Tabs,
    Ta<PERSON>Content,
    <PERSON><PERSON><PERSON>ist,
    TabsTrigger,
} from "@/components/ui/tabs";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Upload,
    Download,
    FileText,
    CheckCircle,
    XCircle,
    AlertTriangle,
    FileSpreadsheet,
    Info,
} from 'lucide-react';
import { toast } from "sonner";
import { Student, ImportResult, SheetResult } from '@/types/student';
import { studentsApi } from '@/services/studentApi';

interface StudentImportExportProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    students: Student[];
    onImportComplete: () => void;
    tenantId: string;
    tenantName: string;
}

const StudentImportExport: React.FC<StudentImportExportProps> = ({
    open,
    onOpenChange,
    students,
    onImportComplete,
    tenantId,
    tenantName,
}) => {
    const [activeTab, setActiveTab] = useState('export');
    const [importFile, setImportFile] = useState<File | null>(null);
    const [importing, setImporting] = useState(false);
    const [exporting, setExporting] = useState(false);
    const [importProgress, setImportProgress] = useState(0);
    const [importResult, setImportResult] = useState<ImportResult | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                toast.error('请选择 Excel 格式的文件 (.xlsx, .xls)');
                return;
            }
            setImportFile(file);
            setImportResult(null);
        }
    };

    const handleImport = async () => {
        if (!importFile) {
            toast.error('请先选择要导入的文件');
            return;
        }

        setImporting(true);
        setImportProgress(0);

        try {
            // 模拟进度更新
            const progressInterval = setInterval(() => {
                setImportProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return prev;
                    }
                    return prev + 10;
                });
            }, 200);

            const response = await studentsApi.importStudents(tenantId, tenantName, importFile);
            
            clearInterval(progressInterval);
            setImportProgress(100);

            if (response.success && response.data) {
                setImportResult(response.data);
                const sheetsCount = response.data.sheets_processed.length;
                toast.success(`导入完成！处理 ${sheetsCount} 个工作表，总计成功 ${response.data.success} 条，失败 ${response.data.failed} 条`);
                
                if (response.data.success > 0) {
                    onImportComplete();
                }
            } else {
                toast.error('导入失败: ' + response.message);
            }
        } catch (error: any) {
            toast.error('导入失败: ' + (error.response?.data?.message || error.message));
            console.error('导入失败:', error);
        } finally {
            setImporting(false);
        }
    };

    const handleExport = async () => {
        setExporting(true);

        try {
            // 准备导出数据
            const exportData = students.map(student => ({
                '学号': student.student_number,
                '姓名': student.student_name,
                '性别': student.gender || '',
                '出生日期': student.birth_date || '',
                '身份证号': student.id_number || '',
                '手机号': student.phone || '',
                '邮箱': student.email || '',
                '地址': student.address || '',
                '监护人姓名': student.guardian_name || '',
                '监护人电话': student.guardian_phone || '',
                '监护人关系': student.guardian_relation || '',
                '班级': student.class_name || '',
                '年级': student.grade_level_name || '',
                '状态': student.status,
                '创建时间': new Date(student.created_at).toLocaleDateString('zh-CN'),
            }));

            // 转换为 CSV 格式
            const headers = Object.keys(exportData[0] || {});
            const csvContent = [
                headers.join(','),
                ...exportData.map(row =>
                    headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
                )
            ].join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `学生数据_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success(`已导出 ${students.length} 条学生数据`);
        } catch (error) {
            toast.error('导出失败');
            console.error('导出失败:', error);
        } finally {
            setExporting(false);
        }
    };

    const downloadTemplate = () => {
        const template = [
            ['序号', '学号', '姓名', '班级', '年级', '出生日期', '身份证号', '手机号', '邮箱', '地址', '监护人姓名', '监护人电话', '监护人关系'],
            ['1', '0221220101', '曾某某', '901', '初一', '2010-01-01', '110101201001011234', '13800138001', '<EMAIL>', '北京市朝阳区', '张父', '13800138002', '父亲'],
            ['2', '0221220102', '曾某红', '902', '初二', '2010-02-01', '110101201002011234', '13800138003', '<EMAIL>', '北京市海淀区', '李母', '13800138004', '母亲'],
        ];

        const csvContent = template.map(row =>
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', '学生导入模板.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('模板文件已下载');
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center">
                        <FileSpreadsheet className="mr-2 h-5 w-5" />
                        学生数据导入导出
                    </DialogTitle>
                    <DialogDescription>
                        批量导入学生数据或导出现有学生信息
                    </DialogDescription>
                </DialogHeader>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="export">导出数据</TabsTrigger>
                        <TabsTrigger value="import">导入数据</TabsTrigger>
                    </TabsList>

                    <TabsContent value="export" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Download className="mr-2 h-5 w-5" />
                                    导出学生数据
                                </CardTitle>
                                <CardDescription>
                                    将当前系统中的学生数据导出为 CSV 文件
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                                        <div>
                                            <h4 className="font-medium text-blue-900">导出说明</h4>
                                            <ul className="text-sm text-blue-800 mt-2 space-y-1">
                                                <li>• 将导出所有学生的基本信息和班级信息</li>
                                                <li>• 导出格式为 CSV，可用 Excel 等软件打开</li>
                                                <li>• 包含学号、姓名、性别、联系方式等完整信息</li>
                                                <li>• 共计 {students.length} 条学生数据</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div>
                                        <h4 className="font-medium">学生数据统计</h4>
                                        <p className="text-sm text-muted-foreground">
                                            总计 {students.length} 个学生，包含完整的学生信息和班级数据
                                        </p>
                                    </div>
                                    <Badge variant="outline" className="text-lg px-3 py-1">
                                        {students.length}
                                    </Badge>
                                </div>

                                <Button
                                    onClick={handleExport}
                                    disabled={exporting || students.length === 0}
                                    className="w-full"
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    {exporting ? '导出中...' : '导出学生数据'}
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="import" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Upload className="mr-2 h-5 w-5" />
                                    导入学生数据
                                </CardTitle>
                                <CardDescription>
                                    从 Excel 文件批量导入学生数据到系统中
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* 导入说明 */}
                                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                        <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                                        <div>
                                            <h4 className="font-medium text-orange-900">导入要求</h4>
                                            <ul className="text-sm text-orange-800 mt-2 space-y-1">
                                                <li>• 文件格式必须为 Excel (.xlsx, .xls)</li>
                                                <li>• <strong>支持多个工作表同时导入</strong></li>
                                                <li>• 列顺序：序号、学号、姓名、班级、年级</li>
                                                <li>• 必填字段：学号、姓名</li>
                                                <li>• 班级代码：3位数字（如901表示9年01班）</li>
                                                <li>• 年级信息：初一/初二/初三等，将自动映射到G7/G8/G9</li>
                                                <li>• 系统将自动创建不存在的行政班级</li>
                                                <li>• 每个工作表的第一行为标题行，数据从第二行开始</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                {/* 模板下载 */}
                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div>
                                        <h4 className="font-medium">下载导入模板</h4>
                                        <p className="text-sm text-muted-foreground">
                                            下载标准的 Excel 模板文件，按照格式填写数据
                                        </p>
                                    </div>
                                    <Button variant="outline" onClick={downloadTemplate}>
                                        <FileText className="mr-2 h-4 w-4" />
                                        下载模板
                                    </Button>
                                </div>

                                {/* 文件选择 */}
                                <div className="space-y-2">
                                    <Label htmlFor="import-file">选择导入文件</Label>
                                    <div className="flex items-center space-x-2">
                                        <Input
                                            id="import-file"
                                            type="file"
                                            accept=".xlsx,.xls"
                                            onChange={handleFileSelect}
                                            ref={fileInputRef}
                                            className="flex-1"
                                        />
                                        {importFile && (
                                            <Badge variant="outline">
                                                {importFile.name}
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                {/* 导入进度 */}
                                {importing && (
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <Label>导入进度</Label>
                                            <span className="text-sm text-muted-foreground">
                                                {importProgress}%
                                            </span>
                                        </div>
                                        <Progress value={importProgress} className="w-full" />
                                    </div>
                                )}

                                {/* 导入结果 */}
                                {importResult && (
                                    <div className="space-y-4">
                                        {/* 总体统计 */}
                                        <div className="grid grid-cols-3 gap-4">
                                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-center">
                                                <FileSpreadsheet className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                                                <p className="font-medium text-blue-900">处理工作表</p>
                                                <p className="text-2xl font-bold text-blue-600">
                                                    {importResult.sheets_processed.length}
                                                </p>
                                            </div>
                                            <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
                                                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                                                <p className="font-medium text-green-900">成功导入</p>
                                                <p className="text-2xl font-bold text-green-600">
                                                    {importResult.success}
                                                </p>
                                            </div>
                                            <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-center">
                                                <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                                                <p className="font-medium text-red-900">导入失败</p>
                                                <p className="text-2xl font-bold text-red-600">
                                                    {importResult.failed}
                                                </p>
                                            </div>
                                        </div>

                                        {/* 各工作表详情 */}
                                        {importResult.sheets_processed.length > 0 && (
                                            <div>
                                                <h4 className="font-medium mb-3">各工作表处理结果</h4>
                                                <div className="space-y-3">
                                                    {importResult.sheets_processed.map((sheet, index) => (
                                                        <div key={index} className="border rounded-lg p-4">
                                                            <div className="flex items-center justify-between mb-2">
                                                                <h5 className="font-medium flex items-center">
                                                                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                                                                    {sheet.sheet_name}
                                                                </h5>
                                                                <div className="flex space-x-2">
                                                                    <Badge variant="outline" className="text-green-600">
                                                                        成功 {sheet.success}
                                                                    </Badge>
                                                                    {sheet.failed > 0 && (
                                                                        <Badge variant="outline" className="text-red-600">
                                                                            失败 {sheet.failed}
                                                                        </Badge>
                                                                    )}
                                                                </div>
                                                            </div>
                                                            
                                                            {/* 工作表错误详情 */}
                                                            {sheet.errors.length > 0 && (
                                                                <div className="mt-3">
                                                                    <p className="text-sm font-medium text-red-600 mb-2">错误详情:</p>
                                                                    <div className="border rounded overflow-hidden">
                                                                        <Table>
                                                                            <TableHeader>
                                                                                <TableRow>
                                                                                    <TableHead className="w-16">行号</TableHead>
                                                                                    <TableHead>错误信息</TableHead>
                                                                                    <TableHead className="w-32">数据</TableHead>
                                                                                </TableRow>
                                                                            </TableHeader>
                                                                            <TableBody>
                                                                                {sheet.errors.map((error, errorIndex) => (
                                                                                    <TableRow key={errorIndex}>
                                                                                        <TableCell>{error.row}</TableCell>
                                                                                        <TableCell className="text-red-600 text-sm">
                                                                                            {error.error}
                                                                                        </TableCell>
                                                                                        <TableCell className="text-xs text-muted-foreground truncate">
                                                                                            {error.data ? JSON.stringify(error.data).substring(0, 50) + '...' : '-'}
                                                                                        </TableCell>
                                                                                    </TableRow>
                                                                                ))}
                                                                            </TableBody>
                                                                        </Table>
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}

                                <Button
                                    onClick={handleImport}
                                    disabled={!importFile || importing}
                                    className="w-full"
                                >
                                    <Upload className="mr-2 h-4 w-4" />
                                    {importing ? '导入中...' : '开始导入'}
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        关闭
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default StudentImportExport;
