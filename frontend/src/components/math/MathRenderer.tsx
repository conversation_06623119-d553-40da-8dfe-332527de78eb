import React, { useMemo } from 'react';
import { MathJax, MathJaxContext } from 'better-react-mathjax';
import parse from 'html-react-parser';

// 改进的正则表达式，更好处理各种情况
const MATH_DELIMITERS = /(\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\]|\$\$[\s\S]*?\$\$|\$(?!\$)[^$]*?\$(?!\$))/g;

interface MathRendererProps {
  content: string;
  className?: string;
}

const MathJaxConfig = {
  loader: { load: ["[tex]/html"] },
  tex: {
    packages: { "[+]": ["html"] },
    inlineMath: [
      ["$", "$"],
      ["\\(", "\\)"]
    ],
    displayMath: [
      ["$$", "$$"],
      ["\\[", "\\]"]
    ]
  }
};

const MathRenderer: React.FC<MathRendererProps> = ({ content, className }) => {
  if (!content) return null;

  const processedParts = useMemo(() => {
    if (!content) return [];

    // 预处理：保护HTML标签不被公式分割破坏
    let processedContent = content
    //   // 临时替换HTML标签
      .replace(/<br\s*\/?>/gi, '___BR___')
      .replace(/<[^>]+>/g, (match) => `___HTML_${btoa(match).replace(/=/g, '')}___`);

    // 使用更精确的方法分割文本和公式
    const parts: Array<{ type: 'formula' | 'html'; content: string }> = [];
    let lastIndex = 0;
    let match;

    // 重置正则表达式的lastIndex
    MATH_DELIMITERS.lastIndex = 0;

    while ((match = MATH_DELIMITERS.exec(processedContent)) !== null) {
      // 添加公式前的文本部分
      if (match.index > lastIndex) {
        const textPart = processedContent.slice(lastIndex, match.index);
        if (textPart.trim()) {
          parts.push({ type: 'html', content: textPart });
        }
      }

      // 添加公式部分
      const formula = match[1];
      if (formula) {
        // 公式部分：恢复转义字符和HTML实体
        const decodedFormula = formula
          .replace(/&gt;/g, '>')
          .replace(/&lt;/g, '<')
          .replace(/&amp;/g, '&')
          .replace(/\$(?!\$)(.*?)(?<!\$)\$(?!\$)/g, '\\($1\\)')
          .replace(/___BR___/g, '<br />')
          .replace(/___HTML_([^_]+)___/g, (_, encoded) => {
            try {
              return atob(encoded);
            } catch {
              return '';
            }
          });
          // .replace(/\\\\\\\\/g, '\\\\') // 处理四个反斜杠变成两个
          // .replace(/\\\\/g, '\\'); // 处理双反斜杠
        parts.push({ type: 'formula', content: decodedFormula });
      }

      lastIndex = match.index + match[0].length;
    }

    // 添加最后剩余的文本部分
    if (lastIndex < processedContent.length) {
      const textPart = processedContent.slice(lastIndex);
      if (textPart.trim()) {
        parts.push({ type: 'html', content: textPart });
      }
    }

    // 恢复HTML标签
    return parts
      .map((part) => {
        if (part.type === 'html') {
          let htmlContent = part.content.replace(/___BR___/g, '<br />').replace(/___HTML_([^_]+)___/g, (_, encoded) => {
            try {
              return atob(encoded);
            } catch {
              return '';
            }
          });
          return { type: 'html', content: htmlContent };
        }
        return part;
      })
      .filter((part) => part.content.trim() !== '');
  }, [content]);

  return (
    <MathJaxContext config={MathJaxConfig} version={3}>
      <div className={className}>
        {processedParts.map((part, index) => {
          if (!part) return null;

          if (part.type === 'formula') {
            const isInline = !part.content.startsWith('\\[') && !part.content.startsWith('$$');

            return (
              <MathJax key={index} inline={isInline} hideUntilTypeset={"first"} dynamic>
                {part.content}
              </MathJax>
            );
          } else {
            return <React.Fragment key={index}>{parse(part.content)}</React.Fragment>;
          }
        })}
      </div>
    </MathJaxContext>
  );
};

export default MathRenderer;
