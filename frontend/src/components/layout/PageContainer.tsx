import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface PageContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'default' | 'full';
  padding?: boolean;
}

const maxWidthClasses = {
  default: 'max-w-7xl',
  full: 'max-w-none',
};

export function PageContainer({ 
  children, 
  className, 
  maxWidth = 'default',
  padding = true 
}: PageContainerProps) {
  return (
    <div 
      className={cn(
        'w-full mx-auto',
        maxWidthClasses[maxWidth],
        padding && 'px-4 sm:px-6 lg:px-8',
        className
      )}
    >
      {children}
    </div>
  );
}
