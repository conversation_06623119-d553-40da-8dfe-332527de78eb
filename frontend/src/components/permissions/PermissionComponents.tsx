import React from 'react';
import { usePermissions } from '@/contexts/PermissionContext';
// 基础权限组件属性
interface BasePermissionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
  showError?: boolean;
}

// 权限检查组件属性
interface PermissionCheckProps extends BasePermissionProps {
  resource: string;
  action: string;
  scope?: string;
}

// 菜单权限检查组件属性
interface MenuPermissionProps extends BasePermissionProps {
  menuId: string;
}

// 默认加载组件
const DefaultLoading: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
  </div>
);

// 单权限检查组件
export const PermissionCheck: React.FC<PermissionCheckProps> = ({
  resource,
  action,
  scope,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { hasPermission, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  if (!hasPermission(resource, action, scope)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 菜单权限检查组件
export const MenuPermissionCheck: React.FC<MenuPermissionProps> = ({
  menuId,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { canAccessMenu, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  if (!canAccessMenu(menuId)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};