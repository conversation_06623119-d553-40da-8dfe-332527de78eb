import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface SmartPaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number, pageSize: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  className?: string;
}

const SmartPagination: React.FC<SmartPaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  showSizeChanger = true,
  showQuickJumper = true,
  showTotal = true,
  className = '',
}) => {
  const totalPages = Math.ceil(total / pageSize);

  const renderPageNumbers = () => {
    const pages = [];
    
    // 如果总页数小于等于7，显示所有页码
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              isActive={i === current}
              onClick={() => onChange(i, pageSize)}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      // 总是显示第一页
      pages.push(
        <PaginationItem key={1}>
          <PaginationLink
            isActive={1 === current}
            onClick={() => onChange(1, pageSize)}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );
      
      // 如果当前页距离第一页较远，显示省略号
      if (current > 4) {
        pages.push(
          <PaginationItem key="ellipsis-start">
            <span className="px-3 py-2 text-gray-400">...</span>
          </PaginationItem>
        );
      }
      
      // 显示当前页附近的页码
      const start = Math.max(2, current - 1);
      const end = Math.min(totalPages - 1, current + 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              isActive={i === current}
              onClick={() => onChange(i, pageSize)}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
      
      // 如果当前页距离最后一页较远，显示省略号
      if (current < totalPages - 3) {
        pages.push(
          <PaginationItem key="ellipsis-end">
            <span className="px-3 py-2 text-gray-400">...</span>
          </PaginationItem>
        );
      }
      
      // 总是显示最后一页
      if (totalPages > 1) {
        pages.push(
          <PaginationItem key={totalPages}>
            <PaginationLink
              isActive={totalPages === current}
              onClick={() => onChange(totalPages, pageSize)}
            >
              {totalPages}
            </PaginationLink>
          </PaginationItem>
        );
      }
    }
    
    return pages;
  };

  if (total === 0) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {showTotal && (
        <div className="flex items-center space-x-4 text-sm text-gray-500">
          <span>共 {total} 条记录</span>
          {showSizeChanger && (
            <div className="flex items-center space-x-2">
              <span>每页</span>
              <select
                className="border rounded px-2 py-1 text-sm"
                value={pageSize}
                onChange={e => onChange(1, Number(e.target.value))}
              >
                {[10, 20, 50, 100].map(size => (
                  <option key={size} value={size}>{size} 条/页</option>
                ))}
              </select>
            </div>
          )}
          {showQuickJumper && (
            <div className="flex items-center space-x-2">
              <span>跳转到</span>
              <input
                type="number"
                min={1}
                max={totalPages}
                defaultValue={current}
                onBlur={e => {
                  let page = Number(e.target.value);
                  if (page < 1) page = 1;
                  if (page > totalPages) page = totalPages;
                  if (page !== current) {
                    onChange(page, pageSize);
                  }
                }}
                className="border rounded px-2 py-1 w-16 text-sm text-center"
              />
              <span>页</span>
            </div>
          )}
        </div>
      )}

      <div className="flex items-center">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => {
                  if (current > 1) {
                    onChange(current - 1, pageSize);
                  }
                }}
                className={current === 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
            
            {renderPageNumbers()}
            
            <PaginationItem>
              <PaginationNext
                onClick={() => {
                  if (current < totalPages) {
                    onChange(current + 1, pageSize);
                  }
                }}
                className={current === totalPages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default SmartPagination;
