import React, { useState, useRef } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Tabs,
    TabsContent,
    <PERSON><PERSON>List,
    TabsTrigger,
} from "@/components/ui/tabs";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
    Upload,
    Download,
    FileText,
    CheckCircle,
    XCircle,
    AlertTriangle,
    FileSpreadsheet,
    Info,
    Users,
} from 'lucide-react';
import { toast } from "sonner";
import { TeacherListVO } from '@/types/teacher';
import { teachersApi } from '@/services/teacherApi';

interface TeacherImportResult {
    success: number;
    failed: number;
    errors: Array<{
        row: number;
        error: string;
        data: any;
    }>;
}

interface TeacherImportExportProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    teachers: TeacherListVO[];
    onImportComplete: () => void;
    tenantId: string;
    tenantName: string;
}

const TeacherImportExport: React.FC<TeacherImportExportProps> = ({
    open,
    onOpenChange,
    teachers,
    onImportComplete,
    tenantId,
    tenantName,
}) => {
    const [activeTab, setActiveTab] = useState('export');
    const [importFile, setImportFile] = useState<File | null>(null);
    const [importing, setImporting] = useState(false);
    const [exporting, setExporting] = useState(false);
    const [importProgress, setImportProgress] = useState(0);
    const [importResult, setImportResult] = useState<TeacherImportResult | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                toast.error('请选择 Excel 格式的文件 (.xlsx, .xls)');
                return;
            }
            setImportFile(file);
            setImportResult(null);
        }
    };

    const handleImport = async () => {
        if (!importFile) {
            toast.error('请先选择要导入的文件');
            return;
        }

        setImporting(true);
        setImportProgress(0);

        try {
            // 模拟进度更新
            const progressInterval = setInterval(() => {
                setImportProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return prev;
                    }
                    return prev + 10;
                });
            }, 200);

            const response = await teachersApi.importTeachers(tenantId, tenantName, importFile);
            
            clearInterval(progressInterval);
            setImportProgress(100);

            if (response.success && response.data) {
                setImportResult(response.data);
                toast.success(`导入完成！成功 ${response.data.success} 条，失败 ${response.data.failed} 条`);
                
                if (response.data.success > 0) {
                    onImportComplete();
                }
            } else {
                toast.error('导入失败: ' + response.message);
            }
        } catch (error: any) {
            toast.error('导入失败: ' + (error.response?.data?.message || error.message));
            console.error('导入失败:', error);
        } finally {
            setImporting(false);
        }
    };

    const handleExport = async () => {
        setExporting(true);

        try {
            // 准备导出数据
            const exportData = teachers.map(teacher => ({
                '工号': teacher.employee_id || '',
                '姓名': teacher.teacher_name,
                '性别': teacher.gender || '',
                '出生日期': teacher.date_of_birth || '',
                '身份证号': teacher.id_card_number || '',
                '手机号': teacher.phone || '',
                '邮箱': teacher.email || '',
                '最高学历': teacher.highest_education || '',
                '毕业院校': teacher.graduation_school || '',
                '入职日期': teacher.hire_date || '',
                '就职状态': teacher.employment_status || '',
                '职称': teacher.title || '',
                '任教科目': teacher.teaching_subjects || '',
                '办公地点': teacher.office_location || '',
                '状态': teacher.is_active ? '启用' : '禁用',
            }));

            // 转换为 CSV 格式
            const headers = Object.keys(exportData[0] || {});
            const csvContent = [
                headers.join(','),
                ...exportData.map(row =>
                    headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
                )
            ].join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `教师数据_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success(`已导出 ${teachers.length} 条教师数据`);
        } catch (error) {
            toast.error('导出失败');
            console.error('导出失败:', error);
        } finally {
            setExporting(false);
        }
    };

    const downloadTemplate = () => {
        const template = [
            ['序号', '科目', '姓名', '手机号', '任教班级', '职位', '年级'],
            ['1', '数学', '张老师', '13800138000', '高一(1)班,高一(2)班', '数学教师', '高一'],
            ['2', '语文', '李老师', '13800138001', '高二(1)班', '班主任', '高二'],
            ['3', '英语', '王老师', '13800138002', '高三(1)班', '英语教师', '高三'],
        ];

        const csvContent = template.map(row =>
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', '教师导入模板.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('模板文件已下载');
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center">
                        <FileSpreadsheet className="mr-2 h-5 w-5" />
                        教师数据导入导出
                    </DialogTitle>
                    <DialogDescription>
                        批量导入教师数据或导出现有教师信息
                    </DialogDescription>
                </DialogHeader>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="export">导出数据</TabsTrigger>
                        <TabsTrigger value="import">导入数据</TabsTrigger>
                    </TabsList>

                    <TabsContent value="export" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Download className="mr-2 h-5 w-5" />
                                    导出教师数据
                                </CardTitle>
                                <CardDescription>
                                    将当前系统中的教师数据导出为 CSV 文件
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                                        <div>
                                            <h4 className="font-medium text-blue-900">导出说明</h4>
                                            <ul className="text-sm text-blue-800 mt-2 space-y-1">
                                                <li>• 将导出所有教师的基本信息和任教信息</li>
                                                <li>• 导出格式为 CSV，可用 Excel 等软件打开</li>
                                                <li>• 包含工号、姓名、联系方式、任教科目等完整信息</li>
                                                <li>• 共计 {teachers.length} 条教师数据</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div>
                                        <h4 className="font-medium">教师数据统计</h4>
                                        <p className="text-sm text-muted-foreground">
                                            总计 {teachers.length} 个教师，包含完整的教师信息和任教数据
                                        </p>
                                    </div>
                                    <Badge variant="outline" className="text-lg px-3 py-1">
                                        {teachers.length}
                                    </Badge>
                                </div>

                                <Button
                                    onClick={handleExport}
                                    disabled={exporting || teachers.length === 0}
                                    className="w-full"
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    {exporting ? '导出中...' : '导出教师数据'}
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="import" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Upload className="mr-2 h-5 w-5" />
                                    导入教师数据
                                </CardTitle>
                                <CardDescription>
                                    从 Excel 文件批量导入教师数据到系统中
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* 导入说明 */}
                                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                        <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                                        <div>
                                            <h4 className="font-medium text-orange-900">导入要求</h4>
                                            <ul className="text-sm text-orange-800 mt-2 space-y-1">
                                                <li>• 文件格式必须为 Excel (.xlsx, .xls)</li>
                                                <li>• 列顺序：序号、科目、姓名、手机号、任教班级、职位、年级</li>
                                                <li>• <strong>必填字段：科目、姓名、年级</strong></li>
                                                <li>• 科目：如数学、语文、英语等</li>
                                                <li>• 年级：如高一、高二、高三、初一、初二、初三</li>
                                                <li>• 任教班级：多个班级用逗号分隔</li>
                                                <li>• 职位：如"班主任"会自动分配管理班级</li>
                                                <li>• 系统将自动创建科目组、教学班级和分配班主任</li>
                                                <li>• 第一行为标题行，数据从第二行开始</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                {/* 模板下载 */}
                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div>
                                        <h4 className="font-medium">下载导入模板</h4>
                                        <p className="text-sm text-muted-foreground">
                                            下载标准的 Excel 模板文件，按照格式填写数据
                                        </p>
                                    </div>
                                    <Button variant="outline" onClick={downloadTemplate}>
                                        <FileText className="mr-2 h-4 w-4" />
                                        下载模板
                                    </Button>
                                </div>

                                {/* 文件选择 */}
                                <div className="space-y-2">
                                    <Label htmlFor="import-file">选择导入文件</Label>
                                    <div className="flex items-center space-x-2">
                                        <Input
                                            id="import-file"
                                            type="file"
                                            accept=".xlsx,.xls"
                                            onChange={handleFileSelect}
                                            ref={fileInputRef}
                                            className="flex-1"
                                        />
                                        {importFile && (
                                            <Badge variant="outline">
                                                {importFile.name}
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                {/* 导入进度 */}
                                {importing && (
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <Label>导入进度</Label>
                                            <span className="text-sm text-muted-foreground">
                                                {importProgress}%
                                            </span>
                                        </div>
                                        <Progress value={importProgress} className="w-full" />
                                    </div>
                                )}

                                {/* 导入结果 */}
                                {importResult && (
                                    <div className="space-y-4">
                                        {/* 总体统计 */}
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
                                                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                                                <p className="font-medium text-green-900">成功导入</p>
                                                <p className="text-2xl font-bold text-green-600">
                                                    {importResult.success}
                                                </p>
                                            </div>
                                            <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-center">
                                                <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                                                <p className="font-medium text-red-900">导入失败</p>
                                                <p className="text-2xl font-bold text-red-600">
                                                    {importResult.failed}
                                                </p>
                                            </div>
                                        </div>

                                        {/* 成功率进度条 */}
                                        <Card>
                                            <CardContent className="pt-6">
                                                <div className="space-y-2">
                                                    <div className="flex justify-between text-sm">
                                                        <span>导入成功率</span>
                                                        <span className="font-medium">
                                                            {importResult.success + importResult.failed > 0 
                                                                ? ((importResult.success / (importResult.success + importResult.failed)) * 100).toFixed(1)
                                                                : 0}%
                                                        </span>
                                                    </div>
                                                    <div className="w-full bg-gray-200 rounded-full h-3">
                                                        <div 
                                                            className="bg-green-500 h-3 rounded-full transition-all duration-300"
                                                            style={{
                                                                width: importResult.success + importResult.failed > 0 
                                                                    ? `${(importResult.success / (importResult.success + importResult.failed)) * 100}%`
                                                                    : '0%'
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>

                                        {/* 错误详情 */}
                                        {importResult.errors.length > 0 && (
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle className="text-lg text-red-700 flex items-center gap-2">
                                                        <Info className="w-5 h-5"/>
                                                        错误详情 ({importResult.errors.length} 条)
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="space-y-3 max-h-80 overflow-y-auto">
                                                        {importResult.errors.map((error, index) => (
                                                            <div key={index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                                                                <div className="flex items-start gap-3">
                                                                    <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                                                        <span className="text-xs font-medium text-red-600">
                                                                            {error.row}
                                                                        </span>
                                                                    </div>
                                                                    <div className="flex-1 min-w-0">
                                                                        <div className="text-sm font-medium text-red-800 mb-1">
                                                                            第 {error.row} 行错误
                                                                        </div>
                                                                        <div className="text-sm text-red-700 mb-2">
                                                                            {error.error}
                                                                        </div>
                                                                        <div className="text-xs text-red-600 bg-red-100 p-2 rounded border">
                                                                            <div className="font-medium mb-1">原始数据：</div>
                                                                            <div className="font-mono break-all">
                                                                                {JSON.stringify(error.data, null, 2)}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        )}

                                        {/* 导入成功且无错误的情况 */}
                                        {importResult.success > 0 && importResult.failed === 0 && (
                                            <Card className="border-green-200 bg-green-50">
                                                <CardContent className="pt-6">
                                                    <div className="text-center space-y-2">
                                                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                                            <Users className="w-8 h-8 text-green-600"/>
                                                        </div>
                                                        <h3 className="font-medium text-green-800">导入完成！</h3>
                                                        <p className="text-sm text-green-700">
                                                            所有 {importResult.success} 条教师记录已成功导入系统
                                                        </p>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        )}
                                    </div>
                                )}

                                <Button
                                    onClick={handleImport}
                                    disabled={!importFile || importing}
                                    className="w-full"
                                >
                                    <Upload className="mr-2 h-4 w-4" />
                                    {importing ? '导入中...' : '开始导入'}
                                </Button>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        关闭
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default TeacherImportExport;
