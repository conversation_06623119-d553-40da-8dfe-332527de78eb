import React from 'react';
import {NavLink} from 'react-router-dom';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@/components/ui/sidebar';
import {MenuPermission} from '@/contexts/MenuContext';
import {usePermissions} from '@/contexts/PermissionContext';
import {Skeleton} from '@/components/ui/skeleton';
// 图标映射
import {
  AlertCircle,
  Anchor,
  Award,
  BarChart2,
  Bell,
  Book,
  BookCheck,
  Bookmark,
  BookOpen,
  Briefcase,
  Building2,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  ClipboardCheck,
  Compass,
  Crown,
  Diamond,
  Download,
  Edit,
  Eye,
  EyeOff,
  FileText,
  Filter,
  Flag,
  Gift,
  GraduationCap,
  Grid,
  Hash,
  Heart,
  HelpCircle,
  Home,
  Info,
  Layers,
  List,
  Lock,
  Map,
  Menu,
  MoreHorizontal,
  MoreVertical,
  Navigation,
  PieChart,
  Plus,
  School,
  Search,
  Settings,
  Share,
  Shield,
  Shuffle,
  Star,
  Tag,
  Target,
  Trophy,
  Upload,
  User,
  UserCheck,
  Users,
  Users2,
  UsersRound, Workflow,
  XCircle,
  Zap
} from 'lucide-react';
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from '@/components/ui/collapsible';

const iconMap: Record<string, any> = {
  // 基础图标
  'home': Home,
  'settings': Settings,
  'users': Users,
  'user': User,
  'shield': Shield,
  'book': Book,
  'book-open': BookOpen,
  'file-text': FileText,
  'book-check': BookCheck,
  'school': School,
  'users-2': Users2,
  'clipboard-check': ClipboardCheck,
  'bar-chart-2': BarChart2,
  'building-2': Building2,
  'graduation-cap': GraduationCap,
  'user-check': UserCheck,
  'users-round': UsersRound,
  'layers': Layers,
  
  // 功能图标
  'trophy': Trophy,
  'calendar': Calendar,
  'pie-chart': PieChart,
  'list': List,
  'plus': Plus,
  'upload': Upload,
  'edit': Edit,
  'shuffle': Shuffle,
  'bell': Bell,
  'lock': Lock,
  'search': Search,
  'filter': Filter,
  'download': Download,
  'share': Share,
  'eye': Eye,
  'eye-off': EyeOff,
  // 工作流
  'workflow': Workflow,
  
  // 状态图标
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'alert-circle': AlertCircle,
  'info': Info,
  'help-circle': HelpCircle,
  'star': Star,
  'heart': Heart,
  'bookmark': Bookmark,
  'flag': Flag,
  'tag': Tag,
  'hash': Hash,
  
  // 布局图标
  'grid': Grid,
  'menu': Menu,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  
  // 特殊图标
  'zap': Zap,
  'target': Target,
  'compass': Compass,
  'map': Map,
  'navigation': Navigation,
  'anchor': Anchor,
  'briefcase': Briefcase,
  'award': Award,
  'gift': Gift,
  'crown': Crown,
  'diamond': Diamond,
};

// 获取图标组件
const getIcon = (iconName?: string) => {
  if (!iconName) return Home;
  return iconMap[iconName.toLowerCase()] || iconMap[iconName] || Home;
};

// 动态菜单项组件属性
interface DynamicMenuItemProps {
  menu: MenuPermission;
  level?: number;
  defaultExpanded?: boolean;
}

// 动态菜单项组件
const DynamicMenuItem: React.FC<DynamicMenuItemProps> = ({ 
  menu, 
  level = 0,
  defaultExpanded = false 
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);
  const IconComponent = getIcon(menu.icon);
  const hasChildren = menu.children && menu.children.length > 0;

  // 如果有子菜单，渲染可折叠菜单
  if (hasChildren) {
    return (
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={menu.name}
              className={level > 0 ? `ml-${level * 4}` : ''}
            >
              <IconComponent className="h-4 w-4" />
              <span>{menu.name}</span>
              {isExpanded ? (
                <ChevronDown className="ml-auto h-4 w-4" />
              ) : (
                <ChevronRight className="ml-auto h-4 w-4" />
              )}
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenu>
              {menu?.children?.map((childMenu) => (
                <DynamicMenuItem
                  key={childMenu.menu_id}
                  menu={childMenu}
                  level={level + 1}
                />
              ))}
            </SidebarMenu>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    );
  }

  // 渲染普通菜单项
  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        tooltip={menu.name}
        className={level > 0 ? `ml-${level * 4}` : ''}
      >
        <NavLink to={menu.path}>
          <IconComponent className="h-4 w-4" />
          <span>{menu.name}</span>
        </NavLink>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
};

// 动态菜单组属性
interface DynamicMenuGroupProps {
  title: string;
  menus: MenuPermission[];
  defaultExpanded?: boolean;
  showEmptyGroup?: boolean;
}

// 动态菜单组组件
const DynamicMenuGroup: React.FC<DynamicMenuGroupProps> = ({
  title,
  menus,
  defaultExpanded = true,
  showEmptyGroup = false
}) => {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>
        {title}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {menus.length > 0 ? (
              menus.map((menu) => (
              <DynamicMenuItem
                key={menu.menu_id}
                menu={menu}
                defaultExpanded={defaultExpanded}
              />
            ))
          ) : (
            showEmptyGroup && (
              <div className="px-2 py-1 text-sm text-muted-foreground">
                暂无可用菜单
              </div>
            )
          )}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
};

// 动态导航属性
interface DynamicNavigationProps {
  showLoading?: boolean;
  showError?: boolean;
  groupByCategory?: boolean;
  defaultExpanded?: boolean;
}

// 主导航组件
export const DynamicNavigation: React.FC<DynamicNavigationProps> = ({
  showLoading = true,
  showError = true,
  groupByCategory = true,
  defaultExpanded = true
}) => {
  const { isLoading, error, getAccessibleMenus } = usePermissions();

  // 加载状态
  if (isLoading && showLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <div className="space-y-2 ml-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-3/4" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <div className="space-y-2 ml-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-4/5" />
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error && showError) {
    return (
      <div className="p-4">
        <div className="relative w-full rounded-lg border border-destructive/50 bg-destructive/5 p-4 text-destructive">
          <AlertCircle className="h-4 w-4 absolute left-4 top-4" />
          <div className="pl-7 text-sm">
            菜单加载失败: {error}
          </div>
        </div>
      </div>
    );
  }

  // 获取可访问的菜单
  const accessibleMenus = getAccessibleMenus();

  if (accessibleMenus.length === 0) {
    return (
      <div className="p-4">
        <div className="relative w-full rounded-lg border bg-blue-50 p-4 text-blue-700">
          <Info className="h-4 w-4 absolute left-4 top-4" />
          <div className="pl-7 text-sm">
            暂无可访问的菜单
          </div>
        </div>
      </div>
    );
  }

  // 如果按类别分组
  if (groupByCategory) {
    // 按菜单类型分组并保持排序
    const functionalMenus = accessibleMenus
      .filter(menu =>
        menu.menu_type === 'functional' &&
        !menu.parent_id
      )
      .sort((a, b) => {
        // 首先按 sort_order 排序，然后按名称排序
        if (a.sort_order !== b.sort_order) {
          return (a.sort_order || 0) - (b.sort_order || 0);
        }
        return a.name.localeCompare(b.name);
      });

    const adminMenus = accessibleMenus
      .filter(menu =>
        menu.menu_type === 'admin' &&
        !menu.parent_id
      )
      .sort((a, b) => {
        if (a.sort_order !== b.sort_order) {
          return (a.sort_order || 0) - (b.sort_order || 0);
        }
        return a.name.localeCompare(b.name);
      });

    const systemMenus = accessibleMenus
      .filter(menu =>
        menu.menu_type === 'system' &&
        !menu.parent_id
      )
      .sort((a, b) => {
        if (a.sort_order !== b.sort_order) {
          return (a.sort_order || 0) - (b.sort_order || 0);
        }
        return a.name.localeCompare(b.name);
      });

    const personalMenus = accessibleMenus
      .filter(menu =>
        menu.menu_type === 'personal' &&
        !menu.parent_id
      )
      .sort((a, b) => {
        if (a.sort_order !== b.sort_order) {
          return (a.sort_order || 0) - (b.sort_order || 0);
        }
        return a.name.localeCompare(b.name);
      });

    return (
      <>
        {functionalMenus.length > 0 && (
          <DynamicMenuGroup
            title="主要功能"
            menus={functionalMenus}
            defaultExpanded={defaultExpanded}
          />
        )}

        {adminMenus.length > 0 && (
          <DynamicMenuGroup
            title="管理功能"
            menus={adminMenus}
            defaultExpanded={defaultExpanded}
          />
        )}

        {systemMenus.length > 0 && (
          <DynamicMenuGroup
            title="系统管理"
            menus={systemMenus}
            defaultExpanded={defaultExpanded}
          />
        )}

        {personalMenus.length > 0 && (
          <DynamicMenuGroup
            title="个人中心"
            menus={personalMenus}
            defaultExpanded={defaultExpanded}
          />
        )}
      </>
    );
  }

  // 不分组，渲染所有顶级菜单并保持排序
  const topLevelMenus = accessibleMenus
    .filter(menu => !menu.parent_id)
    .sort((a, b) => {
      // 首先按 sort_order 排序，然后按名称排序
      if (a.sort_order !== b.sort_order) {
        return (a.sort_order || 0) - (b.sort_order || 0);
      }
      return a.name.localeCompare(b.name);
    });
  
  return (
    <DynamicMenuGroup
      title="菜单"
      menus={topLevelMenus}
      defaultExpanded={defaultExpanded}
      showEmptyGroup={true}
    />
  );
};
// 权限统计组件