import { StringFormatParams } from "zod/v4/core";
import { PageParams } from ".";
import { StudentBaseInfo } from "@/services/homeworkReportApi";


export interface AdministrativeClassesStatistics {
  total_classes: Number,
  total_teacher: Number,
  total_students: Number,
}

export interface AdministrativeClasses {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id: String,
  created_at?: String,
  updated_at?: String,
  is_active: Boolean,
}

export interface CreateAdministrativeClassesParams {
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id?: String,
}
export interface AdministrativeClassesDetail {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id: String,
  created_at?: String,
  updated_at?: String,
  is_active: Boolean,
  //额外信息
  teacher_name?: String,
  grade_level_name?: String,
  total_student: Number,
}

export interface FindAllStudentInClassParams {
  class_id: String,
}

export interface PageStudentInClassParams{
  class_id: String,
  page_params: PageParams,
  name_like?: String,
  status?: String,
  student_number?: String,
}
// 接口定义
export interface ClassStudentHomeworkParams {
  class_id: string;
  subject_id?: string;
  time_range?: string;
}

export interface PageUserClassListParams {
  page_params: PageParams,
  name_like?: String,
  class_code?: String,
  is_active?: Boolean,
}

export interface UpdateAdministrativeClassesParams {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id?: String,
  is_active: Boolean,
}

export interface MoveStudentToAdministrativeClassesParams {
  class_id: String,
  student_id: String,
}

export interface RemoveStudentFromAdministrativeClassesParams {
  class_id: String,
  student_id: String,
}

export interface DeleteAdministrativeClassesParams {
  class_id: String,
}

//班级学生作业汇总分析报告
export interface StudentHomeworkAnalyse{
  student_base_info : StudentBaseInfo,
  total_homework: number,
  absent: number,
  homework_summary: HomeworkReportSummary[],
}
export interface HomeworkReportSummary{
  homework_id: string,
  status: HomeworkStudentStatus,
  score: number,
  rank: number,
  teaching_class_id: string,
}
export enum HomeworkStudentStatus{
  Unsubmitted = 'unsubmitted',
  Error = 'error',
  Done = 'done',
}