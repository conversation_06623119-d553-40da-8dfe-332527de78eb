export interface Book {
    id: string;
    title: string;
    subjectCode?: string;       // 学科
    gradeLevelCode?: string;   // 年级
    publisher?: string;          // 出版社
    distributor?: string;        // 发行机构
    year?: number;               // 出版年份
    coverPath?: string;         // 封面
    isbn?: string;
    edition?: string;            // 版本
    printingVersion?: string;   // 印次
    authors?: string[];          // 作者
    summary?: string;            // 简介或摘要
    updatedAt: string;          // ISO 时间字符串
}