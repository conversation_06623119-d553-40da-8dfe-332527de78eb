import { UserIdentity } from './role';

export interface User {
  user_id: string;
  username: string;
  roles: string[];
  is_admin: boolean;
  is_teacher: boolean;
  is_student: boolean;
  phone_number: string;
  created_at: string;
  phone_verified: boolean;
  is_active: boolean;
  // 新增：用户身份列表
  identities?: UserIdentity[];
  // 新增：主要身份
  primary_identity?: UserIdentity;
}

// 角色分配
export interface RoleAssignment {
  role_id: string;
  target_type?: string;
  target_id?: string;
  subject?: string;
  display_name?: string;
  is_primary?: boolean;
}

// 批量角色操作请求
export interface BatchRoleOperationRequest {
  user_ids: string[];
  operation: 'assign' | 'remove' | 'replace';
  role_assignments: RoleAssignment[];
}