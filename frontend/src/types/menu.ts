// 菜单使用统计
export interface MenuUsageStats {
  total_access_count: number;
  unique_user_count: number;
  denied_access_count: number;
  last_accessed_at?: string;
  avg_daily_access: number;
}

// 菜单项接口
export interface MenuItem {
  id?: string;
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level: number;
  sort_order: number;
  is_active: boolean;
  cache_enabled: boolean;
  metadata?: any;
  version: number;
  last_modified_by?: string;
  last_modified_at?: string;
  created_at?: string;
  updated_at?: string;
  children?: MenuItem[];
  children_count: number;
  depth_level: number;
  usage_stats?: MenuUsageStats;
}

// 权限模板接口
export interface PermissionTemplate {
  id: string;
  template_name: string;
  template_type: string;
  template_category?: string;
  permissions: string[];
  data_scopes?: string[];
  permission_mode: string;
  description?: string;
  usage_count: number;
  is_system_template: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 菜单查询参数
export interface MenuQueryParams {
  menu_type?: string;
  parent_id?: string;
  is_active?: boolean;
  include_children?: boolean;
  include_metadata?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
}

// 菜单创建请求
export interface MenuCreateRequest {
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type?: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level?: number;
  sort_order?: number;
  is_active?: boolean;
  metadata?: any;
}

// 菜单更新请求
export interface MenuUpdateRequest {
  name?: string;
  path?: string;
  icon?: string;
  parent_id?: string;
  menu_type?: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level?: number;
  sort_order?: number;
  is_active?: boolean;
  metadata?: any;
  reason?: string;
}

// 权限模板查询参数
export interface PermissionTemplateQueryParams {
  template_type?: string;
  template_category?: string;
  is_active?: boolean;
  is_system_template?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
}

// 组件Props类型定义
export interface MenuTreeNodeProps {
  menu: MenuItem;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  expandedNodes: Set<string>;
  selectedMenu: MenuItem | null;
  onToggle: (menuId: string) => void;
  onSelect: (menu: MenuItem) => void;
  onEdit: (menu: MenuItem) => void;
  onDelete: (menuId: string) => void;
}

export interface MenuConfigPanelProps {
  menu: MenuItem | null;
  templates: PermissionTemplate[];
  isEditing: boolean;
  isCreating: boolean;
  onSave: (data: Partial<MenuItem>) => void;
  onCancel: () => void;
  onEdit: () => void;
}

export interface FormComponentProps {
  data: Partial<MenuItem>;
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
}
export interface UsageStatsProps {
  stats: MenuUsageStats;
}

// 菜单树面板Props
export interface MenuTreePanelProps {
  menus: MenuItem[];
  loading: boolean;
  searchTerm: string;
  filterType: string;
  selectedMenu: MenuItem | null;
  expandedNodes: Set<string>;
  onSearchChange: (term: string) => void;
  onFilterChange: (type: string) => void;
  onMenuSelect: (menu: MenuItem | null) => void;
  onMenuEdit: (menu: MenuItem) => void;
  onMenuDelete: (menuId: string) => void;
  onCreateMenu: () => void;
  onToggleNode: (menuId: string) => void;
}

// 枚举类型
export type MenuType = 'functional' | 'admin' | 'personal' | 'system';
// 选项配置
export const MENU_TYPE_OPTIONS: { value: MenuType; label: string }[] = [
  { value: 'functional', label: '功能菜单' },
  { value: 'admin', label: '管理菜单' },
  { value: 'personal', label: '个人菜单' },
  { value: 'system', label: '系统菜单' },
];
