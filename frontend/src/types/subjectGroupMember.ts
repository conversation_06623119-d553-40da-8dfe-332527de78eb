// 学科组成员角色显示配置（基于角色代码）
export const SUBJECT_GROUP_MEMBER_ROLE_CONFIG: Record<string, {
  label: string;
  color: string;
  bgColor: string;
  borderColor: string;
  icon: string;
}> = {
  'subject_group_leader': {
    label: '组长',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    icon: 'Crown'
  },
  'subject_group_member': {
    label: '成员',
    color: 'text-gray-600', 
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    icon: 'User'
  }
};

// 获取角色配置的辅助函数
export const getRoleConfig = (roleCode: string) => {
  return SUBJECT_GROUP_MEMBER_ROLE_CONFIG[roleCode] || SUBJECT_GROUP_MEMBER_ROLE_CONFIG['subject_group_member'];
};

// 学科组成员基本信息
export interface SubjectGroupMember {
  id: string;
  subject_group_id: string;
  teacher_id: string;
  teacher_name: string;
  employee_id: string;
  user_id?: string;
  role_code: string;  // 改为使用角色代码
  role_name?: string; // 角色名称（可选）
  joined_at: string;
  is_active: boolean;
  // 扩展信息
  title?: string;
  avatar?: string;
  email?: string;
  phone?: string;
}

// 创建学科组成员参数
export interface CreateSubjectGroupMemberParams {
  subject_group_id: string;
  teacher_id: string;
  role_code: string;  // 改为使用角色代码
}

// 更新学科组成员参数
export interface UpdateSubjectGroupMemberParams {
  id: string;
  role_code?: string;  // 改为使用角色代码
  is_active?: boolean;
}

// 批量添加成员参数
export interface BatchAddMembersParams {
  subject_group_id: string;
  members: Array<{
    teacher_id: string;
    role_code: string;  // 改为使用角色代码
  }>;
}

// 学科组成员统计
export interface SubjectGroupMemberStats {
  total_members: number;
  leaders: number;
  deputy_leaders: number;
  regular_members: number;
  active_members: number;
  inactive_members: number;
}

// 推荐教师信息
export interface RecommendedTeacher {
  teacher_id: string;
  teacher_name: string;
  employee_id: string;
  title?: string;
  reason: 'teaching_class' | 'same_subject' | 'manual';
  reason_detail?: string;
  priority: number; // 推荐优先级 1-5
}

// 学科组成员查询参数
export interface SubjectGroupMemberQueryParams {
  subject_group_id: string;
  role_code?: string;  // 改为使用角色代码
  is_active?: boolean;
  keyword?: string;
}

// API响应类型
export interface SubjectGroupMemberResponse {
  success: boolean;
  data?: SubjectGroupMember[];
  message?: string;
}

export interface SubjectGroupMemberStatsResponse {
  success: boolean;
  data?: SubjectGroupMemberStats;
  message?: string;
}

export interface RecommendedTeachersResponse {
  success: boolean;
  data?: RecommendedTeacher[];
  message?: string;
}
