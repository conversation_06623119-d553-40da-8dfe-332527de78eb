export interface Tenant {
    id: string;
    name: string;
    schemaName: string;
    tenantType: string;
    status: string;
    createdAt: string;
    updatedAt: string;
}

export interface TenantUserAccess {
    id: string;
    user_id: string;
    username: string;
    phone_number: string;
    tenantId: string;
    access_type: 'member' | 'admin' | 'viewer' | 'suspended';
    grantedBy: string;
    granted_at: string;
    expires_at?: string;
    last_accessed_at?: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
}

export interface TenantStatsRes{
    total:number;
    active:number;
    deleted:number;
    schools:number;
    standard:number;
}

export type TenantQueryParams = {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean|null;
    status?: string; // 'active', 'inactive', 'suspended'
    tenant_type?: string; // 'school', 'standard'
    order_by?: string;  // 排序字段：name, schema_name, tenant_type, created_at
    order_direction?: string;  // 排序方向：asc, desc
}

// Default values
export const DEFAULT_TENANT_QUERY: TenantQueryParams = {
    page: 1,
    page_size: 10,
    is_active:null,
    status:'all',
    tenant_type:'all',
};

export interface CreateTenantUserAccessRequest {
    userId: string;
    tenantId: string;
    accessType: 'member' | 'admin' | 'viewer';
    expiresAt?: string;
}

export interface UpdateTenantUserAccessRequest {
    access_type?: 'member' | 'admin' | 'viewer' | 'suspended';
    expires_at?: string;
    notes?: string;
    notify_user?: boolean;
}

export interface TenantUserAccessQueryParams {
    page?: number;
    pageSize?: number;
    searchTerm?: string;
    accessType?: string;
    status?: string;
}

export interface TenantUserAccessResponse {
    data: TenantUserAccess[];
    pagination: {
        current: number;
        pageSize: number;
        total: number;
        totalPages: number;
    };
}
