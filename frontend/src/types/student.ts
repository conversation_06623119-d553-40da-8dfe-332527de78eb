// Student management types for the Deep-Mate frontend application

import { PageParams } from ".";

export interface Student {
  id: string;
  student_number: string;
  student_name: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_relation?: string;
  administrative_class_id?: string;
  grade_level_id?: string;
  user_id?: string;
  enrollment_date?: string;
  status: string;
  profile_level?: string;
  profile_tags?: any;
  notes?: string;
  created_at: string;
  updated_at: string;
  class_name: String,
  grade_level_name: String
}

export interface CreateStudentRequest {
  student_number: string;
  name: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_relation?: string;
  administrative_class_id?: string;
  grade_level_id?: string;
  user_id?: string;
  enrollment_date?: string;
  status?: string;
  profile_level?: string;
  profile_tags?: any;
  notes?: string;
}

export interface StudentSearchParams {
  name?: string;
  student_number?: string;
  id_number?: string;
  phone?: string;
  status?: string;
  profile_level?: string;
  page: number;
  pageSize: number;
  class_id: string,
  grade_level_id: string,
  limit: number,
  offset: number,
}

export interface StudentTeachingClass {
  id: string;
  student_id: string;
  class_id: string;
  subject: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentTeachingClassRequest {
  class_id: string;
  subject: string;
}

export interface StudentProfileLevel {
  id: string;
  student_id: string;
  subject: string;
  level: string;
  level_description?: string;
  assessment_date: string;
  assessed_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentProfileLevelRequest {
  subject: string;
  level: string;
  level_description?: string;
  assessment_date: string;
  assessed_by?: string;
}

export interface StudentProfileTag {
  id: string;
  student_id: string;
  tag_name: string;
  tag_value?: string;
  tag_category: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentProfileTagRequest {
  tag_name: string;
  tag_value?: string;
  tag_category: string;
  created_by?: string;
}

export interface StudentDetail {
  student: Student;
  class_name?: string;
  grade_level_name?: string;
  teaching_classes: StudentTeachingClass[];
  profile_levels: StudentProfileLevel[];
  profile_tags: StudentProfileTag[];
}

// Form validation schemas
export interface StudentFormData extends Omit<CreateStudentRequest, 'grade_level_id' | 'administrative_class_id'> {
  id?: string;
  grade_level_id: string;
  administrative_class_id: string;
}

export interface StudentFormErrors {
  student_id?: string;
  name?: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  guardian_phone?: string;
  enrollment_date?: string;
}

// Component prop types
export interface StudentTableProps {
  students: Student[];
  loading?: boolean;
  onEdit: (student: Student) => void;
  onDelete: (id: string) => void;
  onViewDetail: (student: Student) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

export interface StudentFormProps {
  student?: Student;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: StudentFormData) => Promise<void>;
  loading?: boolean;
  grades: Array<{ id: string; name: string; code: string }>;
  classes: Array<{ id: string; name: string; code: string }>;
}

export interface StudentDetailProps {
  student?: StudentDetail;
  open: boolean;
  onClose: () => void;
  loading?: boolean;
}

export interface StudentSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  classId?: string;
  gradeId?: string;
  className?: string;
}

// Filter and sort options
export const STUDENT_STATUS_OPTIONS = [
  { value: 'active', label: '在校' },
  { value: 'inactive', label: '休学' },
  { value: 'graduated', label: '毕业' },
  { value: 'transferred', label: '转学' },
] as const;

export const STUDENT_PROFILE_LEVELS = [
  { value: 'A+', label: 'A+' },
  { value: 'A', label: 'A' },
  { value: 'B+', label: 'B+' },
  { value: 'B', label: 'B' },
  { value: 'C+', label: 'C+' },
  { value: 'C', label: 'C' },
  { value: 'D+', label: 'D+' },
  { value: 'D', label: 'D' },
] as const;

export const STUDENT_TAG_CATEGORIES = [
  { value: 'academic', label: '学业' },
  { value: 'behavior', label: '行为' },
  { value: 'interest', label: '兴趣' },
  { value: 'ability', label: '能力' },
  { value: 'other', label: '其他' },
] as const;

export const GUARDIAN_RELATIONS = [
  { value: 'father', label: '父亲' },
  { value: 'mother', label: '母亲' },
  { value: 'elder_brother', label: '哥哥' },
  { value: 'elder_sister', label: '姐姐' },
  { value: 'paternal_grandfather', label: '爷爷' },
  { value: 'paternal_grandmother', label: '奶奶' },
  { value: 'maternal_grandfather', label: '外公' },
  { value: 'maternal_grandmother', label: '外婆' },
  { value: 'paternal_uncle', label: '伯父/叔父' },
  { value: 'paternal_aunt', label: '姑妈' },
  { value: 'maternal_uncle', label: '舅舅' },
  { value: 'maternal_aunt', label: '舅妈' },
  { value: 'other', label: '其他' },
] as const;

export const GENDER_OPTIONS = [
  { value: '男', label: '男' },
  { value: '女', label: '女' },
  { value: '未知', label: '未知'}
] as const;

// 默认搜索参数
export const DEFAULT_STUDENT_SEARCH: StudentSearchParams = {
  class_id: "",
  grade_level_id: "",
  limit: 0,
  offset: 0,
  name: '',
  student_number: '',
  id_number: '',
  phone: '',
  page: 1,
  pageSize: 10
};

export interface FindAllStudentParams {
  student_number?: String,
  name_like?: String,
  id_number?: String,
  phone?: String,
  page_params: PageParams,
}

export interface CreateStudentParams {
  student_number: String,
  name: String,
  gender?: String,
  birth_date?: String,
  id_number?: String,
  phone?: String,
  email?: String,
  address?: String,
  guardian_name?: String,
  guardian_phone?: String,
  guardian_relation?: String,
  administrative_class_id?: String,
  grade_level_id?: String,
  user_id?: String,
  enrollment_date?: String,
  status?: String,
  profile_level?: String,
  profile_tags?: any,
  notes?: String,
}

export interface UpdateStudentParams {
  id: String,
  student_number: String,
  name: String,
  gender?: String,
  birth_date?: String,
  id_number?: String,
  phone?: String,
  email?: String,
  address?: String,
  guardian_name?: String,
  guardian_phone?: String,
  guardian_relation?: String,
  administrative_class_id?: String,
  grade_level_id?: String,
  user_id?: String,
  enrollment_date?: String,
  status?: String,
  profile_level?: String,
  profile_tags?: any,
  notes?: String,
}

export interface StudentBaseInfo {
    id: String,
    student_number: String,
    student_name: String,
    gender?:String,
    phone?:String,
    administrative_class_id?:String,
    grade_level_id?:String,
    user_id?:String,
    status: String,
    created_at: String,
    updated_at: String,
}