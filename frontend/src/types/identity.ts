export interface CurrentUserData{
    user_id:string,
    username:string,
    phone_number:string,
    phone_verified:boolean,
    tenants:UserTenantInfo[],
}

export type Role = "admin" | "student" | "teacher" | "parent";

const roleMap: Record<Role, string> = {
    admin: "管理员",
    student: "学生",
    teacher: "老师",
    parent: "家长",
};

export function translateRole(role: Role): string {
    return roleMap[role];
}

export interface UserTenantInfo{
    tenant_id:string,
    tenant_name:string,
    schema_name:string,
    user_switchable_infos:Role[],
    user_hit_info?:Role|undefined,
}

export interface TenantInfo{
    tenant_id:string,
    tenant_name:string,
    schema_name:string,
    user_hit_info?:string|undefined,
}

/** @deprecated 身份相关采用currentUserData userRole tenant*/
export interface IdentityInfo{
    identity_id:string,
    tenant_id:string,
    tenant_name:string|null,
    identity_type:string,
    display_name:string|null,
}

export interface BindIdentityRequest{
    tenant_id:string,
    identity_type:string,
    extend_info:string|null,
}