// Grade management types for the Deep-Mate frontend application

export interface GradeLevel {
  id: string;
  code: string;
  name: string;
  description?: string;
  order_level: number;
  is_active: boolean;
  student_count: number;
  class_count: number;
  created_at: string;
  updated_at: string;
  education_stage_code: string | null;
  education_stage_name?: string;
  education_short_name?: string;
}

export interface GradeLevelSummary {
  id: string;
  code: string;
  name: string;
  is_active: boolean;
}

export interface GradeLevelStatistics {
  total_grades: number;
  active_grades: number;
  inactive_grades: number;
  usage_stats: GradeLevelUsageStats[];
}

export interface GradeLevelUsageStats {
  grade_id: string;
  grade_name: string;
  student_count: number;
  class_count: number;
  exam_count?: number;
}

export interface CreateGradeLevelRequest {
  code: string;
  name: string;
  description?: string;
  order_level: number;
  education_stage_code?: string | null;
}

export interface UpdateGradeLevelRequest {
  name?: string;
  description?: string;
  order_level?: number;
  is_active?: boolean;
  education_stage_code?: string | null;
}

export interface GradeLevelQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  is_active?: boolean;
  order_by?: 'name' | 'code' | 'order_level' | 'created_at';
  order_direction?: 'asc' | 'desc';
  stage_code?: string;
}

export interface GradeLevelOrderUpdate {
  grade_id: string;
  order_level: number;
}

// Form validation schemas
export interface GradeLevelFormData extends Omit<CreateGradeLevelRequest, 'education_stage_code'> {
  id?: string;
  education_stage_code: string | null;
}

export interface GradeLevelFormErrors {
  code?: string;
  name?: string;
  description?: string;
  order_level?: string;
  education_stage_code?: string;
}

// Component prop types
export interface GradeLevelTableProps {
  grades: GradeLevel[];
  loading?: boolean;
  onEdit: (grade: GradeLevel) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

export interface GradeLevelFormProps {
  grade?: GradeLevel;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: GradeLevelFormData) => Promise<void>;
  loading?: boolean;
}

export interface GradeLevelSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  showInactive?: boolean;
  className?: string;
}

// Filter and sort options
export const GRADE_SORT_OPTIONS = [
  { value: 'order_level', label: '排序级别' },
  { value: 'name', label: '年级名称' },
  { value: 'code', label: '年级代码' },
  { value: 'created_at', label: '创建时间' },
] as const;

export const GRADE_STATUS_OPTIONS = [
  { value: true, label: '启用' },
  { value: false, label: '禁用' },
] as const;

// Default values
export const DEFAULT_GRADE_QUERY: GradeLevelQueryParams = {
  page: 1,
  page_size: 10,
  search: '',
  is_active: undefined,
  order_by: 'order_level',
  order_direction: 'asc',
  stage_code: '',
};

// Grade codes constants (matching backend)
export const SYSTEM_GRADES = {
  G1: 'G1',
  G2: 'G2',
  G3: 'G3',
  G4: 'G4',
  G5: 'G5',
  G6: 'G6',
  G7: 'G7',
  G8: 'G8',
  G9: 'G9',
  G10: 'G10',
  G11: 'G11',
  G12: 'G12',
} as const;

export const GRADE_NAME_MAP = {
  [SYSTEM_GRADES.G1]: '一年级',
  [SYSTEM_GRADES.G2]: '二年级',
  [SYSTEM_GRADES.G3]: '三年级',
  [SYSTEM_GRADES.G4]: '四年级',
  [SYSTEM_GRADES.G5]: '五年级',
  [SYSTEM_GRADES.G6]: '六年级',
  [SYSTEM_GRADES.G7]: '七年级',
  [SYSTEM_GRADES.G8]: '八年级',
  [SYSTEM_GRADES.G9]: '九年级',
  [SYSTEM_GRADES.G10]: '十年级',
  [SYSTEM_GRADES.G11]: '十一年级',
  [SYSTEM_GRADES.G12]: '十二年级',
} as const;