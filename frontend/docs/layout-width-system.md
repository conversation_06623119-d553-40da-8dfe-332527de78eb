# Layout Width System

This project uses a simplified layout width system based on shadcn/ui principles, with two main modes: standard width and full-width.

## Components

### PageContainer
A container component that wraps page content with configurable max-width.

```tsx
import { PageContainer } from '@/components/layout/PageContainer';

<PageContainer maxWidth="default" padding={true}>
  {/* Your content */}
</PageContainer>
```

**Props:**
- `maxWidth`: `'default' | 'full'`
- `padding`: boolean (default: true) - adds responsive horizontal padding
- `className`: additional CSS classes

### Layout Width Hook
Use `usePageWidth` in your page components to set the preferred width:

```tsx
import { usePageWidth } from '@/hooks/useLayoutWidth';

export default function MyPage() {
  // Set this page to use full width
  usePageWidth('full');
  
  return (
    <div>
      {/* Page content */}
    </div>
  );
}
```

## Width Options

- `default`: max-w-7xl (1280px) - standard width for most pages
- `full`: max-w-none - full viewport width for dashboards and data-heavy pages

## Usage Examples

### Standard Page (Default)
```tsx
// No need to call usePageWidth - uses 'default' automatically
export default function StandardPage() {
  return (
    <div className="space-y-6">
      <h1>Standard Width Page</h1>
      {/* Content will be constrained to max-w-7xl */}
    </div>
  );
}
```

### Full Width Page (e.g., Dashboard, Statistics)
```tsx
import { usePageWidth } from '@/hooks/useLayoutWidth';

export default function DashboardPage() {
  usePageWidth('full');
  
  return (
    <div className="space-y-6">
      <h1>Full Width Dashboard</h1>
      {/* Content will use full viewport width */}
    </div>
  );
}
```


## Implementation Details

The system is implemented using:
1. **LayoutWidthProvider**: Context provider that manages the current width setting
2. **useLayoutWidth**: Hook for accessing and updating the width context
3. **usePageWidth**: Convenience hook for pages to set their preferred width
4. **PageContainer**: Component that applies the width constraints
5. **RootLayoutWithPermissions**: Updated to use the new system

The width setting automatically resets to default when navigating between pages, ensuring each page can have its own width preference without affecting others.
