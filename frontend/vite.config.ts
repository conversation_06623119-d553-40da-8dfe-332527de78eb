import react from "@vitejs/plugin-react"
import path from "path"
import { defineConfig } from "vite"

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler'
      },
      sass: {
        api: 'modern-compiler'
      }
    }
  },
  server: {
    host: '0.0.0.0',
    open: true,
    proxy: {
      '/files': {
        target: 'http://localhost:3000', // 目标服务器地址
        changeOrigin: true, // 修改请求头中的 Host 为目标 URL
      }
    }
  },
})