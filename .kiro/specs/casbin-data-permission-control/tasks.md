# Casbin 数据权限控制实施计划

## 实施任务

- [x] 1. 实现 RBAC + ABAC 混合权限模型
  - 扩展 Casbin 模型支持多重角色继承（g, g2）
  - 实现 ABAC 属性匹配函数（hasAttribute, inScope）
  - 创建用户属性和资源属性数据结构
  - 设计权限层级继承关系
  - _需求: 1.1, 1.2, 1.3_

- [x] 2. 实现权限上下文构建器
  - [x] 2.1 创建 PermissionContext 结构体
    - 定义用户权限上下文数据结构
    - 实现权限上下文的序列化和反序列化
    - 添加权限范围合并逻辑
    - _需求: 1.3, 6.2_

  - [x] 2.2 实现权限上下文构建服务
    - 从用户角色构建完整的权限上下文
    - 实现多角色权限范围合并算法
    - 添加权限继承逻辑处理
    - _需求: 1.2, 1.3_

- [x] 3. 开发数据过滤器组件
  - [x] 3.1 实现业务层角色的数据过滤器
    - 定义从业务表获取用户角色的数据过滤器
    - 实现基于角色和数据范围匹配的权限检查
    - 实现资源 scope 包含在 obj 中的权限验证
    - _需求: 2.1, 2.2_

  - [x] 3.2 实现数据范围匹配系统
    - 开发教学班、学科组、年级等范围匹配逻辑
    - 实现范围继承关系的验证功能
    - 添加范围匹配结果的缓存优化
    - _需求: 2.1, 2.2, 2.3_

  - [x] 3.3 实现通配符策略权限检查
    - 开发基于 tenant_* 通配符的权限验证
    - 实现特殊租户策略的优先级处理
    - 添加批量权限检查优化
    - _需求: 2.3, 4.2, 4.3_

- [ ] 4. 构建权限缓存系统
  - [ ] 4.1 实现权限缓存服务接口
    - 定义 PermissionCacheService trait
    - 实现基于 Redis 的缓存服务
    - 添加缓存键命名规范和 TTL 管理
    - _需求: 3.1, 3.2, 3.4_

  - [ ] 4.2 实现多层缓存策略
    - 实现用户权限上下文缓存
    - 添加角色权限策略缓存
    - 实现查询结果缓存机制
    - _需求: 3.1, 3.2, 3.4_

  - [ ] 4.3 开发缓存失效和更新机制
    - 实现权限变更时的缓存清理
    - 添加缓存预热功能
    - 实现缓存一致性保障
    - _需求: 3.2, 6.3_

- [-] 5. 实现权限中间件
  - [x] 5.1 创建权限验证中间件
    - 实现 HTTP 请求权限拦截
    - 添加权限上下文注入功能
    - 实现权限验证失败的错误处理
    - _需求: 4.2, 4.3, 5.3_

  - [ ] 5.2 集成审计日志功能
    - 实现权限检查日志记录
    - 添加敏感操作审计功能
    - 实现异常访问检测和告警
    - _需求: 5.1, 5.2, 5.3_

- [ ] 6. 开发策略管理器
  - [ ] 6.1 实现权限策略 CRUD 操作
    - 创建 PolicyManager 结构体
    - 实现数据权限策略的增删改查
    - 添加策略验证和冲突检测
    - _需求: 6.1, 6.2_

  - [ ] 6.2 实现批量权限管理功能
    - 开发角色权限批量更新功能
    - 实现组织结构变更时的权限调整
    - 添加权限配置导入导出功能
    - _需求: 6.3, 6.4_

- [x] 7. 扩展现有服务集成权限控制
  - [x] 7.1 更新 CasbinService 支持通配符策略
    - 扩展现有的 CasbinPermissionService 支持 tenant_* 通配符
    - 实现业务层角色 + Casbin 策略的权限检查
    - 添加通用策略的管理功能
    - 实现特殊租户策略的配置功能
    - _需求: 2.1, 2.2, 9.1_

  - [x] 7.2 实现基于业务表的教育权限控制
    - 实现通过 teachers 表确定任课老师权限范围
    - 实现通过 subject_group_members 表确定学科组权限
    - 实现通过 teachers.grade_level_id 确定年级组长权限
    - 实现通过 teachers.homeroom_class_id 确定班主任权限
    - 添加多重角色权限范围合并逻辑
    - _需求: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8_

  - [x] 7.3 集成到业务服务层
    - 更新作业管理服务集成教师权限过滤
    - 更新考试管理服务集成权限控制
    - 更新成绩管理服务集成权限验证
    - 更新学生管理服务集成权限范围控制
    - _需求: 8.1, 8.2, 8.3, 8.4_

- [-] 8. 实现管理员角色分配和教师绑定管理
  - [x] 8.1 开发业务层角色管理服务
    - 实现在 user_identities 表中的角色分配管理
    - 实现从业务表查询用户角色的服务
    - 实现角色分配验证和冲突检测
    - 添加角色变更审计日志
    - _需求: 8.1, 8.2, 8.4, 8.7_

  - [x] 8.2 开发教师绑定管理服务
    - 实现教师记录创建和用户绑定
    - 实现学科组成员关系管理
    - 实现学科组长角色自动分配
    - 实现教师信息批量导入功能
    - _需求: 8.3, 8.5, 8.7, 8.8_

  - [-] 8.3 实现权限预览和验证功能
    - 开发用户权限范围预览服务
    - 实现角色分配前的权限变化预览
    - 添加权限配置验证工具
    - 实现权限一致性检查功能
    - _需求: 8.6, 6.4, 9.4_

- [-] 9. 开发权限管理界面 API
  - [ ] 9.1 创建用户角色管理 API
    - 实现用户列表和角色查询 API
    - 实现角色分配和移除 API
    - 实现批量角色操作 API
    - 添加权限预览 API
    - _需求: 8.1, 8.2, 8.6, 8.7_

  - [ ] 9.2 创建教师管理 API
    - 实现教师信息 CRUD API
    - 实现学科组成员管理 API
    - 实现教师绑定关系管理 API
    - 实现批量导入导出 API
    - _需求: 8.3, 8.5, 8.7, 8.8_

- [ ] 10. 性能优化和监控
  - [ ] 10.1 实现性能监控指标
    - 添加权限检查耗时监控
    - 实现缓存命中率统计
    - 添加数据过滤性能监控
    - _需求: 3.1, 3.3_

  - [ ] 10.2 优化查询性能
    - 实现权限检查批量化处理
    - 优化数据库查询索引
    - 添加查询结果预编译缓存
    - _需求: 3.1, 3.3_

- [ ] 11. 完善通配符权限策略配置
  - [ ] 11.1 创建标准化通配符策略模板
    - 定义教育场景的标准权限策略模板
    - 实现策略模板的自动化部署
    - 添加策略模板验证和测试
    - _需求: 1.1, 2.1, 7.1_

  - [ ] 11.2 优化现有通配符策略
    - 审查和优化现有的 tenant_* 通配符策略
    - 确保所有教育角色的权限策略完整性
    - 添加缺失的数据范围权限策略
    - _需求: 7.1, 7.2, 7.3, 7.4_

- [ ] 12. 测试和文档
  - [ ] 12.1 编写单元测试
    - 创建权限模型验证测试
    - 实现数据过滤器功能测试
    - 添加缓存服务测试用例
    - _需求: 所有需求_

  - [ ] 12.2 编写集成测试
    - 实现端到端权限验证测试
    - 添加多租户权限隔离测试
    - 创建权限一致性验证测试
    - _需求: 7.1, 7.2, 7.3, 7.4_

  - [ ] 12.3 完善文档和示例
    - 编写权限配置使用指南
    - 创建权限调试和故障排除文档
    - 添加最佳实践和安全建议
    - _需求: 所有需求_