# Casbin 数据权限控制设计文档

## 核心理念

**Casbin 通配符 + 业务表驱动的权限控制**：
- 使用 Casbin 的通配符规则定义数据范围权限
- 业务表提供具体的权限范围数据
- 权限检查时动态匹配通配符规则

## Casbin 模型配置

### 权限模型定义

```conf
[request_definition]
r = sub, dom, obj, act

[policy_definition]  
p = sub, dom, obj, act, eft

[role_definition]
g = _, _, _

[policy_effect]
e = some(where (p.eft == allow)) && !some(where (p.eft == deny))

[matchers]
m = g(r.sub, p.sub, r.dom) && (r.dom == p.dom || p.dom == "tenant_*") && keyMatch2(r.obj, p.obj) && r.act == p.act
```

**关键点：**
- 使用 `keyMatch2` 函数支持通配符匹配
- 对象格式：`resource:scope_type:scope_value`
- 支持多层通配符：`*`, `**`, `/path/*`

## 通配符权限策略

### 1. 数据范围通配符策略

```sql
-- 任课老师权限（教学班范围）
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4) VALUES
('p', 'teacher', 'tenant_*', 'homework:teaching_class:*', 'read', 'allow'),
('p', 'teacher', 'tenant_*', 'homework:teaching_class:*', 'create', 'allow'),
('p', 'teacher', 'tenant_*', 'homework:teaching_class:*', 'update', 'allow'),
('p', 'teacher', 'tenant_*', 'grade:teaching_class:*', 'read', 'allow'),
('p', 'teacher', 'tenant_*', 'grade:teaching_class:*', 'create', 'allow'),
('p', 'teacher', 'tenant_*', 'student:teaching_class:*', 'read', 'allow');

-- 学科组长权限（学科组范围）
('p', 'subject_leader', 'tenant_*', 'homework:subject_group:*', 'manage', 'allow'),
('p', 'subject_leader', 'tenant_*', 'grade:subject_group:*', 'manage', 'allow'),
('p', 'subject_leader', 'tenant_*', 'teaching_class:subject_group:*', 'manage', 'allow'),
('p', 'subject_leader', 'tenant_*', 'student:subject_group:*', 'read', 'allow');

-- 年级组长权限（年级范围）
('p', 'grade_leader', 'tenant_*', 'admin_class:grade:*', 'manage', 'allow'),
('p', 'grade_leader', 'tenant_*', 'student:grade:*', 'read', 'allow'),
('p', 'grade_leader', 'tenant_*', 'homework:grade:*', 'read', 'allow'),
('p', 'grade_leader', 'tenant_*', 'grade:grade:*', 'read', 'allow');

-- 班主任权限（行政班范围）
('p', 'class_teacher', 'tenant_*', 'student:admin_class:*', 'manage', 'allow'),
('p', 'class_teacher', 'tenant_*', 'admin_class:admin_class:*', 'read', 'allow');

-- 教导主任权限（学校范围）
('p', 'academic_director', 'tenant_*', 'exam:school:*', 'manage', 'allow'),
('p', 'academic_director', 'tenant_*', 'curriculum:school:*', 'manage', 'allow'),
('p', 'academic_director', 'tenant_*', 'teacher:school:*', 'manage', 'allow');

-- 校长权限（学校全部数据）
('p', 'principal', 'tenant_*', '*:school:*', '*', 'allow');

-- 系统管理员权限（租户全部数据）
('p', 'admin', 'tenant_*', '*', '*', 'allow');
```

### 2. 对象格式说明

**格式：** `resource:scope_type:scope_value`

**示例：**
- `homework:teaching_class:uuid-123` - 特定教学班的作业
- `student:subject_group:*` - 学科组范围的所有学生
- `admin_class:grade:grade_10` - 高一年级的行政班
- `*:school:school-001` - 指定学校的所有资源

### 3. 角色继承关系

```sql
-- 教学管理线继承
INSERT INTO casbin_policies (ptype, v0, v1, v2) VALUES
('g', 'teacher', 'subject_leader', 'tenant_*'),
('g', 'subject_leader', 'academic_director', 'tenant_*'),

-- 学生管理线继承
('g', 'class_teacher', 'grade_leader', 'tenant_*'),
('g', 'grade_leader', 'academic_director', 'tenant_*'),

-- 最高管理层继承
('g', 'academic_director', 'principal', 'tenant_*'),
('g', 'principal', 'admin', 'tenant_*');
```

## 权限检查实现

### 1. 动态权限检查

```rust
impl CasbinPermissionService {
    /// 检查用户对特定资源的权限
    pub async fn check_resource_permission(
        &self,
        user_id: Uuid,
        resource: &str,
        action: &str,
        scope_type: &str,
        scope_value: &str,
        tenant_id: &str,
    ) -> Result<bool, AppError> {
        // 1. 获取用户角色
        let user_roles = self.get_user_roles(user_id, tenant_id).await?;
        
        // 2. 构建权限检查对象
        let obj = format!("{}:{}:{}", resource, scope_type, scope_value);
        
        // 3. 逐个检查用户角色权限
        for role in user_roles {
            let subject = role.code.clone(); // 直接使用角色代码，不加前缀
            
            let permission_request = PermissionRequest {
                subject,
                domain: tenant_id.to_string(),
                object: obj.clone(),
                action: action.to_string(),
            };
            
            if self.enforce(&permission_request).await? {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// 批量检查权限（用于数据过滤）
    pub async fn check_batch_permissions(
        &self,
        user_id: Uuid,
        resource: &str,
        action: &str,
        scope_items: &[(String, String)], // (scope_type, scope_value)
        tenant_id: &str,
    ) -> Result<Vec<bool>, AppError> {
        let user_roles = self.get_user_roles(user_id, tenant_id).await?;
        let mut results = Vec::new();
        
        for (scope_type, scope_value) in scope_items {
            let obj = format!("{}:{}:{}", resource, scope_type, scope_value);
            let mut has_permission = false;
            
            for role in &user_roles {
                let subject = role.code.clone(); // 直接使用角色代码
                
                let permission_request = PermissionRequest {
                    subject,
                    domain: tenant_id.to_string(),
                    object: obj.clone(),
                    action: action.to_string(),
                };
                
                if self.enforce(&permission_request).await? {
                    has_permission = true;
                    break;
                }
            }
            
            results.push(has_permission);
        }
        
        Ok(results)
    }
}
```

### 2. 业务表驱动的范围获取

```rust
impl BusinessScopeProvider {
    /// 获取用户的教学班范围
    pub async fn get_user_teaching_class_scopes(
        &self,
        user_id: Uuid,
        schema_name: &str,
    ) -> Result<Vec<String>, AppError> {
        let query = format!(
            r#"
            SELECT tc.id::text as scope_value
            FROM "{schema}".teaching_classes tc
            JOIN "{schema}".teachers t ON tc.teacher_id = t.id
            WHERE t.user_id = $1 AND tc.is_active = true
            "#,
            schema = schema_name
        );
        
        let scope_values = sqlx::query_scalar::<_, String>(&query)
            .bind(user_id)
            .fetch_all(&self.db)
            .await?;
        
        Ok(scope_values)
    }
    
    /// 获取用户的学科组范围
    pub async fn get_user_subject_group_scopes(
        &self,
        user_id: Uuid,
        schema_name: &str,
    ) -> Result<Vec<String>, AppError> {
        let query = format!(
            r#"
            SELECT sgm.subject_group_id::text as scope_value
            FROM "{schema}".subject_group_members sgm
            JOIN "{schema}".teachers t ON sgm.teacher_id = t.id
            WHERE t.user_id = $1 AND sgm.role_code = 'leader' AND sgm.is_active = true
            "#,
            schema = schema_name
        );
        
        let scope_values = sqlx::query_scalar::<_, String>(&query)
            .bind(user_id)
            .fetch_all(&self.db)
            .await?;
        
        Ok(scope_values)
    }
    
    /// 获取用户的年级范围
    pub async fn get_user_grade_scopes(
        &self,
        user_id: Uuid,
        schema_name: &str,
    ) -> Result<Vec<String>, AppError> {
        let query = format!(
            r#"
            SELECT gg.grade_level_code as scope_value
            FROM "{schema}".grade_groups gg
            JOIN "{schema}".teachers t ON gg.leader_teacher_id = t.id
            WHERE t.user_id = $1 AND gg.is_active = true
            "#,
            schema = schema_name
        );
        
        let scope_values = sqlx::query_scalar::<_, String>(&query)
            .bind(user_id)
            .fetch_all(&self.db)
            .await?;
        
        Ok(scope_values)
    }
}
```

### 3. 数据过滤器实现

```rust
impl DataFilter for CasbinDataFilter {
    /// 为查询添加权限过滤条件
    async fn apply_filter(
        &self,
        query: &mut QueryBuilder,
        context: &PermissionContext,
        resource: &str,
        action: &str,
        schema_name: &str,
    ) -> Result<(), AppError> {
        // 1. 获取用户在各个范围的权限
        let mut conditions = Vec::new();
        
        // 检查教学班范围权限
        let teaching_class_scopes = self.scope_provider
            .get_user_teaching_class_scopes(context.user_id, schema_name).await?;
        
        for scope_value in teaching_class_scopes {
            if self.casbin_service.check_resource_permission(
                context.user_id,
                resource,
                action,
                "teaching_class",
                &scope_value,
                &context.tenant_id.to_string(),
            ).await? {
                conditions.push(format!("teaching_class_id = '{}'", scope_value));
            }
        }
        
        // 检查学科组范围权限
        let subject_group_scopes = self.scope_provider
            .get_user_subject_group_scopes(context.user_id, schema_name).await?;
        
        for scope_value in subject_group_scopes {
            if self.casbin_service.check_resource_permission(
                context.user_id,
                resource,
                action,
                "subject_group",
                &scope_value,
                &context.tenant_id.to_string(),
            ).await? {
                // 通过学科组获取相关的教学班
                let related_classes = self.get_subject_group_teaching_classes(&scope_value, schema_name).await?;
                for class_id in related_classes {
                    conditions.push(format!("teaching_class_id = '{}'", class_id));
                }
            }
        }
        
        // 检查年级范围权限
        let grade_scopes = self.scope_provider
            .get_user_grade_scopes(context.user_id, schema_name).await?;
        
        for scope_value in grade_scopes {
            if self.casbin_service.check_resource_permission(
                context.user_id,
                resource,
                action,
                "grade",
                &scope_value,
                &context.tenant_id.to_string(),
            ).await? {
                match resource {
                    "admin_class" => {
                        conditions.push(format!("grade_level_code = '{}'", scope_value));
                    }
                    "student" => {
                        conditions.push(format!(
                            "admin_class_id IN (SELECT id FROM \"{}\".admin_classes WHERE grade_level_code = '{}')",
                            schema_name, scope_value
                        ));
                    }
                    _ => {}
                }
            }
        }
        
        // 应用过滤条件
        if !conditions.is_empty() {
            query.push(" AND (");
            query.push(&conditions.join(" OR "));
            query.push(")");
        } else {
            query.push(" AND 1 = 0"); // 无权限
        }
        
        Ok(())
    }
}
```

## 使用示例

### 1. 权限检查示例

```rust
// 检查用户是否可以访问特定教学班的作业
let can_access = casbin_service.check_resource_permission(
    user_id,
    "homework",           // 资源类型
    "read",              // 操作
    "teaching_class",    // 范围类型
    "class-uuid-123",    // 范围值
    "tenant_001"         // 租户
).await?;

// 检查用户是否可以管理学科组
let can_manage = casbin_service.check_resource_permission(
    user_id,
    "teaching_class",
    "manage",
    "subject_group",
    "math-group-456",
    "tenant_001"
).await?;
```

### 2. 控制器中的使用

```rust
impl HomeworkController {
    /// 获取作业列表
    pub async fn get_homework_list(
        &self,
        auth_context: AuthContext,
    ) -> Result<ApiResponse<Vec<HomeworkVO>>, AppError> {
        // 构建基础查询
        let mut query = QueryBuilder::new(
            "SELECT h.*, tc.name as class_name FROM homeworks h 
             JOIN teaching_classes tc ON h.teaching_class_id = tc.id 
             WHERE 1=1"
        );
        
        // 应用权限过滤
        self.data_filter.apply_filter(
            &mut query,
            &auth_context.permission_context,
            "homework",
            "read",
            &auth_context.schema_name,
        ).await?;
        
        // 执行查询
        let homeworks = query.build_query_as::<HomeworkVO>()
            .fetch_all(&self.db)
            .await?;
        
        Ok(ApiResponse::success(homeworks))
    }
}
```

## 优势总结

### 1. Casbin 通配符的优势
- **灵活的模式匹配**：支持 `*`, `**`, `/path/*` 等多种通配符
- **策略简化**：一条通配符策略可以覆盖多个具体权限
- **性能优化**：Casbin 内置的高效匹配算法
- **易于维护**：减少策略数量，降低维护复杂度

### 2. 业务表驱动的优势
- **数据一致性**：权限范围与业务数据保持同步
- **灵活扩展**：新增业务关系无需修改权限策略
- **精确控制**：支持复杂的业务权限逻辑

### 3. 组合方案的优势
- **最佳实践**：结合 Casbin 的策略引擎和业务表的数据准确性
- **高性能**：通配符匹配 + 缓存优化
- **易于理解**：权限策略清晰，业务逻辑明确

## 通配符规则详解

### 1. 通配符策略配置

Casbin 完全支持通配符规则，可以大幅简化数据范围权限配置：

```sql
-- 使用通配符的权限策略
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4) VALUES
-- 任课老师权限（教学班范围通配符）
('p', 'role:teacher', 'tenant_*', 'homework:teaching_class:*', 'read', 'allow'),
('p', 'role:teacher', 'tenant_*', 'homework:teaching_class:*', 'create', 'allow'),
('p', 'role:teacher', 'tenant_*', 'grade:teaching_class:*', 'read', 'allow'),
('p', 'role:teacher', 'tenant_*', 'student:teaching_class:*', 'read', 'allow'),

-- 学科组长权限（学科组范围通配符）
('p', 'role:subject_leader', 'tenant_*', 'homework:subject_group:*', 'manage', 'allow'),
('p', 'role:subject_leader', 'tenant_*', 'teaching_class:subject_group:*', 'manage', 'allow'),
('p', 'role:subject_leader', 'tenant_*', 'student:subject_group:*', 'read', 'allow'),

-- 年级组长权限（年级范围通配符）
('p', 'role:grade_leader', 'tenant_*', 'admin_class:grade:*', 'manage', 'allow'),
('p', 'role:grade_leader', 'tenant_*', 'student:grade:*', 'read', 'allow'),
('p', 'role:grade_leader', 'tenant_*', 'homework:grade:*', 'read', 'allow'),

-- 班主任权限（行政班范围通配符）
('p', 'role:class_teacher', 'tenant_*', 'student:admin_class:*', 'manage', 'allow'),

-- 校长权限（学校范围通配符）
('p', 'role:principal', 'tenant_*', '*:school:*', '*', 'allow'),

-- 管理员权限（租户范围通配符）
('p', 'role:admin', 'tenant_*', '*:tenant:*', '*', 'allow');
```

### 2. 通配符规则格式

**对象格式：** `resource:scope_type:scope_value`

- `homework:teaching_class:*` - 匹配所有教学班范围的作业
- `student:subject_group:*` - 匹配所有学科组范围的学生  
- `*:grade:*` - 匹配年级范围的所有资源
- `*:*:*` - 匹配所有资源和范围

### 3. 权限检查流程

```rust
pub struct WildcardPermissionChecker {
    db: PgPool,
    casbin_service: Arc<dyn CasbinPermissionService>,
}

impl WildcardPermissionChecker {
    /// 检查具体资源的访问权限
    pub async fn check_resource_permission(
        &self,
        user_id: Uuid,
        resource: &str,
        action: &str,
        resource_id: Uuid,
        schema_name: &str,
    ) -> Result<bool, AppError> {
        // 1. 获取用户角色
        let user_roles = self.get_user_roles(user_id, schema_name).await?;
        
        // 2. 获取资源的范围信息
        let resource_scope = self.get_resource_scope(resource, resource_id, schema_name).await?;
        
        // 3. 检查每个角色是否有权限
        for role in user_roles {
            let obj = format!("{}:{}:{}", resource, resource_scope.scope_type, resource_scope.scope_value);
            
            let permission_request = PermissionRequest {
                subject: format!("role:{}", role.code),
                domain: format!("tenant_{}", schema_name),
                object: obj,
                action: action.to_string(),
            };
            
            // Casbin 会自动使用通配符规则进行匹配
            if self.casbin_service.enforce(&permission_request).await? {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// 获取资源的范围信息（从业务表）
    async fn get_resource_scope(
        &self,
        resource: &str,
        resource_id: Uuid,
        schema_name: &str,
    ) -> Result<ResourceScope, AppError> {
        match resource {
            "homework" => {
                // 获取作业所属的教学班
                let query = format!(
                    "SELECT teaching_class_id FROM \"{}\".homeworks WHERE id = $1",
                    schema_name
                );
                let teaching_class_id: Uuid = sqlx::query_scalar(&query)
                    .bind(resource_id)
                    .fetch_one(&self.db)
                    .await?;
                
                Ok(ResourceScope {
                    scope_type: "teaching_class".to_string(),
                    scope_value: teaching_class_id.to_string(),
                })
            }
            
            "student" => {
                // 获取学生所属的行政班
                let query = format!(
                    "SELECT admin_class_id FROM \"{}\".students WHERE id = $1",
                    schema_name
                );
                let admin_class_id: Uuid = sqlx::query_scalar(&query)
                    .bind(resource_id)
                    .fetch_one(&self.db)
                    .await?;
                
                Ok(ResourceScope {
                    scope_type: "admin_class".to_string(),
                    scope_value: admin_class_id.to_string(),
                })
            }
            
            _ => Err(AppError::ValidationError(format!("不支持的资源类型: {}", resource))),
        }
    }
}
```

### 4. 数据过滤器（通配符版本）

```rust
impl DataFilter for WildcardDataFilter {
    /// 为查询添加权限过滤条件
    async fn apply_filter(
        &self,
        query: &mut QueryBuilder,
        context: &PermissionContext,
        resource: &str,
        action: &str,
        schema_name: &str,
    ) -> Result<(), AppError> {
        // 1. 获取用户可访问的范围类型
        let accessible_scope_types = self.get_accessible_scope_types(
            context.user_id, resource, action, schema_name
        ).await?;
        
        if accessible_scope_types.is_empty() {
            query.push(" AND 1 = 0"); // 无权限
            return Ok(());
        }
        
        // 2. 根据范围类型构建SQL条件
        let mut conditions = Vec::new();
        
        for scope_type in accessible_scope_types {
            match scope_type.as_str() {
                "teaching_class" => {
                    let class_ids = self.get_user_teaching_classes(context.user_id, schema_name).await?;
                    if !class_ids.is_empty() {
                        let ids_str = class_ids.iter()
                            .map(|id| format!("'{}'", id))
                            .collect::<Vec<_>>()
                            .join(",");
                        conditions.push(format!("teaching_class_id IN ({})", ids_str));
                    }
                }
                
                "subject_group" => {
                    let class_ids = self.get_subject_group_teaching_classes(context.user_id, schema_name).await?;
                    if !class_ids.is_empty() {
                        let ids_str = class_ids.iter()
                            .map(|id| format!("'{}'", id))
                            .collect::<Vec<_>>()
                            .join(",");
                        conditions.push(format!("teaching_class_id IN ({})", ids_str));
                    }
                }
                
                "grade" => {
                    let grade_conditions = self.build_grade_conditions(context.user_id, resource, schema_name).await?;
                    conditions.extend(grade_conditions);
                }
                
                "admin_class" => {
                    let class_ids = self.get_user_admin_classes(context.user_id, schema_name).await?;
                    if !class_ids.is_empty() {
                        let ids_str = class_ids.iter()
                            .map(|id| format!("'{}'", id))
                            .collect::<Vec<_>>()
                            .join(",");
                        conditions.push(format!("admin_class_id IN ({})", ids_str));
                    }
                }
                
                "tenant" => {
                    // 租户级权限，无需添加条件
                    return Ok(());
                }
                
                _ => {}
            }
        }
        
        if !conditions.is_empty() {
            query.push(" AND (");
            query.push(&conditions.join(" OR "));
            query.push(")");
        }
        
        Ok(())
    }
    
    /// 获取用户可访问的范围类型
    async fn get_accessible_scope_types(
        &self,
        user_id: Uuid,
        resource: &str,
        action: &str,
        schema_name: &str,
    ) -> Result<Vec<String>, AppError> {
        let user_roles = self.get_user_roles(user_id, schema_name).await?;
        let mut scope_types = Vec::new();
        
        for role in user_roles {
            // 检查角色对各种范围类型的权限
            let scope_checks = vec![
                ("teaching_class", format!("{}:teaching_class:*", resource)),
                ("subject_group", format!("{}:subject_group:*", resource)),
                ("grade", format!("{}:grade:*", resource)),
                ("admin_class", format!("{}:admin_class:*", resource)),
                ("tenant", format!("{}:tenant:*", resource)),
            ];
            
            for (scope_type, obj_pattern) in scope_checks {
                let permission_request = PermissionRequest {
                    subject: format!("role:{}", role.code),
                    domain: format!("tenant_{}", schema_name),
                    object: obj_pattern,
                    action: action.to_string(),
                };
                
                if self.casbin_service.enforce(&permission_request).await? {
                    scope_types.push(scope_type.to_string());
                }
            }
        }
        
        // 去重
        scope_types.sort();
        scope_types.dedup();
        
        Ok(scope_types)
    }
}
```

## 通配符规则的优势

### 1. 策略配置大幅简化

**传统方式（具体策略）：**
```sql
-- 需要为每个教学班配置策略
('p', 'user:zhang_teacher', 'tenant_001', 'homework:class_001', 'read', 'allow'),
('p', 'user:zhang_teacher', 'tenant_001', 'homework:class_002', 'read', 'allow'),
-- ... 可能有数百条
```

**通配符方式：**
```sql
-- 一条策略覆盖所有教学班
('p', 'role:teacher', 'tenant_*', 'homework:teaching_class:*', 'read', 'allow'),
```

### 2. 性能显著提升

- **策略数量减少**：从数千条具体策略减少到几十条通配符策略
- **匹配效率高**：Casbin 的 keyMatch2 函数性能优异
- **缓存友好**：策略数量少，缓存命中率高
- **内存占用低**：大幅减少内存使用

### 3. 维护成本降低

- **无需动态策略**：不需要在业务数据变更时同步更新 Casbin 策略
- **配置稳定**：通配符策略一次配置，长期有效
- **扩展容易**：新增数据范围类型只需添加对应的通配符策略
- **调试简单**：策略规则清晰，容易理解和调试

### 4. 使用示例

```rust
impl HomeworkController {
    /// 获取作业列表（使用通配符权限过滤）
    pub async fn get_homework_list(
        &self,
        auth_context: AuthContext,
        query_params: HomeworkQueryParams,
    ) -> Result<ApiResponse<Vec<HomeworkVO>>, AppError> {
        // 1. 构建查询
        let mut query = QueryBuilder::new("SELECT * FROM homeworks WHERE 1=1");
        
        // 2. 应用通配符权限过滤
        self.wildcard_filter.apply_filter(
            &mut query,
            &auth_context.permission_context,
            "homework",
            "read",
            &auth_context.schema_name,
        ).await?;
        
        // 3. 执行查询
        let homeworks = query.build_query_as::<HomeworkVO>()
            .fetch_all(&self.db)
            .await?;
        
        Ok(ApiResponse::success(homeworks))
    }
    
    /// 获取单个作业详情（通配符权限检查）
    pub async fn get_homework_detail(
        &self,
        auth_context: AuthContext,
        homework_id: Uuid,
    ) -> Result<ApiResponse<HomeworkVO>, AppError> {
        // 使用通配符权限检查器验证权限
        let has_permission = self.wildcard_checker.check_resource_permission(
            auth_context.user_id,
            "homework",
            "read",
            homework_id,
            &auth_context.schema_name,
        ).await?;
        
        if !has_permission {
            return Err(AppError::PermissionDenied("无权限访问该作业".to_string()));
        }
        
        // 获取作业详情
        let homework = self.homework_service.get_by_id(homework_id, &auth_context.schema_name).await?;
        
        Ok(ApiResponse::success(homework))
    }
}
```

## 总结

**Casbin 通配符规则完全可以走通，并且是最优解决方案：**

1. **技术可行性**：Casbin 的 keyMatch2 函数完美支持通配符匹配
2. **性能优势**：大幅减少策略数量，提高匹配效率
3. **维护简单**：策略配置稳定，无需动态更新
4. **扩展性强**：新增业务场景只需要添加对应的通配符策略
5. **开发效率**：简化权限逻辑，降低开发复杂度

这种设计既保持了权限控制的灵活性和安全性，又大大简化了系统的复杂度，是一个非常优雅和高效的解决方案。