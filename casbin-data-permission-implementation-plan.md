# Casbin 数据权限控制系统实现方案

## 实施概览

基于验证报告，本文档提供详细的实现方案，按优先级分阶段实施，确保系统满足所有需求。

## 第一阶段: 核心权限控制逻辑 (高优先级)

### 1.1 完善分层数据权限模型

#### 实现继承式权限模型
```rust
// 新增: backend/src/service/permission/hierarchy_resolver.rs
pub struct PermissionHierarchyResolver {
    casbin_service: Arc<MultiTenantCasbinService>,
}

impl PermissionHierarchyResolver {
    /// 解析用户的完整权限范围（包括继承权限）
    pub async fn resolve_user_permissions(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Vec<DataScope>> {
        // 1. 获取用户直接角色
        let direct_roles = self.get_user_direct_roles(user_id, tenant_id).await?;
        
        // 2. 获取继承角色
        let inherited_roles = self.get_inherited_roles(&direct_roles, tenant_id).await?;
        
        // 3. 合并所有角色的权限范围
        let all_roles = [direct_roles, inherited_roles].concat();
        
        // 4. 解析每个角色的数据范围
        let mut all_scopes = Vec::new();
        for role in all_roles {
            let role_scopes = self.get_role_data_scopes(&role, tenant_id, resource).await?;
            all_scopes.extend(role_scopes);
        }
        
        // 5. 合并和去重数据范围
        Ok(self.merge_data_scopes(all_scopes))
    }
    
    /// 合并多个数据范围，处理层级关系
    fn merge_data_scopes(&self, scopes: Vec<DataScope>) -> Vec<DataScope> {
        // 实现数据范围的智能合并
        // 例如：如果有学校级权限，则包含所有班级级权限
        // 如果有学科组权限，则包含该学科组下所有教学班权限
    }
}
```

#### 实现数据范围层级逻辑
```rust
// 新增: backend/src/service/permission/scope_hierarchy.rs
#[derive(Debug, Clone)]
pub enum ScopeLevel {
    Tenant,           // 租户级别
    School,           // 学校级别  
    SubjectGroup,     // 学科组级别
    TeachingClass,    // 教学班级别
    Grade,            // 年级组级别
    AdministrativeClass, // 行政班级别
    Individual,       // 个人级别
}

pub struct ScopeHierarchy {
    pool: PgPool,
}

impl ScopeHierarchy {
    /// 获取范围的所有子范围
    pub async fn get_child_scopes(
        &self,
        scope_type: ScopeLevel,
        scope_value: &str,
        schema_name: &str,
    ) -> Result<Vec<DataScope>> {
        match scope_type {
            ScopeLevel::School => {
                // 学校级别包含所有学科组、年级、班级
                self.get_school_child_scopes(scope_value, schema_name).await
            },
            ScopeLevel::SubjectGroup => {
                // 学科组包含所有相关教学班
                self.get_subject_group_teaching_classes(scope_value, schema_name).await
            },
            ScopeLevel::Grade => {
                // 年级包含所有相关行政班
                self.get_grade_administrative_classes(scope_value, schema_name).await
            },
            _ => Ok(vec![]),
        }
    }
}
```

### 1.2 实现业务资源数据过滤器

#### 考试数据过滤器
```rust
// 新增: backend/src/service/permission/data_filter/exam_filter.rs
pub struct ExamDataFilter {
    query_helper: CasbinQueryHelper,
    scope_hierarchy: ScopeHierarchy,
}

#[async_trait]
impl DataFilter for ExamDataFilter {
    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            return Ok(false); // 不需要过滤
        }

        // 2. 获取用户的考试权限范围
        let exam_scopes = self.get_user_exam_scopes(context).await?;
        
        if exam_scopes.is_empty() {
            // 无权限，返回空结果
            query_builder.push(" AND 1=0");
            count_builder.push(" AND 1=0");
            return Ok(true);
        }

        // 3. 构建过滤条件
        let mut conditions = Vec::new();
        
        for scope in exam_scopes {
            match scope.scope_type.as_str() {
                "school" => {
                    // 学校级权限：可以访问所有考试
                    conditions.push("1=1".to_string());
                    break;
                },
                "subject_group" => {
                    // 学科组权限：只能访问本学科组的考试
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.teaching_classes tc WHERE tc.subject_group_id = '{}' AND e.class_id = tc.id)",
                        context.schema_name, scope.scope_value
                    ));
                },
                "teaching_class" => {
                    // 教学班权限：只能访问特定班级的考试
                    conditions.push(format!("e.class_id = '{}'", scope.scope_value));
                },
                "grade" => {
                    // 年级权限：可以访问该年级所有班级的考试
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.administrative_classes ac WHERE ac.grade_level_code = '{}' AND e.admin_class_id = ac.id)",
                        context.schema_name, scope.scope_value
                    ));
                },
                _ => {}
            }
        }

        if !conditions.is_empty() {
            let filter_condition = format!(" AND ({})", conditions.join(" OR "));
            query_builder.push(&filter_condition);
            count_builder.push(&filter_condition);
            return Ok(true);
        }

        Ok(false)
    }
}
```

#### 成绩数据过滤器
```rust
// 新增: backend/src/service/permission/data_filter/grade_filter.rs
pub struct GradeDataFilter {
    query_helper: CasbinQueryHelper,
    scope_hierarchy: ScopeHierarchy,
}

#[async_trait]
impl DataFilter for GradeDataFilter {
    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        // 1. 获取用户身份
        let user_identities = self.query_helper.get_user_identities(context).await?;
        
        // 2. 检查用户角色和权限范围
        let mut conditions = Vec::new();
        
        for identity in user_identities {
            if identity.starts_with("teacher") {
                // 任课老师：只能查看自己教授班级的成绩
                let teaching_classes = self.query_helper.get_user_teaching_classes(context).await?;
                if !teaching_classes.is_empty() {
                    let class_ids: Vec<String> = teaching_classes.iter()
                        .map(|id| format!("'{}'", id))
                        .collect();
                    conditions.push(format!("g.class_id IN ({})", class_ids.join(",")));
                }
            } else if identity.starts_with("class_teacher") {
                // 班主任：可以查看所管理班级的所有成绩
                let managed_classes = self.query_helper.get_user_managed_classes(context).await?;
                if !managed_classes.is_empty() {
                    let class_ids: Vec<String> = managed_classes.iter()
                        .map(|id| format!("'{}'", id))
                        .collect();
                    conditions.push(format!("g.admin_class_id IN ({})", class_ids.join(",")));
                }
            } else if identity.starts_with("subject_leader") {
                // 学科组长：可以查看本学科组所有班级的成绩
                let subject_groups = self.query_helper.get_user_managed_subject_groups(context).await?;
                for sg_id in subject_groups {
                    conditions.push(format!(
                        "EXISTS (SELECT 1 FROM {}.teaching_classes tc WHERE tc.subject_group_id = '{}' AND g.class_id = tc.id)",
                        context.schema_name, sg_id
                    ));
                }
            } else if identity.starts_with("student") {
                // 学生：只能查看自己的成绩
                if let Some(student_id) = self.query_helper.get_user_student_id(context).await? {
                    conditions.push(format!("g.student_id = '{}'", student_id));
                }
            }
        }

        if !conditions.is_empty() {
            let filter_condition = format!(" AND ({})", conditions.join(" OR "));
            query_builder.push(&filter_condition);
            count_builder.push(&filter_condition);
            return Ok(true);
        }

        // 如果没有匹配的权限，拒绝访问
        query_builder.push(" AND 1=0");
        count_builder.push(" AND 1=0");
        Ok(true)
    }
}
```

### 1.3 完善教育场景权限控制

#### 教师权限控制服务
```rust
// 新增: backend/src/service/permission/teacher_permission.rs
pub struct TeacherPermissionService {
    pool: PgPool,
    casbin_service: Arc<MultiTenantCasbinService>,
}

impl TeacherPermissionService {
    /// 检查教师是否可以访问特定班级的数据
    pub async fn can_access_class_data(
        &self,
        teacher_user_id: &Uuid,
        class_id: &Uuid,
        class_type: &str, // "teaching" or "administrative"
        tenant_id: &str,
        action: &str,
    ) -> Result<bool> {
        // 1. 检查是否为任课老师
        if class_type == "teaching" {
            let is_teaching = self.is_teaching_class_teacher(teacher_user_id, class_id, tenant_id).await?;
            if is_teaching {
                return Ok(true);
            }
        }

        // 2. 检查是否为班主任
        if class_type == "administrative" {
            let is_homeroom = self.is_homeroom_teacher(teacher_user_id, class_id, tenant_id).await?;
            if is_homeroom {
                return Ok(true);
            }
        }

        // 3. 检查是否为学科组长
        let is_subject_leader = self.is_subject_group_leader(teacher_user_id, class_id, tenant_id).await?;
        if is_subject_leader {
            return Ok(true);
        }

        // 4. 检查是否为年级长
        let is_grade_leader = self.is_grade_leader(teacher_user_id, class_id, tenant_id).await?;
        if is_grade_leader {
            return Ok(true);
        }

        // 5. 使用 Casbin 检查其他权限
        let user_identity = format!("user:{}", teacher_user_id);
        let resource = format!("class:{}:{}", class_type, class_id);
        
        self.casbin_service.enforce(&PermissionRequest {
            subject: user_identity,
            domain: tenant_id.to_string(),
            object: resource,
            action: action.to_string(),
        }).await
    }

    /// 获取教师可访问的所有班级
    pub async fn get_accessible_classes(
        &self,
        teacher_user_id: &Uuid,
        tenant_id: &str,
        class_type: &str,
    ) -> Result<Vec<Uuid>> {
        let mut accessible_classes = Vec::new();

        // 1. 获取任课班级
        if class_type == "teaching" {
            let teaching_classes = self.get_teaching_classes(teacher_user_id, tenant_id).await?;
            accessible_classes.extend(teaching_classes);
        }

        // 2. 获取班主任班级
        if class_type == "administrative" {
            let homeroom_classes = self.get_homeroom_classes(teacher_user_id, tenant_id).await?;
            accessible_classes.extend(homeroom_classes);
        }

        // 3. 获取学科组管理的班级
        let subject_group_classes = self.get_subject_group_classes(teacher_user_id, tenant_id).await?;
        accessible_classes.extend(subject_group_classes);

        // 4. 获取年级管理的班级
        let grade_classes = self.get_grade_classes(teacher_user_id, tenant_id).await?;
        accessible_classes.extend(grade_classes);

        // 去重并返回
        accessible_classes.sort();
        accessible_classes.dedup();
        Ok(accessible_classes)
    }
}
```

## 第二阶段: 性能优化和缓存 (中优先级)

### 2.1 权限缓存机制

#### Redis 缓存实现
```rust
// 新增: backend/src/service/permission/cache.rs
use redis::{Client, Commands, Connection};

pub struct PermissionCache {
    redis_client: Client,
    cache_ttl: u64, // 缓存过期时间（秒）
}

impl PermissionCache {
    pub fn new(redis_url: &str, cache_ttl: u64) -> Result<Self> {
        let client = Client::open(redis_url)?;
        Ok(Self {
            redis_client: client,
            cache_ttl,
        })
    }

    /// 缓存用户权限范围
    pub async fn cache_user_permissions(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        permissions: &[DataScope],
    ) -> Result<()> {
        let mut conn = self.redis_client.get_connection()?;
        let cache_key = format!("permissions:{}:{}:{}", user_id, tenant_id, resource);
        let serialized = serde_json::to_string(permissions)?;
        
        conn.setex(cache_key, self.cache_ttl, serialized)?;
        Ok(())
    }

    /// 获取缓存的用户权限范围
    pub async fn get_cached_permissions(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Option<Vec<DataScope>>> {
        let mut conn = self.redis_client.get_connection()?;
        let cache_key = format!("permissions:{}:{}:{}", user_id, tenant_id, resource);
        
        let cached: Option<String> = conn.get(cache_key)?;
        if let Some(serialized) = cached {
            let permissions: Vec<DataScope> = serde_json::from_str(&serialized)?;
            Ok(Some(permissions))
        } else {
            Ok(None)
        }
    }

    /// 清除用户权限缓存
    pub async fn clear_user_cache(&self, user_id: &Uuid, tenant_id: &str) -> Result<()> {
        let mut conn = self.redis_client.get_connection()?;
        let pattern = format!("permissions:{}:{}:*", user_id, tenant_id);
        
        let keys: Vec<String> = conn.keys(pattern)?;
        if !keys.is_empty() {
            conn.del(keys)?;
        }
        Ok(())
    }
}
```

### 2.2 性能监控

#### 权限检查性能监控
```rust
// 修改: backend/src/middleware/permission_middleware.rs
pub async fn permission_middleware(
    State(casbin_service): State<Arc<MultiTenantCasbinService>>,
    Extension(config): Extension<PermissionConfig>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = std::time::Instant::now();
    
    // ... 现有权限检查逻辑 ...
    
    let duration = start_time.elapsed();
    
    // 性能监控
    if duration.as_millis() > 10 {
        warn!(
            "Permission check took {}ms for user {} (exceeds 10ms threshold)",
            duration.as_millis(),
            auth_context.user_id
        );
    }
    
    // 记录性能指标
    metrics::histogram!("permission_check_duration_ms", duration.as_millis() as f64);
    
    Ok(next.run(request).await)
}
```

## 第三阶段: 审计和配置功能 (中优先级)

### 3.1 完善权限审计

#### 敏感数据访问审计
```rust
// 新增: backend/src/service/permission/audit.rs
pub struct PermissionAuditService {
    pool: PgPool,
}

impl PermissionAuditService {
    /// 记录敏感数据访问
    pub async fn log_sensitive_data_access(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        action: &str,
        target_ids: &[Uuid],
        request_ip: Option<&str>,
    ) -> Result<()> {
        let query = r#"
            INSERT INTO public.sensitive_data_access_log 
            (user_id, tenant_id, resource, action, target_ids, request_ip, accessed_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
        "#;
        
        sqlx::query(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(resource)
            .bind(action)
            .bind(target_ids)
            .bind(request_ip)
            .execute(&self.pool)
            .await?;
            
        Ok(())
    }

    /// 记录权限检查失败
    pub async fn log_permission_failure(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        resource: &str,
        action: &str,
        failure_reason: &str,
        request_ip: Option<&str>,
    ) -> Result<()> {
        let query = r#"
            INSERT INTO public.permission_failure_log 
            (user_id, tenant_id, resource, action, failure_reason, request_ip, failed_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
        "#;
        
        sqlx::query(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(resource)
            .bind(action)
            .bind(failure_reason)
            .bind(request_ip)
            .execute(&self.pool)
            .await?;
            
        Ok(())
    }
}
```

### 3.2 权限配置工具

#### 权限预览服务
```rust
// 新增: backend/src/service/permission/preview.rs
pub struct PermissionPreviewService {
    casbin_service: Arc<MultiTenantCasbinService>,
    hierarchy_resolver: PermissionHierarchyResolver,
}

impl PermissionPreviewService {
    /// 预览角色分配后的权限范围
    pub async fn preview_role_assignment(
        &self,
        user_id: &Uuid,
        tenant_id: &str,
        role_codes: &[String],
        target_type: Option<&str>,
        target_id: Option<&Uuid>,
    ) -> Result<PermissionPreview> {
        // 1. 模拟角色分配
        let simulated_roles = self.simulate_role_assignment(
            user_id, tenant_id, role_codes, target_type, target_id
        ).await?;
        
        // 2. 计算权限范围
        let mut preview = PermissionPreview::new();
        
        for resource in ["student", "exam", "grade", "class"] {
            let permissions = self.hierarchy_resolver
                .resolve_simulated_permissions(user_id, tenant_id, resource, &simulated_roles)
                .await?;
            preview.add_resource_permissions(resource, permissions);
        }
        
        // 3. 计算可访问的菜单
        let accessible_menus = self.get_accessible_menus(&simulated_roles, tenant_id).await?;
        preview.set_accessible_menus(accessible_menus);
        
        Ok(preview)
    }
}

#[derive(Debug, Serialize)]
pub struct PermissionPreview {
    pub resource_permissions: HashMap<String, Vec<DataScope>>,
    pub accessible_menus: Vec<String>,
    pub summary: PermissionSummary,
}
```

## 实施时间表

| 阶段 | 功能 | 预估时间 | 依赖关系 |
|------|------|----------|----------|
| 第一阶段 | 分层权限模型 | 1周 | 无 |
| | 业务资源过滤器 | 1.5周 | 分层权限模型 |
| | 教育场景权限控制 | 1周 | 业务资源过滤器 |
| 第二阶段 | 权限缓存机制 | 1周 | 第一阶段完成 |
| | 性能监控 | 0.5周 | 权限缓存机制 |
| 第三阶段 | 权限审计 | 1周 | 第二阶段完成 |
| | 权限配置工具 | 1.5周 | 权限审计 |

**总计**: 7.5周

## 风险评估

### 高风险
- **性能影响**: 新的权限检查逻辑可能影响系统性能
- **数据一致性**: 权限变更时的数据一致性保证

### 中风险  
- **复杂度增加**: 系统复杂度显著增加，维护成本上升
- **测试覆盖**: 需要大量的权限场景测试

### 低风险
- **用户体验**: 权限控制过于严格可能影响用户体验

## 成功标准

1. **性能要求**: 权限检查在10ms内完成
2. **功能完整性**: 所有9个需求100%实现
3. **测试覆盖**: 权限相关代码测试覆盖率>90%
4. **文档完整**: 完整的权限配置和使用文档
