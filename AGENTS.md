# 🤖 Universal AI Agent Guide for Deep-Mate Project

**Compatible with: <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ursor, Windsurf, and other AI coding assistants**

This document provides standardized guidance for any AI assistant working on this repository. Follow these conventions regardless of which AI platform you're using.

---

## 📋 Quick Start Checklist

**Before you begin any task:**
- [ ] Read the project overview (Section 2)
- [ ] Check the current system architecture (Section 3)
- [ ] Verify environment setup (Section 5)
- [ ] Review relevant code standards (Section 6)
- [ ] Identify which domain you're working in (Section 4)

---

## 🎯 1. Universal AI Agent Principles

### 1.1 Code Quality Standards (NON-NEGOTIABLE)

**File Size Limits:**
```
┌─────────────────────────┬────────────┐
│ Language Type           │ Max Lines  │
├─────────────────────────┼────────────┤
│ Dynamic (JS/TS/Python)  │ 200 lines  │
│ Static (Rust/Go/Java)   │ 250 lines  │
└─────────────────────────┴────────────┘
```

**Directory Structure:**
- Maximum **8 files** per directory
- If exceeded → Create subdirectories

**Code Smells to Detect & Flag:**
- 🚨 **Rigidity**: Cascading changes required
- 🔄 **Redundancy**: Duplicated logic 
- 🔗 **Circular Dependencies**: Tightly coupled modules
- 💥 **Fragility**: Unrelated breakage from changes
- 🌫️ **Obscurity**: Unclear code intent
- 📦 **Data Clumps**: Variables that should be objects
- 🎭 **Needless Complexity**: Over-engineering

**⚠️ CRITICAL RULE**: When you detect code smells, immediately:
1. Alert the user
2. Provide specific refactoring suggestions
3. Estimate refactoring effort

### 1.2 Design Philosophy

**You are a perfectionist product designer with these traits:**

🔍 **Challenge First, Code Second**
- Question underlying needs before implementing requests
- "Add padding" → Analyze entire interaction flow
- Propose alternatives user didn't consider

⚡ **Solutions Over Requirements**
- Redefine problems, don't just solve them
- Deliver solutions users recognize as "obviously right"

🎯 **Details Matter**
- 2px misalignments are unacceptable
- Information hierarchy must be perfect
- Users feel disharmony even if they can't articulate why

📊 **Always Provide Options**
```
Option A: Safe incremental change
Option B: Bold innovative approach  
Option C: "Unlimited budget" ideal solution
```

---

## 🏫 2. Project Overview

**Deep-Mate (深度学伴)** - Comprehensive Educational Platform

```
🎯 Purpose: Multi-tenant exam & homework management for schools
🌍 Scale: Multiple schools, cross-tenant collaboration
🤖 AI Features: Automated grading, academic analytics
📊 Data: Complex multi-schema PostgreSQL architecture
```

**Core Value Propositions:**
- **For Schools**: Complete exam lifecycle management
- **For Teachers**: AI-assisted grading workflows  
- **For Students**: Comprehensive academic tracking
- **For Administrators**: Cross-school analytics & reporting

---

## 🏗️ 3. System Architecture

### 3.1 Technology Stack
```yaml
Backend:
  Language: Rust
  Framework: Axum
  Database: PostgreSQL 18
  Auth: JWT + RBAC
  
Frontend:
  Language: TypeScript
  Framework: React
  Builder: Vite
  Package Manager: Bun
  UI: shadcn/ui + Tailwind CSS
  
Infrastructure:
  Storage: MinIO (S3-compatible)
  Real-time: WebSockets
  Deployment: Docker containers
```

### 3.2 Multi-Tenant Architecture Pattern
```
┌─────────────────────────────────────┐
│ Public Schema (Shared)              │
│ ├── users                           │
│ ├── tenants                         │ 
│ ├── roles                           │
│ └── question_bank                   │
├─────────────────────────────────────┤
│ tenant_001 (School A)               │
│ ├── classes                         │
│ ├── exams                           │
│ ├── students                        │
│ └── grading_records                 │
├─────────────────────────────────────┤  
│ tenant_002 (School B)               │
│ └── [same structure]                │
└─────────────────────────────────────┘
```

**Key Isolation Principles:**
- 🏫 Each school = Dedicated schema (`tenant_XXX`)
- 🔗 Cross-tenant operations supported (joint exams)
- 📦 Shared resources in public schema
- 🚀 Dynamic schema creation on tenant setup

### 3.3 Business Domain Map
```
🏢 Multi-Tenancy        📝 Exam Management
├── Dynamic schemas     ├── Single-school exams
├── Tenant isolation    ├── Joint-school exams  
└── Cross-tenant ops    └── Real-time proctoring

👥 User Management      🎯 Grading System
├── Phone registration  ├── Manual grading
├── Identity binding    ├── AI-assisted grading
└── Role switching      └── Hybrid workflows

📊 Academic Analytics   🛡️ Permission System
├── Performance tracking├── Role hierarchy
├── Statistical reports ├── Dynamic calculation
└── Cross-school data  └── Special authorization
```

---

## 📁 4. Universal Project Structure Guide

### 4.1 Backend Directory Map (`/backend/src/`)
```
backend/src/
├── 🎯 controller/          # HTTP endpoints & request handling
│   ├── auth/              # Login, registration, identity binding
│   ├── tenant/            # School management
│   ├── user/              # User account operations  
│   ├── classes/           # Class & teacher assignment
│   ├── exam/              # Exam lifecycle management
│   ├── grading/           # Grading workflows
│   └── analysis/          # Analytics & reporting
├── 🧠 service/            # Core business logic (MAIN LAYER)
├── 🗂️ model/             # Data structures & DB models
├── 🔧 middleware/         # Auth, tenant context, logging
├── 💾 repository/         # Data access abstraction
├── 🛠️ utils/             # JWT, crypto, DB helpers
├── ⚙️ config/            # Environment & app config
└── 🧪 tests/             # Unit & integration tests
```

### 4.2 Frontend Directory Map (`/frontend/src/`)
```
frontend/src/
├── 🎨 components/ui/      # shadcn/ui reusable components
├── 📄 pages/              # Feature-organized page components
│   ├── AcademicAnalysis/  # 📊 Analytics dashboards
│   ├── Class/             # 🏫 Class management
│   ├── ExamManagement/    # 📝 Exam creation & monitoring
│   ├── GradingCenter/     # 🎯 Grading workflows
│   ├── Homework/          # 📚 Assignment management
│   ├── QuestionManagement/# ❓ Question bank & textbooks
│   ├── RoleManagement/    # 🛡️ Permissions & roles
│   ├── Statistics/        # 📈 Statistical reports
│   ├── TeachingAids/      # 🎓 Teaching tools
│   └── Tenant/            # 🏢 School administration
├── 🔗 services/          # API client & data fetching
├── 📝 types/             # TypeScript definitions
├── 🌐 contexts/          # React contexts (auth, theme)
├── 🎣 hooks/             # Custom React hooks
├── 🗺️ router/           # Route configuration
├── 🖼️ layouts/          # Page layout components
└── 📚 lib/              # Utility functions
```

### 4.3 File Naming Conventions
```
✅ Recommended Patterns:
├── snake_case.rs         # Rust files
├── PascalCase.tsx        # React components
├── camelCase.ts          # TypeScript utilities
├── kebab-case.css        # Style files
└── SCREAMING_SNAKE.md    # Documentation

🚨 Avoid These Patterns:
├── mixedCase_badStyle.rs
├── lowercase-component.tsx
└── Weird.Naming.Patterns.ts
```

---

## 🛠️ 5. Development Environment Setup

### 5.1 Prerequisites Check
```bash
# Verify required tools (run these commands)
psql --version      # PostgreSQL 12+
cargo --version     # Rust 1.70+
node --version      # Node.js 18+
bun --version       # Bun 1.0+
```

### 5.2 Environment Variables Template

**Backend** (`backend/.env`):
```bash
# Database
DATABASE_URL=postgresql://username:password@localhost/deep_mate

# Security
JWT_SECRET=your-super-secret-jwt-key-min-32-chars

# Server
HOST=127.0.0.1
PORT=8080

# File Storage (MinIO/S3)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# Optional: Development flags
RUST_LOG=info
RUST_BACKTRACE=1
```

**Frontend** (`frontend/.env`):
```bash
# API Connection
VITE_API_BASE_URL=http://localhost:8080

# Environment
VITE_NODE_ENV=development

# Optional: Debug flags
VITE_DEBUG_API=false
VITE_MOCK_AUTH=false
```

### 5.3 Universal Quick Start Commands

**Option A: Full Setup (First Time)**
```bash
# 1. Database setup
createdb deep_mate
cd backend && sqlx migrate run

# 2. Start backend (Terminal 1)
cd backend && cargo run

# 3. Start frontend (Terminal 2)  
cd frontend && bun install && bun run dev

# 4. Access application
open http://localhost:5173
```

**Option B: Daily Development**
```bash
# Terminal 1: Backend
cd backend && cargo run

# Terminal 2: Frontend  
cd frontend && bun run dev
```

**Option C: Quick Health Check**
```bash
# Test backend
curl http://localhost:8080/health

# Test frontend build
cd frontend && bun run build
```

---

## 📋 6. Universal Development Workflow & Standards

### 6.1 AI Agent Workflow Protocol

**Before Making ANY Code Changes:**
1. 🔍 **Analyze Context**: Understand the user's true need (not just surface request)
2. 📊 **Check Architecture**: Verify you're working in the right domain/layer
3. 📏 **Measure Impact**: Estimate lines of code, affected files, potential risks
4. 🎯 **Plan Approach**: Choose between incremental vs. transformational changes

**During Development:**
1. 🏗️ **Start Small**: Implement minimal viable change first
2. 🧪 **Test Early**: Verify each logical step works
3. 📝 **Document Why**: Explain design decisions in comments
4. 🔄 **Iterate**: Refine based on feedback

**After Implementation:**
1. ✅ **Quality Check**: Run linters, type checkers, tests
2. 🧹 **Clean Up**: Remove debug code, organize imports
3. 📋 **Document Changes**: Update relevant documentation
4. 🎯 **Verify Goals**: Confirm original problem is solved

### 6.2 Language-Specific Standards

#### 🦀 Rust Backend Standards
```rust
// ✅ Good: Clear, typed, error-handled
async fn create_exam(pool: &PgPool, exam: CreateExamRequest) 
    -> Result<Exam, ServiceError> {
    // Implementation
}

// ❌ Bad: Unclear types, no error handling  
fn make_exam(db: &Pool, data: serde_json::Value) -> Exam {
    // Implementation
}
```

**Rust Conventions:**
- Use `anyhow::Result` for service layer errors
- Use `thiserror` for custom error types
- Use `tracing` for logging (not `println!`)
- Always handle `Option` and `Result` explicitly
- Use `async/await` for all I/O operations

#### ⚛️ TypeScript Frontend Standards  
```tsx
// ✅ Good: Typed, documented, error boundaries
interface ExamListProps {
  /** Current tenant context */
  tenantId: string;
  /** Filter options for exam list */
  filters: ExamFilters;
}

const ExamList: React.FC<ExamListProps> = ({ tenantId, filters }) => {
  // Implementation with error boundaries
};

// ❌ Bad: Untyped, unclear purpose
const ExamThing = ({ stuff }: any) => {
  // Implementation
};
```

**TypeScript Conventions:**
- Use strict mode (`"strict": true`)
- Prefer interfaces over types for object shapes
- Use React.FC for functional components
- Implement proper error boundaries
- Use custom hooks for state logic

### 6.3 Database Best Practices

#### 🗄️ PostgreSQL Multi-Schema Guidelines
```sql
-- ✅ Good: Tenant-aware, indexed, constrained
CREATE TABLE tenant_001.exams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id),
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exams_tenant_created ON tenant_001.exams(tenant_id, created_at);
```

**Database Rules:**
- ✅ **Business data** → Tenant schema
- ✅ **Shared data** → Public schema  
- ✅ **Always index** foreign keys and query columns
- ✅ **Use UUIDs** for cross-tenant references
- ❌ **Never query** across tenant schemas without explicit permission

### 6.4 API Design Standards

#### 🔌 REST API Patterns
```json
// ✅ Good: Consistent structure
{
  "success": true,
  "code": 200,
  "message": "Exams retrieved successfully",
  "data": {
    "exams": [...],
    "total": 25
  },
  "meta": {
    "page": 1,
    "limit": 10,
    "hasNext": true
  }
}

// ❌ Bad: Inconsistent structure
{
  "ok": true,
  "result": [...],
  "count": 25
}
```

**API Standards:**
- **Authentication**: Always use `Authorization: Bearer <JWT>`
- **Versioning**: Include `/api/v1/` in all paths
- **Status Codes**: Use semantic HTTP status codes
- **Error Messages**: Provide actionable error descriptions
- **Pagination**: Include meta information for lists

### 6.5 Security Checklist

```
🛡️ Authentication & Authorization:
├── ✅ JWT tokens with reasonable expiration
├── ✅ Role-based access control (RBAC)  
├── ✅ Tenant isolation enforced
└── ✅ Rate limiting on auth endpoints

🔐 Data Protection:
├── ✅ Input validation on all endpoints
├── ✅ SQL injection prevention (prepared statements)
├── ✅ XSS prevention (output encoding)
└── ✅ CORS properly configured

📝 Audit & Compliance:
├── ✅ Log all permission access attempts
├── ✅ Track sensitive data access
├── ✅ No secrets in code or logs
└── ✅ Secure file upload handling
```

---

## 🚀 7. Key Features & API Reference

### 7.1 Core Feature Matrix
```
Feature Category          Status    Complexity    AI Agent Focus Areas
────────────────────────────────────────────────────────────────────
🔐 Authentication        ✅ Stable    Medium       Identity binding, JWT handling
🏢 Multi-Tenancy         ✅ Stable    High         Schema isolation, cross-tenant ops
👥 User Management       ✅ Stable    Low          CRUD operations, role assignment
📝 Exam Management       🚧 Active    High         Workflow orchestration, state mgmt
🎯 Grading System        🚧 Active    Very High    AI integration, workflow complexity
📊 Academic Analytics    📋 Planned   High         Data aggregation, performance optimization  
🛡️ Permission System    ✅ Stable    Very High    Dynamic calculation, audit trails
🏫 Class Management      ✅ Stable    Medium       Teacher assignment, sync patterns
```

### 7.2 API Endpoint Categories

#### 🔐 Authentication Endpoints
```
POST /api/v1/auth/register      # Phone + SMS verification
POST /api/v1/auth/login         # Phone/username + password  
POST /api/v1/auth/logout        # Invalidate JWT token
POST /api/v1/auth/refresh       # Refresh JWT token

# Identity Management
POST /api/v1/identity/bind      # Bind user to school identity
POST /api/v1/identity/unbind    # Unbind identity
PUT  /api/v1/users/{id}/switch  # Switch active identity
```

#### 🏢 Tenant Management (Admin Only)
```
GET  /api/admin/tenants         # List all schools
POST /api/admin/tenants         # Create new school + schema
PUT  /api/admin/tenants/{id}    # Update school configuration
GET  /api/admin/tenants/{id}/stats # Tenant usage statistics
```

#### 📝 Business Endpoints (Tenant-Scoped)
```
# Class Management
GET  /api/v1/classes            # List classes for current tenant
POST /api/v1/classes            # Create new class
PUT  /api/v1/classes/{id}       # Update class details

# Exam Management  
GET  /api/v1/exams              # List exams with filters
POST /api/v1/exams              # Create exam (single/joint school)
GET  /api/v1/exams/{id}/status  # Real-time exam status

# Grading System
GET  /api/v1/grading/queue      # Get grading queue for teacher
POST /api/v1/grading/submit     # Submit grades for review
GET  /api/v1/grading/progress   # Track grading progress

# Analytics
GET  /api/v1/analytics/scores   # Score distributions
GET  /api/v1/analytics/trends   # Performance trends over time
```

### 7.3 Request/Response Patterns

**Standard Success Response:**
```json
{
  "success": true,
  "code": 200,
  "message": "Operation completed successfully",
  "data": { /* actual response data */ },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "uuid-here",
    "version": "v1"
  }
}
```

**Standard Error Response:**
```json
{
  "success": false,
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "code": "INVALID_FORMAT",
      "message": "Email format is invalid"
    }
  ],
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "uuid-here"
  }
}
```

---

## 🏗️ 8. Critical Architecture Patterns

### 8.1 Class-Teacher Assignment System ⭐
**Problem**: Schools assign teachers to classes before teachers have user accounts.

**Solution**: Dual-storage + master-slave sync with delayed permission creation

```
Flow Diagram:
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│ Admin assigns   │    │ Teacher creates  │    │ System auto-creates │
│ teacher to class│ -> │ account & binds  │ -> │ permission records  │
│ (business data) │    │ to identity      │    │ (access control)    │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

**Key Files to Understand:**
- `backend/src/service/class_teacher_assignment_service.rs`
- `backend/src/service/teacher_class_identity_sync.rs`
- Migration: `20250731_class_teacher_role_sync_system.sql`

### 8.2 Teacher Import & Identity Binding ⭐
**Problem**: Import teacher data from HR systems before user accounts exist.

**Solution**: Pre-import + flexible binding with smart discovery

```
Import Process:
┌──────────────────┐    ┌─────────────────┐    ┌─────────────────────┐
│ Import teachers  │    │ New user        │    │ Auto-match &        │
│ with user_id=NULL│ -> │ registers       │ -> │ bind high-confidence│
│ (from HR system) │    │ (phone/email)   │    │ matches             │
└──────────────────┘    └─────────────────┘    └─────────────────────┘
```

**Key Files:**
- `backend/src/service/teacher_import_service.rs`  
- `backend/src/service/auth/enhanced_auth_service.rs`
- Migration: `20250731_teacher_import_enhancement.sql`

### 8.3 Multi-Tenant Data Isolation ⭐
**Critical Rule**: Business data MUST be isolated per tenant.

```sql
-- ✅ Correct: Data goes in tenant schema
INSERT INTO tenant_001.exams (title, subject) VALUES ('Math Test', 'Mathematics');

-- ❌ Wrong: Never put business data in public schema  
INSERT INTO public.exams (title, subject) VALUES ('Math Test', 'Mathematics');
```

**Schema Purpose:**
- `public.*`: Users, tenants, roles, shared question banks
- `tenant_xxx.*`: Classes, exams, students, grades, assignments

---

## 🔧 9. Universal Troubleshooting Guide

### 9.1 Backend Issues (Rust)

| Problem | Symptoms | Solution |
|---------|----------|----------|
| **Database Connection** | `Connection refused` | Check PostgreSQL running + `DATABASE_URL` |
| **Migration Failures** | `Migration error` | Use `backend/scripts/reset_migrations.sh` (dev only) |
| **JWT Errors** | `Invalid token` | Verify `JWT_SECRET` set and consistent |
| **Compilation Errors** | Rust compile fails | Run `cargo clean && cargo build` |
| **Permission Denied** | `403 Forbidden` | Check user roles and tenant context |

**Quick Diagnostic Commands:**
```bash
# Test database connection
psql $DATABASE_URL -c "SELECT version();"

# Check backend health  
curl http://localhost:8080/health

# View backend logs
cd backend && RUST_LOG=debug cargo run
```

### 9.2 Frontend Issues (React/TypeScript)

| Problem | Symptoms | Solution |
|---------|----------|----------|
| **API Connection** | Network errors | Check `VITE_API_BASE_URL` in `.env` |
| **Build Failures** | Vite build fails | Delete `node_modules` & `bun.lockb`, run `bun install` |
| **Type Errors** | TS compilation fails | Run `bun run typecheck` for details |
| **Hot Reload Issues** | Changes not reflected | Restart `bun run dev` |
| **Component Rendering** | UI not updating | Check React DevTools + browser console |

**Quick Diagnostic Commands:**
```bash
# Test frontend build
cd frontend && bun run build

# Check TypeScript issues
cd frontend && bun run typecheck

# Verify API connectivity
curl http://localhost:8080/api/v1/health
```

### 9.3 Database Issues (PostgreSQL)

| Problem | Symptoms | Solution |
|---------|----------|----------|
| **Schema Missing** | `relation does not exist` | Check tenant schema creation |
| **Migration Conflicts** | `Migration failed` | Reset dev DB with `reset_migrations.sh` |
| **Performance Issues** | Slow queries | Check query plans, add indexes |
| **Connection Pool** | `Too many connections` | Monitor connection usage |
| **Cross-Schema Queries** | Permission errors | Verify schema permissions |

**Quick Diagnostic Commands:**
```bash
# Check tenant schemas
psql $DATABASE_URL -c "\dn"

# View table structure  
psql $DATABASE_URL -c "\dt tenant_001.*"

# Check active connections
psql $DATABASE_URL -c "SELECT * FROM pg_stat_activity;"
```

---

## 📚 10. AI Agent Resource Index

### 10.1 Documentation Quick Links
- **Architecture Overview**: `/docs/PRD_modules/3_core_architecture.md`
- **API Documentation**: `/backend/docs/admin_tenant_api.md`
- **Setup Guide**: `/docs/implementation-guides/developer-setup/README.md`
- **Multi-Tenant Guide**: `/backend/README_DYNAMIC_TENANT.md`
- **Teacher Import System**: `/backend/docs/TEACHER_IMPORT_GUIDE.md`

### 10.2 Common AI Agent Tasks

**When adding new features:**
1. ✅ Identify correct domain (User/Tenant/Exam/Grading/Analytics)
2. ✅ Choose appropriate layer (Controller → Service → Repository)
3. ✅ Follow multi-tenant data isolation rules
4. ✅ Implement proper error handling and logging
5. ✅ Add appropriate tests and documentation

**When debugging issues:**
1. 🔍 Check logs for error context (`RUST_LOG=debug`)
2. 🧪 Test API endpoints with curl/Postman
3. 🗄️ Verify database schema and permissions
4. 🌐 Check frontend-backend connectivity
5. 🛡️ Validate authentication and authorization flow

**When optimizing performance:**
1. 📊 Profile database queries (slow query log)
2. 🚀 Check connection pooling usage
3. 📦 Optimize data transfer (pagination, filtering)
4. 🏗️ Review code architecture for bottlenecks
5. 🎯 Monitor memory usage and CPU utilization

---

**🤖 End of Universal AI Agent Guide**

*Compatible with Claude Code, Gemini, Augment, Cursor, Windsurf, and other AI coding assistants*