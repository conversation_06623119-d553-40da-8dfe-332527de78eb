use sqlx::postgres::PgQueryBuilder;
use sqlx::QueryBuilder;
use uuid::Uuid;

fn main() {
    // Test the IN clause generation
    let mut query_builder: QueryBuilder<sqlx::Postgres> = QueryBuilder::new("SELECT h.* FROM tenant_gzcms.homework h WHERE 1=1");
    
    // Simulate what our fixed code should do
    let where_clause = "h.subject_group_id IN (PLACEHOLDER_LIST)";
    let params = vec![
        Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap(),
        Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap(),
    ];
    
    // Apply the fix logic
    if where_clause.contains("PLACEHOLDER_LIST") {
        if !params.is_empty() {
            // Replace PLACEHOLDER_LIST with empty string to get the field part
            let field_part = where_clause.replace("(PLACEHOLDER_LIST)", "");
            query_builder.push(" AND ").push(&field_part).push("(");
            
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    query_builder.push(", ");
                }
                query_builder.push_bind(*param);
            }
            
            query_builder.push(")");
        }
    }
    
    println!("Generated SQL: {}", query_builder.sql());
    println!("Expected: SELECT h.* FROM tenant_gzcms.homework h WHERE 1=1 AND h.subject_group_id IN ($1, $2)");
}
